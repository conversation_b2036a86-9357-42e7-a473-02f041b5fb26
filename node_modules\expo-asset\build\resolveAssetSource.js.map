{"version": 3, "file": "resolveAssetSource.js", "sourceRoot": "", "sources": ["../src/resolveAssetSource.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,wCAAwC,CAAC;AAEtE,OAAO,mBAA4C,MAAM,uBAAuB,CAAC;AAEjF,IAAI,wBAAgF,CAAC;AAErF,MAAM,UAAU,0BAA0B,CACxC,WAAmE;IAEnE,wBAAwB,GAAG,WAAW,CAAC;AACzC,CAAC;AASD;;;GAGG;AACH,SAAS,kBAAkB,CAAC,MAAW;IACrC,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QAC/B,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;IACnC,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,QAAQ,GAAG,IAAI,mBAAmB;IACtC,8CAA8C;IAC9C,kBAAkB,EAClB,IAAI,EACJ,KAAK,CACN,CAAC;IACF,IAAI,wBAAwB,EAAE,CAAC;QAC7B,OAAO,wBAAwB,CAAC,QAAQ,CAAC,CAAC;IAC5C,CAAC;IACD,OAAO,QAAQ,CAAC,YAAY,EAAE,CAAC;AACjC,CAAC;AAED,MAAM,CAAC,cAAc,CAAC,kBAAkB,EAAE,4BAA4B,EAAE;IACtE,GAAG;QACD,OAAO,0BAA0B,CAAC;IACpC,CAAC;CACF,CAAC,CAAC;AAEH,eAAe,kBAAwC,CAAC;AAExD,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,GAAG,mBAAmB,CAAC", "sourcesContent": ["import { getAssetByID } from '@react-native/assets-registry/registry';\n\nimport AssetSourceResolver, { ResolvedAssetSource } from './AssetSourceResolver';\n\nlet _customSourceTransformer: (resolver: AssetSourceResolver) => ResolvedAssetSource;\n\nexport function setCustomSourceTransformer(\n  transformer: (resolver: AssetSourceResolver) => ResolvedAssetSource\n): void {\n  _customSourceTransformer = transformer;\n}\n\ninterface resolveAssetSource {\n  (source: any): ResolvedAssetSource | null;\n  setCustomSourceTransformer(\n    transformer: (resolver: AssetSourceResolver) => ResolvedAssetSource\n  ): ResolvedAssetSource;\n}\n\n/**\n * `source` is either a number (opaque type returned by require('./foo.png'))\n * or an `ImageSource` like { uri: '<http location || file path>' }\n */\nfunction resolveAssetSource(source: any): ResolvedAssetSource | null {\n  if (typeof source === 'object') {\n    return source;\n  }\n\n  const asset = getAssetByID(source);\n  if (!asset) {\n    return null;\n  }\n\n  const resolver = new AssetSourceResolver(\n    // Doesn't matter since this is removed on web\n    'https://expo.dev',\n    null,\n    asset\n  );\n  if (_customSourceTransformer) {\n    return _customSourceTransformer(resolver);\n  }\n  return resolver.defaultAsset();\n}\n\nObject.defineProperty(resolveAssetSource, 'setCustomSourceTransformer', {\n  get() {\n    return setCustomSourceTransformer;\n  },\n});\n\nexport default resolveAssetSource as resolveAssetSource;\n\nexport const { pickScale } = AssetSourceResolver;\n"]}