const t=1024;let e=0,i=class{constructor(t,e){this.from=t,this.to=e}};class n{constructor(t={}){this.id=e++,this.perNode=!!t.perNode,this.deserialize=t.deserialize||(()=>{throw new Error("This node type doesn't define a deserialize function")})}add(t){if(this.perNode)throw new RangeError("Can't add per-node props to node types");return"function"!=typeof t&&(t=o.match(t)),e=>{let i=t(e);return void 0===i?null:[this,i]}}}n.closedBy=new n({deserialize:t=>t.split(" ")}),n.openedBy=new n({deserialize:t=>t.split(" ")}),n.group=new n({deserialize:t=>t.split(" ")}),n.isolate=new n({deserialize:t=>{if(t&&"rtl"!=t&&"ltr"!=t&&"auto"!=t)throw new RangeError("Invalid value for isolate: "+t);return t||"auto"}}),n.contextHash=new n({perNode:!0}),n.lookAhead=new n({perNode:!0}),n.mounted=new n({perNode:!0});class s{constructor(t,e,i){this.tree=t,this.overlay=e,this.parser=i}static get(t){return t&&t.props&&t.props[n.mounted.id]}}const r=Object.create(null);class o{constructor(t,e,i,n=0){this.name=t,this.props=e,this.id=i,this.flags=n}static define(t){let e=t.props&&t.props.length?Object.create(null):r,i=(t.top?1:0)|(t.skipped?2:0)|(t.error?4:0)|(null==t.name?8:0),n=new o(t.name||"",e,t.id,i);if(t.props)for(let i of t.props)if(Array.isArray(i)||(i=i(n)),i){if(i[0].perNode)throw new RangeError("Can't store a per-node prop on a node type");e[i[0].id]=i[1]}return n}prop(t){return this.props[t.id]}get isTop(){return(1&this.flags)>0}get isSkipped(){return(2&this.flags)>0}get isError(){return(4&this.flags)>0}get isAnonymous(){return(8&this.flags)>0}is(t){if("string"==typeof t){if(this.name==t)return!0;let e=this.prop(n.group);return!!e&&e.indexOf(t)>-1}return this.id==t}static match(t){let e=Object.create(null);for(let i in t)for(let n of i.split(" "))e[n]=t[i];return t=>{for(let i=t.prop(n.group),s=-1;s<(i?i.length:0);s++){let n=e[s<0?t.name:i[s]];if(n)return n}}}}o.none=new o("",Object.create(null),0,8);class a{constructor(t){this.types=t;for(let e=0;e<t.length;e++)if(t[e].id!=e)throw new RangeError("Node type ids should correspond to array positions when creating a node set")}extend(...t){let e=[];for(let i of this.types){let n=null;for(let e of t){let t=e(i);t&&(n||(n=Object.assign({},i.props)),n[t[0].id]=t[1])}e.push(n?new o(i.name,n,i.id,i.flags):i)}return new a(e)}}const l=new WeakMap,h=new WeakMap;var c;!function(t){t[t.ExcludeBuffers=1]="ExcludeBuffers",t[t.IncludeAnonymous=2]="IncludeAnonymous",t[t.IgnoreMounts=4]="IgnoreMounts",t[t.IgnoreOverlays=8]="IgnoreOverlays"}(c||(c={}));class O{constructor(t,e,i,n,s){if(this.type=t,this.children=e,this.positions=i,this.length=n,this.props=null,s&&s.length){this.props=Object.create(null);for(let[t,e]of s)this.props["number"==typeof t?t:t.id]=e}}toString(){let t=s.get(this);if(t&&!t.overlay)return t.tree.toString();let e="";for(let t of this.children){let i=t.toString();i&&(e&&(e+=","),e+=i)}return this.type.name?(/\W/.test(this.type.name)&&!this.type.isError?JSON.stringify(this.type.name):this.type.name)+(e.length?"("+e+")":""):e}cursor(t=0){return new x(this.topNode,t)}cursorAt(t,e=0,i=0){let n=l.get(this)||this.topNode,s=new x(n);return s.moveTo(t,e),l.set(this,s._tree),s}get topNode(){return new g(this,0,0,null)}resolve(t,e=0){let i=p(l.get(this)||this.topNode,t,e,!1);return l.set(this,i),i}resolveInner(t,e=0){let i=p(h.get(this)||this.topNode,t,e,!0);return h.set(this,i),i}resolveStack(t,e=0){return function(t,e,i){let n=t.resolveInner(e,i),r=null;for(let t=n instanceof g?n:n.context.parent;t;t=t.parent)if(t.index<0){let s=t.parent;(r||(r=[n])).push(s.resolve(e,i)),t=s}else{let o=s.get(t.tree);if(o&&o.overlay&&o.overlay[0].from<=e&&o.overlay[o.overlay.length-1].to>=e){let s=new g(o.tree,o.overlay[0].from+t.from,-1,t);(r||(r=[n])).push(p(s,e,i,!1))}}return r?w(r):n}(this,t,e)}iterate(t){let{enter:e,leave:i,from:n=0,to:s=this.length}=t,r=t.mode||0,o=(r&c.IncludeAnonymous)>0;for(let t=this.cursor(r|c.IncludeAnonymous);;){let r=!1;if(t.from<=s&&t.to>=n&&(!o&&t.type.isAnonymous||!1!==e(t))){if(t.firstChild())continue;r=!0}for(;r&&i&&(o||!t.type.isAnonymous)&&i(t),!t.nextSibling();){if(!t.parent())return;r=!0}}}prop(t){return t.perNode?this.props?this.props[t.id]:void 0:this.type.prop(t)}get propValues(){let t=[];if(this.props)for(let e in this.props)t.push([+e,this.props[e]]);return t}balance(t={}){return this.children.length<=8?this:Z(o.none,this.children,this.positions,0,this.children.length,0,this.length,((t,e,i)=>new O(this.type,t,e,i,this.propValues)),t.makeTree||((t,e,i)=>new O(o.none,t,e,i)))}static build(e){return function(e){var i;let{buffer:s,nodeSet:r,maxBufferLength:o=t,reused:a=[],minRepeatType:l=r.types.length}=e,h=Array.isArray(s)?new u(s,s.length):s,c=r.types,d=0,p=0;function m(t,e,i,n,s,O){let{id:u,start:y,end:x,size:k}=h,P=p,$=d;for(;k<0;){if(h.next(),-1==k){let e=a[u];return i.push(e),void n.push(y-t)}if(-3==k)return void(d=u);if(-4==k)return void(p=u);throw new RangeError(`Unrecognized record size: ${k}`)}let T,X,R=c[u],A=y-t;if(x-y<=o&&(X=v(h.pos-e,s))){let e=new Uint16Array(X.size-X.skip),i=h.pos-X.size,n=e.length;for(;h.pos>i;)n=w(X.start,e,n);T=new f(e,x-X.start,r),A=X.start-t}else{let t=h.pos-k;h.next();let e=[],i=[],n=u>=l?u:-1,s=0,r=x;for(;h.pos>t;)n>=0&&h.id==n&&h.size>=0?(h.end<=r-o&&(Q(e,i,y,s,h.end,r,n,P,$),s=e.length,r=h.end),h.next()):O>2500?g(y,t,e,i):m(y,t,e,i,n,O+1);if(n>=0&&s>0&&s<e.length&&Q(e,i,y,s,y,r,n,P,$),e.reverse(),i.reverse(),n>-1&&s>0){let t=b(R,$);T=Z(R,e,i,0,e.length,0,x-y,t,t)}else T=S(R,e,i,x-y,P-x,$)}i.push(T),n.push(A)}function g(t,e,i,n){let s=[],a=0,l=-1;for(;h.pos>e;){let{id:t,start:e,end:i,size:n}=h;if(n>4)h.next();else{if(l>-1&&e<l)break;l<0&&(l=i-o),s.push(t,e,i),a++,h.next()}}if(a){let e=new Uint16Array(4*a),o=s[s.length-2];for(let t=s.length-3,i=0;t>=0;t-=3)e[i++]=s[t],e[i++]=s[t+1]-o,e[i++]=s[t+2]-o,e[i++]=i;i.push(new f(e,s[2]-o,r)),n.push(o-t)}}function b(t,e){return(i,s,r)=>{let o,a,l=0,h=i.length-1;if(h>=0&&(o=i[h])instanceof O){if(!h&&o.type==t&&o.length==r)return o;(a=o.prop(n.lookAhead))&&(l=s[h]+o.length+a)}return S(t,i,s,r,l,e)}}function Q(t,e,i,n,s,o,a,l,h){let c=[],O=[];for(;t.length>n;)c.push(t.pop()),O.push(e.pop()+i-s);t.push(S(r.types[a],c,O,o-s,l-o,h)),e.push(s-i)}function S(t,e,i,s,r,o,a){if(o){let t=[n.contextHash,o];a=a?[t].concat(a):[t]}if(r>25){let t=[n.lookAhead,r];a=a?[t].concat(a):[t]}return new O(t,e,i,s,a)}function v(t,e){let i=h.fork(),n=0,s=0,r=0,a=i.end-o,c={size:0,start:0,skip:0};t:for(let o=i.pos-t;i.pos>o;){let t=i.size;if(i.id==e&&t>=0){c.size=n,c.start=s,c.skip=r,r+=4,n+=4,i.next();continue}let h=i.pos-t;if(t<0||h<o||i.start<a)break;let O=i.id>=l?4:0,u=i.start;for(i.next();i.pos>h;){if(i.size<0){if(-3!=i.size)break t;O+=4}else i.id>=l&&(O+=4);i.next()}s=u,n+=t,r+=O}return(e<0||n==t)&&(c.size=n,c.start=s,c.skip=r),c.size>4?c:void 0}function w(t,e,i){let{id:n,start:s,end:r,size:o}=h;if(h.next(),o>=0&&n<l){let a=i;if(o>4){let n=h.pos-(o-4);for(;h.pos>n;)i=w(t,e,i)}e[--i]=a,e[--i]=r-t,e[--i]=s-t,e[--i]=n}else-3==o?d=n:-4==o&&(p=n);return i}let y=[],x=[];for(;h.pos>0;)m(e.start||0,e.bufferStart||0,y,x,-1,0);let k=null!==(i=e.length)&&void 0!==i?i:y.length?x[0]+y[0].length:0;return new O(c[e.topID],y.reverse(),x.reverse(),k)}(e)}}O.empty=new O(o.none,[],[],0);class u{constructor(t,e){this.buffer=t,this.index=e}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}get pos(){return this.index}next(){this.index-=4}fork(){return new u(this.buffer,this.index)}}class f{constructor(t,e,i){this.buffer=t,this.length=e,this.set=i}get type(){return o.none}toString(){let t=[];for(let e=0;e<this.buffer.length;)t.push(this.childString(e)),e=this.buffer[e+3];return t.join(",")}childString(t){let e=this.buffer[t],i=this.buffer[t+3],n=this.set.types[e],s=n.name;if(/\W/.test(s)&&!n.isError&&(s=JSON.stringify(s)),i==(t+=4))return s;let r=[];for(;t<i;)r.push(this.childString(t)),t=this.buffer[t+3];return s+"("+r.join(",")+")"}findChild(t,e,i,n,s){let{buffer:r}=this,o=-1;for(let a=t;a!=e&&!(d(s,n,r[a+1],r[a+2])&&(o=a,i>0));a=r[a+3]);return o}slice(t,e,i){let n=this.buffer,s=new Uint16Array(e-t),r=0;for(let o=t,a=0;o<e;){s[a++]=n[o++],s[a++]=n[o++]-i;let e=s[a++]=n[o++]-i;s[a++]=n[o++]-t,r=Math.max(r,e)}return new f(s,r,this.set)}}function d(t,e,i,n){switch(t){case-2:return i<e;case-1:return n>=e&&i<e;case 0:return i<e&&n>e;case 1:return i<=e&&n>e;case 2:return n>e;case 4:return!0}}function p(t,e,i,n){for(var s;t.from==t.to||(i<1?t.from>=e:t.from>e)||(i>-1?t.to<=e:t.to<e);){let e=!n&&t instanceof g&&t.index<0?null:t.parent;if(!e)return t;t=e}let r=n?0:c.IgnoreOverlays;if(n)for(let n=t,o=n.parent;o;n=o,o=n.parent)n instanceof g&&n.index<0&&(null===(s=o.enter(e,i,r))||void 0===s?void 0:s.from)!=n.from&&(t=o);for(;;){let n=t.enter(e,i,r);if(!n)return t;t=n}}class m{cursor(t=0){return new x(this,t)}getChild(t,e=null,i=null){let n=b(this,t,e,i);return n.length?n[0]:null}getChildren(t,e=null,i=null){return b(this,t,e,i)}resolve(t,e=0){return p(this,t,e,!1)}resolveInner(t,e=0){return p(this,t,e,!0)}matchContext(t){return Q(this.parent,t)}enterUnfinishedNodesBefore(t){let e=this.childBefore(t),i=this;for(;e;){let t=e.lastChild;if(!t||t.to!=e.to)break;t.type.isError&&t.from==t.to?(i=e,e=t.prevSibling):e=t}return i}get node(){return this}get next(){return this.parent}}class g extends m{constructor(t,e,i,n){super(),this._tree=t,this.from=e,this.index=i,this._parent=n}get type(){return this._tree.type}get name(){return this._tree.type.name}get to(){return this.from+this._tree.length}nextChild(t,e,i,n,r=0){for(let o=this;;){for(let{children:a,positions:l}=o._tree,h=e>0?a.length:-1;t!=h;t+=e){let h=a[t],O=l[t]+o.from;if(d(n,i,O,O+h.length))if(h instanceof f){if(r&c.ExcludeBuffers)continue;let s=h.findChild(0,h.buffer.length,e,i-O,n);if(s>-1)return new v(new S(o,h,t,O),null,s)}else if(r&c.IncludeAnonymous||!h.type.isAnonymous||k(h)){let a;if(!(r&c.IgnoreMounts)&&(a=s.get(h))&&!a.overlay)return new g(a.tree,O,t,o);let l=new g(h,O,t,o);return r&c.IncludeAnonymous||!l.type.isAnonymous?l:l.nextChild(e<0?h.children.length-1:0,e,i,n)}}if(r&c.IncludeAnonymous||!o.type.isAnonymous)return null;if(t=o.index>=0?o.index+e:e<0?-1:o._parent._tree.children.length,o=o._parent,!o)return null}}get firstChild(){return this.nextChild(0,1,0,4)}get lastChild(){return this.nextChild(this._tree.children.length-1,-1,0,4)}childAfter(t){return this.nextChild(0,1,t,2)}childBefore(t){return this.nextChild(this._tree.children.length-1,-1,t,-2)}enter(t,e,i=0){let n;if(!(i&c.IgnoreOverlays)&&(n=s.get(this._tree))&&n.overlay){let i=t-this.from;for(let{from:t,to:s}of n.overlay)if((e>0?t<=i:t<i)&&(e<0?s>=i:s>i))return new g(n.tree,n.overlay[0].from+this.from,-1,this)}return this.nextChild(0,1,t,e,i)}nextSignificantParent(){let t=this;for(;t.type.isAnonymous&&t._parent;)t=t._parent;return t}get parent(){return this._parent?this._parent.nextSignificantParent():null}get nextSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index+1,1,0,4):null}get prevSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index-1,-1,0,4):null}get tree(){return this._tree}toTree(){return this._tree}toString(){return this._tree.toString()}}function b(t,e,i,n){let s=t.cursor(),r=[];if(!s.firstChild())return r;if(null!=i)for(let t=!1;!t;)if(t=s.type.is(i),!s.nextSibling())return r;for(;;){if(null!=n&&s.type.is(n))return r;if(s.type.is(e)&&r.push(s.node),!s.nextSibling())return null==n?r:[]}}function Q(t,e,i=e.length-1){for(let n=t;i>=0;n=n.parent){if(!n)return!1;if(!n.type.isAnonymous){if(e[i]&&e[i]!=n.name)return!1;i--}}return!0}class S{constructor(t,e,i,n){this.parent=t,this.buffer=e,this.index=i,this.start=n}}class v extends m{get name(){return this.type.name}get from(){return this.context.start+this.context.buffer.buffer[this.index+1]}get to(){return this.context.start+this.context.buffer.buffer[this.index+2]}constructor(t,e,i){super(),this.context=t,this._parent=e,this.index=i,this.type=t.buffer.set.types[t.buffer.buffer[i]]}child(t,e,i){let{buffer:n}=this.context,s=n.findChild(this.index+4,n.buffer[this.index+3],t,e-this.context.start,i);return s<0?null:new v(this.context,this,s)}get firstChild(){return this.child(1,0,4)}get lastChild(){return this.child(-1,0,4)}childAfter(t){return this.child(1,t,2)}childBefore(t){return this.child(-1,t,-2)}enter(t,e,i=0){if(i&c.ExcludeBuffers)return null;let{buffer:n}=this.context,s=n.findChild(this.index+4,n.buffer[this.index+3],e>0?1:-1,t-this.context.start,e);return s<0?null:new v(this.context,this,s)}get parent(){return this._parent||this.context.parent.nextSignificantParent()}externalSibling(t){return this._parent?null:this.context.parent.nextChild(this.context.index+t,t,0,4)}get nextSibling(){let{buffer:t}=this.context,e=t.buffer[this.index+3];return e<(this._parent?t.buffer[this._parent.index+3]:t.buffer.length)?new v(this.context,this._parent,e):this.externalSibling(1)}get prevSibling(){let{buffer:t}=this.context,e=this._parent?this._parent.index+4:0;return this.index==e?this.externalSibling(-1):new v(this.context,this._parent,t.findChild(e,this.index,-1,0,4))}get tree(){return null}toTree(){let t=[],e=[],{buffer:i}=this.context,n=this.index+4,s=i.buffer[this.index+3];if(s>n){let r=i.buffer[this.index+1];t.push(i.slice(n,s,r)),e.push(0)}return new O(this.type,t,e,this.to-this.from)}toString(){return this.context.buffer.childString(this.index)}}function w(t){if(!t.length)return null;let e=0,i=t[0];for(let n=1;n<t.length;n++){let s=t[n];(s.from>i.from||s.to<i.to)&&(i=s,e=n)}let n=i instanceof g&&i.index<0?null:i.parent,s=t.slice();return n?s[e]=n:s.splice(e,1),new y(s,i)}class y{constructor(t,e){this.heads=t,this.node=e}get next(){return w(this.heads)}}class x{get name(){return this.type.name}constructor(t,e=0){if(this.mode=e,this.buffer=null,this.stack=[],this.index=0,this.bufferNode=null,t instanceof g)this.yieldNode(t);else{this._tree=t.context.parent,this.buffer=t.context;for(let e=t._parent;e;e=e._parent)this.stack.unshift(e.index);this.bufferNode=t,this.yieldBuf(t.index)}}yieldNode(t){return!!t&&(this._tree=t,this.type=t.type,this.from=t.from,this.to=t.to,!0)}yieldBuf(t,e){this.index=t;let{start:i,buffer:n}=this.buffer;return this.type=e||n.set.types[n.buffer[t]],this.from=i+n.buffer[t+1],this.to=i+n.buffer[t+2],!0}yield(t){return!!t&&(t instanceof g?(this.buffer=null,this.yieldNode(t)):(this.buffer=t.context,this.yieldBuf(t.index,t.type)))}toString(){return this.buffer?this.buffer.buffer.childString(this.index):this._tree.toString()}enterChild(t,e,i){if(!this.buffer)return this.yield(this._tree.nextChild(t<0?this._tree._tree.children.length-1:0,t,e,i,this.mode));let{buffer:n}=this.buffer,s=n.findChild(this.index+4,n.buffer[this.index+3],t,e-this.buffer.start,i);return!(s<0)&&(this.stack.push(this.index),this.yieldBuf(s))}firstChild(){return this.enterChild(1,0,4)}lastChild(){return this.enterChild(-1,0,4)}childAfter(t){return this.enterChild(1,t,2)}childBefore(t){return this.enterChild(-1,t,-2)}enter(t,e,i=this.mode){return this.buffer?!(i&c.ExcludeBuffers)&&this.enterChild(1,t,e):this.yield(this._tree.enter(t,e,i))}parent(){if(!this.buffer)return this.yieldNode(this.mode&c.IncludeAnonymous?this._tree._parent:this._tree.parent);if(this.stack.length)return this.yieldBuf(this.stack.pop());let t=this.mode&c.IncludeAnonymous?this.buffer.parent:this.buffer.parent.nextSignificantParent();return this.buffer=null,this.yieldNode(t)}sibling(t){if(!this.buffer)return!!this._tree._parent&&this.yield(this._tree.index<0?null:this._tree._parent.nextChild(this._tree.index+t,t,0,4,this.mode));let{buffer:e}=this.buffer,i=this.stack.length-1;if(t<0){let t=i<0?0:this.stack[i]+4;if(this.index!=t)return this.yieldBuf(e.findChild(t,this.index,-1,0,4))}else{let t=e.buffer[this.index+3];if(t<(i<0?e.buffer.length:e.buffer[this.stack[i]+3]))return this.yieldBuf(t)}return i<0&&this.yield(this.buffer.parent.nextChild(this.buffer.index+t,t,0,4,this.mode))}nextSibling(){return this.sibling(1)}prevSibling(){return this.sibling(-1)}atLastNode(t){let e,i,{buffer:n}=this;if(n){if(t>0){if(this.index<n.buffer.buffer.length)return!1}else for(let t=0;t<this.index;t++)if(n.buffer.buffer[t+3]<this.index)return!1;({index:e,parent:i}=n)}else({index:e,_parent:i}=this._tree);for(;i;({index:e,_parent:i}=i))if(e>-1)for(let n=e+t,s=t<0?-1:i._tree.children.length;n!=s;n+=t){let t=i._tree.children[n];if(this.mode&c.IncludeAnonymous||t instanceof f||!t.type.isAnonymous||k(t))return!1}return!0}move(t,e){if(e&&this.enterChild(t,0,4))return!0;for(;;){if(this.sibling(t))return!0;if(this.atLastNode(t)||!this.parent())return!1}}next(t=!0){return this.move(1,t)}prev(t=!0){return this.move(-1,t)}moveTo(t,e=0){for(;(this.from==this.to||(e<1?this.from>=t:this.from>t)||(e>-1?this.to<=t:this.to<t))&&this.parent(););for(;this.enterChild(1,t,e););return this}get node(){if(!this.buffer)return this._tree;let t=this.bufferNode,e=null,i=0;if(t&&t.context==this.buffer)t:for(let n=this.index,s=this.stack.length;s>=0;){for(let r=t;r;r=r._parent)if(r.index==n){if(n==this.index)return r;e=r,i=s+1;break t}n=this.stack[--s]}for(let t=i;t<this.stack.length;t++)e=new v(this.buffer,e,this.stack[t]);return this.bufferNode=new v(this.buffer,e,this.index)}get tree(){return this.buffer?null:this._tree._tree}iterate(t,e){for(let i=0;;){let n=!1;if(this.type.isAnonymous||!1!==t(this)){if(this.firstChild()){i++;continue}this.type.isAnonymous||(n=!0)}for(;;){if(n&&e&&e(this),n=this.type.isAnonymous,!i)return;if(this.nextSibling())break;this.parent(),i--,n=!0}}}matchContext(t){if(!this.buffer)return Q(this.node.parent,t);let{buffer:e}=this.buffer,{types:i}=e.set;for(let n=t.length-1,s=this.stack.length-1;n>=0;s--){if(s<0)return Q(this._tree,t,n);let r=i[e.buffer[this.stack[s]]];if(!r.isAnonymous){if(t[n]&&t[n]!=r.name)return!1;n--}}return!0}}function k(t){return t.children.some((t=>t instanceof f||!t.type.isAnonymous||k(t)))}const P=new WeakMap;function $(t,e){if(!t.isAnonymous||e instanceof f||e.type!=t)return 1;let i=P.get(e);if(null==i){i=1;for(let n of e.children){if(n.type!=t||!(n instanceof O)){i=1;break}i+=$(t,n)}P.set(e,i)}return i}function Z(t,e,i,n,s,r,o,a,l){let h=0;for(let i=n;i<s;i++)h+=$(t,e[i]);let c=Math.ceil(1.5*h/8),O=[],u=[];return function e(i,n,s,o,a){for(let h=s;h<o;){let s=h,f=n[h],d=$(t,i[h]);for(h++;h<o;h++){let e=$(t,i[h]);if(d+e>=c)break;d+=e}if(h==s+1){if(d>c){let t=i[s];e(t.children,t.positions,0,t.children.length,n[s]+a);continue}O.push(i[s])}else{let e=n[h-1]+i[h-1].length-f;O.push(Z(t,i,n,s,h,f,e,null,l))}u.push(f+a-r)}}(e,i,n,s,0),(a||l)(O,u,o)}class T{constructor(){this.map=new WeakMap}setBuffer(t,e,i){let n=this.map.get(t);n||this.map.set(t,n=new Map),n.set(e,i)}getBuffer(t,e){let i=this.map.get(t);return i&&i.get(e)}set(t,e){t instanceof v?this.setBuffer(t.context.buffer,t.index,e):t instanceof g&&this.map.set(t.tree,e)}get(t){return t instanceof v?this.getBuffer(t.context.buffer,t.index):t instanceof g?this.map.get(t.tree):void 0}cursorSet(t,e){t.buffer?this.setBuffer(t.buffer.buffer,t.index,e):this.map.set(t.tree,e)}cursorGet(t){return t.buffer?this.getBuffer(t.buffer.buffer,t.index):this.map.get(t.tree)}}class X{constructor(t,e,i,n,s=!1,r=!1){this.from=t,this.to=e,this.tree=i,this.offset=n,this.open=(s?1:0)|(r?2:0)}get openStart(){return(1&this.open)>0}get openEnd(){return(2&this.open)>0}static addTree(t,e=[],i=!1){let n=[new X(0,t.length,t,0,!1,i)];for(let i of e)i.to>t.length&&n.push(i);return n}static applyChanges(t,e,i=128){if(!e.length)return t;let n=[],s=1,r=t.length?t[0]:null;for(let o=0,a=0,l=0;;o++){let h=o<e.length?e[o]:null,c=h?h.fromA:1e9;if(c-a>=i)for(;r&&r.from<c;){let e=r;if(a>=e.from||c<=e.to||l){let t=Math.max(e.from,a)-l,i=Math.min(e.to,c)-l;e=t>=i?null:new X(t,i,e.tree,e.offset+l,o>0,!!h)}if(e&&n.push(e),r.to>c)break;r=s<t.length?t[s++]:null}if(!h)break;a=h.toA,l=h.toA-h.toB}return n}}class R{startParse(t,e,n){return"string"==typeof t&&(t=new A(t)),n=n?n.length?n.map((t=>new i(t.from,t.to))):[new i(0,0)]:[new i(0,t.length)],this.createParse(t,e||[],n)}parse(t,e,i){let n=this.startParse(t,e,i);for(;;){let t=n.advance();if(t)return t}}}class A{constructor(t){this.string=t}get length(){return this.string.length}chunk(t){return this.string.slice(t)}get lineChunks(){return!1}read(t,e){return this.string.slice(t,e)}}function C(t){return(e,i,n,s)=>new z(e,t,i,n,s)}class M{constructor(t,e,i,n,s){this.parser=t,this.parse=e,this.overlay=i,this.target=n,this.from=s}}function q(t){if(!t.length||t.some((t=>t.from>=t.to)))throw new RangeError("Invalid inner parse ranges given: "+JSON.stringify(t))}class V{constructor(t,e,i,n,s,r,o){this.parser=t,this.predicate=e,this.mounts=i,this.index=n,this.start=s,this.target=r,this.prev=o,this.depth=0,this.ranges=[]}}const Y=new n({perNode:!0});class z{constructor(t,e,i,n,s){this.nest=e,this.input=i,this.fragments=n,this.ranges=s,this.inner=[],this.innerDone=0,this.baseTree=null,this.stoppedAt=null,this.baseParse=t}advance(){if(this.baseParse){let t=this.baseParse.advance();if(!t)return null;if(this.baseParse=null,this.baseTree=t,this.startInner(),null!=this.stoppedAt)for(let t of this.inner)t.parse.stopAt(this.stoppedAt)}if(this.innerDone==this.inner.length){let t=this.baseTree;return null!=this.stoppedAt&&(t=new O(t.type,t.children,t.positions,t.length,t.propValues.concat([[Y,this.stoppedAt]]))),t}let t=this.inner[this.innerDone],e=t.parse.advance();if(e){this.innerDone++;let i=Object.assign(Object.create(null),t.target.props);i[n.mounted.id]=new s(e,t.overlay,t.parser),t.target.props=i}return null}get parsedPos(){if(this.baseParse)return 0;let t=this.input.length;for(let e=this.innerDone;e<this.inner.length;e++)this.inner[e].from<t&&(t=Math.min(t,this.inner[e].parse.parsedPos));return t}stopAt(t){if(this.stoppedAt=t,this.baseParse)this.baseParse.stopAt(t);else for(let e=this.innerDone;e<this.inner.length;e++)this.inner[e].parse.stopAt(t)}startInner(){let t=new W(this.fragments),e=null,n=null,s=new x(new g(this.baseTree,this.ranges[0].from,0,null),c.IncludeAnonymous|c.IgnoreMounts);t:for(let r,o;;){let a,l=!0;if(null!=this.stoppedAt&&s.from>=this.stoppedAt)l=!1;else if(t.hasNode(s)){if(e){let t=e.mounts.find((t=>t.frag.from<=s.from&&t.frag.to>=s.to&&t.mount.overlay));if(t)for(let i of t.mount.overlay){let n=i.from+t.pos,r=i.to+t.pos;n>=s.from&&r<=s.to&&!e.ranges.some((t=>t.from<r&&t.to>n))&&e.ranges.push({from:n,to:r})}}l=!1}else if(n&&(o=_(n.ranges,s.from,s.to)))l=2!=o;else if(!s.type.isAnonymous&&(r=this.nest(s,this.input))&&(s.from<s.to||!r.overlay)){s.tree||E(s);let o=t.findMounts(s.from,r.parser);if("function"==typeof r.overlay)e=new V(r.parser,r.overlay,o,this.inner.length,s.from,s.tree,e);else{let t=L(this.ranges,r.overlay||(s.from<s.to?[new i(s.from,s.to)]:[]));t.length&&q(t),!t.length&&r.overlay||this.inner.push(new M(r.parser,t.length?r.parser.startParse(this.input,B(o,t),t):r.parser.startParse(""),r.overlay?r.overlay.map((t=>new i(t.from-s.from,t.to-s.from))):null,s.tree,t.length?t[0].from:s.from)),r.overlay?t.length&&(n={ranges:t,depth:0,prev:n}):l=!1}}else if(e&&(a=e.predicate(s))&&(!0===a&&(a=new i(s.from,s.to)),a.from<a.to)){let t=e.ranges.length-1;t>=0&&e.ranges[t].to==a.from?e.ranges[t]={from:e.ranges[t].from,to:a.to}:e.ranges.push(a)}if(l&&s.firstChild())e&&e.depth++,n&&n.depth++;else for(;!s.nextSibling();){if(!s.parent())break t;if(e&&! --e.depth){let t=L(this.ranges,e.ranges);t.length&&(q(t),this.inner.splice(e.index,0,new M(e.parser,e.parser.startParse(this.input,B(e.mounts,t),t),e.ranges.map((t=>new i(t.from-e.start,t.to-e.start))),e.target,t[0].from))),e=e.prev}n&&! --n.depth&&(n=n.prev)}}}}function _(t,e,i){for(let n of t){if(n.from>=i)break;if(n.to>e)return n.from<=e&&n.to>=i?2:1}return 0}function j(t,e,i,n,s,r){if(e<i){let o=t.buffer[e+1];n.push(t.slice(e,i,o)),s.push(o-r)}}function E(t){let{node:e}=t,i=[],n=e.context.buffer;do{i.push(t.index),t.parent()}while(!t.tree);let s=t.tree,r=s.children.indexOf(n),a=s.children[r],l=a.buffer,h=[r];s.children[r]=function t(n,s,r,o,c,u){let f=i[u],d=[],p=[];j(a,n,f,d,p,o);let m=l[f+1],g=l[f+2];h.push(d.length);let b=u?t(f+4,l[f+3],a.set.types[l[f]],m,g-m,u-1):e.toTree();return d.push(b),p.push(m-o),j(a,l[f+3],s,d,p,o),new O(r,d,p,c)}(0,l.length,o.none,0,a.length,i.length-1);for(let e of h){let i=t.tree.children[e],n=t.tree.positions[e];t.yield(new g(i,n+t.from,e,t._tree))}}class D{constructor(t,e){this.offset=e,this.done=!1,this.cursor=t.cursor(c.IncludeAnonymous|c.IgnoreMounts)}moveTo(t){let{cursor:e}=this,i=t-this.offset;for(;!this.done&&e.from<i;)e.to>=t&&e.enter(i,1,c.IgnoreOverlays|c.ExcludeBuffers)||e.next(!1)||(this.done=!0)}hasNode(t){if(this.moveTo(t.from),!this.done&&this.cursor.from+this.offset==t.from&&this.cursor.tree)for(let e=this.cursor.tree;;){if(e==t.tree)return!0;if(!(e.children.length&&0==e.positions[0]&&e.children[0]instanceof O))break;e=e.children[0]}return!1}}let W=class{constructor(t){var e;if(this.fragments=t,this.curTo=0,this.fragI=0,t.length){let i=this.curFrag=t[0];this.curTo=null!==(e=i.tree.prop(Y))&&void 0!==e?e:i.to,this.inner=new D(i.tree,-i.offset)}else this.curFrag=this.inner=null}hasNode(t){for(;this.curFrag&&t.from>=this.curTo;)this.nextFrag();return this.curFrag&&this.curFrag.from<=t.from&&this.curTo>=t.to&&this.inner.hasNode(t)}nextFrag(){var t;if(this.fragI++,this.fragI==this.fragments.length)this.curFrag=this.inner=null;else{let e=this.curFrag=this.fragments[this.fragI];this.curTo=null!==(t=e.tree.prop(Y))&&void 0!==t?t:e.to,this.inner=new D(e.tree,-e.offset)}}findMounts(t,e){var i;let s=[];if(this.inner){this.inner.cursor.moveTo(t,1);for(let t=this.inner.cursor.node;t;t=t.parent){let r=null===(i=t.tree)||void 0===i?void 0:i.prop(n.mounted);if(r&&r.parser==e)for(let e=this.fragI;e<this.fragments.length;e++){let i=this.fragments[e];if(i.from>=t.to)break;i.tree==this.curFrag.tree&&s.push({frag:i,pos:t.from-i.offset,mount:r})}}}return s}};function L(t,e){let n=null,s=e;for(let r=1,o=0;r<t.length;r++){let a=t[r-1].to,l=t[r].from;for(;o<s.length;o++){let t=s[o];if(t.from>=l)break;t.to<=a||(n||(s=n=e.slice()),t.from<a?(n[o]=new i(t.from,a),t.to>l&&n.splice(o+1,0,new i(l,t.to))):t.to>l?n[o--]=new i(l,t.to):n.splice(o--,1))}}return s}function G(t,e,n,s){let r=0,o=0,a=!1,l=!1,h=-1e9,c=[];for(;;){let O=r==t.length?1e9:a?t[r].to:t[r].from,u=o==e.length?1e9:l?e[o].to:e[o].from;if(a!=l){let t=Math.max(h,n),e=Math.min(O,u,s);t<e&&c.push(new i(t,e))}if(h=Math.min(O,u),1e9==h)break;O==h&&(a?(a=!1,r++):a=!0),u==h&&(l?(l=!1,o++):l=!0)}return c}function B(t,e){let n=[];for(let{pos:s,mount:r,frag:o}of t){let t=s+(r.overlay?r.overlay[0].from:0),a=t+r.tree.length,l=Math.max(o.from,t),h=Math.min(o.to,a);if(r.overlay){let a=G(e,r.overlay.map((t=>new i(t.from+s,t.to+s))),l,h);for(let e=0,i=l;;e++){let s=e==a.length,l=s?h:a[e].from;if(l>i&&n.push(new X(i,l,r.tree,-t,o.from>=i||o.openStart,o.to<=l||o.openEnd)),s)break;i=a[e].to}}else n.push(new X(l,h,r.tree,-t,o.from>=t||o.openStart,o.to<=a||o.openEnd))}return n}let U=[],I=[];function N(t){if(t<768)return!1;for(let e=0,i=U.length;;){let n=e+i>>1;if(t<U[n])i=n;else{if(!(t>=I[n]))return!0;e=n+1}if(e==i)return!1}}function H(t){return t>=127462&&t<=127487}(()=>{let t="lc,34,7n,7,7b,19,,,,2,,2,,,20,b,1c,l,g,,2t,7,2,6,2,2,,4,z,,u,r,2j,b,1m,9,9,,o,4,,9,,3,,5,17,3,3b,f,,w,1j,,,,4,8,4,,3,7,a,2,t,,1m,,,,2,4,8,,9,,a,2,q,,2,2,1l,,4,2,4,2,2,3,3,,u,2,3,,b,2,1l,,4,5,,2,4,,k,2,m,6,,,1m,,,2,,4,8,,7,3,a,2,u,,1n,,,,c,,9,,14,,3,,1l,3,5,3,,4,7,2,b,2,t,,1m,,2,,2,,3,,5,2,7,2,b,2,s,2,1l,2,,,2,4,8,,9,,a,2,t,,20,,4,,2,3,,,8,,29,,2,7,c,8,2q,,2,9,b,6,22,2,r,,,,,,1j,e,,5,,2,5,b,,10,9,,2u,4,,6,,2,2,2,p,2,4,3,g,4,d,,2,2,6,,f,,jj,3,qa,3,t,3,t,2,u,2,1s,2,,7,8,,2,b,9,,19,3,3b,2,y,,3a,3,4,2,9,,6,3,63,2,2,,1m,,,7,,,,,2,8,6,a,2,,1c,h,1r,4,1c,7,,,5,,14,9,c,2,w,4,2,2,,3,1k,,,2,3,,,3,1m,8,2,2,48,3,,d,,7,4,,6,,3,2,5i,1m,,5,ek,,5f,x,2da,3,3x,,2o,w,fe,6,2x,2,n9w,4,,a,w,2,28,2,7k,,3,,4,,p,2,5,,47,2,q,i,d,,12,8,p,b,1a,3,1c,,2,4,2,2,13,,1v,6,2,2,2,2,c,,8,,1b,,1f,,,3,2,2,5,2,,,16,2,8,,6m,,2,,4,,fn4,,kh,g,g,g,a6,2,gt,,6a,,45,5,1ae,3,,2,5,4,14,3,4,,4l,2,fx,4,ar,2,49,b,4w,,1i,f,1k,3,1d,4,2,2,1x,3,10,5,,8,1q,,c,2,1g,9,a,4,2,,2n,3,2,,,2,6,,4g,,3,8,l,2,1l,2,,,,,m,,e,7,3,5,5f,8,2,3,,,n,,29,,2,6,,,2,,,2,,2,6j,,2,4,6,2,,2,r,2,2d,8,2,,,2,2y,,,,2,6,,,2t,3,2,4,,5,77,9,,2,6t,,a,2,,,4,,40,4,2,2,4,,w,a,14,6,2,4,8,,9,6,2,3,1a,d,,2,ba,7,,6,,,2a,m,2,7,,2,,2,3e,6,3,,,2,,7,,,20,2,3,,,,9n,2,f0b,5,1n,7,t4,,1r,4,29,,f5k,2,43q,,,3,4,5,8,8,2,7,u,4,44,3,1iz,1j,4,1e,8,,e,,m,5,,f,11s,7,,h,2,7,,2,,5,79,7,c5,4,15s,7,31,7,240,5,gx7k,2o,3k,6o".split(",").map((t=>t?parseInt(t,36):1));for(let e=0,i=0;e<t.length;e++)(e%2?I:U).push(i+=t[e])})();function F(t,e,i=!0,n=!0){return(i?K:J)(t,e,n)}function K(t,e,i){if(e==t.length)return e;e&&et(t.charCodeAt(e))&&it(t.charCodeAt(e-1))&&e--;let n=tt(t,e);for(e+=nt(n);e<t.length;){let s=tt(t,e);if(8205==n||8205==s||i&&N(s))e+=nt(s),n=s;else{if(!H(s))break;{let i=0,n=e-2;for(;n>=0&&H(tt(t,n));)i++,n-=2;if(i%2==0)break;e+=2}}}return e}function J(t,e,i){for(;e>0;){let n=K(t,e-2,i);if(n<e)return n;e--}return 0}function tt(t,e){let i=t.charCodeAt(e);if(!it(i)||e+1==t.length)return i;let n=t.charCodeAt(e+1);return et(n)?n-56320+(i-55296<<10)+65536:i}function et(t){return t>=56320&&t<57344}function it(t){return t>=55296&&t<56320}function nt(t){return t<65536?1:2}class st{lineAt(t){if(t<0||t>this.length)throw new RangeError(`Invalid position ${t} in document of length ${this.length}`);return this.lineInner(t,!1,1,0)}line(t){if(t<1||t>this.lines)throw new RangeError(`Invalid line number ${t} in ${this.lines}-line document`);return this.lineInner(t,!0,1,0)}replace(t,e,i){[t,e]=ft(this,t,e);let n=[];return this.decompose(0,t,n,2),i.length&&i.decompose(0,i.length,n,3),this.decompose(e,this.length,n,1),ot.from(n,this.length-(e-t)+i.length)}append(t){return this.replace(this.length,this.length,t)}slice(t,e=this.length){[t,e]=ft(this,t,e);let i=[];return this.decompose(t,e,i,0),ot.from(i,e-t)}eq(t){if(t==this)return!0;if(t.length!=this.length||t.lines!=this.lines)return!1;let e=this.scanIdentical(t,1),i=this.length-this.scanIdentical(t,-1),n=new ht(this),s=new ht(t);for(let t=e,r=e;;){if(n.next(t),s.next(t),t=0,n.lineBreak!=s.lineBreak||n.done!=s.done||n.value!=s.value)return!1;if(r+=n.value.length,n.done||r>=i)return!0}}iter(t=1){return new ht(this,t)}iterRange(t,e=this.length){return new ct(this,t,e)}iterLines(t,e){let i;if(null==t)i=this.iter();else{null==e&&(e=this.lines+1);let n=this.line(t).from;i=this.iterRange(n,Math.max(n,e==this.lines+1?this.length:e<=1?0:this.line(e-1).to))}return new Ot(i)}toString(){return this.sliceString(0)}toJSON(){let t=[];return this.flatten(t),t}constructor(){}static of(t){if(0==t.length)throw new RangeError("A document must have at least one line");return 1!=t.length||t[0]?t.length<=32?new rt(t):ot.from(rt.split(t,[])):st.empty}}class rt extends st{constructor(t,e=function(t){let e=-1;for(let i of t)e+=i.length+1;return e}(t)){super(),this.text=t,this.length=e}get lines(){return this.text.length}get children(){return null}lineInner(t,e,i,n){for(let s=0;;s++){let r=this.text[s],o=n+r.length;if((e?i:o)>=t)return new ut(n,o,i,r);n=o+1,i++}}decompose(t,e,i,n){let s=t<=0&&e>=this.length?this:new rt(lt(this.text,t,e),Math.min(e,this.length)-Math.max(0,t));if(1&n){let t=i.pop(),e=at(s.text,t.text.slice(),0,s.length);if(e.length<=32)i.push(new rt(e,t.length+s.length));else{let t=e.length>>1;i.push(new rt(e.slice(0,t)),new rt(e.slice(t)))}}else i.push(s)}replace(t,e,i){if(!(i instanceof rt))return super.replace(t,e,i);[t,e]=ft(this,t,e);let n=at(this.text,at(i.text,lt(this.text,0,t)),e),s=this.length+i.length-(e-t);return n.length<=32?new rt(n,s):ot.from(rt.split(n,[]),s)}sliceString(t,e=this.length,i="\n"){[t,e]=ft(this,t,e);let n="";for(let s=0,r=0;s<=e&&r<this.text.length;r++){let o=this.text[r],a=s+o.length;s>t&&r&&(n+=i),t<a&&e>s&&(n+=o.slice(Math.max(0,t-s),e-s)),s=a+1}return n}flatten(t){for(let e of this.text)t.push(e)}scanIdentical(){return 0}static split(t,e){let i=[],n=-1;for(let s of t)i.push(s),n+=s.length+1,32==i.length&&(e.push(new rt(i,n)),i=[],n=-1);return n>-1&&e.push(new rt(i,n)),e}}class ot extends st{constructor(t,e){super(),this.children=t,this.length=e,this.lines=0;for(let e of t)this.lines+=e.lines}lineInner(t,e,i,n){for(let s=0;;s++){let r=this.children[s],o=n+r.length,a=i+r.lines-1;if((e?a:o)>=t)return r.lineInner(t,e,i,n);n=o+1,i=a+1}}decompose(t,e,i,n){for(let s=0,r=0;r<=e&&s<this.children.length;s++){let o=this.children[s],a=r+o.length;if(t<=a&&e>=r){let s=n&((r<=t?1:0)|(a>=e?2:0));r>=t&&a<=e&&!s?i.push(o):o.decompose(t-r,e-r,i,s)}r=a+1}}replace(t,e,i){if([t,e]=ft(this,t,e),i.lines<this.lines)for(let n=0,s=0;n<this.children.length;n++){let r=this.children[n],o=s+r.length;if(t>=s&&e<=o){let a=r.replace(t-s,e-s,i),l=this.lines-r.lines+a.lines;if(a.lines<l>>4&&a.lines>l>>6){let s=this.children.slice();return s[n]=a,new ot(s,this.length-(e-t)+i.length)}return super.replace(s,o,a)}s=o+1}return super.replace(t,e,i)}sliceString(t,e=this.length,i="\n"){[t,e]=ft(this,t,e);let n="";for(let s=0,r=0;s<this.children.length&&r<=e;s++){let o=this.children[s],a=r+o.length;r>t&&s&&(n+=i),t<a&&e>r&&(n+=o.sliceString(t-r,e-r,i)),r=a+1}return n}flatten(t){for(let e of this.children)e.flatten(t)}scanIdentical(t,e){if(!(t instanceof ot))return 0;let i=0,[n,s,r,o]=e>0?[0,0,this.children.length,t.children.length]:[this.children.length-1,t.children.length-1,-1,-1];for(;;n+=e,s+=e){if(n==r||s==o)return i;let a=this.children[n],l=t.children[s];if(a!=l)return i+a.scanIdentical(l,e);i+=a.length+1}}static from(t,e=t.reduce(((t,e)=>t+e.length+1),-1)){let i=0;for(let e of t)i+=e.lines;if(i<32){let i=[];for(let e of t)e.flatten(i);return new rt(i,e)}let n=Math.max(32,i>>5),s=n<<1,r=n>>1,o=[],a=0,l=-1,h=[];function c(t){let e;if(t.lines>s&&t instanceof ot)for(let e of t.children)c(e);else t.lines>r&&(a>r||!a)?(O(),o.push(t)):t instanceof rt&&a&&(e=h[h.length-1])instanceof rt&&t.lines+e.lines<=32?(a+=t.lines,l+=t.length+1,h[h.length-1]=new rt(e.text.concat(t.text),e.length+1+t.length)):(a+t.lines>n&&O(),a+=t.lines,l+=t.length+1,h.push(t))}function O(){0!=a&&(o.push(1==h.length?h[0]:ot.from(h,l)),l=-1,a=h.length=0)}for(let e of t)c(e);return O(),1==o.length?o[0]:new ot(o,e)}}function at(t,e,i=0,n=1e9){for(let s=0,r=0,o=!0;r<t.length&&s<=n;r++){let a=t[r],l=s+a.length;l>=i&&(l>n&&(a=a.slice(0,n-s)),s<i&&(a=a.slice(i-s)),o?(e[e.length-1]+=a,o=!1):e.push(a)),s=l+1}return e}function lt(t,e,i){return at(t,[""],e,i)}st.empty=new rt([""],0);class ht{constructor(t,e=1){this.dir=e,this.done=!1,this.lineBreak=!1,this.value="",this.nodes=[t],this.offsets=[e>0?1:(t instanceof rt?t.text.length:t.children.length)<<1]}nextInner(t,e){for(this.done=this.lineBreak=!1;;){let i=this.nodes.length-1,n=this.nodes[i],s=this.offsets[i],r=s>>1,o=n instanceof rt?n.text.length:n.children.length;if(r==(e>0?o:0)){if(0==i)return this.done=!0,this.value="",this;e>0&&this.offsets[i-1]++,this.nodes.pop(),this.offsets.pop()}else if((1&s)==(e>0?0:1)){if(this.offsets[i]+=e,0==t)return this.lineBreak=!0,this.value="\n",this;t--}else if(n instanceof rt){let s=n.text[r+(e<0?-1:0)];if(this.offsets[i]+=e,s.length>Math.max(0,t))return this.value=0==t?s:e>0?s.slice(t):s.slice(0,s.length-t),this;t-=s.length}else{let s=n.children[r+(e<0?-1:0)];t>s.length?(t-=s.length,this.offsets[i]+=e):(e<0&&this.offsets[i]--,this.nodes.push(s),this.offsets.push(e>0?1:(s instanceof rt?s.text.length:s.children.length)<<1))}}}next(t=0){return t<0&&(this.nextInner(-t,-this.dir),t=this.value.length),this.nextInner(t,this.dir)}}class ct{constructor(t,e,i){this.value="",this.done=!1,this.cursor=new ht(t,e>i?-1:1),this.pos=e>i?t.length:0,this.from=Math.min(e,i),this.to=Math.max(e,i)}nextInner(t,e){if(e<0?this.pos<=this.from:this.pos>=this.to)return this.value="",this.done=!0,this;t+=Math.max(0,e<0?this.pos-this.to:this.from-this.pos);let i=e<0?this.pos-this.from:this.to-this.pos;t>i&&(t=i),i-=t;let{value:n}=this.cursor.next(t);return this.pos+=(n.length+t)*e,this.value=n.length<=i?n:e<0?n.slice(n.length-i):n.slice(0,i),this.done=!this.value,this}next(t=0){return t<0?t=Math.max(t,this.from-this.pos):t>0&&(t=Math.min(t,this.to-this.pos)),this.nextInner(t,this.cursor.dir)}get lineBreak(){return this.cursor.lineBreak&&""!=this.value}}class Ot{constructor(t){this.inner=t,this.afterBreak=!0,this.value="",this.done=!1}next(t=0){let{done:e,lineBreak:i,value:n}=this.inner.next(t);return e&&this.afterBreak?(this.value="",this.afterBreak=!1):e?(this.done=!0,this.value=""):i?this.afterBreak?this.value="":(this.afterBreak=!0,this.next()):(this.value=n,this.afterBreak=!1),this}get lineBreak(){return!1}}"undefined"!=typeof Symbol&&(st.prototype[Symbol.iterator]=function(){return this.iter()},ht.prototype[Symbol.iterator]=ct.prototype[Symbol.iterator]=Ot.prototype[Symbol.iterator]=function(){return this});class ut{constructor(t,e,i,n){this.from=t,this.to=e,this.number=i,this.text=n}get length(){return this.to-this.from}}function ft(t,e,i){return[e=Math.max(0,Math.min(t.length,e)),Math.max(e,Math.min(t.length,i))]}function dt(t,e,i=!0,n=!0){return F(t,e,i,n)}function pt(t,e){let i=t.charCodeAt(e);if(!(n=i,n>=55296&&n<56320&&e+1!=t.length))return i;var n;let s=t.charCodeAt(e+1);return function(t){return t>=56320&&t<57344}(s)?s-56320+(i-55296<<10)+65536:i}function mt(t){return t<=65535?String.fromCharCode(t):(t-=65536,String.fromCharCode(55296+(t>>10),56320+(1023&t)))}function gt(t){return t<65536?1:2}const bt=/\r\n?|\n/;var Qt=function(t){return t[t.Simple=0]="Simple",t[t.TrackDel=1]="TrackDel",t[t.TrackBefore=2]="TrackBefore",t[t.TrackAfter=3]="TrackAfter",t}(Qt||(Qt={}));class St{constructor(t){this.sections=t}get length(){let t=0;for(let e=0;e<this.sections.length;e+=2)t+=this.sections[e];return t}get newLength(){let t=0;for(let e=0;e<this.sections.length;e+=2){let i=this.sections[e+1];t+=i<0?this.sections[e]:i}return t}get empty(){return 0==this.sections.length||2==this.sections.length&&this.sections[1]<0}iterGaps(t){for(let e=0,i=0,n=0;e<this.sections.length;){let s=this.sections[e++],r=this.sections[e++];r<0?(t(i,n,s),n+=s):n+=r,i+=s}}iterChangedRanges(t,e=!1){xt(this,t,e)}get invertedDesc(){let t=[];for(let e=0;e<this.sections.length;){let i=this.sections[e++],n=this.sections[e++];n<0?t.push(i,n):t.push(n,i)}return new St(t)}composeDesc(t){return this.empty?t:t.empty?this:Pt(this,t)}mapDesc(t,e=!1){return t.empty?this:kt(this,t,e)}mapPos(t,e=-1,i=Qt.Simple){let n=0,s=0;for(let r=0;r<this.sections.length;){let o=this.sections[r++],a=this.sections[r++],l=n+o;if(a<0){if(l>t)return s+(t-n);s+=o}else{if(i!=Qt.Simple&&l>=t&&(i==Qt.TrackDel&&n<t&&l>t||i==Qt.TrackBefore&&n<t||i==Qt.TrackAfter&&l>t))return null;if(l>t||l==t&&e<0&&!o)return t==n||e<0?s:s+a;s+=a}n=l}if(t>n)throw new RangeError(`Position ${t} is out of range for changeset of length ${n}`);return s}touchesRange(t,e=t){for(let i=0,n=0;i<this.sections.length&&n<=e;){let s=n+this.sections[i++];if(this.sections[i++]>=0&&n<=e&&s>=t)return!(n<t&&s>e)||"cover";n=s}return!1}toString(){let t="";for(let e=0;e<this.sections.length;){let i=this.sections[e++],n=this.sections[e++];t+=(t?" ":"")+i+(n>=0?":"+n:"")}return t}toJSON(){return this.sections}static fromJSON(t){if(!Array.isArray(t)||t.length%2||t.some((t=>"number"!=typeof t)))throw new RangeError("Invalid JSON representation of ChangeDesc");return new St(t)}static create(t){return new St(t)}}class vt extends St{constructor(t,e){super(t),this.inserted=e}apply(t){if(this.length!=t.length)throw new RangeError("Applying change set to a document with the wrong length");return xt(this,((e,i,n,s,r)=>t=t.replace(n,n+(i-e),r)),!1),t}mapDesc(t,e=!1){return kt(this,t,e,!0)}invert(t){let e=this.sections.slice(),i=[];for(let n=0,s=0;n<e.length;n+=2){let r=e[n],o=e[n+1];if(o>=0){e[n]=o,e[n+1]=r;let a=n>>1;for(;i.length<a;)i.push(st.empty);i.push(r?t.slice(s,s+r):st.empty)}s+=r}return new vt(e,i)}compose(t){return this.empty?t:t.empty?this:Pt(this,t,!0)}map(t,e=!1){return t.empty?this:kt(this,t,e,!0)}iterChanges(t,e=!1){xt(this,t,e)}get desc(){return St.create(this.sections)}filter(t){let e=[],i=[],n=[],s=new $t(this);t:for(let r=0,o=0;;){let a=r==t.length?1e9:t[r++];for(;o<a||o==a&&0==s.len;){if(s.done)break t;let t=Math.min(s.len,a-o);wt(n,t,-1);let r=-1==s.ins?-1:0==s.off?s.ins:0;wt(e,t,r),r>0&&yt(i,e,s.text),s.forward(t),o+=t}let l=t[r++];for(;o<l;){if(s.done)break t;let t=Math.min(s.len,l-o);wt(e,t,-1),wt(n,t,-1==s.ins?-1:0==s.off?s.ins:0),s.forward(t),o+=t}}return{changes:new vt(e,i),filtered:St.create(n)}}toJSON(){let t=[];for(let e=0;e<this.sections.length;e+=2){let i=this.sections[e],n=this.sections[e+1];n<0?t.push(i):0==n?t.push([i]):t.push([i].concat(this.inserted[e>>1].toJSON()))}return t}static of(t,e,i){let n=[],s=[],r=0,o=null;function a(t=!1){if(!t&&!n.length)return;r<e&&wt(n,e-r,-1);let i=new vt(n,s);o=o?o.compose(i.map(o)):i,n=[],s=[],r=0}return function t(l){if(Array.isArray(l))for(let e of l)t(e);else if(l instanceof vt){if(l.length!=e)throw new RangeError(`Mismatched change set length (got ${l.length}, expected ${e})`);a(),o=o?o.compose(l.map(o)):l}else{let{from:t,to:o=t,insert:h}=l;if(t>o||t<0||o>e)throw new RangeError(`Invalid change range ${t} to ${o} (in doc of length ${e})`);let c=h?"string"==typeof h?st.of(h.split(i||bt)):h:st.empty,O=c.length;if(t==o&&0==O)return;t<r&&a(),t>r&&wt(n,t-r,-1),wt(n,o-t,O),yt(s,n,c),r=o}}(t),a(!o),o}static empty(t){return new vt(t?[t,-1]:[],[])}static fromJSON(t){if(!Array.isArray(t))throw new RangeError("Invalid JSON representation of ChangeSet");let e=[],i=[];for(let n=0;n<t.length;n++){let s=t[n];if("number"==typeof s)e.push(s,-1);else{if(!Array.isArray(s)||"number"!=typeof s[0]||s.some(((t,e)=>e&&"string"!=typeof t)))throw new RangeError("Invalid JSON representation of ChangeSet");if(1==s.length)e.push(s[0],0);else{for(;i.length<n;)i.push(st.empty);i[n]=st.of(s.slice(1)),e.push(s[0],i[n].length)}}}return new vt(e,i)}static createSet(t,e){return new vt(t,e)}}function wt(t,e,i,n=!1){if(0==e&&i<=0)return;let s=t.length-2;s>=0&&i<=0&&i==t[s+1]?t[s]+=e:s>=0&&0==e&&0==t[s]?t[s+1]+=i:n?(t[s]+=e,t[s+1]+=i):t.push(e,i)}function yt(t,e,i){if(0==i.length)return;let n=e.length-2>>1;if(n<t.length)t[t.length-1]=t[t.length-1].append(i);else{for(;t.length<n;)t.push(st.empty);t.push(i)}}function xt(t,e,i){let n=t.inserted;for(let s=0,r=0,o=0;o<t.sections.length;){let a=t.sections[o++],l=t.sections[o++];if(l<0)s+=a,r+=a;else{let h=s,c=r,O=st.empty;for(;h+=a,c+=l,l&&n&&(O=O.append(n[o-2>>1])),!(i||o==t.sections.length||t.sections[o+1]<0);)a=t.sections[o++],l=t.sections[o++];e(s,h,r,c,O),s=h,r=c}}}function kt(t,e,i,n=!1){let s=[],r=n?[]:null,o=new $t(t),a=new $t(e);for(let t=-1;;){if(o.done&&a.len||a.done&&o.len)throw new Error("Mismatched change set lengths");if(-1==o.ins&&-1==a.ins){let t=Math.min(o.len,a.len);wt(s,t,-1),o.forward(t),a.forward(t)}else if(a.ins>=0&&(o.ins<0||t==o.i||0==o.off&&(a.len<o.len||a.len==o.len&&!i))){let e=a.len;for(wt(s,a.ins,-1);e;){let i=Math.min(o.len,e);o.ins>=0&&t<o.i&&o.len<=i&&(wt(s,0,o.ins),r&&yt(r,s,o.text),t=o.i),o.forward(i),e-=i}a.next()}else{if(!(o.ins>=0)){if(o.done&&a.done)return r?vt.createSet(s,r):St.create(s);throw new Error("Mismatched change set lengths")}{let e=0,i=o.len;for(;i;)if(-1==a.ins){let t=Math.min(i,a.len);e+=t,i-=t,a.forward(t)}else{if(!(0==a.ins&&a.len<i))break;i-=a.len,a.next()}wt(s,e,t<o.i?o.ins:0),r&&t<o.i&&yt(r,s,o.text),t=o.i,o.forward(o.len-i)}}}}function Pt(t,e,i=!1){let n=[],s=i?[]:null,r=new $t(t),o=new $t(e);for(let t=!1;;){if(r.done&&o.done)return s?vt.createSet(n,s):St.create(n);if(0==r.ins)wt(n,r.len,0,t),r.next();else if(0!=o.len||o.done){if(r.done||o.done)throw new Error("Mismatched change set lengths");{let e=Math.min(r.len2,o.len),i=n.length;if(-1==r.ins){let i=-1==o.ins?-1:o.off?0:o.ins;wt(n,e,i,t),s&&i&&yt(s,n,o.text)}else-1==o.ins?(wt(n,r.off?0:r.len,e,t),s&&yt(s,n,r.textBit(e))):(wt(n,r.off?0:r.len,o.off?0:o.ins,t),s&&!o.off&&yt(s,n,o.text));t=(r.ins>e||o.ins>=0&&o.len>e)&&(t||n.length>i),r.forward2(e),o.forward(e)}}else wt(n,0,o.ins,t),s&&yt(s,n,o.text),o.next()}}class $t{constructor(t){this.set=t,this.i=0,this.next()}next(){let{sections:t}=this.set;this.i<t.length?(this.len=t[this.i++],this.ins=t[this.i++]):(this.len=0,this.ins=-2),this.off=0}get done(){return-2==this.ins}get len2(){return this.ins<0?this.len:this.ins}get text(){let{inserted:t}=this.set,e=this.i-2>>1;return e>=t.length?st.empty:t[e]}textBit(t){let{inserted:e}=this.set,i=this.i-2>>1;return i>=e.length&&!t?st.empty:e[i].slice(this.off,null==t?void 0:this.off+t)}forward(t){t==this.len?this.next():(this.len-=t,this.off+=t)}forward2(t){-1==this.ins?this.forward(t):t==this.ins?this.next():(this.ins-=t,this.off+=t)}}class Zt{constructor(t,e,i){this.from=t,this.to=e,this.flags=i}get anchor(){return 32&this.flags?this.to:this.from}get head(){return 32&this.flags?this.from:this.to}get empty(){return this.from==this.to}get assoc(){return 8&this.flags?-1:16&this.flags?1:0}get bidiLevel(){let t=7&this.flags;return 7==t?null:t}get goalColumn(){let t=this.flags>>6;return 16777215==t?void 0:t}map(t,e=-1){let i,n;return this.empty?i=n=t.mapPos(this.from,e):(i=t.mapPos(this.from,1),n=t.mapPos(this.to,-1)),i==this.from&&n==this.to?this:new Zt(i,n,this.flags)}extend(t,e=t){if(t<=this.anchor&&e>=this.anchor)return Tt.range(t,e);let i=Math.abs(t-this.anchor)>Math.abs(e-this.anchor)?t:e;return Tt.range(this.anchor,i)}eq(t,e=!1){return!(this.anchor!=t.anchor||this.head!=t.head||e&&this.empty&&this.assoc!=t.assoc)}toJSON(){return{anchor:this.anchor,head:this.head}}static fromJSON(t){if(!t||"number"!=typeof t.anchor||"number"!=typeof t.head)throw new RangeError("Invalid JSON representation for SelectionRange");return Tt.range(t.anchor,t.head)}static create(t,e,i){return new Zt(t,e,i)}}class Tt{constructor(t,e){this.ranges=t,this.mainIndex=e}map(t,e=-1){return t.empty?this:Tt.create(this.ranges.map((i=>i.map(t,e))),this.mainIndex)}eq(t,e=!1){if(this.ranges.length!=t.ranges.length||this.mainIndex!=t.mainIndex)return!1;for(let i=0;i<this.ranges.length;i++)if(!this.ranges[i].eq(t.ranges[i],e))return!1;return!0}get main(){return this.ranges[this.mainIndex]}asSingle(){return 1==this.ranges.length?this:new Tt([this.main],0)}addRange(t,e=!0){return Tt.create([t].concat(this.ranges),e?0:this.mainIndex+1)}replaceRange(t,e=this.mainIndex){let i=this.ranges.slice();return i[e]=t,Tt.create(i,this.mainIndex)}toJSON(){return{ranges:this.ranges.map((t=>t.toJSON())),main:this.mainIndex}}static fromJSON(t){if(!t||!Array.isArray(t.ranges)||"number"!=typeof t.main||t.main>=t.ranges.length)throw new RangeError("Invalid JSON representation for EditorSelection");return new Tt(t.ranges.map((t=>Zt.fromJSON(t))),t.main)}static single(t,e=t){return new Tt([Tt.range(t,e)],0)}static create(t,e=0){if(0==t.length)throw new RangeError("A selection needs at least one range");for(let i=0,n=0;n<t.length;n++){let s=t[n];if(s.empty?s.from<=i:s.from<i)return Tt.normalized(t.slice(),e);i=s.to}return new Tt(t,e)}static cursor(t,e=0,i,n){return Zt.create(t,t,(0==e?0:e<0?8:16)|(null==i?7:Math.min(6,i))|(null!=n?n:16777215)<<6)}static range(t,e,i,n){let s=(null!=i?i:16777215)<<6|(null==n?7:Math.min(6,n));return e<t?Zt.create(e,t,48|s):Zt.create(t,e,(e>t?8:0)|s)}static normalized(t,e=0){let i=t[e];t.sort(((t,e)=>t.from-e.from)),e=t.indexOf(i);for(let i=1;i<t.length;i++){let n=t[i],s=t[i-1];if(n.empty?n.from<=s.to:n.from<s.to){let r=s.from,o=Math.max(n.to,s.to);i<=e&&e--,t.splice(--i,2,n.anchor>n.head?Tt.range(o,r):Tt.range(r,o))}}return new Tt(t,e)}}function Xt(t,e){for(let i of t.ranges)if(i.to>e)throw new RangeError("Selection points outside of document")}let Rt=0;class At{constructor(t,e,i,n,s){this.combine=t,this.compareInput=e,this.compare=i,this.isStatic=n,this.id=Rt++,this.default=t([]),this.extensions="function"==typeof s?s(this):s}get reader(){return this}static define(t={}){return new At(t.combine||(t=>t),t.compareInput||((t,e)=>t===e),t.compare||(t.combine?(t,e)=>t===e:Ct),!!t.static,t.enables)}of(t){return new Mt([],this,0,t)}compute(t,e){if(this.isStatic)throw new Error("Can't compute a static facet");return new Mt(t,this,1,e)}computeN(t,e){if(this.isStatic)throw new Error("Can't compute a static facet");return new Mt(t,this,2,e)}from(t,e){return e||(e=t=>t),this.compute([t],(i=>e(i.field(t))))}}function Ct(t,e){return t==e||t.length==e.length&&t.every(((t,i)=>t===e[i]))}class Mt{constructor(t,e,i,n){this.dependencies=t,this.facet=e,this.type=i,this.value=n,this.id=Rt++}dynamicSlot(t){var e;let i=this.value,n=this.facet.compareInput,s=this.id,r=t[s]>>1,o=2==this.type,a=!1,l=!1,h=[];for(let i of this.dependencies)"doc"==i?a=!0:"selection"==i?l=!0:1&(null!==(e=t[i.id])&&void 0!==e?e:1)||h.push(t[i.id]);return{create:t=>(t.values[r]=i(t),1),update(t,e){if(a&&e.docChanged||l&&(e.docChanged||e.selection)||Vt(t,h)){let e=i(t);if(o?!qt(e,t.values[r],n):!n(e,t.values[r]))return t.values[r]=e,1}return 0},reconfigure:(t,e)=>{let a,l=e.config.address[s];if(null!=l){let s=Ft(e,l);if(this.dependencies.every((i=>i instanceof At?e.facet(i)===t.facet(i):!(i instanceof _t)||e.field(i,!1)==t.field(i,!1)))||(o?qt(a=i(t),s,n):n(a=i(t),s)))return t.values[r]=s,0}else a=i(t);return t.values[r]=a,1}}}}function qt(t,e,i){if(t.length!=e.length)return!1;for(let n=0;n<t.length;n++)if(!i(t[n],e[n]))return!1;return!0}function Vt(t,e){let i=!1;for(let n of e)1&Ht(t,n)&&(i=!0);return i}function Yt(t,e,i){let n=i.map((e=>t[e.id])),s=i.map((t=>t.type)),r=n.filter((t=>!(1&t))),o=t[e.id]>>1;function a(t){let i=[];for(let e=0;e<n.length;e++){let r=Ft(t,n[e]);if(2==s[e])for(let t of r)i.push(t);else i.push(r)}return e.combine(i)}return{create(t){for(let e of n)Ht(t,e);return t.values[o]=a(t),1},update(t,i){if(!Vt(t,r))return 0;let n=a(t);return e.compare(n,t.values[o])?0:(t.values[o]=n,1)},reconfigure(t,s){let r=Vt(t,n),l=s.config.facets[e.id],h=s.facet(e);if(l&&!r&&Ct(i,l))return t.values[o]=h,0;let c=a(t);return e.compare(c,h)?(t.values[o]=h,0):(t.values[o]=c,1)}}}const zt=At.define({static:!0});class _t{constructor(t,e,i,n,s){this.id=t,this.createF=e,this.updateF=i,this.compareF=n,this.spec=s,this.provides=void 0}static define(t){let e=new _t(Rt++,t.create,t.update,t.compare||((t,e)=>t===e),t);return t.provide&&(e.provides=t.provide(e)),e}create(t){let e=t.facet(zt).find((t=>t.field==this));return((null==e?void 0:e.create)||this.createF)(t)}slot(t){let e=t[this.id]>>1;return{create:t=>(t.values[e]=this.create(t),1),update:(t,i)=>{let n=t.values[e],s=this.updateF(n,i);return this.compareF(n,s)?0:(t.values[e]=s,1)},reconfigure:(t,i)=>{let n,s=t.facet(zt),r=i.facet(zt);return(n=s.find((t=>t.field==this)))&&n!=r.find((t=>t.field==this))?(t.values[e]=n.create(t),1):null!=i.config.address[this.id]?(t.values[e]=i.field(this),0):(t.values[e]=this.create(t),1)}}}init(t){return[this,zt.of({field:this,create:t})]}get extension(){return this}}const jt=4,Et=3,Dt=2,Wt=1;function Lt(t){return e=>new Bt(e,t)}const Gt={highest:Lt(0),high:Lt(Wt),default:Lt(Dt),low:Lt(Et),lowest:Lt(jt)};class Bt{constructor(t,e){this.inner=t,this.prec=e}}class Ut{of(t){return new It(this,t)}reconfigure(t){return Ut.reconfigure.of({compartment:this,extension:t})}get(t){return t.config.compartments.get(this)}}class It{constructor(t,e){this.compartment=t,this.inner=e}}class Nt{constructor(t,e,i,n,s,r){for(this.base=t,this.compartments=e,this.dynamicSlots=i,this.address=n,this.staticValues=s,this.facets=r,this.statusTemplate=[];this.statusTemplate.length<i.length;)this.statusTemplate.push(0)}staticFacet(t){let e=this.address[t.id];return null==e?t.default:this.staticValues[e>>1]}static resolve(t,e,i){let n=[],s=Object.create(null),r=new Map;for(let i of function(t,e,i){let n=[[],[],[],[],[]],s=new Map;function r(t,o){let a=s.get(t);if(null!=a){if(a<=o)return;let e=n[a].indexOf(t);e>-1&&n[a].splice(e,1),t instanceof It&&i.delete(t.compartment)}if(s.set(t,o),Array.isArray(t))for(let e of t)r(e,o);else if(t instanceof It){if(i.has(t.compartment))throw new RangeError("Duplicate use of compartment in extensions");let n=e.get(t.compartment)||t.inner;i.set(t.compartment,n),r(n,o)}else if(t instanceof Bt)r(t.inner,t.prec);else if(t instanceof _t)n[o].push(t),t.provides&&r(t.provides,o);else if(t instanceof Mt)n[o].push(t),t.facet.extensions&&r(t.facet.extensions,Dt);else{let e=t.extension;if(!e)throw new Error(`Unrecognized extension value in extension set (${t}). This sometimes happens because multiple instances of @codemirror/state are loaded, breaking instanceof checks.`);r(e,o)}}return r(t,Dt),n.reduce(((t,e)=>t.concat(e)))}(t,e,r))i instanceof _t?n.push(i):(s[i.facet.id]||(s[i.facet.id]=[])).push(i);let o=Object.create(null),a=[],l=[];for(let t of n)o[t.id]=l.length<<1,l.push((e=>t.slot(e)));let h=null==i?void 0:i.config.facets;for(let t in s){let e=s[t],n=e[0].facet,r=h&&h[t]||[];if(e.every((t=>0==t.type)))if(o[n.id]=a.length<<1|1,Ct(r,e))a.push(i.facet(n));else{let t=n.combine(e.map((t=>t.value)));a.push(i&&n.compare(t,i.facet(n))?i.facet(n):t)}else{for(let t of e)0==t.type?(o[t.id]=a.length<<1|1,a.push(t.value)):(o[t.id]=l.length<<1,l.push((e=>t.dynamicSlot(e))));o[n.id]=l.length<<1,l.push((t=>Yt(t,n,e)))}}let c=l.map((t=>t(o)));return new Nt(t,r,c,o,a,s)}}function Ht(t,e){if(1&e)return 2;let i=e>>1,n=t.status[i];if(4==n)throw new Error("Cyclic dependency between fields and/or facets");if(2&n)return n;t.status[i]=4;let s=t.computeSlot(t,t.config.dynamicSlots[i]);return t.status[i]=2|s}function Ft(t,e){return 1&e?t.config.staticValues[e>>1]:t.values[e>>1]}const Kt=At.define(),Jt=At.define({combine:t=>t.some((t=>t)),static:!0}),te=At.define({combine:t=>t.length?t[0]:void 0,static:!0}),ee=At.define(),ie=At.define(),ne=At.define(),se=At.define({combine:t=>!!t.length&&t[0]});class re{constructor(t,e){this.type=t,this.value=e}static define(){return new oe}}class oe{of(t){return new re(this,t)}}class ae{constructor(t){this.map=t}of(t){return new le(this,t)}}class le{constructor(t,e){this.type=t,this.value=e}map(t){let e=this.type.map(this.value,t);return void 0===e?void 0:e==this.value?this:new le(this.type,e)}is(t){return this.type==t}static define(t={}){return new ae(t.map||(t=>t))}static mapEffects(t,e){if(!t.length)return t;let i=[];for(let n of t){let t=n.map(e);t&&i.push(t)}return i}}le.reconfigure=le.define(),le.appendConfig=le.define();class he{constructor(t,e,i,n,s,r){this.startState=t,this.changes=e,this.selection=i,this.effects=n,this.annotations=s,this.scrollIntoView=r,this._doc=null,this._state=null,i&&Xt(i,e.newLength),s.some((t=>t.type==he.time))||(this.annotations=s.concat(he.time.of(Date.now())))}static create(t,e,i,n,s,r){return new he(t,e,i,n,s,r)}get newDoc(){return this._doc||(this._doc=this.changes.apply(this.startState.doc))}get newSelection(){return this.selection||this.startState.selection.map(this.changes)}get state(){return this._state||this.startState.applyTransaction(this),this._state}annotation(t){for(let e of this.annotations)if(e.type==t)return e.value}get docChanged(){return!this.changes.empty}get reconfigured(){return this.startState.config!=this.state.config}isUserEvent(t){let e=this.annotation(he.userEvent);return!(!e||!(e==t||e.length>t.length&&e.slice(0,t.length)==t&&"."==e[t.length]))}}function ce(t,e){let i=[];for(let n=0,s=0;;){let r,o;if(n<t.length&&(s==e.length||e[s]>=t[n]))r=t[n++],o=t[n++];else{if(!(s<e.length))return i;r=e[s++],o=e[s++]}!i.length||i[i.length-1]<r?i.push(r,o):i[i.length-1]<o&&(i[i.length-1]=o)}}function Oe(t,e,i){var n;let s,r,o;return i?(s=e.changes,r=vt.empty(e.changes.length),o=t.changes.compose(e.changes)):(s=e.changes.map(t.changes),r=t.changes.mapDesc(e.changes,!0),o=t.changes.compose(s)),{changes:o,selection:e.selection?e.selection.map(r):null===(n=t.selection)||void 0===n?void 0:n.map(s),effects:le.mapEffects(t.effects,s).concat(le.mapEffects(e.effects,r)),annotations:t.annotations.length?t.annotations.concat(e.annotations):e.annotations,scrollIntoView:t.scrollIntoView||e.scrollIntoView}}function ue(t,e,i){let n=e.selection,s=pe(e.annotations);return e.userEvent&&(s=s.concat(he.userEvent.of(e.userEvent))),{changes:e.changes instanceof vt?e.changes:vt.of(e.changes||[],i,t.facet(te)),selection:n&&(n instanceof Tt?n:Tt.single(n.anchor,n.head)),effects:pe(e.effects),annotations:s,scrollIntoView:!!e.scrollIntoView}}function fe(t,e,i){let n=ue(t,e.length?e[0]:{},t.doc.length);e.length&&!1===e[0].filter&&(i=!1);for(let s=1;s<e.length;s++){!1===e[s].filter&&(i=!1);let r=!!e[s].sequential;n=Oe(n,ue(t,e[s],r?n.changes.newLength:t.doc.length),r)}let s=he.create(t,n.changes,n.selection,n.effects,n.annotations,n.scrollIntoView);return function(t){let e=t.startState,i=e.facet(ne),n=t;for(let s=i.length-1;s>=0;s--){let r=i[s](t);r&&Object.keys(r).length&&(n=Oe(n,ue(e,r,t.changes.newLength),!0))}return n==t?t:he.create(e,t.changes,t.selection,n.effects,n.annotations,n.scrollIntoView)}(i?function(t){let e=t.startState,i=!0;for(let n of e.facet(ee)){let e=n(t);if(!1===e){i=!1;break}Array.isArray(e)&&(i=!0===i?e:ce(i,e))}if(!0!==i){let n,s;if(!1===i)s=t.changes.invertedDesc,n=vt.empty(e.doc.length);else{let e=t.changes.filter(i);n=e.changes,s=e.filtered.mapDesc(e.changes).invertedDesc}t=he.create(e,n,t.selection&&t.selection.map(s),le.mapEffects(t.effects,s),t.annotations,t.scrollIntoView)}let n=e.facet(ie);for(let i=n.length-1;i>=0;i--){let s=n[i](t);t=s instanceof he?s:Array.isArray(s)&&1==s.length&&s[0]instanceof he?s[0]:fe(e,pe(s),!1)}return t}(s):s)}he.time=re.define(),he.userEvent=re.define(),he.addToHistory=re.define(),he.remote=re.define();const de=[];function pe(t){return null==t?de:Array.isArray(t)?t:[t]}var me=function(t){return t[t.Word=0]="Word",t[t.Space=1]="Space",t[t.Other=2]="Other",t}(me||(me={}));const ge=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;let be;try{be=new RegExp("[\\p{Alphabetic}\\p{Number}_]","u")}catch(t){}function Qe(t){return e=>{if(!/\S/.test(e))return me.Space;if(function(t){if(be)return be.test(t);for(let e=0;e<t.length;e++){let i=t[e];if(/\w/.test(i)||i>""&&(i.toUpperCase()!=i.toLowerCase()||ge.test(i)))return!0}return!1}(e))return me.Word;for(let i=0;i<t.length;i++)if(e.indexOf(t[i])>-1)return me.Word;return me.Other}}class Se{constructor(t,e,i,n,s,r){this.config=t,this.doc=e,this.selection=i,this.values=n,this.status=t.statusTemplate.slice(),this.computeSlot=s,r&&(r._state=this);for(let t=0;t<this.config.dynamicSlots.length;t++)Ht(this,t<<1);this.computeSlot=null}field(t,e=!0){let i=this.config.address[t.id];if(null!=i)return Ht(this,i),Ft(this,i);if(e)throw new RangeError("Field is not present in this state")}update(...t){return fe(this,t,!0)}applyTransaction(t){let e,i=this.config,{base:n,compartments:s}=i;for(let e of t.effects)e.is(Ut.reconfigure)?(i&&(s=new Map,i.compartments.forEach(((t,e)=>s.set(e,t))),i=null),s.set(e.value.compartment,e.value.extension)):e.is(le.reconfigure)?(i=null,n=e.value):e.is(le.appendConfig)&&(i=null,n=pe(n).concat(e.value));if(i)e=t.startState.values.slice();else{i=Nt.resolve(n,s,this),e=new Se(i,this.doc,this.selection,i.dynamicSlots.map((()=>null)),((t,e)=>e.reconfigure(t,this)),null).values}let r=t.startState.facet(Jt)?t.newSelection:t.newSelection.asSingle();new Se(i,t.newDoc,r,e,((e,i)=>i.update(e,t)),t)}replaceSelection(t){return"string"==typeof t&&(t=this.toText(t)),this.changeByRange((e=>({changes:{from:e.from,to:e.to,insert:t},range:Tt.cursor(e.from+t.length)})))}changeByRange(t){let e=this.selection,i=t(e.ranges[0]),n=this.changes(i.changes),s=[i.range],r=pe(i.effects);for(let i=1;i<e.ranges.length;i++){let o=t(e.ranges[i]),a=this.changes(o.changes),l=a.map(n);for(let t=0;t<i;t++)s[t]=s[t].map(l);let h=n.mapDesc(a,!0);s.push(o.range.map(h)),n=n.compose(l),r=le.mapEffects(r,l).concat(le.mapEffects(pe(o.effects),h))}return{changes:n,selection:Tt.create(s,e.mainIndex),effects:r}}changes(t=[]){return t instanceof vt?t:vt.of(t,this.doc.length,this.facet(Se.lineSeparator))}toText(t){return st.of(t.split(this.facet(Se.lineSeparator)||bt))}sliceDoc(t=0,e=this.doc.length){return this.doc.sliceString(t,e,this.lineBreak)}facet(t){let e=this.config.address[t.id];return null==e?t.default:(Ht(this,e),Ft(this,e))}toJSON(t){let e={doc:this.sliceDoc(),selection:this.selection.toJSON()};if(t)for(let i in t){let n=t[i];n instanceof _t&&null!=this.config.address[n.id]&&(e[i]=n.spec.toJSON(this.field(t[i]),this))}return e}static fromJSON(t,e={},i){if(!t||"string"!=typeof t.doc)throw new RangeError("Invalid JSON representation for EditorState");let n=[];if(i)for(let e in i)if(Object.prototype.hasOwnProperty.call(t,e)){let s=i[e],r=t[e];n.push(s.init((t=>s.spec.fromJSON(r,t))))}return Se.create({doc:t.doc,selection:Tt.fromJSON(t.selection),extensions:e.extensions?n.concat([e.extensions]):n})}static create(t={}){let e=Nt.resolve(t.extensions||[],new Map),i=t.doc instanceof st?t.doc:st.of((t.doc||"").split(e.staticFacet(Se.lineSeparator)||bt)),n=t.selection?t.selection instanceof Tt?t.selection:Tt.single(t.selection.anchor,t.selection.head):Tt.single(0);return Xt(n,i.length),e.staticFacet(Jt)||(n=n.asSingle()),new Se(e,i,n,e.dynamicSlots.map((()=>null)),((t,e)=>e.create(t)),null)}get tabSize(){return this.facet(Se.tabSize)}get lineBreak(){return this.facet(Se.lineSeparator)||"\n"}get readOnly(){return this.facet(se)}phrase(t,...e){for(let e of this.facet(Se.phrases))if(Object.prototype.hasOwnProperty.call(e,t)){t=e[t];break}return e.length&&(t=t.replace(/\$(\$|\d*)/g,((t,i)=>{if("$"==i)return"$";let n=+(i||1);return!n||n>e.length?t:e[n-1]}))),t}languageDataAt(t,e,i=-1){let n=[];for(let s of this.facet(Kt))for(let r of s(this,e,i))Object.prototype.hasOwnProperty.call(r,t)&&n.push(r[t]);return n}charCategorizer(t){return Qe(this.languageDataAt("wordChars",t).join(""))}wordAt(t){let{text:e,from:i,length:n}=this.doc.lineAt(t),s=this.charCategorizer(t),r=t-i,o=t-i;for(;r>0;){let t=dt(e,r,!1);if(s(e.slice(t,r))!=me.Word)break;r=t}for(;o<n;){let t=dt(e,o);if(s(e.slice(o,t))!=me.Word)break;o=t}return r==o?null:Tt.range(r+i,o+i)}}function ve(t,e,i={}){let n={};for(let e of t)for(let t of Object.keys(e)){let s=e[t],r=n[t];if(void 0===r)n[t]=s;else if(r===s||void 0===s);else{if(!Object.hasOwnProperty.call(i,t))throw new Error("Config merge conflict for field "+t);n[t]=i[t](r,s)}}for(let t in e)void 0===n[t]&&(n[t]=e[t]);return n}Se.allowMultipleSelections=Jt,Se.tabSize=At.define({combine:t=>t.length?t[0]:4}),Se.lineSeparator=te,Se.readOnly=se,Se.phrases=At.define({compare(t,e){let i=Object.keys(t),n=Object.keys(e);return i.length==n.length&&i.every((i=>t[i]==e[i]))}}),Se.languageData=Kt,Se.changeFilter=ee,Se.transactionFilter=ie,Se.transactionExtender=ne,Ut.reconfigure=le.define();class we{eq(t){return this==t}range(t,e=t){return ye.create(t,e,this)}}we.prototype.startSide=we.prototype.endSide=0,we.prototype.point=!1,we.prototype.mapMode=Qt.TrackDel;class ye{constructor(t,e,i){this.from=t,this.to=e,this.value=i}static create(t,e,i){return new ye(t,e,i)}}function xe(t,e){return t.from-e.from||t.value.startSide-e.value.startSide}class ke{constructor(t,e,i,n){this.from=t,this.to=e,this.value=i,this.maxPoint=n}get length(){return this.to[this.to.length-1]}findIndex(t,e,i,n=0){let s=i?this.to:this.from;for(let r=n,o=s.length;;){if(r==o)return r;let n=r+o>>1,a=s[n]-t||(i?this.value[n].endSide:this.value[n].startSide)-e;if(n==r)return a>=0?r:o;a>=0?o=n:r=n+1}}between(t,e,i,n){for(let s=this.findIndex(e,-1e9,!0),r=this.findIndex(i,1e9,!1,s);s<r;s++)if(!1===n(this.from[s]+t,this.to[s]+t,this.value[s]))return!1}map(t,e){let i=[],n=[],s=[],r=-1,o=-1;for(let a=0;a<this.value.length;a++){let l,h,c=this.value[a],O=this.from[a]+t,u=this.to[a]+t;if(O==u){let t=e.mapPos(O,c.startSide,c.mapMode);if(null==t)continue;if(l=h=t,c.startSide!=c.endSide&&(h=e.mapPos(O,c.endSide),h<l))continue}else if(l=e.mapPos(O,c.startSide),h=e.mapPos(u,c.endSide),l>h||l==h&&c.startSide>0&&c.endSide<=0)continue;(h-l||c.endSide-c.startSide)<0||(r<0&&(r=l),c.point&&(o=Math.max(o,h-l)),i.push(c),n.push(l-r),s.push(h-r))}return{mapped:i.length?new ke(n,s,i,o):null,pos:r}}}class Pe{constructor(t,e,i,n){this.chunkPos=t,this.chunk=e,this.nextLayer=i,this.maxPoint=n}static create(t,e,i,n){return new Pe(t,e,i,n)}get length(){let t=this.chunk.length-1;return t<0?0:Math.max(this.chunkEnd(t),this.nextLayer.length)}get size(){if(this.isEmpty)return 0;let t=this.nextLayer.size;for(let e of this.chunk)t+=e.value.length;return t}chunkEnd(t){return this.chunkPos[t]+this.chunk[t].length}update(t){let{add:e=[],sort:i=!1,filterFrom:n=0,filterTo:s=this.length}=t,r=t.filter;if(0==e.length&&!r)return this;if(i&&(e=e.slice().sort(xe)),this.isEmpty)return e.length?Pe.of(e):this;let o=new Te(this,null,-1).goto(0),a=0,l=[],h=new $e;for(;o.value||a<e.length;)if(a<e.length&&(o.from-e[a].from||o.startSide-e[a].value.startSide)>=0){let t=e[a++];h.addInner(t.from,t.to,t.value)||l.push(t)}else 1==o.rangeIndex&&o.chunkIndex<this.chunk.length&&(a==e.length||this.chunkEnd(o.chunkIndex)<e[a].from)&&(!r||n>this.chunkEnd(o.chunkIndex)||s<this.chunkPos[o.chunkIndex])&&h.addChunk(this.chunkPos[o.chunkIndex],this.chunk[o.chunkIndex])?o.nextChunk():((!r||n>o.to||s<o.from||r(o.from,o.to,o.value))&&(h.addInner(o.from,o.to,o.value)||l.push(ye.create(o.from,o.to,o.value))),o.next());return h.finishInner(this.nextLayer.isEmpty&&!l.length?Pe.empty:this.nextLayer.update({add:l,filter:r,filterFrom:n,filterTo:s}))}map(t){if(t.empty||this.isEmpty)return this;let e=[],i=[],n=-1;for(let s=0;s<this.chunk.length;s++){let r=this.chunkPos[s],o=this.chunk[s],a=t.touchesRange(r,r+o.length);if(!1===a)n=Math.max(n,o.maxPoint),e.push(o),i.push(t.mapPos(r));else if(!0===a){let{mapped:s,pos:a}=o.map(r,t);s&&(n=Math.max(n,s.maxPoint),e.push(s),i.push(a))}}let s=this.nextLayer.map(t);return 0==e.length?s:new Pe(i,e,s||Pe.empty,n)}between(t,e,i){if(!this.isEmpty){for(let n=0;n<this.chunk.length;n++){let s=this.chunkPos[n],r=this.chunk[n];if(e>=s&&t<=s+r.length&&!1===r.between(s,t-s,e-s,i))return}this.nextLayer.between(t,e,i)}}iter(t=0){return Xe.from([this]).goto(t)}get isEmpty(){return this.nextLayer==this}static iter(t,e=0){return Xe.from(t).goto(e)}static compare(t,e,i,n,s=-1){let r=t.filter((t=>t.maxPoint>0||!t.isEmpty&&t.maxPoint>=s)),o=e.filter((t=>t.maxPoint>0||!t.isEmpty&&t.maxPoint>=s)),a=Ze(r,o,i),l=new Ae(r,a,s),h=new Ae(o,a,s);i.iterGaps(((t,e,i)=>Ce(l,t,h,e,i,n))),i.empty&&0==i.length&&Ce(l,0,h,0,0,n)}static eq(t,e,i=0,n){null==n&&(n=999999999);let s=t.filter((t=>!t.isEmpty&&e.indexOf(t)<0)),r=e.filter((e=>!e.isEmpty&&t.indexOf(e)<0));if(s.length!=r.length)return!1;if(!s.length)return!0;let o=Ze(s,r),a=new Ae(s,o,0).goto(i),l=new Ae(r,o,0).goto(i);for(;;){if(a.to!=l.to||!Me(a.active,l.active)||a.point&&(!l.point||!a.point.eq(l.point)))return!1;if(a.to>n)return!0;a.next(),l.next()}}static spans(t,e,i,n,s=-1){let r=new Ae(t,null,s).goto(e),o=e,a=r.openStart;for(;;){let t=Math.min(r.to,i);if(r.point){let i=r.activeForPoint(r.to),s=r.pointFrom<e?i.length+1:r.point.startSide<0?i.length:Math.min(i.length,a);n.point(o,t,r.point,i,s,r.pointRank),a=Math.min(r.openEnd(t),i.length)}else t>o&&(n.span(o,t,r.active,a),a=r.openEnd(t));if(r.to>i)return a+(r.point&&r.to>i?1:0);o=r.to,r.next()}}static of(t,e=!1){let i=new $e;for(let n of t instanceof ye?[t]:e?function(t){if(t.length>1)for(let e=t[0],i=1;i<t.length;i++){let n=t[i];if(xe(e,n)>0)return t.slice().sort(xe);e=n}return t}(t):t)i.add(n.from,n.to,n.value);return i.finish()}static join(t){if(!t.length)return Pe.empty;let e=t[t.length-1];for(let i=t.length-2;i>=0;i--)for(let n=t[i];n!=Pe.empty;n=n.nextLayer)e=new Pe(n.chunkPos,n.chunk,e,Math.max(n.maxPoint,e.maxPoint));return e}}Pe.empty=new Pe([],[],null,-1),Pe.empty.nextLayer=Pe.empty;class $e{finishChunk(t){this.chunks.push(new ke(this.from,this.to,this.value,this.maxPoint)),this.chunkPos.push(this.chunkStart),this.chunkStart=-1,this.setMaxPoint=Math.max(this.setMaxPoint,this.maxPoint),this.maxPoint=-1,t&&(this.from=[],this.to=[],this.value=[])}constructor(){this.chunks=[],this.chunkPos=[],this.chunkStart=-1,this.last=null,this.lastFrom=-1e9,this.lastTo=-1e9,this.from=[],this.to=[],this.value=[],this.maxPoint=-1,this.setMaxPoint=-1,this.nextLayer=null}add(t,e,i){this.addInner(t,e,i)||(this.nextLayer||(this.nextLayer=new $e)).add(t,e,i)}addInner(t,e,i){let n=t-this.lastTo||i.startSide-this.last.endSide;if(n<=0&&(t-this.lastFrom||i.startSide-this.last.startSide)<0)throw new Error("Ranges must be added sorted by `from` position and `startSide`");return!(n<0)&&(250==this.from.length&&this.finishChunk(!0),this.chunkStart<0&&(this.chunkStart=t),this.from.push(t-this.chunkStart),this.to.push(e-this.chunkStart),this.last=i,this.lastFrom=t,this.lastTo=e,this.value.push(i),i.point&&(this.maxPoint=Math.max(this.maxPoint,e-t)),!0)}addChunk(t,e){if((t-this.lastTo||e.value[0].startSide-this.last.endSide)<0)return!1;this.from.length&&this.finishChunk(!0),this.setMaxPoint=Math.max(this.setMaxPoint,e.maxPoint),this.chunks.push(e),this.chunkPos.push(t);let i=e.value.length-1;return this.last=e.value[i],this.lastFrom=e.from[i]+t,this.lastTo=e.to[i]+t,!0}finish(){return this.finishInner(Pe.empty)}finishInner(t){if(this.from.length&&this.finishChunk(!1),0==this.chunks.length)return t;let e=Pe.create(this.chunkPos,this.chunks,this.nextLayer?this.nextLayer.finishInner(t):t,this.setMaxPoint);return this.from=null,e}}function Ze(t,e,i){let n=new Map;for(let e of t)for(let t=0;t<e.chunk.length;t++)e.chunk[t].maxPoint<=0&&n.set(e.chunk[t],e.chunkPos[t]);let s=new Set;for(let t of e)for(let e=0;e<t.chunk.length;e++){let r=n.get(t.chunk[e]);null==r||(i?i.mapPos(r):r)!=t.chunkPos[e]||(null==i?void 0:i.touchesRange(r,r+t.chunk[e].length))||s.add(t.chunk[e])}return s}class Te{constructor(t,e,i,n=0){this.layer=t,this.skip=e,this.minPoint=i,this.rank=n}get startSide(){return this.value?this.value.startSide:0}get endSide(){return this.value?this.value.endSide:0}goto(t,e=-1e9){return this.chunkIndex=this.rangeIndex=0,this.gotoInner(t,e,!1),this}gotoInner(t,e,i){for(;this.chunkIndex<this.layer.chunk.length;){let e=this.layer.chunk[this.chunkIndex];if(!(this.skip&&this.skip.has(e)||this.layer.chunkEnd(this.chunkIndex)<t||e.maxPoint<this.minPoint))break;this.chunkIndex++,i=!1}if(this.chunkIndex<this.layer.chunk.length){let n=this.layer.chunk[this.chunkIndex].findIndex(t-this.layer.chunkPos[this.chunkIndex],e,!0);(!i||this.rangeIndex<n)&&this.setRangeIndex(n)}this.next()}forward(t,e){(this.to-t||this.endSide-e)<0&&this.gotoInner(t,e,!0)}next(){for(;;){if(this.chunkIndex==this.layer.chunk.length){this.from=this.to=1e9,this.value=null;break}{let t=this.layer.chunkPos[this.chunkIndex],e=this.layer.chunk[this.chunkIndex],i=t+e.from[this.rangeIndex];if(this.from=i,this.to=t+e.to[this.rangeIndex],this.value=e.value[this.rangeIndex],this.setRangeIndex(this.rangeIndex+1),this.minPoint<0||this.value.point&&this.to-this.from>=this.minPoint)break}}}setRangeIndex(t){if(t==this.layer.chunk[this.chunkIndex].value.length){if(this.chunkIndex++,this.skip)for(;this.chunkIndex<this.layer.chunk.length&&this.skip.has(this.layer.chunk[this.chunkIndex]);)this.chunkIndex++;this.rangeIndex=0}else this.rangeIndex=t}nextChunk(){this.chunkIndex++,this.rangeIndex=0,this.next()}compare(t){return this.from-t.from||this.startSide-t.startSide||this.rank-t.rank||this.to-t.to||this.endSide-t.endSide}}class Xe{constructor(t){this.heap=t}static from(t,e=null,i=-1){let n=[];for(let s=0;s<t.length;s++)for(let r=t[s];!r.isEmpty;r=r.nextLayer)r.maxPoint>=i&&n.push(new Te(r,e,i,s));return 1==n.length?n[0]:new Xe(n)}get startSide(){return this.value?this.value.startSide:0}goto(t,e=-1e9){for(let i of this.heap)i.goto(t,e);for(let t=this.heap.length>>1;t>=0;t--)Re(this.heap,t);return this.next(),this}forward(t,e){for(let i of this.heap)i.forward(t,e);for(let t=this.heap.length>>1;t>=0;t--)Re(this.heap,t);(this.to-t||this.value.endSide-e)<0&&this.next()}next(){if(0==this.heap.length)this.from=this.to=1e9,this.value=null,this.rank=-1;else{let t=this.heap[0];this.from=t.from,this.to=t.to,this.value=t.value,this.rank=t.rank,t.value&&t.next(),Re(this.heap,0)}}}function Re(t,e){for(let i=t[e];;){let n=1+(e<<1);if(n>=t.length)break;let s=t[n];if(n+1<t.length&&s.compare(t[n+1])>=0&&(s=t[n+1],n++),i.compare(s)<0)break;t[n]=i,t[e]=s,e=n}}class Ae{constructor(t,e,i){this.minPoint=i,this.active=[],this.activeTo=[],this.activeRank=[],this.minActive=-1,this.point=null,this.pointFrom=0,this.pointRank=0,this.to=-1e9,this.endSide=0,this.openStart=-1,this.cursor=Xe.from(t,e,i)}goto(t,e=-1e9){return this.cursor.goto(t,e),this.active.length=this.activeTo.length=this.activeRank.length=0,this.minActive=-1,this.to=t,this.endSide=e,this.openStart=-1,this.next(),this}forward(t,e){for(;this.minActive>-1&&(this.activeTo[this.minActive]-t||this.active[this.minActive].endSide-e)<0;)this.removeActive(this.minActive);this.cursor.forward(t,e)}removeActive(t){qe(this.active,t),qe(this.activeTo,t),qe(this.activeRank,t),this.minActive=Ye(this.active,this.activeTo)}addActive(t){let e=0,{value:i,to:n,rank:s}=this.cursor;for(;e<this.activeRank.length&&(s-this.activeRank[e]||n-this.activeTo[e])>0;)e++;Ve(this.active,e,i),Ve(this.activeTo,e,n),Ve(this.activeRank,e,s),t&&Ve(t,e,this.cursor.from),this.minActive=Ye(this.active,this.activeTo)}next(){let t=this.to,e=this.point;this.point=null;let i=this.openStart<0?[]:null;for(;;){let n=this.minActive;if(n>-1&&(this.activeTo[n]-this.cursor.from||this.active[n].endSide-this.cursor.startSide)<0){if(this.activeTo[n]>t){this.to=this.activeTo[n],this.endSide=this.active[n].endSide;break}this.removeActive(n),i&&qe(i,n)}else{if(!this.cursor.value){this.to=this.endSide=1e9;break}if(this.cursor.from>t){this.to=this.cursor.from,this.endSide=this.cursor.startSide;break}{let t=this.cursor.value;if(t.point){if(!(e&&this.cursor.to==this.to&&this.cursor.from<this.cursor.to)){this.point=t,this.pointFrom=this.cursor.from,this.pointRank=this.cursor.rank,this.to=this.cursor.to,this.endSide=t.endSide,this.cursor.next(),this.forward(this.to,this.endSide);break}this.cursor.next()}else this.addActive(i),this.cursor.next()}}}if(i){this.openStart=0;for(let e=i.length-1;e>=0&&i[e]<t;e--)this.openStart++}}activeForPoint(t){if(!this.active.length)return this.active;let e=[];for(let i=this.active.length-1;i>=0&&!(this.activeRank[i]<this.pointRank);i--)(this.activeTo[i]>t||this.activeTo[i]==t&&this.active[i].endSide>=this.point.endSide)&&e.push(this.active[i]);return e.reverse()}openEnd(t){let e=0;for(let i=this.activeTo.length-1;i>=0&&this.activeTo[i]>t;i--)e++;return e}}function Ce(t,e,i,n,s,r){t.goto(e),i.goto(n);let o=n+s,a=n,l=n-e;for(;;){let e=t.to+l-i.to,n=e||t.endSide-i.endSide,s=n<0?t.to+l:i.to,h=Math.min(s,o);if(t.point||i.point?t.point&&i.point&&(t.point==i.point||t.point.eq(i.point))&&Me(t.activeForPoint(t.to),i.activeForPoint(i.to))||r.comparePoint(a,h,t.point,i.point):h>a&&!Me(t.active,i.active)&&r.compareRange(a,h,t.active,i.active),s>o)break;(e||t.openEnd!=i.openEnd)&&r.boundChange&&r.boundChange(s),a=s,n<=0&&t.next(),n>=0&&i.next()}}function Me(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++)if(t[i]!=e[i]&&!t[i].eq(e[i]))return!1;return!0}function qe(t,e){for(let i=e,n=t.length-1;i<n;i++)t[i]=t[i+1];t.pop()}function Ve(t,e,i){for(let i=t.length-1;i>=e;i--)t[i+1]=t[i];t[e]=i}function Ye(t,e){let i=-1,n=1e9;for(let s=0;s<e.length;s++)(e[s]-n||t[s].endSide-t[i].endSide)<0&&(i=s,n=e[s]);return i}function ze(t,e,i=t.length){let n=0;for(let s=0;s<i&&s<t.length;)9==t.charCodeAt(s)?(n+=e-n%e,s++):(n++,s=dt(t,s));return n}const _e="undefined"==typeof Symbol?"__ͼ":Symbol.for("ͼ"),je="undefined"==typeof Symbol?"__styleSet"+Math.floor(1e8*Math.random()):Symbol("styleSet"),Ee="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:{};class De{constructor(t,e){this.rules=[];let{finish:i}=e||{};function n(t){return/^@/.test(t)?[t]:t.split(/,\s*/)}function s(t,e,r,o){let a=[],l=/^@(\w+)\b/.exec(t[0]),h=l&&"keyframes"==l[1];if(l&&null==e)return r.push(t[0]+";");for(let i in e){let o=e[i];if(/&/.test(i))s(i.split(/,\s*/).map((e=>t.map((t=>e.replace(/&/,t))))).reduce(((t,e)=>t.concat(e))),o,r);else if(o&&"object"==typeof o){if(!l)throw new RangeError("The value of a property ("+i+") should be a primitive value.");s(n(i),o,a,h)}else null!=o&&a.push(i.replace(/_.*/,"").replace(/[A-Z]/g,(t=>"-"+t.toLowerCase()))+": "+o+";")}(a.length||h)&&r.push((!i||l||o?t:t.map(i)).join(", ")+" {"+a.join(" ")+"}")}for(let e in t)s(n(e),t[e],this.rules)}getRules(){return this.rules.join("\n")}static newName(){let t=Ee[_e]||1;return Ee[_e]=t+1,"ͼ"+t.toString(36)}static mount(t,e,i){let n=t[je],s=i&&i.nonce;n?s&&n.setNonce(s):n=new Le(t,s),n.mount(Array.isArray(e)?e:[e])}}let We=new Map;class Le{constructor(t,e){let i=t.ownerDocument||t,n=i.defaultView;if(!t.head&&t.adoptedStyleSheets&&n.CSSStyleSheet){let e=We.get(i);if(e)return t.adoptedStyleSheets=[e.sheet,...t.adoptedStyleSheets],t[je]=e;this.sheet=new n.CSSStyleSheet,t.adoptedStyleSheets=[this.sheet,...t.adoptedStyleSheets],We.set(i,this)}else{this.styleTag=i.createElement("style"),e&&this.styleTag.setAttribute("nonce",e);let n=t.head||t;n.insertBefore(this.styleTag,n.firstChild)}this.modules=[],t[je]=this}mount(t){let e=this.sheet,i=0,n=0;for(let s=0;s<t.length;s++){let r=t[s],o=this.modules.indexOf(r);if(o<n&&o>-1&&(this.modules.splice(o,1),n--,o=-1),-1==o){if(this.modules.splice(n++,0,r),e)for(let t=0;t<r.rules.length;t++)e.insertRule(r.rules[t],i++)}else{for(;n<o;)i+=this.modules[n++].rules.length;i+=r.rules.length,n++}}if(!e){let t="";for(let e=0;e<this.modules.length;e++)t+=this.modules[e].getRules()+"\n";this.styleTag.textContent=t}}setNonce(t){this.styleTag&&this.styleTag.getAttribute("nonce")!=t&&this.styleTag.setAttribute("nonce",t)}}for(var Ge={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},Be={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},Ue="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),Ie="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),Ne=0;Ne<10;Ne++)Ge[48+Ne]=Ge[96+Ne]=String(Ne);for(Ne=1;Ne<=24;Ne++)Ge[Ne+111]="F"+Ne;for(Ne=65;Ne<=90;Ne++)Ge[Ne]=String.fromCharCode(Ne+32),Be[Ne]=String.fromCharCode(Ne);for(var He in Ge)Be.hasOwnProperty(He)||(Be[He]=Ge[He]);function Fe(t){let e;return e=11==t.nodeType?t.getSelection?t:t.ownerDocument:t,e.getSelection()}function Ke(t,e){return!!e&&(t==e||t.contains(1!=e.nodeType?e.parentNode:e))}function Je(t,e){if(!e.anchorNode)return!1;try{return Ke(t,e.anchorNode)}catch(t){return!1}}function ti(t){return 3==t.nodeType?ui(t,0,t.nodeValue.length).getClientRects():1==t.nodeType?t.getClientRects():[]}function ei(t,e,i,n){return!!i&&(ni(t,e,i,n,-1)||ni(t,e,i,n,1))}function ii(t){for(var e=0;;e++)if(!(t=t.previousSibling))return e}function ni(t,e,i,n,s){for(;;){if(t==i&&e==n)return!0;if(e==(s<0?0:si(t))){if("DIV"==t.nodeName)return!1;let i=t.parentNode;if(!i||1!=i.nodeType)return!1;e=ii(t)+(s<0?0:1),t=i}else{if(1!=t.nodeType)return!1;if(1==(t=t.childNodes[e+(s<0?-1:0)]).nodeType&&"false"==t.contentEditable)return!1;e=s<0?si(t):0}}}function si(t){return 3==t.nodeType?t.nodeValue.length:t.childNodes.length}function ri(t,e){let i=e?t.left:t.right;return{left:i,right:i,top:t.top,bottom:t.bottom}}function oi(t){return{left:0,right:t.innerWidth,top:0,bottom:t.innerHeight}}function ai(t,e){let i=e.width/t.offsetWidth,n=e.height/t.offsetHeight;return(i>.995&&i<1.005||!isFinite(i)||Math.abs(e.width-t.offsetWidth)<1)&&(i=1),(n>.995&&n<1.005||!isFinite(n)||Math.abs(e.height-t.offsetHeight)<1)&&(n=1),{scaleX:i,scaleY:n}}class li{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}eq(t){return this.anchorNode==t.anchorNode&&this.anchorOffset==t.anchorOffset&&this.focusNode==t.focusNode&&this.focusOffset==t.focusOffset}setRange(t){let{anchorNode:e,focusNode:i}=t;this.set(e,Math.min(t.anchorOffset,e?si(e):0),i,Math.min(t.focusOffset,i?si(i):0))}set(t,e,i,n){this.anchorNode=t,this.anchorOffset=e,this.focusNode=i,this.focusOffset=n}}let hi,ci=null;function Oi(t){if(t.setActive)return t.setActive();if(ci)return t.focus(ci);let e=[];for(let i=t;i&&(e.push(i,i.scrollTop,i.scrollLeft),i!=i.ownerDocument);i=i.parentNode);if(t.focus(null==ci?{get preventScroll(){return ci={preventScroll:!0},!0}}:void 0),!ci){ci=!1;for(let t=0;t<e.length;){let i=e[t++],n=e[t++],s=e[t++];i.scrollTop!=n&&(i.scrollTop=n),i.scrollLeft!=s&&(i.scrollLeft=s)}}}function ui(t,e,i=e){let n=hi||(hi=document.createRange());return n.setEnd(t,i),n.setStart(t,e),n}function fi(t,e,i){let n={key:e,code:e,keyCode:i,which:i,cancelable:!0},s=new KeyboardEvent("keydown",n);s.synthetic=!0,t.dispatchEvent(s);let r=new KeyboardEvent("keyup",n);return r.synthetic=!0,t.dispatchEvent(r),s.defaultPrevented||r.defaultPrevented}function di(t){for(;t.attributes.length;)t.removeAttributeNode(t.attributes[0])}function pi(t){return t.scrollTop>Math.max(1,t.scrollHeight-t.clientHeight-4)}class mi{constructor(t,e,i=!0){this.node=t,this.offset=e,this.precise=i}static before(t,e){return new mi(t.parentNode,ii(t),e)}static after(t,e){return new mi(t.parentNode,ii(t)+1,e)}}const gi=[];class bi{constructor(){this.parent=null,this.dom=null,this.flags=2}get overrideDOMText(){return null}get posAtStart(){return this.parent?this.parent.posBefore(this):0}get posAtEnd(){return this.posAtStart+this.length}posBefore(t){let e=this.posAtStart;for(let i of this.children){if(i==t)return e;e+=i.length+i.breakAfter}throw new RangeError("Invalid child in posBefore")}posAfter(t){return this.posBefore(t)+t.length}sync(t,e){if(2&this.flags){let i,n=this.dom,s=null;for(let r of this.children){if(7&r.flags){if(!r.dom&&(i=s?s.nextSibling:n.firstChild)){let t=bi.get(i);(!t||!t.parent&&t.canReuseDOM(r))&&r.reuseDOM(i)}r.sync(t,e),r.flags&=-8}if(i=s?s.nextSibling:n.firstChild,e&&!e.written&&e.node==n&&i!=r.dom&&(e.written=!0),r.dom.parentNode==n)for(;i&&i!=r.dom;)i=Qi(i);else n.insertBefore(r.dom,i);s=r.dom}for(i=s?s.nextSibling:n.firstChild,i&&e&&e.node==n&&(e.written=!0);i;)i=Qi(i)}else if(1&this.flags)for(let i of this.children)7&i.flags&&(i.sync(t,e),i.flags&=-8)}reuseDOM(t){}localPosFromDOM(t,e){let i;if(t==this.dom)i=this.dom.childNodes[e];else{let n=0==si(t)?0:0==e?-1:1;for(;;){let e=t.parentNode;if(e==this.dom)break;0==n&&e.firstChild!=e.lastChild&&(n=t==e.firstChild?-1:1),t=e}i=n<0?t:t.nextSibling}if(i==this.dom.firstChild)return 0;for(;i&&!bi.get(i);)i=i.nextSibling;if(!i)return this.length;for(let t=0,e=0;;t++){let n=this.children[t];if(n.dom==i)return e;e+=n.length+n.breakAfter}}domBoundsAround(t,e,i=0){let n=-1,s=-1,r=-1,o=-1;for(let a=0,l=i,h=i;a<this.children.length;a++){let i=this.children[a],c=l+i.length;if(l<t&&c>e)return i.domBoundsAround(t,e,l);if(c>=t&&-1==n&&(n=a,s=l),l>e&&i.dom.parentNode==this.dom){r=a,o=h;break}h=c,l=c+i.breakAfter}return{from:s,to:o<0?i+this.length:o,startDOM:(n?this.children[n-1].dom.nextSibling:null)||this.dom.firstChild,endDOM:r<this.children.length&&r>=0?this.children[r].dom:null}}markDirty(t=!1){this.flags|=2,this.markParentsDirty(t)}markParentsDirty(t){for(let e=this.parent;e;e=e.parent){if(t&&(e.flags|=2),1&e.flags)return;e.flags|=1,t=!1}}setParent(t){this.parent!=t&&(this.parent=t,7&this.flags&&this.markParentsDirty(!0))}setDOM(t){this.dom!=t&&(this.dom&&(this.dom.cmView=null),this.dom=t,t.cmView=this)}get rootView(){for(let t=this;;){let e=t.parent;if(!e)return t;t=e}}replaceChildren(t,e,i=gi){this.markDirty();for(let n=t;n<e;n++){let t=this.children[n];t.parent==this&&i.indexOf(t)<0&&t.destroy()}this.children.splice(t,e-t,...i);for(let t=0;t<i.length;t++)i[t].setParent(this)}ignoreMutation(t){return!1}ignoreEvent(t){return!1}childCursor(t=this.length){return new Si(this.children,t,this.children.length)}childPos(t,e=1){return this.childCursor().findPos(t,e)}toString(){let t=this.constructor.name.replace("View","");return t+(this.children.length?"("+this.children.join()+")":this.length?"["+("Text"==t?this.text:this.length)+"]":"")+(this.breakAfter?"#":"")}static get(t){return t.cmView}get isEditable(){return!0}get isWidget(){return!1}get isHidden(){return!1}merge(t,e,i,n,s,r){return!1}become(t){return!1}canReuseDOM(t){return t.constructor==this.constructor&&!(8&(this.flags|t.flags))}getSide(){return 0}destroy(){for(let t of this.children)t.parent==this&&t.destroy();this.parent=null}}function Qi(t){let e=t.nextSibling;return t.parentNode.removeChild(t),e}bi.prototype.breakAfter=0;class Si{constructor(t,e,i){this.children=t,this.pos=e,this.i=i,this.off=0}findPos(t,e=1){for(;;){if(t>this.pos||t==this.pos&&(e>0||0==this.i||this.children[this.i-1].breakAfter))return this.off=t-this.pos,this;let i=this.children[--this.i];this.pos-=i.length+i.breakAfter}}}function vi(t,e,i,n,s,r,o,a,l){let{children:h}=t,c=h.length?h[e]:null,O=r.length?r[r.length-1]:null,u=O?O.breakAfter:o;if(!(e==n&&c&&!o&&!u&&r.length<2&&c.merge(i,s,r.length?O:null,0==i,a,l))){if(n<h.length){let t=h[n];t&&(s<t.length||t.breakAfter&&(null==O?void 0:O.breakAfter))?(e==n&&(t=t.split(s),s=0),!u&&O&&t.merge(0,s,O,!0,0,l)?r[r.length-1]=t:((s||t.children.length&&!t.children[0].length)&&t.merge(0,s,null,!1,0,l),r.push(t))):(null==t?void 0:t.breakAfter)&&(O?O.breakAfter=1:o=1),n++}for(c&&(c.breakAfter=o,i>0&&(!o&&r.length&&c.merge(i,c.length,r[0],!1,a,0)?c.breakAfter=r.shift().breakAfter:(i<c.length||c.children.length&&0==c.children[c.children.length-1].length)&&c.merge(i,c.length,null,!1,a,0),e++));e<n&&r.length;)if(h[n-1].become(r[r.length-1]))n--,r.pop(),l=r.length?0:a;else{if(!h[e].become(r[0]))break;e++,r.shift(),a=r.length?0:l}!r.length&&e&&n<h.length&&!h[e-1].breakAfter&&h[n].merge(0,0,h[e-1],!1,a,l)&&e--,(e<n||r.length)&&t.replaceChildren(e,n,r)}}function wi(t,e,i,n,s,r){let o=t.childCursor(),{i:a,off:l}=o.findPos(i,1),{i:h,off:c}=o.findPos(e,-1),O=e-i;for(let t of n)O+=t.length;t.length+=O,vi(t,h,c,a,l,n,0,s,r)}let yi="undefined"!=typeof navigator?navigator:{userAgent:"",vendor:"",platform:""},xi="undefined"!=typeof document?document:{documentElement:{style:{}}};const ki=/Edge\/(\d+)/.exec(yi.userAgent),Pi=/MSIE \d/.test(yi.userAgent),$i=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(yi.userAgent),Zi=!!(Pi||$i||ki),Ti=!Zi&&/gecko\/(\d+)/i.test(yi.userAgent),Xi=!Zi&&/Chrome\/(\d+)/.exec(yi.userAgent),Ri="webkitFontSmoothing"in xi.documentElement.style,Ai=!Zi&&/Apple Computer/.test(yi.vendor),Ci=Ai&&(/Mobile\/\w+/.test(yi.userAgent)||yi.maxTouchPoints>2);var Mi={mac:Ci||/Mac/.test(yi.platform),windows:/Win/.test(yi.platform),linux:/Linux|X11/.test(yi.platform),ie:Zi,ie_version:Pi?xi.documentMode||6:$i?+$i[1]:ki?+ki[1]:0,gecko:Ti,gecko_version:Ti?+(/Firefox\/(\d+)/.exec(yi.userAgent)||[0,0])[1]:0,chrome:!!Xi,chrome_version:Xi?+Xi[1]:0,ios:Ci,android:/Android\b/.test(yi.userAgent),webkit:Ri,safari:Ai,webkit_version:Ri?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0,tabSize:null!=xi.documentElement.style.tabSize?"tab-size":"-moz-tab-size"};class qi extends bi{constructor(t){super(),this.text=t}get length(){return this.text.length}createDOM(t){this.setDOM(t||document.createTextNode(this.text))}sync(t,e){this.dom||this.createDOM(),this.dom.nodeValue!=this.text&&(e&&e.node==this.dom&&(e.written=!0),this.dom.nodeValue=this.text)}reuseDOM(t){3==t.nodeType&&this.createDOM(t)}merge(t,e,i){return!(8&this.flags||i&&(!(i instanceof qi)||this.length-(e-t)+i.length>256||8&i.flags))&&(this.text=this.text.slice(0,t)+(i?i.text:"")+this.text.slice(e),this.markDirty(),!0)}split(t){let e=new qi(this.text.slice(t));return this.text=this.text.slice(0,t),this.markDirty(),e.flags|=8&this.flags,e}localPosFromDOM(t,e){return t==this.dom?e:e?this.text.length:0}domAtPos(t){return new mi(this.dom,t)}domBoundsAround(t,e,i){return{from:i,to:i+this.length,startDOM:this.dom,endDOM:this.dom.nextSibling}}coordsAt(t,e){return function(t,e,i){let n=t.nodeValue.length;e>n&&(e=n);let s=e,r=e,o=0;0==e&&i<0||e==n&&i>=0?Mi.chrome||Mi.gecko||(e?(s--,o=1):r<n&&(r++,o=-1)):i<0?s--:r<n&&r++;let a=ui(t,s,r).getClientRects();if(!a.length)return null;let l=a[(o?o<0:i>=0)?0:a.length-1];Mi.safari&&!o&&0==l.width&&(l=Array.prototype.find.call(a,(t=>t.width))||l);return o?ri(l,o<0):l||null}(this.dom,t,e)}}class Vi extends bi{constructor(t,e=[],i=0){super(),this.mark=t,this.children=e,this.length=i;for(let t of e)t.setParent(this)}setAttrs(t){if(di(t),this.mark.class&&(t.className=this.mark.class),this.mark.attrs)for(let e in this.mark.attrs)t.setAttribute(e,this.mark.attrs[e]);return t}canReuseDOM(t){return super.canReuseDOM(t)&&!(8&(this.flags|t.flags))}reuseDOM(t){t.nodeName==this.mark.tagName.toUpperCase()&&(this.setDOM(t),this.flags|=6)}sync(t,e){this.dom?4&this.flags&&this.setAttrs(this.dom):this.setDOM(this.setAttrs(document.createElement(this.mark.tagName))),super.sync(t,e)}merge(t,e,i,n,s,r){return(!i||!(!(i instanceof Vi&&i.mark.eq(this.mark))||t&&s<=0||e<this.length&&r<=0))&&(wi(this,t,e,i?i.children.slice():[],s-1,r-1),this.markDirty(),!0)}split(t){let e=[],i=0,n=-1,s=0;for(let r of this.children){let o=i+r.length;o>t&&e.push(i<t?r.split(t-i):r),n<0&&i>=t&&(n=s),i=o,s++}let r=this.length-t;return this.length=t,n>-1&&(this.children.length=n,this.markDirty()),new Vi(this.mark,e,r)}domAtPos(t){return _i(this,t)}coordsAt(t,e){return Ei(this,t,e)}}class Yi extends bi{static create(t,e,i){return new Yi(t,e,i)}constructor(t,e,i){super(),this.widget=t,this.length=e,this.side=i,this.prevWidget=null}split(t){let e=Yi.create(this.widget,this.length-t,this.side);return this.length-=t,e}sync(t){this.dom&&this.widget.updateDOM(this.dom,t)||(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(t)),this.widget.editable||(this.dom.contentEditable="false"))}getSide(){return this.side}merge(t,e,i,n,s,r){return!(i&&(!(i instanceof Yi&&this.widget.compare(i.widget))||t>0&&s<=0||e<this.length&&r<=0))&&(this.length=t+(i?i.length:0)+(this.length-e),!0)}become(t){return t instanceof Yi&&t.side==this.side&&this.widget.constructor==t.widget.constructor&&(this.widget.compare(t.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=t.widget,this.length=t.length,!0)}ignoreMutation(){return!0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get overrideDOMText(){if(0==this.length)return st.empty;let t=this;for(;t.parent;)t=t.parent;let{view:e}=t,i=e&&e.state.doc,n=this.posAtStart;return i?i.slice(n,n+this.length):st.empty}domAtPos(t){return(this.length?0==t:this.side>0)?mi.before(this.dom):mi.after(this.dom,t==this.length)}domBoundsAround(){return null}coordsAt(t,e){let i=this.widget.coordsAt(this.dom,t,e);if(i)return i;let n=this.dom.getClientRects(),s=null;if(!n.length)return null;let r=this.side?this.side<0:t>0;for(let e=r?n.length-1:0;s=n[e],!(t>0?0==e:e==n.length-1||s.top<s.bottom);e+=r?-1:1);return ri(s,!r)}get isEditable(){return!1}get isWidget(){return!0}get isHidden(){return this.widget.isHidden}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}}class zi extends bi{constructor(t){super(),this.side=t}get length(){return 0}merge(){return!1}become(t){return t instanceof zi&&t.side==this.side}split(){return new zi(this.side)}sync(){if(!this.dom){let t=document.createElement("img");t.className="cm-widgetBuffer",t.setAttribute("aria-hidden","true"),this.setDOM(t)}}getSide(){return this.side}domAtPos(t){return this.side>0?mi.before(this.dom):mi.after(this.dom)}localPosFromDOM(){return 0}domBoundsAround(){return null}coordsAt(t){return this.dom.getBoundingClientRect()}get overrideDOMText(){return st.empty}get isHidden(){return!0}}function _i(t,e){let i=t.dom,{children:n}=t,s=0;for(let t=0;s<n.length;s++){let r=n[s],o=t+r.length;if(!(o==t&&r.getSide()<=0)){if(e>t&&e<o&&r.dom.parentNode==i)return r.domAtPos(e-t);if(e<=t)break;t=o}}for(let t=s;t>0;t--){let e=n[t-1];if(e.dom.parentNode==i)return e.domAtPos(e.length)}for(let t=s;t<n.length;t++){let e=n[t];if(e.dom.parentNode==i)return e.domAtPos(0)}return new mi(i,0)}function ji(t,e,i){let n,{children:s}=t;i>0&&e instanceof Vi&&s.length&&(n=s[s.length-1])instanceof Vi&&n.mark.eq(e.mark)?ji(n,e.children[0],i-1):(s.push(e),e.setParent(t)),t.length+=e.length}function Ei(t,e,i){let n=null,s=-1,r=null,o=-1;!function t(e,a){for(let l=0,h=0;l<e.children.length&&h<=a;l++){let c=e.children[l],O=h+c.length;O>=a&&(c.children.length?t(c,a-h):(!r||r.isHidden&&i>0)&&(O>a||h==O&&c.getSide()>0)?(r=c,o=a-h):(h<a||h==O&&c.getSide()<0&&!c.isHidden)&&(n=c,s=a-h)),h=O}}(t,e);let a=(i<0?n:r)||n||r;return a?a.coordsAt(Math.max(0,a==n?s:o),i):function(t){let e=t.dom.lastChild;if(!e)return t.dom.getBoundingClientRect();let i=ti(e);return i[i.length-1]||null}(t)}function Di(t,e){for(let i in t)"class"==i&&e.class?e.class+=" "+t.class:"style"==i&&e.style?e.style+=";"+t.style:e[i]=t[i];return e}qi.prototype.children=Yi.prototype.children=zi.prototype.children=gi;const Wi=Object.create(null);function Li(t,e,i){if(t==e)return!0;t||(t=Wi),e||(e=Wi);let n=Object.keys(t),s=Object.keys(e);if(n.length-(i&&n.indexOf(i)>-1?1:0)!=s.length-(i&&s.indexOf(i)>-1?1:0))return!1;for(let r of n)if(r!=i&&(-1==s.indexOf(r)||t[r]!==e[r]))return!1;return!0}function Gi(t,e,i){let n=!1;if(e)for(let s in e)i&&s in i||(n=!0,"style"==s?t.style.cssText="":t.removeAttribute(s));if(i)for(let s in i)e&&e[s]==i[s]||(n=!0,"style"==s?t.style.cssText=i[s]:t.setAttribute(s,i[s]));return n}function Bi(t){let e=Object.create(null);for(let i=0;i<t.attributes.length;i++){let n=t.attributes[i];e[n.name]=n.value}return e}class Ui extends bi{constructor(){super(...arguments),this.children=[],this.length=0,this.prevAttrs=void 0,this.attrs=null,this.breakAfter=0}merge(t,e,i,n,s,r){if(i){if(!(i instanceof Ui))return!1;this.dom||i.transferDOM(this)}return n&&this.setDeco(i?i.attrs:null),wi(this,t,e,i?i.children.slice():[],s,r),!0}split(t){let e=new Ui;if(e.breakAfter=this.breakAfter,0==this.length)return e;let{i:i,off:n}=this.childPos(t);n&&(e.append(this.children[i].split(n),0),this.children[i].merge(n,this.children[i].length,null,!1,0,0),i++);for(let t=i;t<this.children.length;t++)e.append(this.children[t],0);for(;i>0&&0==this.children[i-1].length;)this.children[--i].destroy();return this.children.length=i,this.markDirty(),this.length=t,e}transferDOM(t){this.dom&&(this.markDirty(),t.setDOM(this.dom),t.prevAttrs=void 0===this.prevAttrs?this.attrs:this.prevAttrs,this.prevAttrs=void 0,this.dom=null)}setDeco(t){Li(this.attrs,t)||(this.dom&&(this.prevAttrs=this.attrs,this.markDirty()),this.attrs=t)}append(t,e){ji(this,t,e)}addLineDeco(t){let e=t.spec.attributes,i=t.spec.class;e&&(this.attrs=Di(e,this.attrs||{})),i&&(this.attrs=Di({class:i},this.attrs||{}))}domAtPos(t){return _i(this,t)}reuseDOM(t){"DIV"==t.nodeName&&(this.setDOM(t),this.flags|=6)}sync(t,e){var i;this.dom?4&this.flags&&(di(this.dom),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0):(this.setDOM(document.createElement("div")),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0),void 0!==this.prevAttrs&&(Gi(this.dom,this.prevAttrs,this.attrs),this.dom.classList.add("cm-line"),this.prevAttrs=void 0),super.sync(t,e);let n=this.dom.lastChild;for(;n&&bi.get(n)instanceof Vi;)n=n.lastChild;if(!(n&&this.length&&("BR"==n.nodeName||0!=(null===(i=bi.get(n))||void 0===i?void 0:i.isEditable)||Mi.ios&&this.children.some((t=>t instanceof qi))))){let t=document.createElement("BR");t.cmIgnore=!0,this.dom.appendChild(t)}}measureTextSize(){if(0==this.children.length||this.length>20)return null;let t,e=0;for(let i of this.children){if(!(i instanceof qi)||/[^ -~]/.test(i.text))return null;let n=ti(i.dom);if(1!=n.length)return null;e+=n[0].width,t=n[0].height}return e?{lineHeight:this.dom.getBoundingClientRect().height,charWidth:e/this.length,textHeight:t}:null}coordsAt(t,e){let i=Ei(this,t,e);if(!this.children.length&&i&&this.parent){let{heightOracle:t}=this.parent.view.viewState,e=i.bottom-i.top;if(Math.abs(e-t.lineHeight)<2&&t.textHeight<e){let n=(e-t.textHeight)/2;return{top:i.top+n,bottom:i.bottom-n,left:i.left,right:i.left}}}return i}become(t){return!1}covers(){return!0}static find(t,e){for(let i=0,n=0;i<t.children.length;i++){let s=t.children[i],r=n+s.length;if(r>=e){if(s instanceof Ui)return s;if(r>e)break}n=r+s.breakAfter}return null}}class Ii extends bi{constructor(t,e,i){super(),this.widget=t,this.length=e,this.deco=i,this.breakAfter=0,this.prevWidget=null}merge(t,e,i,n,s,r){return!(i&&(!(i instanceof Ii&&this.widget.compare(i.widget))||t>0&&s<=0||e<this.length&&r<=0))&&(this.length=t+(i?i.length:0)+(this.length-e),!0)}domAtPos(t){return 0==t?mi.before(this.dom):mi.after(this.dom,t==this.length)}split(t){let e=this.length-t;this.length=t;let i=new Ii(this.widget,e,this.deco);return i.breakAfter=this.breakAfter,i}get children(){return gi}sync(t){this.dom&&this.widget.updateDOM(this.dom,t)||(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(t)),this.widget.editable||(this.dom.contentEditable="false"))}get overrideDOMText(){return this.parent?this.parent.view.state.doc.slice(this.posAtStart,this.posAtEnd):st.empty}domBoundsAround(){return null}become(t){return t instanceof Ii&&t.widget.constructor==this.widget.constructor&&(t.widget.compare(this.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=t.widget,this.length=t.length,this.deco=t.deco,this.breakAfter=t.breakAfter,!0)}ignoreMutation(){return!0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get isEditable(){return!1}get isWidget(){return!0}coordsAt(t,e){return this.widget.coordsAt(this.dom,t,e)}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}covers(t){let{startSide:e,endSide:i}=this.deco;return e!=i&&(t<0?e<0:i>0)}}class Ni{eq(t){return!1}updateDOM(t,e){return!1}compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}get estimatedHeight(){return-1}get lineBreaks(){return 0}ignoreEvent(t){return!0}coordsAt(t,e,i){return null}get isHidden(){return!1}get editable(){return!1}destroy(t){}}var Hi=function(t){return t[t.Text=0]="Text",t[t.WidgetBefore=1]="WidgetBefore",t[t.WidgetAfter=2]="WidgetAfter",t[t.WidgetRange=3]="WidgetRange",t}(Hi||(Hi={}));class Fi extends we{constructor(t,e,i,n){super(),this.startSide=t,this.endSide=e,this.widget=i,this.spec=n}get heightRelevant(){return!1}static mark(t){return new Ki(t)}static widget(t){let e=Math.max(-1e4,Math.min(1e4,t.side||0)),i=!!t.block;return e+=i&&!t.inlineOrder?e>0?3e8:-4e8:e>0?1e8:-1e8,new tn(t,e,e,i,t.widget||null,!1)}static replace(t){let e,i,n=!!t.block;if(t.isBlockGap)e=-5e8,i=4e8;else{let{start:s,end:r}=en(t,n);e=(s?n?-3e8:-1:5e8)-1,i=1+(r?n?2e8:1:-6e8)}return new tn(t,e,i,n,t.widget||null,!0)}static line(t){return new Ji(t)}static set(t,e=!1){return Pe.of(t,e)}hasHeight(){return!!this.widget&&this.widget.estimatedHeight>-1}}Fi.none=Pe.empty;class Ki extends Fi{constructor(t){let{start:e,end:i}=en(t);super(e?-1:5e8,i?1:-6e8,null,t),this.tagName=t.tagName||"span",this.class=t.class||"",this.attrs=t.attributes||null}eq(t){var e,i;return this==t||t instanceof Ki&&this.tagName==t.tagName&&(this.class||(null===(e=this.attrs)||void 0===e?void 0:e.class))==(t.class||(null===(i=t.attrs)||void 0===i?void 0:i.class))&&Li(this.attrs,t.attrs,"class")}range(t,e=t){if(t>=e)throw new RangeError("Mark decorations may not be empty");return super.range(t,e)}}Ki.prototype.point=!1;class Ji extends Fi{constructor(t){super(-2e8,-2e8,null,t)}eq(t){return t instanceof Ji&&this.spec.class==t.spec.class&&Li(this.spec.attributes,t.spec.attributes)}range(t,e=t){if(e!=t)throw new RangeError("Line decoration ranges must be zero-length");return super.range(t,e)}}Ji.prototype.mapMode=Qt.TrackBefore,Ji.prototype.point=!0;class tn extends Fi{constructor(t,e,i,n,s,r){super(e,i,s,t),this.block=n,this.isReplace=r,this.mapMode=n?e<=0?Qt.TrackBefore:Qt.TrackAfter:Qt.TrackDel}get type(){return this.startSide!=this.endSide?Hi.WidgetRange:this.startSide<=0?Hi.WidgetBefore:Hi.WidgetAfter}get heightRelevant(){return this.block||!!this.widget&&(this.widget.estimatedHeight>=5||this.widget.lineBreaks>0)}eq(t){return t instanceof tn&&(e=this.widget,i=t.widget,e==i||!!(e&&i&&e.compare(i)))&&this.block==t.block&&this.startSide==t.startSide&&this.endSide==t.endSide;var e,i}range(t,e=t){if(this.isReplace&&(t>e||t==e&&this.startSide>0&&this.endSide<=0))throw new RangeError("Invalid range for replacement decoration");if(!this.isReplace&&e!=t)throw new RangeError("Widget decorations can only have zero-length ranges");return super.range(t,e)}}function en(t,e=!1){let{inclusiveStart:i,inclusiveEnd:n}=t;return null==i&&(i=t.inclusive),null==n&&(n=t.inclusive),{start:null!=i?i:e,end:null!=n?n:e}}function nn(t,e,i,n=0){let s=i.length-1;s>=0&&i[s]+n>=t?i[s]=Math.max(i[s],e):i.push(t,e)}tn.prototype.point=!0;class sn{constructor(t,e,i,n){this.doc=t,this.pos=e,this.end=i,this.disallowBlockEffectsFor=n,this.content=[],this.curLine=null,this.breakAtStart=0,this.pendingBuffer=0,this.bufferMarks=[],this.atCursorPos=!0,this.openStart=-1,this.openEnd=-1,this.text="",this.textOff=0,this.cursor=t.iter(),this.skip=e}posCovered(){if(0==this.content.length)return!this.breakAtStart&&this.doc.lineAt(this.pos).from!=this.pos;let t=this.content[this.content.length-1];return!(t.breakAfter||t instanceof Ii&&t.deco.endSide<0)}getLine(){return this.curLine||(this.content.push(this.curLine=new Ui),this.atCursorPos=!0),this.curLine}flushBuffer(t=this.bufferMarks){this.pendingBuffer&&(this.curLine.append(rn(new zi(-1),t),t.length),this.pendingBuffer=0)}addBlockWidget(t){this.flushBuffer(),this.curLine=null,this.content.push(t)}finish(t){this.pendingBuffer&&t<=this.bufferMarks.length?this.flushBuffer():this.pendingBuffer=0,this.posCovered()||t&&this.content.length&&this.content[this.content.length-1]instanceof Ii||this.getLine()}buildText(t,e,i){for(;t>0;){if(this.textOff==this.text.length){let{value:e,lineBreak:i,done:n}=this.cursor.next(this.skip);if(this.skip=0,n)throw new Error("Ran out of text content when drawing inline views");if(i){this.posCovered()||this.getLine(),this.content.length?this.content[this.content.length-1].breakAfter=1:this.breakAtStart=1,this.flushBuffer(),this.curLine=null,this.atCursorPos=!0,t--;continue}this.text=e,this.textOff=0}let n=Math.min(this.text.length-this.textOff,t,512);this.flushBuffer(e.slice(e.length-i)),this.getLine().append(rn(new qi(this.text.slice(this.textOff,this.textOff+n)),e),i),this.atCursorPos=!0,this.textOff+=n,t-=n,i=0}}span(t,e,i,n){this.buildText(e-t,i,n),this.pos=e,this.openStart<0&&(this.openStart=n)}point(t,e,i,n,s,r){if(this.disallowBlockEffectsFor[r]&&i instanceof tn){if(i.block)throw new RangeError("Block decorations may not be specified via plugins");if(e>this.doc.lineAt(this.pos).to)throw new RangeError("Decorations that replace line breaks may not be specified via plugins")}let o=e-t;if(i instanceof tn)if(i.block)i.startSide>0&&!this.posCovered()&&this.getLine(),this.addBlockWidget(new Ii(i.widget||new on("div"),o,i));else{let r=Yi.create(i.widget||new on("span"),o,o?0:i.startSide),a=this.atCursorPos&&!r.isEditable&&s<=n.length&&(t<e||i.startSide>0),l=!r.isEditable&&(t<e||s>n.length||i.startSide<=0),h=this.getLine();2!=this.pendingBuffer||a||r.isEditable||(this.pendingBuffer=0),this.flushBuffer(n),a&&(h.append(rn(new zi(1),n),s),s=n.length+Math.max(0,s-n.length)),h.append(rn(r,n),s),this.atCursorPos=l,this.pendingBuffer=l?t<e||s>n.length?1:2:0,this.pendingBuffer&&(this.bufferMarks=n.slice())}else this.doc.lineAt(this.pos).from==this.pos&&this.getLine().addLineDeco(i);o&&(this.textOff+o<=this.text.length?this.textOff+=o:(this.skip+=o-(this.text.length-this.textOff),this.text="",this.textOff=0),this.pos=e),this.openStart<0&&(this.openStart=s)}static build(t,e,i,n,s){let r=new sn(t,e,i,s);return r.openEnd=Pe.spans(n,e,i,r),r.openStart<0&&(r.openStart=r.openEnd),r.finish(r.openEnd),r}}function rn(t,e){for(let i of e)t=new Vi(i,[t],t.length);return t}class on extends Ni{constructor(t){super(),this.tag=t}eq(t){return t.tag==this.tag}toDOM(){return document.createElement(this.tag)}updateDOM(t){return t.nodeName.toLowerCase()==this.tag}get isHidden(){return!0}}var an=function(t){return t[t.LTR=0]="LTR",t[t.RTL=1]="RTL",t}(an||(an={}));const ln=an.LTR,hn=an.RTL;function cn(t){let e=[];for(let i=0;i<t.length;i++)e.push(1<<+t[i]);return e}const On=cn("88888888888888888888888888888888888666888888787833333333337888888000000000000000000000000008888880000000000000000000000000088888888888888888888888888888888888887866668888088888663380888308888800000000000000000000000800000000000000000000000000000008"),un=cn("4444448826627288999999999992222222222222222222222222222222222222222222222229999999999999999999994444444444644222822222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222999999949999999229989999223333333333"),fn=Object.create(null),dn=[];for(let t of["()","[]","{}"]){let e=t.charCodeAt(0),i=t.charCodeAt(1);fn[e]=i,fn[i]=-e}function pn(t){return t<=247?On[t]:1424<=t&&t<=1524?2:1536<=t&&t<=1785?un[t-1536]:1774<=t&&t<=2220?4:8192<=t&&t<=8204?256:64336<=t&&t<=65023?4:1}const mn=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac\ufb50-\ufdff]/;class gn{get dir(){return this.level%2?hn:ln}constructor(t,e,i){this.from=t,this.to=e,this.level=i}side(t,e){return this.dir==e==t?this.to:this.from}forward(t,e){return t==(this.dir==e)}static find(t,e,i,n){let s=-1;for(let r=0;r<t.length;r++){let o=t[r];if(o.from<=e&&o.to>=e){if(o.level==i)return r;(s<0||(0!=n?n<0?o.from<e:o.to>e:t[s].level>o.level))&&(s=r)}}if(s<0)throw new RangeError("Index out of range");return s}}function bn(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++){let n=t[i],s=e[i];if(n.from!=s.from||n.to!=s.to||n.direction!=s.direction||!bn(n.inner,s.inner))return!1}return!0}const Qn=[];function Sn(t,e,i,n,s,r,o){let a=n%2?2:1;if(n%2==s%2)for(let l=e,h=0;l<i;){let e=!0,c=!1;if(h==r.length||l<r[h].from){let t=Qn[l];t!=a&&(e=!1,c=16==t)}let O=e||1!=a?null:[],u=e?n:n+1,f=l;t:for(;;)if(h<r.length&&f==r[h].from){if(c)break t;let d=r[h];if(!e)for(let t=d.to,e=h+1;;){if(t==i)break t;if(!(e<r.length&&r[e].from==t)){if(Qn[t]==a)break t;break}t=r[e++].to}if(h++,O)O.push(d);else{d.from>l&&o.push(new gn(l,d.from,u)),vn(t,d.direction==ln!=!(u%2)?n+1:n,s,d.inner,d.from,d.to,o),l=d.to}f=d.to}else{if(f==i||(e?Qn[f]!=a:Qn[f]==a))break;f++}O?Sn(t,l,f,n+1,s,O,o):l<f&&o.push(new gn(l,f,u)),l=f}else for(let l=i,h=r.length;l>e;){let i=!0,c=!1;if(!h||l>r[h-1].to){let t=Qn[l-1];t!=a&&(i=!1,c=16==t)}let O=i||1!=a?null:[],u=i?n:n+1,f=l;t:for(;;)if(h&&f==r[h-1].to){if(c)break t;let d=r[--h];if(!i)for(let t=d.from,i=h;;){if(t==e)break t;if(!i||r[i-1].to!=t){if(Qn[t-1]==a)break t;break}t=r[--i].from}if(O)O.push(d);else{d.to<l&&o.push(new gn(d.to,l,u)),vn(t,d.direction==ln!=!(u%2)?n+1:n,s,d.inner,d.from,d.to,o),l=d.from}f=d.from}else{if(f==e||(i?Qn[f-1]!=a:Qn[f-1]==a))break;f--}O?Sn(t,f,l,n+1,s,O,o):f<l&&o.push(new gn(f,l,u)),l=f}}function vn(t,e,i,n,s,r,o){let a=e%2?2:1;!function(t,e,i,n,s){for(let r=0;r<=n.length;r++){let o=r?n[r-1].to:e,a=r<n.length?n[r].from:i,l=r?256:s;for(let e=o,i=l,n=l;e<a;e++){let s=pn(t.charCodeAt(e));512==s?s=i:8==s&&4==n&&(s=16),Qn[e]=4==s?2:s,7&s&&(n=s),i=s}for(let t=o,e=l,n=l;t<a;t++){let s=Qn[t];if(128==s)t<a-1&&e==Qn[t+1]&&24&e?s=Qn[t]=e:Qn[t]=256;else if(64==s){let s=t+1;for(;s<a&&64==Qn[s];)s++;let r=t&&8==e||s<i&&8==Qn[s]?1==n?1:8:256;for(let e=t;e<s;e++)Qn[e]=r;t=s-1}else 8==s&&1==n&&(Qn[t]=1);e=s,7&s&&(n=s)}}}(t,s,r,n,a),function(t,e,i,n,s){let r=1==s?2:1;for(let o=0,a=0,l=0;o<=n.length;o++){let h=o?n[o-1].to:e,c=o<n.length?n[o].from:i;for(let e,i,n,o=h;o<c;o++)if(i=fn[e=t.charCodeAt(o)])if(i<0){for(let t=a-3;t>=0;t-=3)if(dn[t+1]==-i){let e=dn[t+2],i=2&e?s:4&e?1&e?r:s:0;i&&(Qn[o]=Qn[dn[t]]=i),a=t;break}}else{if(189==dn.length)break;dn[a++]=o,dn[a++]=e,dn[a++]=l}else if(2==(n=Qn[o])||1==n){let t=n==s;l=t?0:1;for(let e=a-3;e>=0;e-=3){let i=dn[e+2];if(2&i)break;if(t)dn[e+2]|=2;else{if(4&i)break;dn[e+2]|=4}}}}}(t,s,r,n,a),function(t,e,i,n){for(let s=0,r=n;s<=i.length;s++){let o=s?i[s-1].to:t,a=s<i.length?i[s].from:e;for(let l=o;l<a;){let o=Qn[l];if(256==o){let o=l+1;for(;;)if(o==a){if(s==i.length)break;o=i[s++].to,a=s<i.length?i[s].from:e}else{if(256!=Qn[o])break;o++}let h=1==r,c=h==(1==(o<e?Qn[o]:n))?h?1:2:n;for(let e=o,n=s,r=n?i[n-1].to:t;e>l;)e==r&&(e=i[--n].from,r=n?i[n-1].to:t),Qn[--e]=c;l=o}else r=o,l++}}}(s,r,n,a),Sn(t,s,r,e,i,n,o)}function wn(t){return[new gn(0,t,0)]}let yn="";function xn(t,e,i,n,s){var r;let o=n.head-t.from,a=gn.find(e,o,null!==(r=n.bidiLevel)&&void 0!==r?r:-1,n.assoc),l=e[a],h=l.side(s,i);if(o==h){let t=a+=s?1:-1;if(t<0||t>=e.length)return null;l=e[a=t],o=l.side(!s,i),h=l.side(s,i)}let c=dt(t.text,o,l.forward(s,i));(c<l.from||c>l.to)&&(c=h),yn=t.text.slice(Math.min(o,c),Math.max(o,c));let O=a==(s?e.length-1:0)?null:e[a+(s?1:-1)];return O&&c==h&&O.level+(s?0:1)<l.level?Tt.cursor(O.side(!s,i)+t.from,O.forward(s,i)?1:-1,O.level):Tt.cursor(c+t.from,l.forward(s,i)?-1:1,l.level)}function kn(t,e,i){for(let n=e;n<i;n++){let e=pn(t.charCodeAt(n));if(1==e)return ln;if(2==e||4==e)return hn}return ln}const Pn=At.define(),$n=At.define(),Zn=At.define(),Tn=At.define(),Xn=At.define(),Rn=At.define(),An=At.define(),Cn=At.define({combine:t=>t.some((t=>t))}),Mn=At.define({combine:t=>t.some((t=>t))});class qn{constructor(t,e="nearest",i="nearest",n=5,s=5,r=!1){this.range=t,this.y=e,this.x=i,this.yMargin=n,this.xMargin=s,this.isSnapshot=r}map(t){return t.empty?this:new qn(this.range.map(t),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}clip(t){return this.range.to<=t.doc.length?this:new qn(Tt.cursor(t.doc.length),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}}const Vn=le.define({map:(t,e)=>t.map(e)});function Yn(t,e,i){let n=t.facet(Tn);n.length?n[0](e):window.onerror?window.onerror(String(e),i,void 0,void 0,e):i?console.error(i+":",e):console.error(e)}const zn=At.define({combine:t=>!t.length||t[0]});let _n=0;const jn=At.define();class En{constructor(t,e,i,n,s){this.id=t,this.create=e,this.domEventHandlers=i,this.domEventObservers=n,this.extension=s(this)}static define(t,e){const{eventHandlers:i,eventObservers:n,provide:s,decorations:r}=e||{};return new En(_n++,t,i,n,(t=>{let e=[jn.of(t)];return r&&e.push(Gn.of((e=>{let i=e.plugin(t);return i?r(i):Fi.none}))),s&&e.push(s(t)),e}))}static fromClass(t,e){return En.define((e=>new t(e)),e)}}class Dn{constructor(t){this.spec=t,this.mustUpdate=null,this.value=null}update(t){if(this.value){if(this.mustUpdate){let t=this.mustUpdate;if(this.mustUpdate=null,this.value.update)try{this.value.update(t)}catch(e){if(Yn(t.state,e,"CodeMirror plugin crashed"),this.value.destroy)try{this.value.destroy()}catch(t){}this.deactivate()}}}else if(this.spec)try{this.value=this.spec.create(t)}catch(e){Yn(t.state,e,"CodeMirror plugin crashed"),this.deactivate()}return this}destroy(t){var e;if(null===(e=this.value)||void 0===e?void 0:e.destroy)try{this.value.destroy()}catch(e){Yn(t.state,e,"CodeMirror plugin crashed")}}deactivate(){this.spec=this.value=null}}const Wn=At.define(),Ln=At.define(),Gn=At.define(),Bn=At.define(),Un=At.define(),In=At.define();function Nn(t,e){let i=t.state.facet(In);if(!i.length)return i;let n=i.map((e=>e instanceof Function?e(t):e)),s=[];return Pe.spans(n,e.from,e.to,{point(){},span(t,i,n,r){let o=t-e.from,a=i-e.from,l=s;for(let t=n.length-1;t>=0;t--,r--){let i,s=n[t].spec.bidiIsolate;if(null==s&&(s=kn(e.text,o,a)),r>0&&l.length&&(i=l[l.length-1]).to==o&&i.direction==s)i.to=a,l=i.inner;else{let t={from:o,to:a,direction:s,inner:[]};l.push(t),l=t.inner}}}}),s}const Hn=At.define();function Fn(t){let e=0,i=0,n=0,s=0;for(let r of t.state.facet(Hn)){let o=r(t);o&&(null!=o.left&&(e=Math.max(e,o.left)),null!=o.right&&(i=Math.max(i,o.right)),null!=o.top&&(n=Math.max(n,o.top)),null!=o.bottom&&(s=Math.max(s,o.bottom)))}return{left:e,right:i,top:n,bottom:s}}const Kn=At.define();class Jn{constructor(t,e,i,n){this.fromA=t,this.toA=e,this.fromB=i,this.toB=n}join(t){return new Jn(Math.min(this.fromA,t.fromA),Math.max(this.toA,t.toA),Math.min(this.fromB,t.fromB),Math.max(this.toB,t.toB))}addToSet(t){let e=t.length,i=this;for(;e>0;e--){let n=t[e-1];if(!(n.fromA>i.toA)){if(n.toA<i.fromA)break;i=i.join(n),t.splice(e-1,1)}}return t.splice(e,0,i),t}static extendWithRanges(t,e){if(0==e.length)return t;let i=[];for(let n=0,s=0,r=0,o=0;;n++){let a=n==t.length?null:t[n],l=r-o,h=a?a.fromB:1e9;for(;s<e.length&&e[s]<h;){let t=e[s],n=e[s+1],r=Math.max(o,t),a=Math.min(h,n);if(r<=a&&new Jn(r+l,a+l,r,a).addToSet(i),n>h)break;s+=2}if(!a)return i;new Jn(a.fromA,a.toA,a.fromB,a.toB).addToSet(i),r=a.toA,o=a.toB}}}class ts{constructor(t,e,i){this.view=t,this.state=e,this.transactions=i,this.flags=0,this.startState=t.state,this.changes=vt.empty(this.startState.doc.length);for(let t of i)this.changes=this.changes.compose(t.changes);let n=[];this.changes.iterChangedRanges(((t,e,i,s)=>n.push(new Jn(t,e,i,s)))),this.changedRanges=n}static create(t,e,i){return new ts(t,e,i)}get viewportChanged(){return(4&this.flags)>0}get heightChanged(){return(2&this.flags)>0}get geometryChanged(){return this.docChanged||(10&this.flags)>0}get focusChanged(){return(1&this.flags)>0}get docChanged(){return!this.changes.empty}get selectionSet(){return this.transactions.some((t=>t.selection))}get empty(){return 0==this.flags&&0==this.transactions.length}}class es extends bi{get length(){return this.view.state.doc.length}constructor(t){super(),this.view=t,this.decorations=[],this.dynamicDecorationMap=[],this.domChanged=null,this.hasComposition=null,this.markedForComposition=new Set,this.minWidth=0,this.minWidthFrom=0,this.minWidthTo=0,this.impreciseAnchor=null,this.impreciseHead=null,this.forceSelection=!1,this.lastUpdate=Date.now(),this.setDOM(t.contentDOM),this.children=[new Ui],this.children[0].setParent(this),this.updateDeco(),this.updateInner([new Jn(0,0,0,t.state.doc.length)],0,null)}update(t){var e;let i=t.changedRanges;this.minWidth>0&&i.length&&(i.every((({fromA:t,toA:e})=>e<this.minWidthFrom||t>this.minWidthTo))?(this.minWidthFrom=t.changes.mapPos(this.minWidthFrom,1),this.minWidthTo=t.changes.mapPos(this.minWidthTo,1)):this.minWidth=this.minWidthFrom=this.minWidthTo=0);let n=-1;this.view.inputState.composing>=0&&((null===(e=this.domChanged)||void 0===e?void 0:e.newSel)?n=this.domChanged.newSel.head:function(t,e){let i=!1;e&&t.iterChangedRanges(((t,n)=>{t<e.to&&n>e.from&&(i=!0)}));return i}(t.changes,this.hasComposition)||t.selectionSet||(n=t.state.selection.main.head));let s=n>-1?function(t,e,i){let n=ns(t,i);if(!n)return null;let{node:s,from:r,to:o}=n,a=s.nodeValue;if(/[\n\r]/.test(a))return null;if(t.state.doc.sliceString(n.from,n.to)!=a)return null;let l=e.invertedDesc,h=new Jn(l.mapPos(r),l.mapPos(o),r,o),c=[];for(let e=s.parentNode;;e=e.parentNode){let i=bi.get(e);if(i instanceof Vi)c.push({node:e,deco:i.mark});else{if(i instanceof Ui||"DIV"==e.nodeName&&e.parentNode==t.contentDOM)return{range:h,text:s,marks:c,line:e};if(e==t.contentDOM)return null;c.push({node:e,deco:new Ki({inclusive:!0,attributes:Bi(e),tagName:e.tagName.toLowerCase()})})}}}(this.view,t.changes,n):null;if(this.domChanged=null,this.hasComposition){this.markedForComposition.clear();let{from:e,to:n}=this.hasComposition;i=new Jn(e,n,t.changes.mapPos(e,-1),t.changes.mapPos(n,1)).addToSet(i.slice())}this.hasComposition=s?{from:s.range.fromB,to:s.range.toB}:null,(Mi.ie||Mi.chrome)&&!s&&t&&t.state.doc.lines!=t.startState.doc.lines&&(this.forceSelection=!0);let r=function(t,e,i){let n=new rs;return Pe.compare(t,e,i,n),n.changes}(this.decorations,this.updateDeco(),t.changes);return i=Jn.extendWithRanges(i,r),!!(7&this.flags||0!=i.length)&&(this.updateInner(i,t.startState.doc.length,s),t.transactions.length&&(this.lastUpdate=Date.now()),!0)}updateInner(t,e,i){this.view.viewState.mustMeasureContent=!0,this.updateChildren(t,e,i);let{observer:n}=this.view;n.ignore((()=>{this.dom.style.height=this.view.viewState.contentHeight/this.view.scaleY+"px",this.dom.style.flexBasis=this.minWidth?this.minWidth+"px":"";let t=Mi.chrome||Mi.ios?{node:n.selectionRange.focusNode,written:!1}:void 0;this.sync(this.view,t),this.flags&=-8,t&&(t.written||n.selectionRange.focusNode!=t.node)&&(this.forceSelection=!0),this.dom.style.height=""})),this.markedForComposition.forEach((t=>t.flags&=-9));let s=[];if(this.view.viewport.from||this.view.viewport.to<this.view.state.doc.length)for(let t of this.children)t instanceof Ii&&t.widget instanceof is&&s.push(t.dom);n.updateGaps(s)}updateChildren(t,e,i){let n=i?i.range.addToSet(t.slice()):t,s=this.childCursor(e);for(let t=n.length-1;;t--){let e=t>=0?n[t]:null;if(!e)break;let r,o,a,l,{fromA:h,toA:c,fromB:O,toB:u}=e;if(i&&i.range.fromB<u&&i.range.toB>O){let t=sn.build(this.view.state.doc,O,i.range.fromB,this.decorations,this.dynamicDecorationMap),e=sn.build(this.view.state.doc,i.range.toB,u,this.decorations,this.dynamicDecorationMap);o=t.breakAtStart,a=t.openStart,l=e.openEnd;let n=this.compositionView(i);e.breakAtStart?n.breakAfter=1:e.content.length&&n.merge(n.length,n.length,e.content[0],!1,e.openStart,0)&&(n.breakAfter=e.content[0].breakAfter,e.content.shift()),t.content.length&&n.merge(0,0,t.content[t.content.length-1],!0,0,t.openEnd)&&t.content.pop(),r=t.content.concat(n).concat(e.content)}else({content:r,breakAtStart:o,openStart:a,openEnd:l}=sn.build(this.view.state.doc,O,u,this.decorations,this.dynamicDecorationMap));let{i:f,off:d}=s.findPos(c,1),{i:p,off:m}=s.findPos(h,-1);vi(this,p,m,f,d,r,o,a,l)}i&&this.fixCompositionDOM(i)}compositionView(t){let e=new qi(t.text.nodeValue);e.flags|=8;for(let{deco:i}of t.marks)e=new Vi(i,[e],e.length);let i=new Ui;return i.append(e,0),i}fixCompositionDOM(t){let e=(t,e)=>{e.flags|=8|(e.children.some((t=>7&t.flags))?1:0),this.markedForComposition.add(e);let i=bi.get(t);i&&i!=e&&(i.dom=null),e.setDOM(t)},i=this.childPos(t.range.fromB,1),n=this.children[i.i];e(t.line,n);for(let s=t.marks.length-1;s>=-1;s--)i=n.childPos(i.off,1),n=n.children[i.i],e(s>=0?t.marks[s].node:t.text,n)}updateSelection(t=!1,e=!1){!t&&this.view.observer.selectionRange.focusNode||this.view.observer.readSelectionRange();let i=this.view.root.activeElement,n=i==this.dom,s=!n&&Je(this.dom,this.view.observer.selectionRange)&&!(i&&this.dom.contains(i));if(!(n||e||s))return;let r=this.forceSelection;this.forceSelection=!1;let o=this.view.state.selection.main,a=this.moveToLine(this.domAtPos(o.anchor)),l=o.empty?a:this.moveToLine(this.domAtPos(o.head));if(Mi.gecko&&o.empty&&!this.hasComposition&&(1==(h=a).node.nodeType&&h.node.firstChild&&(0==h.offset||"false"==h.node.childNodes[h.offset-1].contentEditable)&&(h.offset==h.node.childNodes.length||"false"==h.node.childNodes[h.offset].contentEditable))){let t=document.createTextNode("");this.view.observer.ignore((()=>a.node.insertBefore(t,a.node.childNodes[a.offset]||null))),a=l=new mi(t,0),r=!0}var h;let c=this.view.observer.selectionRange;!r&&c.focusNode&&(ei(a.node,a.offset,c.anchorNode,c.anchorOffset)&&ei(l.node,l.offset,c.focusNode,c.focusOffset)||this.suppressWidgetCursorChange(c,o))||(this.view.observer.ignore((()=>{Mi.android&&Mi.chrome&&this.dom.contains(c.focusNode)&&function(t,e){for(let i=t;i&&i!=e;i=i.assignedSlot||i.parentNode)if(1==i.nodeType&&"false"==i.contentEditable)return!0;return!1}(c.focusNode,this.dom)&&(this.dom.blur(),this.dom.focus({preventScroll:!0}));let t=Fe(this.view.root);if(t)if(o.empty){if(Mi.gecko){let t=(e=a.node,n=a.offset,1!=e.nodeType?0:(n&&"false"==e.childNodes[n-1].contentEditable?1:0)|(n<e.childNodes.length&&"false"==e.childNodes[n].contentEditable?2:0));if(t&&3!=t){let e=ss(a.node,a.offset,1==t?1:-1);e&&(a=new mi(e.node,e.offset))}}t.collapse(a.node,a.offset),null!=o.bidiLevel&&void 0!==t.caretBidiLevel&&(t.caretBidiLevel=o.bidiLevel)}else if(t.extend){t.collapse(a.node,a.offset);try{t.extend(l.node,l.offset)}catch(t){}}else{let e=document.createRange();o.anchor>o.head&&([a,l]=[l,a]),e.setEnd(l.node,l.offset),e.setStart(a.node,a.offset),t.removeAllRanges(),t.addRange(e)}else;var e,n;s&&this.view.root.activeElement==this.dom&&(this.dom.blur(),i&&i.focus())})),this.view.observer.setSelectionRange(a,l)),this.impreciseAnchor=a.precise?null:new mi(c.anchorNode,c.anchorOffset),this.impreciseHead=l.precise?null:new mi(c.focusNode,c.focusOffset)}suppressWidgetCursorChange(t,e){return this.hasComposition&&e.empty&&ei(t.focusNode,t.focusOffset,t.anchorNode,t.anchorOffset)&&this.posFromDOM(t.focusNode,t.focusOffset)==e.head}enforceCursorAssoc(){if(this.hasComposition)return;let{view:t}=this,e=t.state.selection.main,i=Fe(t.root),{anchorNode:n,anchorOffset:s}=t.observer.selectionRange;if(!(i&&e.empty&&e.assoc&&i.modify))return;let r=Ui.find(this,e.head);if(!r)return;let o=r.posAtStart;if(e.head==o||e.head==o+r.length)return;let a=this.coordsAt(e.head,-1),l=this.coordsAt(e.head,1);if(!a||!l||a.bottom>l.top)return;let h=this.domAtPos(e.head+e.assoc);i.collapse(h.node,h.offset),i.modify("move",e.assoc<0?"forward":"backward","lineboundary"),t.observer.readSelectionRange();let c=t.observer.selectionRange;t.docView.posFromDOM(c.anchorNode,c.anchorOffset)!=e.from&&i.collapse(n,s)}moveToLine(t){let e,i=this.dom;if(t.node!=i)return t;for(let n=t.offset;!e&&n<i.childNodes.length;n++){let t=bi.get(i.childNodes[n]);t instanceof Ui&&(e=t.domAtPos(0))}for(let n=t.offset-1;!e&&n>=0;n--){let t=bi.get(i.childNodes[n]);t instanceof Ui&&(e=t.domAtPos(t.length))}return e?new mi(e.node,e.offset,!0):t}nearest(t){for(let e=t;e;){let t=bi.get(e);if(t&&t.rootView==this)return t;e=e.parentNode}return null}posFromDOM(t,e){let i=this.nearest(t);if(!i)throw new RangeError("Trying to find position for a DOM position outside of the document");return i.localPosFromDOM(t,e)+i.posAtStart}domAtPos(t){let{i:e,off:i}=this.childCursor().findPos(t,-1);for(;e<this.children.length-1;){let t=this.children[e];if(i<t.length||t instanceof Ui)break;e++,i=0}return this.children[e].domAtPos(i)}coordsAt(t,e){let i=null,n=0;for(let s=this.length,r=this.children.length-1;r>=0;r--){let o=this.children[r],a=s-o.breakAfter,l=a-o.length;if(a<t)break;l<=t&&(l<t||o.covers(-1))&&(a>t||o.covers(1))&&(!i||o instanceof Ui&&!(i instanceof Ui&&e>=0))&&(i=o,n=l),s=l}return i?i.coordsAt(t-n,e):null}coordsForChar(t){let{i:e,off:i}=this.childPos(t,1),n=this.children[e];if(!(n instanceof Ui))return null;for(;n.children.length;){let{i:t,off:e}=n.childPos(i,1);for(;;t++){if(t==n.children.length)return null;if((n=n.children[t]).length)break}i=e}if(!(n instanceof qi))return null;let s=dt(n.text,i);if(s==i)return null;let r=ui(n.dom,i,s).getClientRects();for(let t=0;t<r.length;t++){let e=r[t];if(t==r.length-1||e.top<e.bottom&&e.left<e.right)return e}return null}measureVisibleLineHeights(t){let e=[],{from:i,to:n}=t,s=this.view.contentDOM.clientWidth,r=s>Math.max(this.view.scrollDOM.clientWidth,this.minWidth)+1,o=-1,a=this.view.textDirection==an.LTR;for(let t=0,l=0;l<this.children.length;l++){let h=this.children[l],c=t+h.length;if(c>n)break;if(t>=i){let i=h.dom.getBoundingClientRect();if(e.push(i.height),r){let e=h.dom.lastChild,n=e?ti(e):[];if(n.length){let e=n[n.length-1],r=a?e.right-i.left:i.right-e.left;r>o&&(o=r,this.minWidth=s,this.minWidthFrom=t,this.minWidthTo=c)}}}t=c+h.breakAfter}return e}textDirectionAt(t){let{i:e}=this.childPos(t,1);return"rtl"==getComputedStyle(this.children[e].dom).direction?an.RTL:an.LTR}measureTextSize(){for(let t of this.children)if(t instanceof Ui){let e=t.measureTextSize();if(e)return e}let t,e,i,n=document.createElement("div");return n.className="cm-line",n.style.width="99999px",n.style.position="absolute",n.textContent="abc def ghi jkl mno pqr stu",this.view.observer.ignore((()=>{this.dom.appendChild(n);let s=ti(n.firstChild)[0];t=n.getBoundingClientRect().height,e=s?s.width/27:7,i=s?s.height:t,n.remove()})),{lineHeight:t,charWidth:e,textHeight:i}}childCursor(t=this.length){let e=this.children.length;return e&&(t-=this.children[--e].length),new Si(this.children,t,e)}computeBlockGapDeco(){let t=[],e=this.view.viewState;for(let i=0,n=0;;n++){let s=n==e.viewports.length?null:e.viewports[n],r=s?s.from-1:this.length;if(r>i){let n=(e.lineBlockAt(r).bottom-e.lineBlockAt(i).top)/this.view.scaleY;t.push(Fi.replace({widget:new is(n),block:!0,inclusive:!0,isBlockGap:!0}).range(i,r))}if(!s)break;i=s.to+1}return Fi.set(t)}updateDeco(){let t=this.view.state.facet(Gn).map(((t,e)=>(this.dynamicDecorationMap[e]="function"==typeof t)?t(this.view):t)),e=!1,i=this.view.state.facet(Bn).map(((t,i)=>{let n="function"==typeof t;return n&&(e=!0),n?t(this.view):t}));i.length&&(this.dynamicDecorationMap[t.length]=e,t.push(Pe.join(i)));for(let e=t.length;e<t.length+3;e++)this.dynamicDecorationMap[e]=!1;return this.decorations=[...t,this.computeBlockGapDeco(),this.view.viewState.lineGapDeco]}scrollIntoView(t){if(t.isSnapshot){let e=this.view.viewState.lineBlockAt(t.range.head);return this.view.scrollDOM.scrollTop=e.top-t.yMargin,void(this.view.scrollDOM.scrollLeft=t.xMargin)}let e,{range:i}=t,n=this.coordsAt(i.head,i.empty?i.assoc:i.head>i.anchor?-1:1);if(!n)return;!i.empty&&(e=this.coordsAt(i.anchor,i.anchor>i.head?-1:1))&&(n={left:Math.min(n.left,e.left),top:Math.min(n.top,e.top),right:Math.max(n.right,e.right),bottom:Math.max(n.bottom,e.bottom)});let s=Fn(this.view),r={left:n.left-s.left,top:n.top-s.top,right:n.right+s.right,bottom:n.bottom+s.bottom},{offsetWidth:o,offsetHeight:a}=this.view.scrollDOM;!function(t,e,i,n,s,r,o,a){let l=t.ownerDocument,h=l.defaultView||window;for(let c=t,O=!1;c&&!O;)if(1==c.nodeType){let t,u=c==l.body,f=1,d=1;if(u)t=oi(h);else{if(/^(fixed|sticky)$/.test(getComputedStyle(c).position)&&(O=!0),c.scrollHeight<=c.clientHeight&&c.scrollWidth<=c.clientWidth){c=c.assignedSlot||c.parentNode;continue}let e=c.getBoundingClientRect();({scaleX:f,scaleY:d}=ai(c,e)),t={left:e.left,right:e.left+c.clientWidth*f,top:e.top,bottom:e.top+c.clientHeight*d}}let p=0,m=0;if("nearest"==s)e.top<t.top?(m=-(t.top-e.top+o),i>0&&e.bottom>t.bottom+m&&(m=e.bottom-t.bottom+m+o)):e.bottom>t.bottom&&(m=e.bottom-t.bottom+o,i<0&&e.top-m<t.top&&(m=-(t.top+m-e.top+o)));else{let n=e.bottom-e.top,r=t.bottom-t.top;m=("center"==s&&n<=r?e.top+n/2-r/2:"start"==s||"center"==s&&i<0?e.top-o:e.bottom-r+o)-t.top}if("nearest"==n?e.left<t.left?(p=-(t.left-e.left+r),i>0&&e.right>t.right+p&&(p=e.right-t.right+p+r)):e.right>t.right&&(p=e.right-t.right+r,i<0&&e.left<t.left+p&&(p=-(t.left+p-e.left+r))):p=("center"==n?e.left+(e.right-e.left)/2-(t.right-t.left)/2:"start"==n==a?e.left-r:e.right-(t.right-t.left)+r)-t.left,p||m)if(u)h.scrollBy(p,m);else{let t=0,i=0;if(m){let t=c.scrollTop;c.scrollTop+=m/d,i=(c.scrollTop-t)*d}if(p){let e=c.scrollLeft;c.scrollLeft+=p/f,t=(c.scrollLeft-e)*f}e={left:e.left-t,top:e.top-i,right:e.right-t,bottom:e.bottom-i},t&&Math.abs(t-p)<1&&(n="nearest"),i&&Math.abs(i-m)<1&&(s="nearest")}if(u)break;c=c.assignedSlot||c.parentNode}else{if(11!=c.nodeType)break;c=c.host}}(this.view.scrollDOM,r,i.head<i.anchor?-1:1,t.x,t.y,Math.max(Math.min(t.xMargin,o),-o),Math.max(Math.min(t.yMargin,a),-a),this.view.textDirection==an.LTR)}}class is extends Ni{constructor(t){super(),this.height=t}toDOM(){let t=document.createElement("div");return t.className="cm-gap",this.updateDOM(t),t}eq(t){return t.height==this.height}updateDOM(t){return t.style.height=this.height+"px",!0}get editable(){return!0}get estimatedHeight(){return this.height}}function ns(t,e){let i=t.observer.selectionRange,n=i.focusNode&&ss(i.focusNode,i.focusOffset,0);if(!n)return null;let s=e-n.offset;return{from:s,to:s+n.node.nodeValue.length,node:n.node}}function ss(t,e,i){if(i<=0)for(let i=t,n=e;;){if(3==i.nodeType)return{node:i,offset:n};if(!(1==i.nodeType&&n>0))break;i=i.childNodes[n-1],n=si(i)}if(i>=0)for(let n=t,s=e;;){if(3==n.nodeType)return{node:n,offset:s};if(!(1==n.nodeType&&s<n.childNodes.length&&i>=0))break;n=n.childNodes[s],s=0}return null}let rs=class{constructor(){this.changes=[]}compareRange(t,e){nn(t,e,this.changes)}comparePoint(t,e){nn(t,e,this.changes)}};function os(t,e){return e.left>t?e.left-t:Math.max(0,t-e.right)}function as(t,e){return e.top>t?e.top-t:Math.max(0,t-e.bottom)}function ls(t,e){return t.top<e.bottom-1&&t.bottom>e.top+1}function hs(t,e){return e<t.top?{top:e,left:t.left,right:t.right,bottom:t.bottom}:t}function cs(t,e){return e>t.bottom?{top:t.top,left:t.left,right:t.right,bottom:e}:t}function Os(t,e,i){let n,s,r,o,a,l,h,c,O=!1;for(let u=t.firstChild;u;u=u.nextSibling){let t=ti(u);for(let f=0;f<t.length;f++){let d=t[f];s&&ls(s,d)&&(d=hs(cs(d,s.bottom),s.top));let p=os(e,d),m=as(i,d);if(0==p&&0==m)return 3==u.nodeType?us(u,e,i):Os(u,e,i);if(!n||o>m||o==m&&r>p){n=u,s=d,r=p,o=m;let a=m?i<d.top?-1:1:p?e<d.left?-1:1:0;O=!a||(a>0?f<t.length-1:f>0)}0==p?i>d.bottom&&(!h||h.bottom<d.bottom)?(a=u,h=d):i<d.top&&(!c||c.top>d.top)&&(l=u,c=d):h&&ls(h,d)?h=cs(h,d.bottom):c&&ls(c,d)&&(c=hs(c,d.top))}}if(h&&h.bottom>=i?(n=a,s=h):c&&c.top<=i&&(n=l,s=c),!n)return{node:t,offset:0};let u=Math.max(s.left,Math.min(s.right,e));return 3==n.nodeType?us(n,u,i):O&&"false"!=n.contentEditable?Os(n,u,i):{node:t,offset:Array.prototype.indexOf.call(t.childNodes,n)+(e>=(s.left+s.right)/2?1:0)}}function us(t,e,i){let n=t.nodeValue.length,s=-1,r=1e9,o=0;for(let a=0;a<n;a++){let n=ui(t,a,a+1).getClientRects();for(let l=0;l<n.length;l++){let h=n[l];if(h.top==h.bottom)continue;o||(o=e-h.left);let c=(h.top>i?h.top-i:i-h.bottom)-1;if(h.left-1<=e&&h.right+1>=e&&c<r){let i=e>=(h.left+h.right)/2,n=i;if(Mi.chrome||Mi.gecko){ui(t,a).getBoundingClientRect().left==h.right&&(n=!i)}if(c<=0)return{node:t,offset:a+(n?1:0)};s=a+(n?1:0),r=c}}}return{node:t,offset:s>-1?s:o>0?t.nodeValue.length:0}}function fs(t,e,i,n=-1){var s,r;let o,a=t.contentDOM.getBoundingClientRect(),l=a.top+t.viewState.paddingTop,{docHeight:h}=t.viewState,{x:c,y:O}=e,u=O-l;if(u<0)return 0;if(u>h)return t.state.doc.length;for(let e=t.viewState.heightOracle.textHeight/2,s=!1;o=t.elementAtHeight(u),o.type!=Hi.Text;)for(;u=n>0?o.bottom+e:o.top-e,!(u>=0&&u<=h);){if(s)return i?null:0;s=!0,n=-n}O=l+u;let f=o.from;if(f<t.viewport.from)return 0==t.viewport.from?0:i?null:ds(t,a,o,c,O);if(f>t.viewport.to)return t.viewport.to==t.state.doc.length?t.state.doc.length:i?null:ds(t,a,o,c,O);let d=t.dom.ownerDocument,p=t.root.elementFromPoint?t.root:d,m=p.elementFromPoint(c,O);m&&!t.contentDOM.contains(m)&&(m=null),m||(c=Math.max(a.left+1,Math.min(a.right-1,c)),m=p.elementFromPoint(c,O),m&&!t.contentDOM.contains(m)&&(m=null));let g,b=-1;if(m&&0!=(null===(s=t.docView.nearest(m))||void 0===s?void 0:s.isEditable))if(d.caretPositionFromPoint){let t=d.caretPositionFromPoint(c,O);t&&({offsetNode:g,offset:b}=t)}else if(d.caretRangeFromPoint){let e=d.caretRangeFromPoint(c,O);e&&(({startContainer:g,startOffset:b}=e),(!t.contentDOM.contains(g)||Mi.safari&&function(t,e,i){let n;if(3!=t.nodeType||e!=(n=t.nodeValue.length))return!1;for(let e=t.nextSibling;e;e=e.nextSibling)if(1!=e.nodeType||"BR"!=e.nodeName)return!1;return ui(t,n-1,n).getBoundingClientRect().left>i}(g,b,c)||Mi.chrome&&function(t,e,i){if(0!=e)return!1;for(let e=t;;){let t=e.parentNode;if(!t||1!=t.nodeType||t.firstChild!=e)return!1;if(t.classList.contains("cm-line"))break;e=t}let n=1==t.nodeType?t.getBoundingClientRect():ui(t,0,Math.max(t.nodeValue.length,1)).getBoundingClientRect();return i-n.left>5}(g,b,c))&&(g=void 0))}if(!g||!t.docView.dom.contains(g)){let e=Ui.find(t.docView,f);if(!e)return u>o.top+o.height/2?o.to:o.from;({node:g,offset:b}=Os(e.dom,c,O))}let Q=t.docView.nearest(g);if(!Q)return null;if(Q.isWidget&&1==(null===(r=Q.dom)||void 0===r?void 0:r.nodeType)){let t=Q.dom.getBoundingClientRect();return e.y<t.top||e.y<=t.bottom&&e.x<=(t.left+t.right)/2?Q.posAtStart:Q.posAtEnd}return Q.localPosFromDOM(g,b)+Q.posAtStart}function ds(t,e,i,n,s){let r=Math.round((n-e.left)*t.defaultCharacterWidth);if(t.lineWrapping&&i.height>1.5*t.defaultLineHeight){let e=t.viewState.heightOracle.textHeight;r+=Math.floor((s-i.top-.5*(t.defaultLineHeight-e))/e)*t.viewState.heightOracle.lineLength}let o=t.state.sliceDoc(i.from,i.to);return i.from+function(t,e,i,n){for(let n=0,s=0;;){if(s>=e)return n;if(n==t.length)break;s+=9==t.charCodeAt(n)?i-s%i:1,n=dt(t,n)}return!0===n?-1:t.length}(o,r,t.state.tabSize)}function ps(t,e){let i=t.lineBlockAt(e);if(Array.isArray(i.type))for(let t of i.type)if(t.to>e||t.to==e&&(t.to==i.to||t.type==Hi.Text))return t;return i}function ms(t,e,i,n){let s=t.state.doc.lineAt(e.head),r=t.bidiSpans(s),o=t.textDirectionAt(s.from);for(let a=e,l=null;;){let e=xn(s,r,o,a,i),h=yn;if(!e){if(s.number==(i?t.state.doc.lines:1))return a;h="\n",s=t.state.doc.line(s.number+(i?1:-1)),r=t.bidiSpans(s),e=t.visualLineSide(s,!i)}if(l){if(!l(h))return a}else{if(!n)return e;l=n(h)}a=e}}function gs(t,e,i){for(;;){let n=0;for(let s of t)s.between(e-1,e+1,((t,s,r)=>{if(e>t&&e<s){let r=n||i||(e-t<s-e?-1:1);e=r<0?t:s,n=r}}));if(!n)return e}}function bs(t,e,i){let n=gs(t.state.facet(Un).map((e=>e(t))),i.from,e.head>i.from?-1:1);return n==i.from?i:Tt.cursor(n,n<i.from?1:-1)}class Qs{setSelectionOrigin(t){this.lastSelectionOrigin=t,this.lastSelectionTime=Date.now()}constructor(t){this.view=t,this.lastKeyCode=0,this.lastKeyTime=0,this.lastTouchTime=0,this.lastFocusTime=0,this.lastScrollTop=0,this.lastScrollLeft=0,this.pendingIOSKey=void 0,this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastEscPress=0,this.lastContextMenu=0,this.scrollHandlers=[],this.handlers=Object.create(null),this.composing=-1,this.compositionFirstChange=null,this.compositionEndedAt=0,this.compositionPendingKey=!1,this.compositionPendingChange=!1,this.mouseSelection=null,this.draggedContent=null,this.handleEvent=this.handleEvent.bind(this),this.notifiedFocused=t.hasFocus,Mi.safari&&t.contentDOM.addEventListener("input",(()=>null)),Mi.gecko&&function(t){Bs.has(t)||(Bs.add(t),t.addEventListener("copy",(()=>{})),t.addEventListener("cut",(()=>{})))}(t.contentDOM.ownerDocument)}handleEvent(t){(function(t,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let i,n=e.target;n!=t.contentDOM;n=n.parentNode)if(!n||11==n.nodeType||(i=bi.get(n))&&i.ignoreEvent(e))return!1;return!0})(this.view,t)&&!this.ignoreDuringComposition(t)&&("keydown"==t.type&&this.keydown(t)||this.runHandlers(t.type,t))}runHandlers(t,e){let i=this.handlers[t];if(i){for(let t of i.observers)t(this.view,e);for(let t of i.handlers){if(e.defaultPrevented)break;if(t(this.view,e)){e.preventDefault();break}}}}ensureHandlers(t){let e=vs(t),i=this.handlers,n=this.view.contentDOM;for(let t in e)if("scroll"!=t){let s=!e[t].handlers.length,r=i[t];r&&s!=!r.handlers.length&&(n.removeEventListener(t,this.handleEvent),r=null),r||n.addEventListener(t,this.handleEvent,{passive:s})}for(let t in i)"scroll"==t||e[t]||n.removeEventListener(t,this.handleEvent);this.handlers=e}keydown(t){if(this.lastKeyCode=t.keyCode,this.lastKeyTime=Date.now(),9==t.keyCode&&Date.now()<this.lastEscPress+2e3)return!0;if(27!=t.keyCode&&xs.indexOf(t.keyCode)<0&&(this.view.inputState.lastEscPress=0),Mi.android&&Mi.chrome&&!t.synthetic&&(13==t.keyCode||8==t.keyCode))return this.view.observer.delayAndroidKey(t.key,t.keyCode),!0;let e;return!Mi.ios||t.synthetic||t.altKey||t.metaKey||!((e=ws.find((e=>e.keyCode==t.keyCode)))&&!t.ctrlKey||ys.indexOf(t.key)>-1&&t.ctrlKey&&!t.shiftKey)?(229!=t.keyCode&&this.view.observer.forceFlush(),!1):(this.pendingIOSKey=e||t,setTimeout((()=>this.flushIOSKey()),250),!0)}flushIOSKey(){let t=this.pendingIOSKey;return!!t&&(this.pendingIOSKey=void 0,fi(this.view.contentDOM,t.key,t.keyCode))}ignoreDuringComposition(t){return!!/^key/.test(t.type)&&(this.composing>0||!!(Mi.safari&&!Mi.ios&&this.compositionPendingKey&&Date.now()-this.compositionEndedAt<100)&&(this.compositionPendingKey=!1,!0))}startMouseSelection(t){this.mouseSelection&&this.mouseSelection.destroy(),this.mouseSelection=t}update(t){this.mouseSelection&&this.mouseSelection.update(t),this.draggedContent&&t.docChanged&&(this.draggedContent=this.draggedContent.map(t.changes)),t.transactions.length&&(this.lastKeyCode=this.lastSelectionTime=0)}destroy(){this.mouseSelection&&this.mouseSelection.destroy()}}function Ss(t,e){return(i,n)=>{try{return e.call(t,n,i)}catch(t){Yn(i.state,t)}}}function vs(t){let e=Object.create(null);function i(t){return e[t]||(e[t]={observers:[],handlers:[]})}for(let e of t){let t=e.spec;if(t&&t.domEventHandlers)for(let n in t.domEventHandlers){let s=t.domEventHandlers[n];s&&i(n).handlers.push(Ss(e.value,s))}if(t&&t.domEventObservers)for(let n in t.domEventObservers){let s=t.domEventObservers[n];s&&i(n).observers.push(Ss(e.value,s))}}for(let t in $s)i(t).handlers.push($s[t]);for(let t in Zs)i(t).observers.push(Zs[t]);return e}const ws=[{key:"Backspace",keyCode:8,inputType:"deleteContentBackward"},{key:"Enter",keyCode:13,inputType:"insertParagraph"},{key:"Enter",keyCode:13,inputType:"insertLineBreak"},{key:"Delete",keyCode:46,inputType:"deleteContentForward"}],ys="dthko",xs=[16,17,18,20,91,92,224,225];function ks(t){return.7*Math.max(0,t)+8}class Ps{constructor(t,e,i,n){this.view=t,this.startEvent=e,this.style=i,this.mustSelect=n,this.scrollSpeed={x:0,y:0},this.scrolling=-1,this.lastEvent=e,this.scrollParent=function(t){let e=t.ownerDocument;for(let i=t.parentNode;i&&i!=e.body;)if(1==i.nodeType){if(i.scrollHeight>i.clientHeight||i.scrollWidth>i.clientWidth)return i;i=i.assignedSlot||i.parentNode}else{if(11!=i.nodeType)break;i=i.host}return null}(t.contentDOM),this.atoms=t.state.facet(Un).map((e=>e(t)));let s=t.contentDOM.ownerDocument;s.addEventListener("mousemove",this.move=this.move.bind(this)),s.addEventListener("mouseup",this.up=this.up.bind(this)),this.extend=e.shiftKey,this.multiple=t.state.facet(Se.allowMultipleSelections)&&function(t,e){let i=t.state.facet(Pn);return i.length?i[0](e):Mi.mac?e.metaKey:e.ctrlKey}(t,e),this.dragging=!(!function(t,e){let{main:i}=t.state.selection;if(i.empty)return!1;let n=Fe(t.root);if(!n||0==n.rangeCount)return!0;let s=n.getRangeAt(0).getClientRects();for(let t=0;t<s.length;t++){let i=s[t];if(i.left<=e.clientX&&i.right>=e.clientX&&i.top<=e.clientY&&i.bottom>=e.clientY)return!0}return!1}(t,e)||1!=js(e))&&null}start(t){!1===this.dragging&&this.select(t)}move(t){var e,i,n;if(0==t.buttons)return this.destroy();if(this.dragging||null==this.dragging&&(i=this.startEvent,n=t,Math.max(Math.abs(i.clientX-n.clientX),Math.abs(i.clientY-n.clientY))<10))return;this.select(this.lastEvent=t);let s=0,r=0,o=(null===(e=this.scrollParent)||void 0===e?void 0:e.getBoundingClientRect())||{left:0,top:0,right:this.view.win.innerWidth,bottom:this.view.win.innerHeight},a=Fn(this.view);t.clientX-a.left<=o.left+6?s=-ks(o.left-t.clientX):t.clientX+a.right>=o.right-6&&(s=ks(t.clientX-o.right)),t.clientY-a.top<=o.top+6?r=-ks(o.top-t.clientY):t.clientY+a.bottom>=o.bottom-6&&(r=ks(t.clientY-o.bottom)),this.setScrollSpeed(s,r)}up(t){null==this.dragging&&this.select(this.lastEvent),this.dragging||t.preventDefault(),this.destroy()}destroy(){this.setScrollSpeed(0,0);let t=this.view.contentDOM.ownerDocument;t.removeEventListener("mousemove",this.move),t.removeEventListener("mouseup",this.up),this.view.inputState.mouseSelection=this.view.inputState.draggedContent=null}setScrollSpeed(t,e){this.scrollSpeed={x:t,y:e},t||e?this.scrolling<0&&(this.scrolling=setInterval((()=>this.scroll()),50)):this.scrolling>-1&&(clearInterval(this.scrolling),this.scrolling=-1)}scroll(){this.scrollParent?(this.scrollParent.scrollLeft+=this.scrollSpeed.x,this.scrollParent.scrollTop+=this.scrollSpeed.y):this.view.win.scrollBy(this.scrollSpeed.x,this.scrollSpeed.y),!1===this.dragging&&this.select(this.lastEvent)}skipAtoms(t){let e=null;for(let i=0;i<t.ranges.length;i++){let n=t.ranges[i],s=null;if(n.empty){let t=gs(this.atoms,n.from,0);t!=n.from&&(s=Tt.cursor(t,-1))}else{let t=gs(this.atoms,n.from,-1),e=gs(this.atoms,n.to,1);t==n.from&&e==n.to||(s=Tt.range(n.from==n.anchor?t:e,n.from==n.head?t:e))}s&&(e||(e=t.ranges.slice()),e[i]=s)}return e?Tt.create(e,t.mainIndex):t}select(t){let{view:e}=this,i=this.skipAtoms(this.style.get(t,this.extend,this.multiple));!this.mustSelect&&i.eq(e.state.selection,!1===this.dragging)||this.view.dispatch({selection:i,userEvent:"select.pointer"}),this.mustSelect=!1}update(t){this.style.update(t)&&setTimeout((()=>this.select(this.lastEvent)),20)}}const $s=Object.create(null),Zs=Object.create(null),Ts=Mi.ie&&Mi.ie_version<15||Mi.ios&&Mi.webkit_version<604;function Xs(t,e){let i,{state:n}=t,s=1,r=n.toText(e),o=r.lines==n.selection.ranges.length;if(null!=Ds&&n.selection.ranges.every((t=>t.empty))&&Ds==r.toString()){let t=-1;i=n.changeByRange((i=>{let a=n.doc.lineAt(i.from);if(a.from==t)return{range:i};t=a.from;let l=n.toText((o?r.line(s++).text:e)+n.lineBreak);return{changes:{from:a.from,insert:l},range:Tt.cursor(i.from+l.length)}}))}else i=o?n.changeByRange((t=>{let e=r.line(s++);return{changes:{from:t.from,to:t.to,insert:e.text},range:Tt.cursor(t.from+e.length)}})):n.replaceSelection(r);t.dispatch(i,{userEvent:"input.paste",scrollIntoView:!0})}function Rs(t,e,i,n){if(1==n)return Tt.cursor(e,i);if(2==n)return function(t,e,i=1){let n=t.charCategorizer(e),s=t.doc.lineAt(e),r=e-s.from;if(0==s.length)return Tt.cursor(e);0==r?i=1:r==s.length&&(i=-1);let o=r,a=r;i<0?o=dt(s.text,r,!1):a=dt(s.text,r);let l=n(s.text.slice(o,a));for(;o>0;){let t=dt(s.text,o,!1);if(n(s.text.slice(t,o))!=l)break;o=t}for(;a<s.length;){let t=dt(s.text,a);if(n(s.text.slice(a,t))!=l)break;a=t}return Tt.range(o+s.from,a+s.from)}(t.state,e,i);{let i=Ui.find(t.docView,e),n=t.state.doc.lineAt(i?i.posAtEnd:e),s=i?i.posAtStart:n.from,r=i?i.posAtEnd:n.to;return r<t.state.doc.length&&r==n.to&&r++,Tt.range(s,r)}}Zs.scroll=t=>{t.inputState.lastScrollTop=t.scrollDOM.scrollTop,t.inputState.lastScrollLeft=t.scrollDOM.scrollLeft},$s.keydown=(t,e)=>(t.inputState.setSelectionOrigin("select"),27==e.keyCode&&(t.inputState.lastEscPress=Date.now()),!1),Zs.touchstart=(t,e)=>{t.inputState.lastTouchTime=Date.now(),t.inputState.setSelectionOrigin("select.pointer")},Zs.touchmove=t=>{t.inputState.setSelectionOrigin("select.pointer")},$s.mousedown=(t,e)=>{if(t.observer.flush(),t.inputState.lastTouchTime>Date.now()-2e3)return!1;let i=null;for(let n of t.state.facet(Zn))if(i=n(t,e),i)break;if(i||0!=e.button||(i=function(t,e){let i=qs(t,e),n=js(e),s=t.state.selection;return{update(t){t.docChanged&&(i.pos=t.changes.mapPos(i.pos),s=s.map(t.changes))},get(e,r,o){let a,l=qs(t,e),h=Rs(t,l.pos,l.bias,n);if(i.pos!=l.pos&&!r){let e=Rs(t,i.pos,i.bias,n),s=Math.min(e.from,h.from),r=Math.max(e.to,h.to);h=s<h.from?Tt.range(s,r):Tt.range(r,s)}return r?s.replaceRange(s.main.extend(h.from,h.to)):o&&1==n&&s.ranges.length>1&&(a=function(t,e){for(let i=0;i<t.ranges.length;i++){let{from:n,to:s}=t.ranges[i];if(n<=e&&s>=e)return Tt.create(t.ranges.slice(0,i).concat(t.ranges.slice(i+1)),t.mainIndex==i?0:t.mainIndex-(t.mainIndex>i?1:0))}return null}(s,l.pos))?a:o?s.addRange(h):Tt.create([h])}}}(t,e)),i){let n=!t.hasFocus;t.inputState.startMouseSelection(new Ps(t,e,i,n)),n&&t.observer.ignore((()=>Oi(t.contentDOM)));let s=t.inputState.mouseSelection;if(s)return s.start(e),!1===s.dragging}return!1};let As=(t,e)=>t>=e.top&&t<=e.bottom,Cs=(t,e,i)=>As(e,i)&&t>=i.left&&t<=i.right;function Ms(t,e,i,n){let s=Ui.find(t.docView,e);if(!s)return 1;let r=e-s.posAtStart;if(0==r)return 1;if(r==s.length)return-1;let o=s.coordsAt(r,-1);if(o&&Cs(i,n,o))return-1;let a=s.coordsAt(r,1);return a&&Cs(i,n,a)?1:o&&As(n,o)?-1:1}function qs(t,e){let i=t.posAtCoords({x:e.clientX,y:e.clientY},!1);return{pos:i,bias:Ms(t,i,e.clientX,e.clientY)}}const Vs=Mi.ie&&Mi.ie_version<=11;let Ys=null,zs=0,_s=0;function js(t){if(!Vs)return t.detail;let e=Ys,i=_s;return Ys=t,_s=Date.now(),zs=!e||i>Date.now()-400&&Math.abs(e.clientX-t.clientX)<2&&Math.abs(e.clientY-t.clientY)<2?(zs+1)%3:1}function Es(t,e,i,n){if(!i)return;let s=t.posAtCoords({x:e.clientX,y:e.clientY},!1),{draggedContent:r}=t.inputState,o=n&&r&&function(t,e){let i=t.state.facet($n);return i.length?i[0](e):Mi.mac?!e.altKey:!e.ctrlKey}(t,e)?{from:r.from,to:r.to}:null,a={from:s,insert:i},l=t.state.changes(o?[o,a]:a);t.focus(),t.dispatch({changes:l,selection:{anchor:l.mapPos(s,-1),head:l.mapPos(s,1)},userEvent:o?"move.drop":"input.drop"}),t.inputState.draggedContent=null}$s.dragstart=(t,e)=>{let{selection:{main:i}}=t.state;if(e.target.draggable){let n=t.docView.nearest(e.target);if(n&&n.isWidget){let t=n.posAtStart,e=t+n.length;(t>=i.to||e<=i.from)&&(i=Tt.range(t,e))}}let{inputState:n}=t;return n.mouseSelection&&(n.mouseSelection.dragging=!0),n.draggedContent=i,e.dataTransfer&&(e.dataTransfer.setData("Text",t.state.sliceDoc(i.from,i.to)),e.dataTransfer.effectAllowed="copyMove"),!1},$s.dragend=t=>(t.inputState.draggedContent=null,!1),$s.drop=(t,e)=>{if(!e.dataTransfer)return!1;if(t.state.readOnly)return!0;let i=e.dataTransfer.files;if(i&&i.length){let n=Array(i.length),s=0,r=()=>{++s==i.length&&Es(t,e,n.filter((t=>null!=t)).join(t.state.lineBreak),!1)};for(let t=0;t<i.length;t++){let e=new FileReader;e.onerror=r,e.onload=()=>{/[\x00-\x08\x0e-\x1f]{2}/.test(e.result)||(n[t]=e.result),r()},e.readAsText(i[t])}return!0}{let i=e.dataTransfer.getData("Text");if(i)return Es(t,e,i,!0),!0}return!1},$s.paste=(t,e)=>{if(t.state.readOnly)return!0;t.observer.flush();let i=Ts?null:e.clipboardData;return i?(Xs(t,i.getData("text/plain")||i.getData("text/uri-text")),!0):(function(t){let e=t.dom.parentNode;if(!e)return;let i=e.appendChild(document.createElement("textarea"));i.style.cssText="position: fixed; left: -10000px; top: 10px",i.focus(),setTimeout((()=>{t.focus(),i.remove(),Xs(t,i.value)}),50)}(t),!1)};let Ds=null;$s.copy=$s.cut=(t,e)=>{let{text:i,ranges:n,linewise:s}=function(t){let e=[],i=[],n=!1;for(let n of t.selection.ranges)n.empty||(e.push(t.sliceDoc(n.from,n.to)),i.push(n));if(!e.length){let s=-1;for(let{from:n}of t.selection.ranges){let r=t.doc.lineAt(n);r.number>s&&(e.push(r.text),i.push({from:r.from,to:Math.min(t.doc.length,r.to+1)})),s=r.number}n=!0}return{text:e.join(t.lineBreak),ranges:i,linewise:n}}(t.state);if(!i&&!s)return!1;Ds=s?i:null,"cut"!=e.type||t.state.readOnly||t.dispatch({changes:n,scrollIntoView:!0,userEvent:"delete.cut"});let r=Ts?null:e.clipboardData;return r?(r.clearData(),r.setData("text/plain",i),!0):(function(t,e){let i=t.dom.parentNode;if(!i)return;let n=i.appendChild(document.createElement("textarea"));n.style.cssText="position: fixed; left: -10000px; top: 10px",n.value=e,n.focus(),n.selectionEnd=e.length,n.selectionStart=0,setTimeout((()=>{n.remove(),t.focus()}),50)}(t,i),!1)};const Ws=re.define();function Ls(t,e){let i=[];for(let n of t.facet(An)){let s=n(t,e);s&&i.push(s)}return i?t.update({effects:i,annotations:Ws.of(!0)}):null}function Gs(t){setTimeout((()=>{let e=t.hasFocus;if(e!=t.inputState.notifiedFocused){let i=Ls(t.state,e);i?t.dispatch(i):t.update([])}}),10)}Zs.focus=t=>{t.inputState.lastFocusTime=Date.now(),t.scrollDOM.scrollTop||!t.inputState.lastScrollTop&&!t.inputState.lastScrollLeft||(t.scrollDOM.scrollTop=t.inputState.lastScrollTop,t.scrollDOM.scrollLeft=t.inputState.lastScrollLeft),Gs(t)},Zs.blur=t=>{t.observer.clearSelectionRange(),Gs(t)},Zs.compositionstart=Zs.compositionupdate=t=>{null==t.inputState.compositionFirstChange&&(t.inputState.compositionFirstChange=!0),t.inputState.composing<0&&(t.inputState.composing=0)},Zs.compositionend=t=>{t.inputState.composing=-1,t.inputState.compositionEndedAt=Date.now(),t.inputState.compositionPendingKey=!0,t.inputState.compositionPendingChange=t.observer.pendingRecords().length>0,t.inputState.compositionFirstChange=null,Mi.chrome&&Mi.android?t.observer.flushSoon():t.inputState.compositionPendingChange?Promise.resolve().then((()=>t.observer.flush())):setTimeout((()=>{t.inputState.composing<0&&t.docView.hasComposition&&t.update([])}),50)},Zs.contextmenu=t=>{t.inputState.lastContextMenu=Date.now()},$s.beforeinput=(t,e)=>{var i;let n;if(Mi.chrome&&Mi.android&&(n=ws.find((t=>t.inputType==e.inputType)))&&(t.observer.delayAndroidKey(n.key,n.keyCode),"Backspace"==n.key||"Delete"==n.key)){let e=(null===(i=window.visualViewport)||void 0===i?void 0:i.height)||0;setTimeout((()=>{var i;((null===(i=window.visualViewport)||void 0===i?void 0:i.height)||0)>e+10&&t.hasFocus&&(t.contentDOM.blur(),t.focus())}),100)}return!1};const Bs=new Set;const Us=["pre-wrap","normal","pre-line","break-spaces"];class Is{constructor(t){this.lineWrapping=t,this.doc=st.empty,this.heightSamples={},this.lineHeight=14,this.charWidth=7,this.textHeight=14,this.lineLength=30,this.heightChanged=!1}heightForGap(t,e){let i=this.doc.lineAt(e).number-this.doc.lineAt(t).number+1;return this.lineWrapping&&(i+=Math.max(0,Math.ceil((e-t-i*this.lineLength*.5)/this.lineLength))),this.lineHeight*i}heightForLine(t){if(!this.lineWrapping)return this.lineHeight;return(1+Math.max(0,Math.ceil((t-this.lineLength)/(this.lineLength-5))))*this.lineHeight}setDoc(t){return this.doc=t,this}mustRefreshForWrapping(t){return Us.indexOf(t)>-1!=this.lineWrapping}mustRefreshForHeights(t){let e=!1;for(let i=0;i<t.length;i++){let n=t[i];n<0?i++:this.heightSamples[Math.floor(10*n)]||(e=!0,this.heightSamples[Math.floor(10*n)]=!0)}return e}refresh(t,e,i,n,s,r){let o=Us.indexOf(t)>-1,a=Math.round(e)!=Math.round(this.lineHeight)||this.lineWrapping!=o;if(this.lineWrapping=o,this.lineHeight=e,this.charWidth=i,this.textHeight=n,this.lineLength=s,a){this.heightSamples={};for(let t=0;t<r.length;t++){let e=r[t];e<0?t++:this.heightSamples[Math.floor(10*e)]=!0}}return a}}class Ns{constructor(t,e){this.from=t,this.heights=e,this.index=0}get more(){return this.index<this.heights.length}}class Hs{constructor(t,e,i,n,s){this.from=t,this.length=e,this.top=i,this.height=n,this._content=s}get type(){return"number"==typeof this._content?Hi.Text:Array.isArray(this._content)?this._content:this._content.type}get to(){return this.from+this.length}get bottom(){return this.top+this.height}get widget(){return this._content instanceof tn?this._content.widget:null}get widgetLineBreaks(){return"number"==typeof this._content?this._content:0}join(t){let e=(Array.isArray(this._content)?this._content:[this]).concat(Array.isArray(t._content)?t._content:[t]);return new Hs(this.from,this.length+t.length,this.top,this.height+t.height,e)}}var Fs=function(t){return t[t.ByPos=0]="ByPos",t[t.ByHeight=1]="ByHeight",t[t.ByPosNoHeight=2]="ByPosNoHeight",t}(Fs||(Fs={}));const Ks=.001;class Js{constructor(t,e,i=2){this.length=t,this.height=e,this.flags=i}get outdated(){return(2&this.flags)>0}set outdated(t){this.flags=(t?2:0)|-3&this.flags}setHeight(t,e){this.height!=e&&(Math.abs(this.height-e)>Ks&&(t.heightChanged=!0),this.height=e)}replace(t,e,i){return Js.of(i)}decomposeLeft(t,e){e.push(this)}decomposeRight(t,e){e.push(this)}applyChanges(t,e,i,n){let s=this,r=i.doc;for(let o=n.length-1;o>=0;o--){let{fromA:a,toA:l,fromB:h,toB:c}=n[o],O=s.lineAt(a,Fs.ByPosNoHeight,i.setDoc(e),0,0),u=O.to>=l?O:s.lineAt(l,Fs.ByPosNoHeight,i,0,0);for(c+=u.to-l,l=u.to;o>0&&O.from<=n[o-1].toA;)a=n[o-1].fromA,h=n[o-1].fromB,o--,a<O.from&&(O=s.lineAt(a,Fs.ByPosNoHeight,i,0,0));h+=O.from-a,a=O.from;let f=rr.build(i.setDoc(r),t,h,c);s=s.replace(a,l,f)}return s.updateHeight(i,0)}static empty(){return new er(0,0)}static of(t){if(1==t.length)return t[0];let e=0,i=t.length,n=0,s=0;for(;;)if(e==i)if(n>2*s){let s=t[e-1];s.break?t.splice(--e,1,s.left,null,s.right):t.splice(--e,1,s.left,s.right),i+=1+s.break,n-=s.size}else{if(!(s>2*n))break;{let e=t[i];e.break?t.splice(i,1,e.left,null,e.right):t.splice(i,1,e.left,e.right),i+=2+e.break,s-=e.size}}else if(n<s){let i=t[e++];i&&(n+=i.size)}else{let e=t[--i];e&&(s+=e.size)}let r=0;return null==t[e-1]?(r=1,e--):null==t[e]&&(r=1,i++),new nr(Js.of(t.slice(0,e)),r,Js.of(t.slice(i)))}}Js.prototype.size=1;class tr extends Js{constructor(t,e,i){super(t,e),this.deco=i}blockAt(t,e,i,n){return new Hs(n,this.length,i,this.height,this.deco||0)}lineAt(t,e,i,n,s){return this.blockAt(0,i,n,s)}forEachLine(t,e,i,n,s,r){t<=s+this.length&&e>=s&&r(this.blockAt(0,i,n,s))}updateHeight(t,e=0,i=!1,n){return n&&n.from<=e&&n.more&&this.setHeight(t,n.heights[n.index++]),this.outdated=!1,this}toString(){return`block(${this.length})`}}class er extends tr{constructor(t,e){super(t,e,null),this.collapsed=0,this.widgetHeight=0,this.breaks=0}blockAt(t,e,i,n){return new Hs(n,this.length,i,this.height,this.breaks)}replace(t,e,i){let n=i[0];return 1==i.length&&(n instanceof er||n instanceof ir&&4&n.flags)&&Math.abs(this.length-n.length)<10?(n instanceof ir?n=new er(n.length,this.height):n.height=this.height,this.outdated||(n.outdated=!1),n):Js.of(i)}updateHeight(t,e=0,i=!1,n){return n&&n.from<=e&&n.more?this.setHeight(t,n.heights[n.index++]):(i||this.outdated)&&this.setHeight(t,Math.max(this.widgetHeight,t.heightForLine(this.length-this.collapsed))+this.breaks*t.lineHeight),this.outdated=!1,this}toString(){return`line(${this.length}${this.collapsed?-this.collapsed:""}${this.widgetHeight?":"+this.widgetHeight:""})`}}class ir extends Js{constructor(t){super(t,0)}heightMetrics(t,e){let i,n=t.doc.lineAt(e).number,s=t.doc.lineAt(e+this.length).number,r=s-n+1,o=0;if(t.lineWrapping){let e=Math.min(this.height,t.lineHeight*r);i=e/r,this.length>r+1&&(o=(this.height-e)/(this.length-r-1))}else i=this.height/r;return{firstLine:n,lastLine:s,perLine:i,perChar:o}}blockAt(t,e,i,n){let{firstLine:s,lastLine:r,perLine:o,perChar:a}=this.heightMetrics(e,n);if(e.lineWrapping){let s=n+Math.round(Math.max(0,Math.min(1,(t-i)/this.height))*this.length),r=e.doc.lineAt(s),l=o+r.length*a,h=Math.max(i,t-l/2);return new Hs(r.from,r.length,h,l,0)}{let n=Math.max(0,Math.min(r-s,Math.floor((t-i)/o))),{from:a,length:l}=e.doc.line(s+n);return new Hs(a,l,i+o*n,o,0)}}lineAt(t,e,i,n,s){if(e==Fs.ByHeight)return this.blockAt(t,i,n,s);if(e==Fs.ByPosNoHeight){let{from:e,to:n}=i.doc.lineAt(t);return new Hs(e,n-e,0,0,0)}let{firstLine:r,perLine:o,perChar:a}=this.heightMetrics(i,s),l=i.doc.lineAt(t),h=o+l.length*a,c=l.number-r,O=n+o*c+a*(l.from-s-c);return new Hs(l.from,l.length,Math.max(n,Math.min(O,n+this.height-h)),h,0)}forEachLine(t,e,i,n,s,r){t=Math.max(t,s),e=Math.min(e,s+this.length);let{firstLine:o,perLine:a,perChar:l}=this.heightMetrics(i,s);for(let h=t,c=n;h<=e;){let e=i.doc.lineAt(h);if(h==t){let i=e.number-o;c+=a*i+l*(t-s-i)}let n=a+l*e.length;r(new Hs(e.from,e.length,c,n,0)),c+=n,h=e.to+1}}replace(t,e,i){let n=this.length-e;if(n>0){let t=i[i.length-1];t instanceof ir?i[i.length-1]=new ir(t.length+n):i.push(null,new ir(n-1))}if(t>0){let e=i[0];e instanceof ir?i[0]=new ir(t+e.length):i.unshift(new ir(t-1),null)}return Js.of(i)}decomposeLeft(t,e){e.push(new ir(t-1),null)}decomposeRight(t,e){e.push(null,new ir(this.length-t-1))}updateHeight(t,e=0,i=!1,n){let s=e+this.length;if(n&&n.from<=e+this.length&&n.more){let i=[],r=Math.max(e,n.from),o=-1;for(n.from>e&&i.push(new ir(n.from-e-1).updateHeight(t,e));r<=s&&n.more;){let e=t.doc.lineAt(r).length;i.length&&i.push(null);let s=n.heights[n.index++];-1==o?o=s:Math.abs(s-o)>=Ks&&(o=-2);let a=new er(e,s);a.outdated=!1,i.push(a),r+=e+1}r<=s&&i.push(null,new ir(s-r).updateHeight(t,r));let a=Js.of(i);return(o<0||Math.abs(a.height-this.height)>=Ks||Math.abs(o-this.heightMetrics(t,e).perLine)>=Ks)&&(t.heightChanged=!0),a}return(i||this.outdated)&&(this.setHeight(t,t.heightForGap(e,e+this.length)),this.outdated=!1),this}toString(){return`gap(${this.length})`}}class nr extends Js{constructor(t,e,i){super(t.length+e+i.length,t.height+i.height,e|(t.outdated||i.outdated?2:0)),this.left=t,this.right=i,this.size=t.size+i.size}get break(){return 1&this.flags}blockAt(t,e,i,n){let s=i+this.left.height;return t<s?this.left.blockAt(t,e,i,n):this.right.blockAt(t,e,s,n+this.left.length+this.break)}lineAt(t,e,i,n,s){let r=n+this.left.height,o=s+this.left.length+this.break,a=e==Fs.ByHeight?t<r:t<o,l=a?this.left.lineAt(t,e,i,n,s):this.right.lineAt(t,e,i,r,o);if(this.break||(a?l.to<o:l.from>o))return l;let h=e==Fs.ByPosNoHeight?Fs.ByPosNoHeight:Fs.ByPos;return a?l.join(this.right.lineAt(o,h,i,r,o)):this.left.lineAt(o,h,i,n,s).join(l)}forEachLine(t,e,i,n,s,r){let o=n+this.left.height,a=s+this.left.length+this.break;if(this.break)t<a&&this.left.forEachLine(t,e,i,n,s,r),e>=a&&this.right.forEachLine(t,e,i,o,a,r);else{let l=this.lineAt(a,Fs.ByPos,i,n,s);t<l.from&&this.left.forEachLine(t,l.from-1,i,n,s,r),l.to>=t&&l.from<=e&&r(l),e>l.to&&this.right.forEachLine(l.to+1,e,i,o,a,r)}}replace(t,e,i){let n=this.left.length+this.break;if(e<n)return this.balanced(this.left.replace(t,e,i),this.right);if(t>this.left.length)return this.balanced(this.left,this.right.replace(t-n,e-n,i));let s=[];t>0&&this.decomposeLeft(t,s);let r=s.length;for(let t of i)s.push(t);if(t>0&&sr(s,r-1),e<this.length){let t=s.length;this.decomposeRight(e,s),sr(s,t)}return Js.of(s)}decomposeLeft(t,e){let i=this.left.length;if(t<=i)return this.left.decomposeLeft(t,e);e.push(this.left),this.break&&(i++,t>=i&&e.push(null)),t>i&&this.right.decomposeLeft(t-i,e)}decomposeRight(t,e){let i=this.left.length,n=i+this.break;if(t>=n)return this.right.decomposeRight(t-n,e);t<i&&this.left.decomposeRight(t,e),this.break&&t<n&&e.push(null),e.push(this.right)}balanced(t,e){return t.size>2*e.size||e.size>2*t.size?Js.of(this.break?[t,null,e]:[t,e]):(this.left=t,this.right=e,this.height=t.height+e.height,this.outdated=t.outdated||e.outdated,this.size=t.size+e.size,this.length=t.length+this.break+e.length,this)}updateHeight(t,e=0,i=!1,n){let{left:s,right:r}=this,o=e+s.length+this.break,a=null;return n&&n.from<=e+s.length&&n.more?a=s=s.updateHeight(t,e,i,n):s.updateHeight(t,e,i),n&&n.from<=o+r.length&&n.more?a=r=r.updateHeight(t,o,i,n):r.updateHeight(t,o,i),a?this.balanced(s,r):(this.height=this.left.height+this.right.height,this.outdated=!1,this)}toString(){return this.left+(this.break?" ":"-")+this.right}}function sr(t,e){let i,n;null==t[e]&&(i=t[e-1])instanceof ir&&(n=t[e+1])instanceof ir&&t.splice(e-1,3,new ir(i.length+1+n.length))}class rr{constructor(t,e){this.pos=t,this.oracle=e,this.nodes=[],this.lineStart=-1,this.lineEnd=-1,this.covering=null,this.writtenTo=t}get isCovered(){return this.covering&&this.nodes[this.nodes.length-1]==this.covering}span(t,e){if(this.lineStart>-1){let t=Math.min(e,this.lineEnd),i=this.nodes[this.nodes.length-1];i instanceof er?i.length+=t-this.pos:(t>this.pos||!this.isCovered)&&this.nodes.push(new er(t-this.pos,-1)),this.writtenTo=t,e>t&&(this.nodes.push(null),this.writtenTo++,this.lineStart=-1)}this.pos=e}point(t,e,i){if(t<e||i.heightRelevant){let n=i.widget?i.widget.estimatedHeight:0,s=i.widget?i.widget.lineBreaks:0;n<0&&(n=this.oracle.lineHeight);let r=e-t;i.block?this.addBlock(new tr(r,n,i)):(r||s||n>=5)&&this.addLineDeco(n,s,r)}else e>t&&this.span(t,e);this.lineEnd>-1&&this.lineEnd<this.pos&&(this.lineEnd=this.oracle.doc.lineAt(this.pos).to)}enterLine(){if(this.lineStart>-1)return;let{from:t,to:e}=this.oracle.doc.lineAt(this.pos);this.lineStart=t,this.lineEnd=e,this.writtenTo<t&&((this.writtenTo<t-1||null==this.nodes[this.nodes.length-1])&&this.nodes.push(this.blankContent(this.writtenTo,t-1)),this.nodes.push(null)),this.pos>t&&this.nodes.push(new er(this.pos-t,-1)),this.writtenTo=this.pos}blankContent(t,e){let i=new ir(e-t);return this.oracle.doc.lineAt(t).to==e&&(i.flags|=4),i}ensureLine(){this.enterLine();let t=this.nodes.length?this.nodes[this.nodes.length-1]:null;if(t instanceof er)return t;let e=new er(0,-1);return this.nodes.push(e),e}addBlock(t){this.enterLine();let e=t.deco;e&&e.startSide>0&&!this.isCovered&&this.ensureLine(),this.nodes.push(t),this.writtenTo=this.pos=this.pos+t.length,e&&e.endSide>0&&(this.covering=t)}addLineDeco(t,e,i){let n=this.ensureLine();n.length+=i,n.collapsed+=i,n.widgetHeight=Math.max(n.widgetHeight,t),n.breaks+=e,this.writtenTo=this.pos=this.pos+i}finish(t){let e=0==this.nodes.length?null:this.nodes[this.nodes.length-1];!(this.lineStart>-1)||e instanceof er||this.isCovered?(this.writtenTo<this.pos||null==e)&&this.nodes.push(this.blankContent(this.writtenTo,this.pos)):this.nodes.push(new er(0,-1));let i=t;for(let t of this.nodes)t instanceof er&&t.updateHeight(this.oracle,i),i+=t?t.length:1;return this.nodes}static build(t,e,i,n){let s=new rr(i,t);return Pe.spans(e,i,n,s,0),s.finish(i)}}class or{constructor(){this.changes=[]}compareRange(){}comparePoint(t,e,i,n){(t<e||i&&i.heightRelevant||n&&n.heightRelevant)&&nn(t,e,this.changes,5)}}function ar(t,e){let i=t.getBoundingClientRect(),n=t.ownerDocument,s=n.defaultView||window,r=Math.max(0,i.left),o=Math.min(s.innerWidth,i.right),a=Math.max(0,i.top),l=Math.min(s.innerHeight,i.bottom);for(let e=t.parentNode;e&&e!=n.body;)if(1==e.nodeType){let i=e,n=window.getComputedStyle(i);if((i.scrollHeight>i.clientHeight||i.scrollWidth>i.clientWidth)&&"visible"!=n.overflow){let n=i.getBoundingClientRect();r=Math.max(r,n.left),o=Math.min(o,n.right),a=Math.max(a,n.top),l=e==t.parentNode?n.bottom:Math.min(l,n.bottom)}e="absolute"==n.position||"fixed"==n.position?i.offsetParent:i.parentNode}else{if(11!=e.nodeType)break;e=e.host}return{left:r-i.left,right:Math.max(r,o)-i.left,top:a-(i.top+e),bottom:Math.max(a,l)-(i.top+e)}}function lr(t,e){let i=t.getBoundingClientRect();return{left:0,right:i.right-i.left,top:e,bottom:i.bottom-(i.top+e)}}class hr{constructor(t,e,i){this.from=t,this.to=e,this.size=i}static same(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++){let n=t[i],s=e[i];if(n.from!=s.from||n.to!=s.to||n.size!=s.size)return!1}return!0}draw(t,e){return Fi.replace({widget:new cr(this.size*(e?t.scaleY:t.scaleX),e)}).range(this.from,this.to)}}class cr extends Ni{constructor(t,e){super(),this.size=t,this.vertical=e}eq(t){return t.size==this.size&&t.vertical==this.vertical}toDOM(){let t=document.createElement("div");return this.vertical?t.style.height=this.size+"px":(t.style.width=this.size+"px",t.style.height="2px",t.style.display="inline-block"),t}get estimatedHeight(){return this.vertical?this.size:-1}}class Or{constructor(t){this.state=t,this.pixelViewport={left:0,right:window.innerWidth,top:0,bottom:0},this.inView=!0,this.paddingTop=0,this.paddingBottom=0,this.contentDOMWidth=0,this.contentDOMHeight=0,this.editorHeight=0,this.editorWidth=0,this.scrollTop=0,this.scrolledToBottom=!0,this.scaleX=1,this.scaleY=1,this.scrollAnchorPos=0,this.scrollAnchorHeight=-1,this.scaler=mr,this.scrollTarget=null,this.printing=!1,this.mustMeasureContent=!0,this.defaultTextDirection=an.LTR,this.visibleRanges=[],this.mustEnforceCursorAssoc=!1;let e=t.facet(Ln).some((t=>"function"!=typeof t&&"cm-lineWrapping"==t.class));this.heightOracle=new Is(e),this.stateDeco=t.facet(Gn).filter((t=>"function"!=typeof t)),this.heightMap=Js.empty().applyChanges(this.stateDeco,st.empty,this.heightOracle.setDoc(t.doc),[new Jn(0,0,0,t.doc.length)]),this.viewport=this.getViewport(0,null),this.updateViewportLines(),this.updateForViewport(),this.lineGaps=this.ensureLineGaps([]),this.lineGapDeco=Fi.set(this.lineGaps.map((t=>t.draw(this,!1)))),this.computeVisibleRanges()}updateForViewport(){let t=[this.viewport],{main:e}=this.state.selection;for(let i=0;i<=1;i++){let n=i?e.head:e.anchor;if(!t.some((({from:t,to:e})=>n>=t&&n<=e))){let{from:e,to:i}=this.lineBlockAt(n);t.push(new ur(e,i))}}this.viewports=t.sort(((t,e)=>t.from-e.from)),this.scaler=this.heightMap.height<=7e6?mr:new gr(this.heightOracle,this.heightMap,this.viewports)}updateViewportLines(){this.viewportLines=[],this.heightMap.forEachLine(this.viewport.from,this.viewport.to,this.heightOracle.setDoc(this.state.doc),0,0,(t=>{this.viewportLines.push(1==this.scaler.scale?t:br(t,this.scaler))}))}update(t,e=null){this.state=t.state;let i=this.stateDeco;this.stateDeco=this.state.facet(Gn).filter((t=>"function"!=typeof t));let n=t.changedRanges,s=Jn.extendWithRanges(n,function(t,e,i){let n=new or;return Pe.compare(t,e,i,n,0),n.changes}(i,this.stateDeco,t?t.changes:vt.empty(this.state.doc.length))),r=this.heightMap.height,o=this.scrolledToBottom?null:this.scrollAnchorAt(this.scrollTop);this.heightMap=this.heightMap.applyChanges(this.stateDeco,t.startState.doc,this.heightOracle.setDoc(this.state.doc),s),this.heightMap.height!=r&&(t.flags|=2),o?(this.scrollAnchorPos=t.changes.mapPos(o.from,-1),this.scrollAnchorHeight=o.top):(this.scrollAnchorPos=-1,this.scrollAnchorHeight=this.heightMap.height);let a=s.length?this.mapViewport(this.viewport,t.changes):this.viewport;(e&&(e.range.head<a.from||e.range.head>a.to)||!this.viewportIsAppropriate(a))&&(a=this.getViewport(0,e));let l=!t.changes.empty||2&t.flags||a.from!=this.viewport.from||a.to!=this.viewport.to;this.viewport=a,this.updateForViewport(),l&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(this.mapLineGaps(this.lineGaps,t.changes))),t.flags|=this.computeVisibleRanges(),e&&(this.scrollTarget=e),!this.mustEnforceCursorAssoc&&t.selectionSet&&t.view.lineWrapping&&t.state.selection.main.empty&&t.state.selection.main.assoc&&!t.state.facet(Mn)&&(this.mustEnforceCursorAssoc=!0)}measure(t){let e=t.contentDOM,i=window.getComputedStyle(e),n=this.heightOracle,s=i.whiteSpace;this.defaultTextDirection="rtl"==i.direction?an.RTL:an.LTR;let r=this.heightOracle.mustRefreshForWrapping(s),o=e.getBoundingClientRect(),a=r||this.mustMeasureContent||this.contentDOMHeight!=o.height;this.contentDOMHeight=o.height,this.mustMeasureContent=!1;let l=0,h=0;if(o.width&&o.height){let{scaleX:t,scaleY:i}=ai(e,o);this.scaleX==t&&this.scaleY==i||(this.scaleX=t,this.scaleY=i,l|=8,r=a=!0)}let c=(parseInt(i.paddingTop)||0)*this.scaleY,O=(parseInt(i.paddingBottom)||0)*this.scaleY;this.paddingTop==c&&this.paddingBottom==O||(this.paddingTop=c,this.paddingBottom=O,l|=10),this.editorWidth!=t.scrollDOM.clientWidth&&(n.lineWrapping&&(a=!0),this.editorWidth=t.scrollDOM.clientWidth,l|=8);let u=t.scrollDOM.scrollTop*this.scaleY;this.scrollTop!=u&&(this.scrollAnchorHeight=-1,this.scrollTop=u),this.scrolledToBottom=pi(t.scrollDOM);let f=(this.printing?lr:ar)(e,this.paddingTop),d=f.top-this.pixelViewport.top,p=f.bottom-this.pixelViewport.bottom;this.pixelViewport=f;let m=this.pixelViewport.bottom>this.pixelViewport.top&&this.pixelViewport.right>this.pixelViewport.left;if(m!=this.inView&&(this.inView=m,m&&(a=!0)),!this.inView&&!this.scrollTarget)return 0;let g=o.width;if(this.contentDOMWidth==g&&this.editorHeight==t.scrollDOM.clientHeight||(this.contentDOMWidth=o.width,this.editorHeight=t.scrollDOM.clientHeight,l|=8),a){let e=t.docView.measureVisibleLineHeights(this.viewport);if(n.mustRefreshForHeights(e)&&(r=!0),r||n.lineWrapping&&Math.abs(g-this.contentDOMWidth)>n.charWidth){let{lineHeight:i,charWidth:o,textHeight:a}=t.docView.measureTextSize();r=i>0&&n.refresh(s,i,o,a,g/o,e),r&&(t.docView.minWidth=0,l|=8)}d>0&&p>0?h=Math.max(d,p):d<0&&p<0&&(h=Math.min(d,p)),n.heightChanged=!1;for(let i of this.viewports){let s=i.from==this.viewport.from?e:t.docView.measureVisibleLineHeights(i);this.heightMap=(r?Js.empty().applyChanges(this.stateDeco,st.empty,this.heightOracle,[new Jn(0,0,0,t.state.doc.length)]):this.heightMap).updateHeight(n,0,r,new Ns(i.from,s))}n.heightChanged&&(l|=2)}let b=!this.viewportIsAppropriate(this.viewport,h)||this.scrollTarget&&(this.scrollTarget.range.head<this.viewport.from||this.scrollTarget.range.head>this.viewport.to);return b&&(this.viewport=this.getViewport(h,this.scrollTarget)),this.updateForViewport(),(2&l||b)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(r?[]:this.lineGaps,t)),l|=this.computeVisibleRanges(),this.mustEnforceCursorAssoc&&(this.mustEnforceCursorAssoc=!1,t.docView.enforceCursorAssoc()),l}get visibleTop(){return this.scaler.fromDOM(this.pixelViewport.top)}get visibleBottom(){return this.scaler.fromDOM(this.pixelViewport.bottom)}getViewport(t,e){let i=.5-Math.max(-.5,Math.min(.5,t/1e3/2)),n=this.heightMap,s=this.heightOracle,{visibleTop:r,visibleBottom:o}=this,a=new ur(n.lineAt(r-1e3*i,Fs.ByHeight,s,0,0).from,n.lineAt(o+1e3*(1-i),Fs.ByHeight,s,0,0).to);if(e){let{head:t}=e.range;if(t<a.from||t>a.to){let i,r=Math.min(this.editorHeight,this.pixelViewport.bottom-this.pixelViewport.top),o=n.lineAt(t,Fs.ByPos,s,0,0);i="center"==e.y?(o.top+o.bottom)/2-r/2:"start"==e.y||"nearest"==e.y&&t<a.from?o.top:o.bottom-r,a=new ur(n.lineAt(i-500,Fs.ByHeight,s,0,0).from,n.lineAt(i+r+500,Fs.ByHeight,s,0,0).to)}}return a}mapViewport(t,e){let i=e.mapPos(t.from,-1),n=e.mapPos(t.to,1);return new ur(this.heightMap.lineAt(i,Fs.ByPos,this.heightOracle,0,0).from,this.heightMap.lineAt(n,Fs.ByPos,this.heightOracle,0,0).to)}viewportIsAppropriate({from:t,to:e},i=0){if(!this.inView)return!0;let{top:n}=this.heightMap.lineAt(t,Fs.ByPos,this.heightOracle,0,0),{bottom:s}=this.heightMap.lineAt(e,Fs.ByPos,this.heightOracle,0,0),{visibleTop:r,visibleBottom:o}=this;return(0==t||n<=r-Math.max(10,Math.min(-i,250)))&&(e==this.state.doc.length||s>=o+Math.max(10,Math.min(i,250)))&&n>r-2e3&&s<o+2e3}mapLineGaps(t,e){if(!t.length||e.empty)return t;let i=[];for(let n of t)e.touchesRange(n.from,n.to)||i.push(new hr(e.mapPos(n.from),e.mapPos(n.to),n.size));return i}ensureLineGaps(t,e){let i=this.heightOracle.lineWrapping,n=i?1e4:2e3,s=n>>1,r=n<<1;if(this.defaultTextDirection!=an.LTR&&!i)return[];let o=[],a=(n,r,l,h)=>{if(r-n<s)return;let c=this.state.selection.main,O=[c.from];c.empty||O.push(c.to);for(let t of O)if(t>n&&t<r)return a(n,t-10,l,h),void a(t+10,r,l,h);let u=function(t,e){for(let i of t)if(e(i))return i;return}(t,(t=>t.from>=l.from&&t.to<=l.to&&Math.abs(t.from-n)<s&&Math.abs(t.to-r)<s&&!O.some((e=>t.from<e&&t.to>e))));if(!u){if(r<l.to&&e&&i&&e.visibleRanges.some((t=>t.from<=r&&t.to>=r))){let t=e.moveToLineBoundary(Tt.cursor(r),!1,!0).head;t>n&&(r=t)}u=new hr(n,r,this.gapSize(l,n,r,h))}o.push(u)};for(let t of this.viewportLines){if(t.length<r)continue;let e=fr(t.from,t.to,this.stateDeco);if(e.total<r)continue;let s,o,l=this.scrollTarget?this.scrollTarget.range.head:null;if(i){let i,r,a=n/this.heightOracle.lineLength*this.heightOracle.lineHeight;if(null!=l){let n=pr(e,l),s=((this.visibleBottom-this.visibleTop)/2+a)/t.height;i=n-s,r=n+s}else i=(this.visibleTop-t.top-a)/t.height,r=(this.visibleBottom-t.top+a)/t.height;s=dr(e,i),o=dr(e,r)}else{let t,i,r=e.total*this.heightOracle.charWidth,a=n*this.heightOracle.charWidth;if(null!=l){let n=pr(e,l),s=((this.pixelViewport.right-this.pixelViewport.left)/2+a)/r;t=n-s,i=n+s}else t=(this.pixelViewport.left-a)/r,i=(this.pixelViewport.right+a)/r;s=dr(e,t),o=dr(e,i)}s>t.from&&a(t.from,s,t,e),o<t.to&&a(o,t.to,t,e)}return o}gapSize(t,e,i,n){let s=pr(n,i)-pr(n,e);return this.heightOracle.lineWrapping?t.height*s:n.total*this.heightOracle.charWidth*s}updateLineGaps(t){hr.same(t,this.lineGaps)||(this.lineGaps=t,this.lineGapDeco=Fi.set(t.map((t=>t.draw(this,this.heightOracle.lineWrapping)))))}computeVisibleRanges(){let t=this.stateDeco;this.lineGaps.length&&(t=t.concat(this.lineGapDeco));let e=[];Pe.spans(t,this.viewport.from,this.viewport.to,{span(t,i){e.push({from:t,to:i})},point(){}},20);let i=e.length!=this.visibleRanges.length||this.visibleRanges.some(((t,i)=>t.from!=e[i].from||t.to!=e[i].to));return this.visibleRanges=e,i?4:0}lineBlockAt(t){return t>=this.viewport.from&&t<=this.viewport.to&&this.viewportLines.find((e=>e.from<=t&&e.to>=t))||br(this.heightMap.lineAt(t,Fs.ByPos,this.heightOracle,0,0),this.scaler)}lineBlockAtHeight(t){return br(this.heightMap.lineAt(this.scaler.fromDOM(t),Fs.ByHeight,this.heightOracle,0,0),this.scaler)}scrollAnchorAt(t){let e=this.lineBlockAtHeight(t+8);return e.from>=this.viewport.from||this.viewportLines[0].top-t>200?e:this.viewportLines[0]}elementAtHeight(t){return br(this.heightMap.blockAt(this.scaler.fromDOM(t),this.heightOracle,0,0),this.scaler)}get docHeight(){return this.scaler.toDOM(this.heightMap.height)}get contentHeight(){return this.docHeight+this.paddingTop+this.paddingBottom}}class ur{constructor(t,e){this.from=t,this.to=e}}function fr(t,e,i){let n=[],s=t,r=0;return Pe.spans(i,t,e,{span(){},point(t,e){t>s&&(n.push({from:s,to:t}),r+=t-s),s=e}},20),s<e&&(n.push({from:s,to:e}),r+=e-s),{total:r,ranges:n}}function dr({total:t,ranges:e},i){if(i<=0)return e[0].from;if(i>=1)return e[e.length-1].to;let n=Math.floor(t*i);for(let t=0;;t++){let{from:i,to:s}=e[t],r=s-i;if(n<=r)return i+n;n-=r}}function pr(t,e){let i=0;for(let{from:n,to:s}of t.ranges){if(e<=s){i+=e-n;break}i+=s-n}return i/t.total}const mr={toDOM:t=>t,fromDOM:t=>t,scale:1};class gr{constructor(t,e,i){let n=0,s=0,r=0;this.viewports=i.map((({from:i,to:s})=>{let r=e.lineAt(i,Fs.ByPos,t,0,0).top,o=e.lineAt(s,Fs.ByPos,t,0,0).bottom;return n+=o-r,{from:i,to:s,top:r,bottom:o,domTop:0,domBottom:0}})),this.scale=(7e6-n)/(e.height-n);for(let t of this.viewports)t.domTop=r+(t.top-s)*this.scale,r=t.domBottom=t.domTop+(t.bottom-t.top),s=t.bottom}toDOM(t){for(let e=0,i=0,n=0;;e++){let s=e<this.viewports.length?this.viewports[e]:null;if(!s||t<s.top)return n+(t-i)*this.scale;if(t<=s.bottom)return s.domTop+(t-s.top);i=s.bottom,n=s.domBottom}}fromDOM(t){for(let e=0,i=0,n=0;;e++){let s=e<this.viewports.length?this.viewports[e]:null;if(!s||t<s.domTop)return i+(t-n)/this.scale;if(t<=s.domBottom)return s.top+(t-s.domTop);i=s.bottom,n=s.domBottom}}}function br(t,e){if(1==e.scale)return t;let i=e.toDOM(t.top),n=e.toDOM(t.bottom);return new Hs(t.from,t.length,i,n-i,Array.isArray(t._content)?t._content.map((t=>br(t,e))):t._content)}const Qr=At.define({combine:t=>t.join(" ")}),Sr=At.define({combine:t=>t.indexOf(!0)>-1}),vr=De.newName(),wr=De.newName(),yr=De.newName(),xr={"&light":"."+wr,"&dark":"."+yr};function kr(t,e,i){return new De(e,{finish:e=>/&/.test(e)?e.replace(/&\w*/,(e=>{if("&"==e)return t;if(!i||!i[e])throw new RangeError(`Unsupported selector: ${e}`);return i[e]})):t+" "+e})}const Pr=kr("."+vr,{"&":{position:"relative !important",boxSizing:"border-box","&.cm-focused":{outline:"1px dotted #212121"},display:"flex !important",flexDirection:"column"},".cm-scroller":{display:"flex !important",alignItems:"flex-start !important",fontFamily:"monospace",lineHeight:1.4,height:"100%",overflowX:"auto",position:"relative",zIndex:0},".cm-content":{margin:0,flexGrow:2,flexShrink:0,display:"block",whiteSpace:"pre",wordWrap:"normal",boxSizing:"border-box",minHeight:"100%",padding:"4px 0",outline:"none","&[contenteditable=true]":{WebkitUserModify:"read-write-plaintext-only"}},".cm-lineWrapping":{whiteSpace_fallback:"pre-wrap",whiteSpace:"break-spaces",wordBreak:"break-word",overflowWrap:"anywhere",flexShrink:1},"&light .cm-content":{caretColor:"black"},"&dark .cm-content":{caretColor:"white"},".cm-line":{display:"block",padding:"0 2px 0 6px"},".cm-layer":{position:"absolute",left:0,top:0,contain:"size style","& > *":{position:"absolute"}},"&light .cm-selectionBackground":{background:"#d9d9d9"},"&dark .cm-selectionBackground":{background:"#222"},"&light.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#d7d4f0"},"&dark.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#233"},".cm-cursorLayer":{pointerEvents:"none"},"&.cm-focused > .cm-scroller > .cm-cursorLayer":{animation:"steps(1) cm-blink 1.2s infinite"},"@keyframes cm-blink":{"0%":{},"50%":{opacity:0},"100%":{}},"@keyframes cm-blink2":{"0%":{},"50%":{opacity:0},"100%":{}},".cm-cursor, .cm-dropCursor":{borderLeft:"1.2px solid black",marginLeft:"-0.6px",pointerEvents:"none"},".cm-cursor":{display:"none"},"&dark .cm-cursor":{borderLeftColor:"#444"},".cm-dropCursor":{position:"absolute"},"&.cm-focused > .cm-scroller > .cm-cursorLayer .cm-cursor":{display:"block"},".cm-iso":{unicodeBidi:"isolate"},".cm-announced":{position:"fixed",top:"-10000px"},"@media print":{".cm-announced":{display:"none"}},"&light .cm-activeLine":{backgroundColor:"#cceeff44"},"&dark .cm-activeLine":{backgroundColor:"#99eeff33"},"&light .cm-specialChar":{color:"red"},"&dark .cm-specialChar":{color:"#f78"},".cm-gutters":{flexShrink:0,display:"flex",height:"100%",boxSizing:"border-box",insetInlineStart:0,zIndex:200},"&light .cm-gutters":{backgroundColor:"#f5f5f5",color:"#6c6c6c",borderRight:"1px solid #ddd"},"&dark .cm-gutters":{backgroundColor:"#333338",color:"#ccc"},".cm-gutter":{display:"flex !important",flexDirection:"column",flexShrink:0,boxSizing:"border-box",minHeight:"100%",overflow:"hidden"},".cm-gutterElement":{boxSizing:"border-box"},".cm-lineNumbers .cm-gutterElement":{padding:"0 3px 0 5px",minWidth:"20px",textAlign:"right",whiteSpace:"nowrap"},"&light .cm-activeLineGutter":{backgroundColor:"#e2f2ff"},"&dark .cm-activeLineGutter":{backgroundColor:"#222227"},".cm-panels":{boxSizing:"border-box",position:"sticky",left:0,right:0},"&light .cm-panels":{backgroundColor:"#f5f5f5",color:"black"},"&light .cm-panels-top":{borderBottom:"1px solid #ddd"},"&light .cm-panels-bottom":{borderTop:"1px solid #ddd"},"&dark .cm-panels":{backgroundColor:"#333338",color:"white"},".cm-tab":{display:"inline-block",overflow:"hidden",verticalAlign:"bottom"},".cm-widgetBuffer":{verticalAlign:"text-top",height:"1em",width:0,display:"inline"},".cm-placeholder":{color:"#888",display:"inline-block",verticalAlign:"top"},".cm-highlightSpace:before":{content:"attr(data-display)",position:"absolute",pointerEvents:"none",color:"#888"},".cm-highlightTab":{backgroundImage:'url(\'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="20"><path stroke="%23888" stroke-width="1" fill="none" d="M1 10H196L190 5M190 15L196 10M197 4L197 16"/></svg>\')',backgroundSize:"auto 100%",backgroundPosition:"right 90%",backgroundRepeat:"no-repeat"},".cm-trailingSpace":{backgroundColor:"#ff332255"},".cm-button":{verticalAlign:"middle",color:"inherit",fontSize:"70%",padding:".2em 1em",borderRadius:"1px"},"&light .cm-button":{backgroundImage:"linear-gradient(#eff1f5, #d9d9df)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#b4b4b4, #d0d3d6)"}},"&dark .cm-button":{backgroundImage:"linear-gradient(#393939, #111)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#111, #333)"}},".cm-textfield":{verticalAlign:"middle",color:"inherit",fontSize:"70%",border:"1px solid silver",padding:".2em .5em"},"&light .cm-textfield":{backgroundColor:"white"},"&dark .cm-textfield":{border:"1px solid #555",backgroundColor:"inherit"}},xr),$r="￿";class Zr{constructor(t,e){this.points=t,this.text="",this.lineSeparator=e.facet(Se.lineSeparator)}append(t){this.text+=t}lineBreak(){this.text+=$r}readRange(t,e){if(!t)return this;let i=t.parentNode;for(let n=t;;){this.findPointBefore(i,n);let t=this.text.length;this.readNode(n);let s=n.nextSibling;if(s==e)break;let r=bi.get(n),o=bi.get(s);(r&&o?r.breakAfter:(r?r.breakAfter:Xr(n))||Xr(s)&&("BR"!=n.nodeName||n.cmIgnore)&&this.text.length>t)&&this.lineBreak(),n=s}return this.findPointBefore(i,e),this}readTextNode(t){let e=t.nodeValue;for(let i of this.points)i.node==t&&(i.pos=this.text.length+Math.min(i.offset,e.length));for(let i=0,n=this.lineSeparator?null:/\r\n?|\n/g;;){let s,r=-1,o=1;if(this.lineSeparator?(r=e.indexOf(this.lineSeparator,i),o=this.lineSeparator.length):(s=n.exec(e))&&(r=s.index,o=s[0].length),this.append(e.slice(i,r<0?e.length:r)),r<0)break;if(this.lineBreak(),o>1)for(let e of this.points)e.node==t&&e.pos>this.text.length&&(e.pos-=o-1);i=r+o}}readNode(t){if(t.cmIgnore)return;let e=bi.get(t),i=e&&e.overrideDOMText;if(null!=i){this.findPointInside(t,i.length);for(let t=i.iter();!t.next().done;)t.lineBreak?this.lineBreak():this.append(t.value)}else 3==t.nodeType?this.readTextNode(t):"BR"==t.nodeName?t.nextSibling&&this.lineBreak():1==t.nodeType&&this.readRange(t.firstChild,null)}findPointBefore(t,e){for(let i of this.points)i.node==t&&t.childNodes[i.offset]==e&&(i.pos=this.text.length)}findPointInside(t,e){for(let i of this.points)(3==t.nodeType?i.node==t:t.contains(i.node))&&(i.pos=this.text.length+(Tr(t,i.node,i.offset)?e:0))}}function Tr(t,e,i){for(;;){if(!e||i<si(e))return!1;if(e==t)return!0;i=ii(e)+1,e=e.parentNode}}function Xr(t){return 1==t.nodeType&&/^(DIV|P|LI|UL|OL|BLOCKQUOTE|DD|DT|H\d|SECTION|PRE)$/.test(t.nodeName)}class Rr{constructor(t,e){this.node=t,this.offset=e,this.pos=-1}}class Ar{constructor(t,e,i,n){this.typeOver=n,this.bounds=null,this.text="";let{impreciseHead:s,impreciseAnchor:r}=t.docView;if(t.state.readOnly&&e>-1)this.newSel=null;else if(e>-1&&(this.bounds=t.docView.domBoundsAround(e,i,0))){let e=s||r?[]:function(t){let e=[];if(t.root.activeElement!=t.contentDOM)return e;let{anchorNode:i,anchorOffset:n,focusNode:s,focusOffset:r}=t.observer.selectionRange;i&&(e.push(new Rr(i,n)),s==i&&r==n||e.push(new Rr(s,r)));return e}(t),i=new Zr(e,t.state);i.readRange(this.bounds.startDOM,this.bounds.endDOM),this.text=i.text,this.newSel=function(t,e){if(0==t.length)return null;let i=t[0].pos,n=2==t.length?t[1].pos:i;return i>-1&&n>-1?Tt.single(i+e,n+e):null}(e,this.bounds.from)}else{let e=t.observer.selectionRange,i=s&&s.node==e.focusNode&&s.offset==e.focusOffset||!Ke(t.contentDOM,e.focusNode)?t.state.selection.main.head:t.docView.posFromDOM(e.focusNode,e.focusOffset),n=r&&r.node==e.anchorNode&&r.offset==e.anchorOffset||!Ke(t.contentDOM,e.anchorNode)?t.state.selection.main.anchor:t.docView.posFromDOM(e.anchorNode,e.anchorOffset),o=t.viewport;if(Mi.ios&&t.state.selection.main.empty&&i!=n&&(o.from>0||o.to<t.state.doc.length)){let e=o.from-Math.min(i,n),s=o.to-Math.max(i,n);0!=e&&1!=e||0!=s&&-1!=s||(i=0,n=t.state.doc.length)}this.newSel=Tt.single(n,i)}}}function Cr(t,e){let i,{newSel:n}=e,s=t.state.selection.main,r=t.inputState.lastKeyTime>Date.now()-100?t.inputState.lastKeyCode:-1;if(e.bounds){let{from:n,to:o}=e.bounds,a=s.from,l=null;(8===r||Mi.android&&e.text.length<o-n)&&(a=s.to,l="end");let h=function(t,e,i,n){let s=Math.min(t.length,e.length),r=0;for(;r<s&&t.charCodeAt(r)==e.charCodeAt(r);)r++;if(r==s&&t.length==e.length)return null;let o=t.length,a=e.length;for(;o>0&&a>0&&t.charCodeAt(o-1)==e.charCodeAt(a-1);)o--,a--;if("end"==n){i-=o+Math.max(0,r-Math.min(o,a))-r}if(o<r&&t.length<e.length){r-=i<=r&&i>=o?r-i:0,a=r+(a-o),o=r}else if(a<r){r-=i<=r&&i>=a?r-i:0,o=r+(o-a),a=r}return{from:r,toA:o,toB:a}}(t.state.doc.sliceString(n,o,$r),e.text,a-n,l);h&&(Mi.chrome&&13==r&&h.toB==h.from+2&&e.text.slice(h.from,h.toB)==$r+$r&&h.toB--,i={from:n+h.from,to:n+h.toA,insert:st.of(e.text.slice(h.from,h.toB).split($r))})}else n&&(!t.hasFocus&&t.state.facet(zn)||n.main.eq(s))&&(n=null);if(!i&&!n)return!1;if(!i&&e.typeOver&&!s.empty&&n&&n.main.empty?i={from:s.from,to:s.to,insert:t.state.doc.slice(s.from,s.to)}:i&&i.from>=s.from&&i.to<=s.to&&(i.from!=s.from||i.to!=s.to)&&s.to-s.from-(i.to-i.from)<=4?i={from:s.from,to:s.to,insert:t.state.doc.slice(s.from,i.from).append(i.insert).append(t.state.doc.slice(i.to,s.to))}:(Mi.mac||Mi.android)&&i&&i.from==i.to&&i.from==s.head-1&&/^\. ?$/.test(i.insert.toString())&&"off"==t.contentDOM.getAttribute("autocorrect")?(n&&2==i.insert.length&&(n=Tt.single(n.main.anchor-1,n.main.head-1)),i={from:s.from,to:s.to,insert:st.of([" "])}):Mi.chrome&&i&&i.from==i.to&&i.from==s.head&&"\n "==i.insert.toString()&&t.lineWrapping&&(n&&(n=Tt.single(n.main.anchor-1,n.main.head-1)),i={from:s.from,to:s.to,insert:st.of([" "])}),i){if(Mi.ios&&t.inputState.flushIOSKey())return!0;if(Mi.android&&(i.from==s.from&&i.to==s.to&&1==i.insert.length&&2==i.insert.lines&&fi(t.contentDOM,"Enter",13)||(i.from==s.from-1&&i.to==s.to&&0==i.insert.length||8==r&&i.insert.length<i.to-i.from&&i.to>s.head)&&fi(t.contentDOM,"Backspace",8)||i.from==s.from&&i.to==s.to+1&&0==i.insert.length&&fi(t.contentDOM,"Delete",46)))return!0;let e,o=i.insert.toString();t.inputState.composing>=0&&t.inputState.composing++;let a=()=>e||(e=function(t,e,i){let n,s=t.state,r=s.selection.main;if(e.from>=r.from&&e.to<=r.to&&e.to-e.from>=(r.to-r.from)/3&&(!i||i.main.empty&&i.main.from==e.from+e.insert.length)&&t.inputState.composing<0){let i=r.from<e.from?s.sliceDoc(r.from,e.from):"",o=r.to>e.to?s.sliceDoc(e.to,r.to):"";n=s.replaceSelection(t.state.toText(i+e.insert.sliceString(0,void 0,t.state.lineBreak)+o))}else{let o=s.changes(e),a=i&&i.main.to<=o.newLength?i.main:void 0;if(s.selection.ranges.length>1&&t.inputState.composing>=0&&e.to<=r.to&&e.to>=r.to-10){let l,h=t.state.sliceDoc(e.from,e.to),c=i&&ns(t,i.main.head);if(c){let t=e.insert.length-(e.to-e.from);l={from:c.from,to:c.to-t}}else l=t.state.doc.lineAt(r.head);let O=r.to-e.to,u=r.to-r.from;n=s.changeByRange((i=>{if(i.from==r.from&&i.to==r.to)return{changes:o,range:a||i.map(o)};let n=i.to-O,c=n-h.length;if(i.to-i.from!=u||t.state.sliceDoc(c,n)!=h||i.to>=l.from&&i.from<=l.to)return{range:i};let f=s.changes({from:c,to:n,insert:e.insert}),d=i.to-r.to;return{changes:f,range:a?Tt.range(Math.max(0,a.anchor+d),Math.max(0,a.head+d)):i.map(f)}}))}else n={changes:o,selection:a&&s.selection.replaceRange(a)}}let o="input.type";(t.composing||t.inputState.compositionPendingChange&&t.inputState.compositionEndedAt>Date.now()-50)&&(t.inputState.compositionPendingChange=!1,o+=".compose",t.inputState.compositionFirstChange&&(o+=".start",t.inputState.compositionFirstChange=!1));return s.update(n,{userEvent:o,scrollIntoView:!0})}(t,i,n));return t.state.facet(Rn).some((e=>e(t,i.from,i.to,o,a)))||t.dispatch(a()),!0}if(n&&!n.main.eq(s)){let e=!1,i="select";return t.inputState.lastSelectionTime>Date.now()-50&&("select"==t.inputState.lastSelectionOrigin&&(e=!0),i=t.inputState.lastSelectionOrigin),t.dispatch({selection:n,scrollIntoView:e,userEvent:i}),!0}return!1}const Mr={childList:!0,characterData:!0,subtree:!0,attributes:!0,characterDataOldValue:!0},qr=Mi.ie&&Mi.ie_version<=11;class Vr{constructor(t){this.view=t,this.active=!1,this.selectionRange=new li,this.selectionChanged=!1,this.delayedFlush=-1,this.resizeTimeout=-1,this.queue=[],this.delayedAndroidKey=null,this.flushingAndroidKey=-1,this.lastChange=0,this.scrollTargets=[],this.intersection=null,this.resizeScroll=null,this.intersecting=!1,this.gapIntersection=null,this.gaps=[],this.parentCheck=-1,this.dom=t.contentDOM,this.observer=new MutationObserver((e=>{for(let t of e)this.queue.push(t);(Mi.ie&&Mi.ie_version<=11||Mi.ios&&t.composing)&&e.some((t=>"childList"==t.type&&t.removedNodes.length||"characterData"==t.type&&t.oldValue.length>t.target.nodeValue.length))?this.flushSoon():this.flush()})),qr&&(this.onCharData=t=>{this.queue.push({target:t.target,type:"characterData",oldValue:t.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this),this.onResize=this.onResize.bind(this),this.onPrint=this.onPrint.bind(this),this.onScroll=this.onScroll.bind(this),"function"==typeof ResizeObserver&&(this.resizeScroll=new ResizeObserver((()=>{var t;(null===(t=this.view.docView)||void 0===t?void 0:t.lastUpdate)<Date.now()-75&&this.onResize()})),this.resizeScroll.observe(t.scrollDOM)),this.addWindowListeners(this.win=t.win),this.start(),"function"==typeof IntersectionObserver&&(this.intersection=new IntersectionObserver((t=>{this.parentCheck<0&&(this.parentCheck=setTimeout(this.listenForScroll.bind(this),1e3)),t.length>0&&t[t.length-1].intersectionRatio>0!=this.intersecting&&(this.intersecting=!this.intersecting,this.intersecting!=this.view.inView&&this.onScrollChanged(document.createEvent("Event")))}),{threshold:[0,.001]}),this.intersection.observe(this.dom),this.gapIntersection=new IntersectionObserver((t=>{t.length>0&&t[t.length-1].intersectionRatio>0&&this.onScrollChanged(document.createEvent("Event"))}),{})),this.listenForScroll(),this.readSelectionRange()}onScrollChanged(t){this.view.inputState.runHandlers("scroll",t),this.intersecting&&this.view.measure()}onScroll(t){this.intersecting&&this.flush(!1),this.onScrollChanged(t)}onResize(){this.resizeTimeout<0&&(this.resizeTimeout=setTimeout((()=>{this.resizeTimeout=-1,this.view.requestMeasure()}),50))}onPrint(){this.view.viewState.printing=!0,this.view.measure(),setTimeout((()=>{this.view.viewState.printing=!1,this.view.requestMeasure()}),500)}updateGaps(t){if(this.gapIntersection&&(t.length!=this.gaps.length||this.gaps.some(((e,i)=>e!=t[i])))){this.gapIntersection.disconnect();for(let e of t)this.gapIntersection.observe(e);this.gaps=t}}onSelectionChange(t){let e=this.selectionChanged;if(!this.readSelectionRange()||this.delayedAndroidKey)return;let{view:i}=this,n=this.selectionRange;if(i.state.facet(zn)?i.root.activeElement!=this.dom:!Je(i.dom,n))return;let s=n.anchorNode&&i.docView.nearest(n.anchorNode);s&&s.ignoreEvent(t)?e||(this.selectionChanged=!1):(Mi.ie&&Mi.ie_version<=11||Mi.android&&Mi.chrome)&&!i.state.selection.main.empty&&n.focusNode&&ei(n.focusNode,n.focusOffset,n.anchorNode,n.anchorOffset)?this.flushSoon():this.flush(!1)}readSelectionRange(){let{view:t}=this,e=Mi.safari&&11==t.root.nodeType&&function(t){let e=t.activeElement;for(;e&&e.shadowRoot;)e=e.shadowRoot.activeElement;return e}(this.dom.ownerDocument)==this.dom&&function(t){let e=null;function i(t){t.preventDefault(),t.stopImmediatePropagation(),e=t.getTargetRanges()[0]}if(t.contentDOM.addEventListener("beforeinput",i,!0),t.dom.ownerDocument.execCommand("indent"),t.contentDOM.removeEventListener("beforeinput",i,!0),!e)return null;let n=e.startContainer,s=e.startOffset,r=e.endContainer,o=e.endOffset,a=t.docView.domAtPos(t.state.selection.main.anchor);ei(a.node,a.offset,r,o)&&([n,s,r,o]=[r,o,n,s]);return{anchorNode:n,anchorOffset:s,focusNode:r,focusOffset:o}}(this.view)||Fe(t.root);if(!e||this.selectionRange.eq(e))return!1;let i=Je(this.dom,e);return i&&!this.selectionChanged&&t.inputState.lastFocusTime>Date.now()-200&&t.inputState.lastTouchTime<Date.now()-300&&function(t,e){let i=e.focusNode,n=e.focusOffset;if(!i||e.anchorNode!=i||e.anchorOffset!=n)return!1;for(n=Math.min(n,si(i));;)if(n){if(1!=i.nodeType)return!1;let t=i.childNodes[n-1];"false"==t.contentEditable?n--:(i=t,n=si(i))}else{if(i==t)return!0;n=ii(i),i=i.parentNode}}(this.dom,e)?(this.view.inputState.lastFocusTime=0,t.docView.updateSelection(),!1):(this.selectionRange.setRange(e),i&&(this.selectionChanged=!0),!0)}setSelectionRange(t,e){this.selectionRange.set(t.node,t.offset,e.node,e.offset),this.selectionChanged=!1}clearSelectionRange(){this.selectionRange.set(null,0,null,0)}listenForScroll(){this.parentCheck=-1;let t=0,e=null;for(let i=this.dom;i;)if(1==i.nodeType)!e&&t<this.scrollTargets.length&&this.scrollTargets[t]==i?t++:e||(e=this.scrollTargets.slice(0,t)),e&&e.push(i),i=i.assignedSlot||i.parentNode;else{if(11!=i.nodeType)break;i=i.host}if(t<this.scrollTargets.length&&!e&&(e=this.scrollTargets.slice(0,t)),e){for(let t of this.scrollTargets)t.removeEventListener("scroll",this.onScroll);for(let t of this.scrollTargets=e)t.addEventListener("scroll",this.onScroll)}}ignore(t){if(!this.active)return t();try{return this.stop(),t()}finally{this.start(),this.clear()}}start(){this.active||(this.observer.observe(this.dom,Mr),qr&&this.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.active=!0)}stop(){this.active&&(this.active=!1,this.observer.disconnect(),qr&&this.dom.removeEventListener("DOMCharacterDataModified",this.onCharData))}clear(){this.processRecords(),this.queue.length=0,this.selectionChanged=!1}delayAndroidKey(t,e){var i;if(!this.delayedAndroidKey){let t=()=>{let t=this.delayedAndroidKey;if(t){this.clearDelayedAndroidKey(),this.view.inputState.lastKeyCode=t.keyCode,this.view.inputState.lastKeyTime=Date.now(),!this.flush()&&t.force&&fi(this.dom,t.key,t.keyCode)}};this.flushingAndroidKey=this.view.win.requestAnimationFrame(t)}this.delayedAndroidKey&&"Enter"!=t||(this.delayedAndroidKey={key:t,keyCode:e,force:this.lastChange<Date.now()-50||!!(null===(i=this.delayedAndroidKey)||void 0===i?void 0:i.force)})}clearDelayedAndroidKey(){this.win.cancelAnimationFrame(this.flushingAndroidKey),this.delayedAndroidKey=null,this.flushingAndroidKey=-1}flushSoon(){this.delayedFlush<0&&(this.delayedFlush=this.view.win.requestAnimationFrame((()=>{this.delayedFlush=-1,this.flush()})))}forceFlush(){this.delayedFlush>=0&&(this.view.win.cancelAnimationFrame(this.delayedFlush),this.delayedFlush=-1),this.flush()}pendingRecords(){for(let t of this.observer.takeRecords())this.queue.push(t);return this.queue}processRecords(){let t=this.pendingRecords();t.length&&(this.queue=[]);let e=-1,i=-1,n=!1;for(let s of t){let t=this.readMutation(s);t&&(t.typeOver&&(n=!0),-1==e?({from:e,to:i}=t):(e=Math.min(t.from,e),i=Math.max(t.to,i)))}return{from:e,to:i,typeOver:n}}readChange(){let{from:t,to:e,typeOver:i}=this.processRecords(),n=this.selectionChanged&&Je(this.dom,this.selectionRange);if(t<0&&!n)return null;t>-1&&(this.lastChange=Date.now()),this.view.inputState.lastFocusTime=0,this.selectionChanged=!1;let s=new Ar(this.view,t,e,i);return this.view.docView.domChanged={newSel:s.newSel?s.newSel.main:null},s}flush(t=!0){if(this.delayedFlush>=0||this.delayedAndroidKey)return!1;t&&this.readSelectionRange();let e=this.readChange();if(!e)return this.view.requestMeasure(),!1;let i=this.view.state,n=Cr(this.view,e);return this.view.state==i&&this.view.update([]),n}readMutation(t){let e=this.view.docView.nearest(t.target);if(!e||e.ignoreMutation(t))return null;if(e.markDirty("attributes"==t.type),"attributes"==t.type&&(e.flags|=4),"childList"==t.type){let i=Yr(e,t.previousSibling||t.target.previousSibling,-1),n=Yr(e,t.nextSibling||t.target.nextSibling,1);return{from:i?e.posAfter(i):e.posAtStart,to:n?e.posBefore(n):e.posAtEnd,typeOver:!1}}return"characterData"==t.type?{from:e.posAtStart,to:e.posAtEnd,typeOver:t.target.nodeValue==t.oldValue}:null}setWindow(t){t!=this.win&&(this.removeWindowListeners(this.win),this.win=t,this.addWindowListeners(this.win))}addWindowListeners(t){t.addEventListener("resize",this.onResize),t.addEventListener("beforeprint",this.onPrint),t.addEventListener("scroll",this.onScroll),t.document.addEventListener("selectionchange",this.onSelectionChange)}removeWindowListeners(t){t.removeEventListener("scroll",this.onScroll),t.removeEventListener("resize",this.onResize),t.removeEventListener("beforeprint",this.onPrint),t.document.removeEventListener("selectionchange",this.onSelectionChange)}destroy(){var t,e,i;this.stop(),null===(t=this.intersection)||void 0===t||t.disconnect(),null===(e=this.gapIntersection)||void 0===e||e.disconnect(),null===(i=this.resizeScroll)||void 0===i||i.disconnect();for(let t of this.scrollTargets)t.removeEventListener("scroll",this.onScroll);this.removeWindowListeners(this.win),clearTimeout(this.parentCheck),clearTimeout(this.resizeTimeout),this.win.cancelAnimationFrame(this.delayedFlush),this.win.cancelAnimationFrame(this.flushingAndroidKey)}}function Yr(t,e,i){for(;e;){let n=bi.get(e);if(n&&n.parent==t)return n;let s=e.parentNode;e=s!=t.dom?s:i>0?e.nextSibling:e.previousSibling}return null}class zr{get state(){return this.viewState.state}get viewport(){return this.viewState.viewport}get visibleRanges(){return this.viewState.visibleRanges}get inView(){return this.viewState.inView}get composing(){return this.inputState.composing>0}get compositionStarted(){return this.inputState.composing>=0}get root(){return this._root}get win(){return this.dom.ownerDocument.defaultView||window}constructor(t={}){this.plugins=[],this.pluginMap=new Map,this.editorAttrs={},this.contentAttrs={},this.bidiCache=[],this.destroyed=!1,this.updateState=2,this.measureScheduled=-1,this.measureRequests=[],this.contentDOM=document.createElement("div"),this.scrollDOM=document.createElement("div"),this.scrollDOM.tabIndex=-1,this.scrollDOM.className="cm-scroller",this.scrollDOM.appendChild(this.contentDOM),this.announceDOM=document.createElement("div"),this.announceDOM.className="cm-announced",this.announceDOM.setAttribute("aria-live","polite"),this.dom=document.createElement("div"),this.dom.appendChild(this.announceDOM),this.dom.appendChild(this.scrollDOM),t.parent&&t.parent.appendChild(this.dom);let{dispatch:e}=t;this.dispatchTransactions=t.dispatchTransactions||e&&(t=>t.forEach((t=>e(t,this))))||(t=>this.update(t)),this.dispatch=this.dispatch.bind(this),this._root=t.root||function(t){for(;t;){if(t&&(9==t.nodeType||11==t.nodeType&&t.host))return t;t=t.assignedSlot||t.parentNode}return null}(t.parent)||document,this.viewState=new Or(t.state||Se.create(t)),t.scrollTo&&t.scrollTo.is(Vn)&&(this.viewState.scrollTarget=t.scrollTo.value.clip(this.viewState.state)),this.plugins=this.state.facet(jn).map((t=>new Dn(t)));for(let t of this.plugins)t.update(this);this.observer=new Vr(this),this.inputState=new Qs(this),this.inputState.ensureHandlers(this.plugins),this.docView=new es(this),this.mountStyles(),this.updateAttrs(),this.updateState=0,this.requestMeasure()}dispatch(...t){let e=1==t.length&&t[0]instanceof he?t:1==t.length&&Array.isArray(t[0])?t[0]:[this.state.update(...t)];this.dispatchTransactions(e,this)}update(t){if(0!=this.updateState)throw new Error("Calls to EditorView.update are not allowed while an update is in progress");let e,i=!1,n=!1,s=this.state;for(let e of t){if(e.startState!=s)throw new RangeError("Trying to update state with a transaction that doesn't start from the previous state.");s=e.state}if(this.destroyed)return void(this.viewState.state=s);let r=this.hasFocus,o=0,a=null;t.some((t=>t.annotation(Ws)))?(this.inputState.notifiedFocused=r,o=1):r!=this.inputState.notifiedFocused&&(this.inputState.notifiedFocused=r,a=Ls(s,r),a||(o=1));let l=this.observer.delayedAndroidKey,h=null;if(l?(this.observer.clearDelayedAndroidKey(),h=this.observer.readChange(),(h&&!this.state.doc.eq(s.doc)||!this.state.selection.eq(s.selection))&&(h=null)):this.observer.clear(),s.facet(Se.phrases)!=this.state.facet(Se.phrases))return this.setState(s);e=ts.create(this,s,t),e.flags|=o;let c=this.viewState.scrollTarget;try{this.updateState=2;for(let e of t){if(c&&(c=c.map(e.changes)),e.scrollIntoView){let{main:t}=e.state.selection;c=new qn(t.empty?t:Tt.cursor(t.head,t.head>t.anchor?-1:1))}for(let t of e.effects)t.is(Vn)&&(c=t.value.clip(this.state))}this.viewState.update(e,c),this.bidiCache=Er.update(this.bidiCache,e.changes),e.empty||(this.updatePlugins(e),this.inputState.update(e)),i=this.docView.update(e),this.state.facet(Kn)!=this.styleModules&&this.mountStyles(),n=this.updateAttrs(),this.showAnnouncements(t),this.docView.updateSelection(i,t.some((t=>t.isUserEvent("select.pointer"))))}finally{this.updateState=0}if(e.startState.facet(Qr)!=e.state.facet(Qr)&&(this.viewState.mustMeasureContent=!0),(i||n||c||this.viewState.mustEnforceCursorAssoc||this.viewState.mustMeasureContent)&&this.requestMeasure(),!e.empty)for(let t of this.state.facet(Xn))try{t(e)}catch(t){Yn(this.state,t,"update listener")}(a||h)&&Promise.resolve().then((()=>{a&&this.state==a.startState&&this.dispatch(a),h&&!Cr(this,h)&&l.force&&fi(this.contentDOM,l.key,l.keyCode)}))}setState(t){if(0!=this.updateState)throw new Error("Calls to EditorView.setState are not allowed while an update is in progress");if(this.destroyed)return void(this.viewState.state=t);this.updateState=2;let e=this.hasFocus;try{for(let t of this.plugins)t.destroy(this);this.viewState=new Or(t),this.plugins=t.facet(jn).map((t=>new Dn(t))),this.pluginMap.clear();for(let t of this.plugins)t.update(this);this.docView.destroy(),this.docView=new es(this),this.inputState.ensureHandlers(this.plugins),this.mountStyles(),this.updateAttrs(),this.bidiCache=[]}finally{this.updateState=0}e&&this.focus(),this.requestMeasure()}updatePlugins(t){let e=t.startState.facet(jn),i=t.state.facet(jn);if(e!=i){let n=[];for(let s of i){let i=e.indexOf(s);if(i<0)n.push(new Dn(s));else{let e=this.plugins[i];e.mustUpdate=t,n.push(e)}}for(let e of this.plugins)e.mustUpdate!=t&&e.destroy(this);this.plugins=n,this.pluginMap.clear()}else for(let e of this.plugins)e.mustUpdate=t;for(let t=0;t<this.plugins.length;t++)this.plugins[t].update(this);e!=i&&this.inputState.ensureHandlers(this.plugins)}measure(t=!0){if(this.destroyed)return;if(this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.observer.delayedAndroidKey)return this.measureScheduled=-1,void this.requestMeasure();this.measureScheduled=0,t&&this.observer.forceFlush();let e=null,i=this.scrollDOM,n=i.scrollTop*this.scaleY,{scrollAnchorPos:s,scrollAnchorHeight:r}=this.viewState;Math.abs(n-this.viewState.scrollTop)>1&&(r=-1),this.viewState.scrollAnchorHeight=-1;try{for(let t=0;;t++){if(r<0)if(pi(i))s=-1,r=this.viewState.heightMap.height;else{let t=this.viewState.scrollAnchorAt(n);s=t.from,r=t.top}this.updateState=1;let o=this.viewState.measure(this);if(!o&&!this.measureRequests.length&&null==this.viewState.scrollTarget)break;if(t>5){console.warn(this.measureRequests.length?"Measure loop restarted more than 5 times":"Viewport failed to stabilize");break}let a=[];4&o||([this.measureRequests,a]=[a,this.measureRequests]);let l=a.map((t=>{try{return t.read(this)}catch(t){return Yn(this.state,t),jr}})),h=ts.create(this,this.state,[]),c=!1;h.flags|=o,e?e.flags|=o:e=h,this.updateState=2,h.empty||(this.updatePlugins(h),this.inputState.update(h),this.updateAttrs(),c=this.docView.update(h));for(let t=0;t<a.length;t++)if(l[t]!=jr)try{let e=a[t];e.write&&e.write(l[t],this)}catch(t){Yn(this.state,t)}if(c&&this.docView.updateSelection(!0),!h.viewportChanged&&0==this.measureRequests.length){if(this.viewState.editorHeight){if(this.viewState.scrollTarget){this.docView.scrollIntoView(this.viewState.scrollTarget),this.viewState.scrollTarget=null,r=-1;continue}{let t=(s<0?this.viewState.heightMap.height:this.viewState.lineBlockAt(s).top)-r;if(t>1||t<-1){n+=t,i.scrollTop=n/this.scaleY,r=-1;continue}}}break}}}finally{this.updateState=0,this.measureScheduled=-1}if(e&&!e.empty)for(let t of this.state.facet(Xn))t(e)}get themeClasses(){return vr+" "+(this.state.facet(Sr)?yr:wr)+" "+this.state.facet(Qr)}updateAttrs(){let t=Dr(this,Wn,{class:"cm-editor"+(this.hasFocus?" cm-focused ":" ")+this.themeClasses}),e={spellcheck:"false",autocorrect:"off",autocapitalize:"off",translate:"no",contenteditable:this.state.facet(zn)?"true":"false",class:"cm-content",style:`${Mi.tabSize}: ${this.state.tabSize}`,role:"textbox","aria-multiline":"true"};this.state.readOnly&&(e["aria-readonly"]="true"),Dr(this,Ln,e);let i=this.observer.ignore((()=>{let i=Gi(this.contentDOM,this.contentAttrs,e),n=Gi(this.dom,this.editorAttrs,t);return i||n}));return this.editorAttrs=t,this.contentAttrs=e,i}showAnnouncements(t){let e=!0;for(let i of t)for(let t of i.effects)if(t.is(zr.announce)){e&&(this.announceDOM.textContent=""),e=!1,this.announceDOM.appendChild(document.createElement("div")).textContent=t.value}}mountStyles(){this.styleModules=this.state.facet(Kn);let t=this.state.facet(zr.cspNonce);De.mount(this.root,this.styleModules.concat(Pr).reverse(),t?{nonce:t}:void 0)}readMeasured(){if(2==this.updateState)throw new Error("Reading the editor layout isn't allowed during an update");0==this.updateState&&this.measureScheduled>-1&&this.measure(!1)}requestMeasure(t){if(this.measureScheduled<0&&(this.measureScheduled=this.win.requestAnimationFrame((()=>this.measure()))),t){if(this.measureRequests.indexOf(t)>-1)return;if(null!=t.key)for(let e=0;e<this.measureRequests.length;e++)if(this.measureRequests[e].key===t.key)return void(this.measureRequests[e]=t);this.measureRequests.push(t)}}plugin(t){let e=this.pluginMap.get(t);return(void 0===e||e&&e.spec!=t)&&this.pluginMap.set(t,e=this.plugins.find((e=>e.spec==t))||null),e&&e.update(this).value}get documentTop(){return this.contentDOM.getBoundingClientRect().top+this.viewState.paddingTop}get documentPadding(){return{top:this.viewState.paddingTop,bottom:this.viewState.paddingBottom}}get scaleX(){return this.viewState.scaleX}get scaleY(){return this.viewState.scaleY}elementAtHeight(t){return this.readMeasured(),this.viewState.elementAtHeight(t)}lineBlockAtHeight(t){return this.readMeasured(),this.viewState.lineBlockAtHeight(t)}get viewportLineBlocks(){return this.viewState.viewportLines}lineBlockAt(t){return this.viewState.lineBlockAt(t)}get contentHeight(){return this.viewState.contentHeight}moveByChar(t,e,i){return bs(this,t,ms(this,t,e,i))}moveByGroup(t,e){return bs(this,t,ms(this,t,e,(e=>function(t,e,i){let n=t.state.charCategorizer(e),s=n(i);return t=>{let e=n(t);return s==me.Space&&(s=e),s==e}}(this,t.head,e))))}visualLineSide(t,e){let i=this.bidiSpans(t),n=this.textDirectionAt(t.from),s=i[e?i.length-1:0];return Tt.cursor(s.side(e,n)+t.from,s.forward(!e,n)?1:-1)}moveToLineBoundary(t,e,i=!0){return function(t,e,i,n){let s=ps(t,e.head),r=n&&s.type==Hi.Text&&(t.lineWrapping||s.widgetLineBreaks)?t.coordsAtPos(e.assoc<0&&e.head>s.from?e.head-1:e.head):null;if(r){let e=t.dom.getBoundingClientRect(),n=t.textDirectionAt(s.from),o=t.posAtCoords({x:i==(n==an.LTR)?e.right-1:e.left+1,y:(r.top+r.bottom)/2});if(null!=o)return Tt.cursor(o,i?-1:1)}return Tt.cursor(i?s.to:s.from,i?-1:1)}(this,t,e,i)}moveVertically(t,e,i){return bs(this,t,function(t,e,i,n){let s=e.head,r=i?1:-1;if(s==(i?t.state.doc.length:0))return Tt.cursor(s,e.assoc);let o,a=e.goalColumn,l=t.contentDOM.getBoundingClientRect(),h=t.coordsAtPos(s,e.assoc||-1),c=t.documentTop;if(h)null==a&&(a=h.left-l.left),o=r<0?h.top:h.bottom;else{let e=t.viewState.lineBlockAt(s);null==a&&(a=Math.min(l.right-l.left,t.defaultCharacterWidth*(s-e.from))),o=(r<0?e.top:e.bottom)+c}let O=l.left+a,u=null!=n?n:t.viewState.heightOracle.textHeight>>1;for(let e=0;;e+=10){let i=o+(u+e)*r,n=fs(t,{x:O,y:i},!1,r);if(i<l.top||i>l.bottom||(r<0?n<s:n>s)){let e=t.docView.coordsForChar(n),s=!e||i<e.top?-1:1;return Tt.cursor(n,s,void 0,a)}}}(this,t,e,i))}domAtPos(t){return this.docView.domAtPos(t)}posAtDOM(t,e=0){return this.docView.posFromDOM(t,e)}posAtCoords(t,e=!0){return this.readMeasured(),fs(this,t,e)}coordsAtPos(t,e=1){this.readMeasured();let i=this.docView.coordsAt(t,e);if(!i||i.left==i.right)return i;let n=this.state.doc.lineAt(t),s=this.bidiSpans(n);return ri(i,s[gn.find(s,t-n.from,-1,e)].dir==an.LTR==e>0)}coordsForChar(t){return this.readMeasured(),this.docView.coordsForChar(t)}get defaultCharacterWidth(){return this.viewState.heightOracle.charWidth}get defaultLineHeight(){return this.viewState.heightOracle.lineHeight}get textDirection(){return this.viewState.defaultTextDirection}textDirectionAt(t){return!this.state.facet(Cn)||t<this.viewport.from||t>this.viewport.to?this.textDirection:(this.readMeasured(),this.docView.textDirectionAt(t))}get lineWrapping(){return this.viewState.heightOracle.lineWrapping}bidiSpans(t){if(t.length>_r)return wn(t.length);let e,i=this.textDirectionAt(t.from);for(let n of this.bidiCache)if(n.from==t.from&&n.dir==i&&(n.fresh||bn(n.isolates,e=Nn(this,t))))return n.order;e||(e=Nn(this,t));let n=function(t,e,i){if(!t)return[new gn(0,0,e==hn?1:0)];if(e==ln&&!i.length&&!mn.test(t))return wn(t.length);if(i.length)for(;t.length>Qn.length;)Qn[Qn.length]=256;let n=[],s=e==ln?0:1;return vn(t,s,s,i,0,t.length,n),n}(t.text,i,e);return this.bidiCache.push(new Er(t.from,t.to,i,e,!0,n)),n}get hasFocus(){var t;return(this.dom.ownerDocument.hasFocus()||Mi.safari&&(null===(t=this.inputState)||void 0===t?void 0:t.lastContextMenu)>Date.now()-3e4)&&this.root.activeElement==this.contentDOM}focus(){this.observer.ignore((()=>{Oi(this.contentDOM),this.docView.updateSelection()}))}setRoot(t){this._root!=t&&(this._root=t,this.observer.setWindow((9==t.nodeType?t:t.ownerDocument).defaultView||window),this.mountStyles())}destroy(){for(let t of this.plugins)t.destroy(this);this.plugins=[],this.inputState.destroy(),this.docView.destroy(),this.dom.remove(),this.observer.destroy(),this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.destroyed=!0}static scrollIntoView(t,e={}){return Vn.of(new qn("number"==typeof t?Tt.cursor(t):t,e.y,e.x,e.yMargin,e.xMargin))}scrollSnapshot(){let{scrollTop:t,scrollLeft:e}=this.scrollDOM,i=this.viewState.scrollAnchorAt(t);return Vn.of(new qn(Tt.cursor(i.from),"start","start",i.top-t,e,!0))}static domEventHandlers(t){return En.define((()=>({})),{eventHandlers:t})}static domEventObservers(t){return En.define((()=>({})),{eventObservers:t})}static theme(t,e){let i=De.newName(),n=[Qr.of(i),Kn.of(kr(`.${i}`,t))];return e&&e.dark&&n.push(Sr.of(!0)),n}static baseTheme(t){return Gt.lowest(Kn.of(kr("."+vr,t,xr)))}static findFromDOM(t){var e;let i=t.querySelector(".cm-content"),n=i&&bi.get(i)||bi.get(t);return(null===(e=null==n?void 0:n.rootView)||void 0===e?void 0:e.view)||null}}zr.styleModule=Kn,zr.inputHandler=Rn,zr.focusChangeEffect=An,zr.perLineTextDirection=Cn,zr.exceptionSink=Tn,zr.updateListener=Xn,zr.editable=zn,zr.mouseSelectionStyle=Zn,zr.dragMovesSelection=$n,zr.clickAddsSelectionRange=Pn,zr.decorations=Gn,zr.outerDecorations=Bn,zr.atomicRanges=Un,zr.bidiIsolatedRanges=In,zr.scrollMargins=Hn,zr.darkTheme=Sr,zr.cspNonce=At.define({combine:t=>t.length?t[0]:""}),zr.contentAttributes=Ln,zr.editorAttributes=Wn,zr.lineWrapping=zr.contentAttributes.of({class:"cm-lineWrapping"}),zr.announce=le.define();const _r=4096,jr={};class Er{constructor(t,e,i,n,s,r){this.from=t,this.to=e,this.dir=i,this.isolates=n,this.fresh=s,this.order=r}static update(t,e){if(e.empty&&!t.some((t=>t.fresh)))return t;let i=[],n=t.length?t[t.length-1].dir:an.LTR;for(let s=Math.max(0,t.length-10);s<t.length;s++){let r=t[s];r.dir!=n||e.touchesRange(r.from,r.to)||i.push(new Er(e.mapPos(r.from,1),e.mapPos(r.to,-1),r.dir,r.isolates,!1,r.order))}return i}}function Dr(t,e,i){for(let n=t.state.facet(e),s=n.length-1;s>=0;s--){let e=n[s],r="function"==typeof e?e(t):e;r&&Di(r,i)}return i}const Wr=Mi.mac?"mac":Mi.windows?"win":Mi.linux?"linux":"key";function Lr(t,e,i){return e.altKey&&(t="Alt-"+t),e.ctrlKey&&(t="Ctrl-"+t),e.metaKey&&(t="Meta-"+t),!1!==i&&e.shiftKey&&(t="Shift-"+t),t}const Gr=Gt.default(zr.domEventHandlers({keydown:(t,e)=>function(t,e,i,n){let s=function(t){var e=!(Ue&&t.metaKey&&t.shiftKey&&!t.ctrlKey&&!t.altKey||Ie&&t.shiftKey&&t.key&&1==t.key.length||"Unidentified"==t.key)&&t.key||(t.shiftKey?Be:Ge)[t.keyCode]||t.key||"Unidentified";return"Esc"==e&&(e="Escape"),"Del"==e&&(e="Delete"),"Left"==e&&(e="ArrowLeft"),"Up"==e&&(e="ArrowUp"),"Right"==e&&(e="ArrowRight"),"Down"==e&&(e="ArrowDown"),e}(e),r=pt(s,0),o=gt(r)==s.length&&" "!=s,a="",l=!1,h=!1,c=!1;Ir&&Ir.view==i&&Ir.scope==n&&(a=Ir.prefix+" ",xs.indexOf(e.keyCode)<0&&(h=!0,Ir=null));let O,u,f=new Set,d=t=>{if(t){for(let n of t.run)if(!f.has(n)&&(f.add(n),n(i,e)))return t.stopPropagation&&(c=!0),!0;t.preventDefault&&(t.stopPropagation&&(c=!0),h=!0)}return!1},p=t[n];p&&(d(p[a+Lr(s,e,!o)])?l=!0:o&&(e.altKey||e.metaKey||e.ctrlKey)&&!(Mi.windows&&e.ctrlKey&&e.altKey)&&(O=Ge[e.keyCode])&&O!=s?(d(p[a+Lr(O,e,!0)])||e.shiftKey&&(u=Be[e.keyCode])!=s&&u!=O&&d(p[a+Lr(u,e,!1)]))&&(l=!0):o&&e.shiftKey&&d(p[a+Lr(s,e,!0)])&&(l=!0),!l&&d(p._any)&&(l=!0));h&&(l=!0);l&&c&&e.stopPropagation();return l}(function(t){let e=t.facet(Br),i=Ur.get(e);i||Ur.set(e,i=function(t,e=Wr){let i=Object.create(null),n=Object.create(null),s=(t,e)=>{let i=n[t];if(null==i)n[t]=e;else if(i!=e)throw new Error("Key binding "+t+" is used both as a regular binding and as a multi-stroke prefix")},r=(t,n,r,o,a)=>{var l,h;let c=i[t]||(i[t]=Object.create(null)),O=n.split(/ (?!$)/).map((t=>function(t,e){const i=t.split(/-(?!$)/);let n,s,r,o,a=i[i.length-1];"Space"==a&&(a=" ");for(let t=0;t<i.length-1;++t){const a=i[t];if(/^(cmd|meta|m)$/i.test(a))o=!0;else if(/^a(lt)?$/i.test(a))n=!0;else if(/^(c|ctrl|control)$/i.test(a))s=!0;else if(/^s(hift)?$/i.test(a))r=!0;else{if(!/^mod$/i.test(a))throw new Error("Unrecognized modifier name: "+a);"mac"==e?o=!0:s=!0}}return n&&(a="Alt-"+a),s&&(a="Ctrl-"+a),o&&(a="Meta-"+a),r&&(a="Shift-"+a),a}(t,e)));for(let e=1;e<O.length;e++){let i=O.slice(0,e).join(" ");s(i,!0),c[i]||(c[i]={preventDefault:!0,stopPropagation:!1,run:[e=>{let n=Ir={view:e,prefix:i,scope:t};return setTimeout((()=>{Ir==n&&(Ir=null)}),Nr),!0}]})}let u=O.join(" ");s(u,!1);let f=c[u]||(c[u]={preventDefault:!1,stopPropagation:!1,run:(null===(h=null===(l=c._any)||void 0===l?void 0:l.run)||void 0===h?void 0:h.slice())||[]});r&&f.run.push(r),o&&(f.preventDefault=!0),a&&(f.stopPropagation=!0)};for(let n of t){let t=n.scope?n.scope.split(" "):["editor"];if(n.any)for(let e of t){let t=i[e]||(i[e]=Object.create(null));t._any||(t._any={preventDefault:!1,stopPropagation:!1,run:[]});for(let e in t)t[e].run.push(n.any)}let s=n[e]||n.key;if(s)for(let e of t)r(e,s,n.run,n.preventDefault,n.stopPropagation),n.shift&&r(e,"Shift-"+s,n.shift,n.preventDefault,n.stopPropagation)}return i}(e.reduce(((t,e)=>t.concat(e)),[])));return i}(e.state),t,e,"editor")})),Br=At.define({enables:Gr}),Ur=new WeakMap;let Ir=null;const Nr=4e3;class Hr{constructor(t,e,i,n,s){this.className=t,this.left=e,this.top=i,this.width=n,this.height=s}draw(){let t=document.createElement("div");return t.className=this.className,this.adjust(t),t}update(t,e){return e.className==this.className&&(this.adjust(t),!0)}adjust(t){t.style.left=this.left+"px",t.style.top=this.top+"px",null!=this.width&&(t.style.width=this.width+"px"),t.style.height=this.height+"px"}eq(t){return this.left==t.left&&this.top==t.top&&this.width==t.width&&this.height==t.height&&this.className==t.className}static forRange(t,e,i){if(i.empty){let n=t.coordsAtPos(i.head,i.assoc||1);if(!n)return[];let s=Fr(t);return[new Hr(e,n.left-s.left,n.top-s.top,null,n.bottom-n.top)]}return function(t,e,i){if(i.to<=t.viewport.from||i.from>=t.viewport.to)return[];let n=Math.max(i.from,t.viewport.from),s=Math.min(i.to,t.viewport.to),r=t.textDirection==an.LTR,o=t.contentDOM,a=o.getBoundingClientRect(),l=Fr(t),h=o.querySelector(".cm-line"),c=h&&window.getComputedStyle(h),O=a.left+(c?parseInt(c.paddingLeft)+Math.min(0,parseInt(c.textIndent)):0),u=a.right-(c?parseInt(c.paddingRight):0),f=ps(t,n),d=ps(t,s),p=f.type==Hi.Text?f:null,m=d.type==Hi.Text?d:null;p&&(t.lineWrapping||f.widgetLineBreaks)&&(p=Kr(t,n,p));m&&(t.lineWrapping||d.widgetLineBreaks)&&(m=Kr(t,s,m));if(p&&m&&p.from==m.from)return b(Q(i.from,i.to,p));{let e=p?Q(i.from,null,p):S(f,!1),n=m?Q(null,i.to,m):S(d,!0),s=[];return(p||f).to<(m||d).from-(p&&m?1:0)||f.widgetLineBreaks>1&&e.bottom+t.defaultLineHeight/2<n.top?s.push(g(O,e.bottom,u,n.top)):e.bottom<n.top&&t.elementAtHeight((e.bottom+n.top)/2).type==Hi.Text&&(e.bottom=n.top=(e.bottom+n.top)/2),b(e).concat(s).concat(b(n))}function g(t,i,n,s){return new Hr(e,t-l.left,i-l.top-.01,n-t,s-i+.01)}function b({top:t,bottom:e,horizontal:i}){let n=[];for(let s=0;s<i.length;s+=2)n.push(g(i[s],t,i[s+1],e));return n}function Q(e,i,n){let s=1e9,o=-1e9,a=[];function l(e,i,l,h,c){let f=t.coordsAtPos(e,e==n.to?-2:2),d=t.coordsAtPos(l,l==n.from?2:-2);f&&d&&(s=Math.min(f.top,d.top,s),o=Math.max(f.bottom,d.bottom,o),c==an.LTR?a.push(r&&i?O:f.left,r&&h?u:d.right):a.push(!r&&h?O:d.left,!r&&i?u:f.right))}let h=null!=e?e:n.from,c=null!=i?i:n.to;for(let n of t.visibleRanges)if(n.to>h&&n.from<c)for(let s=Math.max(n.from,h),r=Math.min(n.to,c);;){let n=t.state.doc.lineAt(s);for(let o of t.bidiSpans(n)){let t=o.from+n.from,a=o.to+n.from;if(t>=r)break;a>s&&l(Math.max(t,s),null==e&&t<=h,Math.min(a,r),null==i&&a>=c,o.dir)}if(s=n.to+1,s>=r)break}return 0==a.length&&l(h,null==e,c,null==i,t.textDirection),{top:s,bottom:o,horizontal:a}}function S(t,e){let i=a.top+(e?t.top:t.bottom);return{top:i,bottom:i,horizontal:[]}}}(t,e,i)}}function Fr(t){let e=t.scrollDOM.getBoundingClientRect();return{left:(t.textDirection==an.LTR?e.left:e.right-t.scrollDOM.clientWidth*t.scaleX)-t.scrollDOM.scrollLeft*t.scaleX,top:e.top-t.scrollDOM.scrollTop*t.scaleY}}function Kr(t,e,i){let n=Tt.cursor(e);return{from:Math.max(i.from,t.moveToLineBoundary(n,!1,!0).from),to:Math.min(i.to,t.moveToLineBoundary(n,!0,!0).from),type:Hi.Text}}class Jr{constructor(t,e){this.view=t,this.layer=e,this.drawn=[],this.scaleX=1,this.scaleY=1,this.measureReq={read:this.measure.bind(this),write:this.draw.bind(this)},this.dom=t.scrollDOM.appendChild(document.createElement("div")),this.dom.classList.add("cm-layer"),e.above&&this.dom.classList.add("cm-layer-above"),e.class&&this.dom.classList.add(e.class),this.scale(),this.dom.setAttribute("aria-hidden","true"),this.setOrder(t.state),t.requestMeasure(this.measureReq),e.mount&&e.mount(this.dom,t)}update(t){t.startState.facet(to)!=t.state.facet(to)&&this.setOrder(t.state),(this.layer.update(t,this.dom)||t.geometryChanged)&&(this.scale(),t.view.requestMeasure(this.measureReq))}setOrder(t){let e=0,i=t.facet(to);for(;e<i.length&&i[e]!=this.layer;)e++;this.dom.style.zIndex=String((this.layer.above?150:-1)-e)}measure(){return this.layer.markers(this.view)}scale(){let{scaleX:t,scaleY:e}=this.view;t==this.scaleX&&e==this.scaleY||(this.scaleX=t,this.scaleY=e,this.dom.style.transform=`scale(${1/t}, ${1/e})`)}draw(t){if(t.length!=this.drawn.length||t.some(((t,e)=>{return i=t,n=this.drawn[e],!(i.constructor==n.constructor&&i.eq(n));var i,n}))){let e=this.dom.firstChild,i=0;for(let n of t)n.update&&e&&n.constructor&&this.drawn[i].constructor&&n.update(e,this.drawn[i])?(e=e.nextSibling,i++):this.dom.insertBefore(n.draw(),e);for(;e;){let t=e.nextSibling;e.remove(),e=t}this.drawn=t}}destroy(){this.layer.destroy&&this.layer.destroy(this.dom,this.view),this.dom.remove()}}const to=At.define();function eo(t){return[En.define((e=>new Jr(e,t))),to.of(t)]}const io=!Mi.ios,no=At.define({combine:t=>ve(t,{cursorBlinkRate:1200,drawRangeCursor:!0},{cursorBlinkRate:(t,e)=>Math.min(t,e),drawRangeCursor:(t,e)=>t||e})});function so(t={}){return[no.of(t),oo,lo,co,Mn.of(!0)]}function ro(t){return t.startState.facet(no)!=t.state.facet(no)}const oo=eo({above:!0,markers(t){let{state:e}=t,i=e.facet(no),n=[];for(let s of e.selection.ranges){let r=s==e.selection.main;if(s.empty?!r||io:i.drawRangeCursor){let e=r?"cm-cursor cm-cursor-primary":"cm-cursor cm-cursor-secondary",i=s.empty?s:Tt.cursor(s.head,s.head>s.anchor?-1:1);for(let s of Hr.forRange(t,e,i))n.push(s)}}return n},update(t,e){t.transactions.some((t=>t.selection))&&(e.style.animationName="cm-blink"==e.style.animationName?"cm-blink2":"cm-blink");let i=ro(t);return i&&ao(t.state,e),t.docChanged||t.selectionSet||i},mount(t,e){ao(e.state,t)},class:"cm-cursorLayer"});function ao(t,e){e.style.animationDuration=t.facet(no).cursorBlinkRate+"ms"}const lo=eo({above:!1,markers:t=>t.state.selection.ranges.map((e=>e.empty?[]:Hr.forRange(t,"cm-selectionBackground",e))).reduce(((t,e)=>t.concat(e))),update:(t,e)=>t.docChanged||t.selectionSet||t.viewportChanged||ro(t),class:"cm-selectionLayer"}),ho={".cm-line":{"& ::selection":{backgroundColor:"transparent !important"},"&::selection":{backgroundColor:"transparent !important"}}};io&&(ho[".cm-line"].caretColor="transparent !important",ho[".cm-content"]={caretColor:"transparent !important"});const co=Gt.highest(zr.theme(ho));function Oo(t,e,i,n,s){e.lastIndex=0;for(let r,o=t.iterRange(i,n),a=i;!o.next().done;a+=o.value.length)if(!o.lineBreak)for(;r=e.exec(o.value);)s(a+r.index,r)}class uo{constructor(t){const{regexp:e,decoration:i,decorate:n,boundary:s,maxLength:r=1e3}=t;if(!e.global)throw new RangeError("The regular expression given to MatchDecorator should have its 'g' flag set");if(this.regexp=e,n)this.addMatch=(t,e,i,s)=>n(s,i,i+t[0].length,t,e);else if("function"==typeof i)this.addMatch=(t,e,n,s)=>{let r=i(t,e,n);r&&s(n,n+t[0].length,r)};else{if(!i)throw new RangeError("Either 'decorate' or 'decoration' should be provided to MatchDecorator");this.addMatch=(t,e,n,s)=>s(n,n+t[0].length,i)}this.boundary=s,this.maxLength=r}createDeco(t){let e=new $e,i=e.add.bind(e);for(let{from:e,to:n}of function(t,e){let i=t.visibleRanges;if(1==i.length&&i[0].from==t.viewport.from&&i[0].to==t.viewport.to)return i;let n=[];for(let{from:s,to:r}of i)s=Math.max(t.state.doc.lineAt(s).from,s-e),r=Math.min(t.state.doc.lineAt(r).to,r+e),n.length&&n[n.length-1].to>=s?n[n.length-1].to=r:n.push({from:s,to:r});return n}(t,this.maxLength))Oo(t.state.doc,this.regexp,e,n,((e,n)=>this.addMatch(n,t,e,i)));return e.finish()}updateDeco(t,e){let i=1e9,n=-1;return t.docChanged&&t.changes.iterChanges(((e,s,r,o)=>{o>t.view.viewport.from&&r<t.view.viewport.to&&(i=Math.min(r,i),n=Math.max(o,n))})),t.viewportChanged||n-i>1e3?this.createDeco(t.view):n>-1?this.updateRange(t.view,e.map(t.changes),i,n):e}updateRange(t,e,i,n){for(let s of t.visibleRanges){let r=Math.max(s.from,i),o=Math.min(s.to,n);if(o>r){let i=t.state.doc.lineAt(r),n=i.to<o?t.state.doc.lineAt(o):i,a=Math.max(s.from,i.from),l=Math.min(s.to,n.to);if(this.boundary){for(;r>i.from;r--)if(this.boundary.test(i.text[r-1-i.from])){a=r;break}for(;o<n.to;o++)if(this.boundary.test(n.text[o-n.from])){l=o;break}}let h,c=[],O=(t,e,i)=>c.push(i.range(t,e));if(i==n)for(this.regexp.lastIndex=a-i.from;(h=this.regexp.exec(i.text))&&h.index<l-i.from;)this.addMatch(h,t,h.index+i.from,O);else Oo(t.state.doc,this.regexp,a,l,((e,i)=>this.addMatch(i,t,e,O)));e=e.update({filterFrom:a,filterTo:l,filter:(t,e)=>t<a||e>l,add:c})}}return e}}const fo=null!=/x/.unicode?"gu":"g",po=new RegExp("[\0-\b\n--­؜​‎‏\u2028\u2029‭‮⁦⁧⁩\ufeff￹-￼]",fo),mo={0:"null",7:"bell",8:"backspace",10:"newline",11:"vertical tab",13:"carriage return",27:"escape",8203:"zero width space",8204:"zero width non-joiner",8205:"zero width joiner",8206:"left-to-right mark",8207:"right-to-left mark",8232:"line separator",8237:"left-to-right override",8238:"right-to-left override",8294:"left-to-right isolate",8295:"right-to-left isolate",8297:"pop directional isolate",8233:"paragraph separator",65279:"zero width no-break space",65532:"object replacement"};let go=null;const bo=At.define({combine(t){let e=ve(t,{render:null,specialChars:po,addSpecialChars:null});return(e.replaceTabs=!function(){var t;if(null==go&&"undefined"!=typeof document&&document.body){let e=document.body.style;go=null!=(null!==(t=e.tabSize)&&void 0!==t?t:e.MozTabSize)}return go||!1}())&&(e.specialChars=new RegExp("\t|"+e.specialChars.source,fo)),e.addSpecialChars&&(e.specialChars=new RegExp(e.specialChars.source+"|"+e.addSpecialChars.source,fo)),e}});function Qo(t={}){return[bo.of(t),So||(So=En.fromClass(class{constructor(t){this.view=t,this.decorations=Fi.none,this.decorationCache=Object.create(null),this.decorator=this.makeDecorator(t.state.facet(bo)),this.decorations=this.decorator.createDeco(t)}makeDecorator(t){return new uo({regexp:t.specialChars,decoration:(e,i,n)=>{let{doc:s}=i.state,r=pt(e[0],0);if(9==r){let t=s.lineAt(n),e=i.state.tabSize,r=ze(t.text,e,n-t.from);return Fi.replace({widget:new wo((e-r%e)*this.view.defaultCharacterWidth/this.view.scaleX)})}return this.decorationCache[r]||(this.decorationCache[r]=Fi.replace({widget:new vo(t,r)}))},boundary:t.replaceTabs?void 0:/[^]/})}update(t){let e=t.state.facet(bo);t.startState.facet(bo)!=e?(this.decorator=this.makeDecorator(e),this.decorations=this.decorator.createDeco(t.view)):this.decorations=this.decorator.updateDeco(t,this.decorations)}},{decorations:t=>t.decorations}))]}let So=null;class vo extends Ni{constructor(t,e){super(),this.options=t,this.code=e}eq(t){return t.code==this.code}toDOM(t){let e=function(t){return t>=32?"•":10==t?"␤":String.fromCharCode(9216+t)}(this.code),i=t.state.phrase("Control character")+" "+(mo[this.code]||"0x"+this.code.toString(16)),n=this.options.render&&this.options.render(this.code,i,e);if(n)return n;let s=document.createElement("span");return s.textContent=e,s.title=i,s.setAttribute("aria-label",i),s.className="cm-specialChar",s}ignoreEvent(){return!1}}class wo extends Ni{constructor(t){super(),this.width=t}eq(t){return t.width==this.width}toDOM(){let t=document.createElement("span");return t.textContent="\t",t.className="cm-tab",t.style.width=this.width+"px",t}ignoreEvent(){return!1}}const yo=En.fromClass(class{constructor(){this.height=1e3,this.attrs={style:"padding-bottom: 1000px"}}update(t){let{view:e}=t,i=e.viewState.editorHeight*e.scaleY-e.defaultLineHeight-e.documentPadding.top-.5;i>=0&&i!=this.height&&(this.height=i,this.attrs={style:`padding-bottom: ${i}px`})}});function xo(){return[yo,Ln.of((t=>{var e;return(null===(e=t.plugin(yo))||void 0===e?void 0:e.attrs)||null}))]}class ko extends Ni{constructor(t){super(),this.content=t}toDOM(){let t=document.createElement("span");return t.className="cm-placeholder",t.style.pointerEvents="none",t.appendChild("string"==typeof this.content?document.createTextNode(this.content):this.content),"string"==typeof this.content?t.setAttribute("aria-label","placeholder "+this.content):t.setAttribute("aria-hidden","true"),t}coordsAt(t){let e=t.firstChild?ti(t.firstChild):[];if(!e.length)return null;let i=window.getComputedStyle(t.parentNode),n=ri(e[0],"rtl"!=i.direction),s=parseInt(i.lineHeight);return n.bottom-n.top>1.5*s?{left:n.left,right:n.right,top:n.top,bottom:n.top+s}:n}ignoreEvent(){return!1}}function Po(t){return En.fromClass(class{constructor(e){this.view=e,this.placeholder=t?Fi.set([Fi.widget({widget:new ko(t),side:1}).range(0)]):Fi.none}get decorations(){return this.view.state.doc.length?Fi.none:this.placeholder}},{decorations:t=>t.decorations})}const $o="-10000px";class Zo{constructor(t,e,i){this.facet=e,this.createTooltipView=i,this.input=t.state.facet(e),this.tooltips=this.input.filter((t=>t)),this.tooltipViews=this.tooltips.map(i)}update(t,e){var i;let n=t.state.facet(this.facet),s=n.filter((t=>t));if(n===this.input){for(let e of this.tooltipViews)e.update&&e.update(t);return!1}let r=[],o=e?[]:null;for(let i=0;i<s.length;i++){let n=s[i],a=-1;if(n){for(let t=0;t<this.tooltips.length;t++){let e=this.tooltips[t];e&&e.create==n.create&&(a=t)}if(a<0)r[i]=this.createTooltipView(n),o&&(o[i]=!!n.above);else{let n=r[i]=this.tooltipViews[a];o&&(o[i]=e[a]),n.update&&n.update(t)}}}for(let t of this.tooltipViews)r.indexOf(t)<0&&(t.dom.remove(),null===(i=t.destroy)||void 0===i||i.call(t));return e&&(o.forEach(((t,i)=>e[i]=t)),e.length=o.length),this.input=n,this.tooltips=s,this.tooltipViews=r,!0}}function To(t={}){return Ro.of(t)}function Xo(t){let{win:e}=t;return{top:0,left:0,bottom:e.innerHeight,right:e.innerWidth}}const Ro=At.define({combine:t=>{var e,i,n;return{position:Mi.ios?"absolute":(null===(e=t.find((t=>t.position)))||void 0===e?void 0:e.position)||"fixed",parent:(null===(i=t.find((t=>t.parent)))||void 0===i?void 0:i.parent)||null,tooltipSpace:(null===(n=t.find((t=>t.tooltipSpace)))||void 0===n?void 0:n.tooltipSpace)||Xo}}}),Ao=new WeakMap,Co=En.fromClass(class{constructor(t){this.view=t,this.above=[],this.inView=!0,this.madeAbsolute=!1,this.lastTransaction=0,this.measureTimeout=-1;let e=t.state.facet(Ro);this.position=e.position,this.parent=e.parent,this.classes=t.themeClasses,this.createContainer(),this.measureReq={read:this.readMeasure.bind(this),write:this.writeMeasure.bind(this),key:this},this.manager=new Zo(t,Vo,(t=>this.createTooltip(t))),this.intersectionObserver="function"==typeof IntersectionObserver?new IntersectionObserver((t=>{Date.now()>this.lastTransaction-50&&t.length>0&&t[t.length-1].intersectionRatio<1&&this.measureSoon()}),{threshold:[1]}):null,this.observeIntersection(),t.win.addEventListener("resize",this.measureSoon=this.measureSoon.bind(this)),this.maybeMeasure()}createContainer(){this.parent?(this.container=document.createElement("div"),this.container.style.position="relative",this.container.className=this.view.themeClasses,this.parent.appendChild(this.container)):this.container=this.view.dom}observeIntersection(){if(this.intersectionObserver){this.intersectionObserver.disconnect();for(let t of this.manager.tooltipViews)this.intersectionObserver.observe(t.dom)}}measureSoon(){this.measureTimeout<0&&(this.measureTimeout=setTimeout((()=>{this.measureTimeout=-1,this.maybeMeasure()}),50))}update(t){t.transactions.length&&(this.lastTransaction=Date.now());let e=this.manager.update(t,this.above);e&&this.observeIntersection();let i=e||t.geometryChanged,n=t.state.facet(Ro);if(n.position!=this.position&&!this.madeAbsolute){this.position=n.position;for(let t of this.manager.tooltipViews)t.dom.style.position=this.position;i=!0}if(n.parent!=this.parent){this.parent&&this.container.remove(),this.parent=n.parent,this.createContainer();for(let t of this.manager.tooltipViews)this.container.appendChild(t.dom);i=!0}else this.parent&&this.view.themeClasses!=this.classes&&(this.classes=this.container.className=this.view.themeClasses);i&&this.maybeMeasure()}createTooltip(t){let e=t.create(this.view);if(e.dom.classList.add("cm-tooltip"),t.arrow&&!e.dom.querySelector(".cm-tooltip > .cm-tooltip-arrow")){let t=document.createElement("div");t.className="cm-tooltip-arrow",e.dom.appendChild(t)}return e.dom.style.position=this.position,e.dom.style.top=$o,e.dom.style.left="0px",this.container.appendChild(e.dom),e.mount&&e.mount(this.view),e}destroy(){var t,e;this.view.win.removeEventListener("resize",this.measureSoon);for(let e of this.manager.tooltipViews)e.dom.remove(),null===(t=e.destroy)||void 0===t||t.call(e);this.parent&&this.container.remove(),null===(e=this.intersectionObserver)||void 0===e||e.disconnect(),clearTimeout(this.measureTimeout)}readMeasure(){let t=this.view.dom.getBoundingClientRect(),e=1,i=1,n=!1;if("fixed"==this.position&&this.manager.tooltipViews.length){let{dom:t}=this.manager.tooltipViews[0];if(Mi.gecko)n=t.offsetParent!=this.container.ownerDocument.body;else if(t.style.top==$o&&"0px"==t.style.left){let e=t.getBoundingClientRect();n=Math.abs(e.top+1e4)>1||Math.abs(e.left)>1}}if(n||"absolute"==this.position)if(this.parent){let t=this.parent.getBoundingClientRect();t.width&&t.height&&(e=t.width/this.parent.offsetWidth,i=t.height/this.parent.offsetHeight)}else({scaleX:e,scaleY:i}=this.view.viewState);return{editor:t,parent:this.parent?this.container.getBoundingClientRect():t,pos:this.manager.tooltips.map(((t,e)=>{let i=this.manager.tooltipViews[e];return i.getCoords?i.getCoords(t.pos):this.view.coordsAtPos(t.pos)})),size:this.manager.tooltipViews.map((({dom:t})=>t.getBoundingClientRect())),space:this.view.state.facet(Ro).tooltipSpace(this.view),scaleX:e,scaleY:i,makeAbsolute:n}}writeMeasure(t){var e;if(t.makeAbsolute){this.madeAbsolute=!0,this.position="absolute";for(let t of this.manager.tooltipViews)t.dom.style.position="absolute"}let{editor:i,space:n,scaleX:s,scaleY:r}=t,o=[];for(let a=0;a<this.manager.tooltips.length;a++){let l=this.manager.tooltips[a],h=this.manager.tooltipViews[a],{dom:c}=h,O=t.pos[a],u=t.size[a];if(!O||O.bottom<=Math.max(i.top,n.top)||O.top>=Math.min(i.bottom,n.bottom)||O.right<Math.max(i.left,n.left)-.1||O.left>Math.min(i.right,n.right)+.1){c.style.top=$o;continue}let f=l.arrow?h.dom.querySelector(".cm-tooltip-arrow"):null,d=f?7:0,p=u.right-u.left,m=null!==(e=Ao.get(h))&&void 0!==e?e:u.bottom-u.top,g=h.offset||qo,b=this.view.textDirection==an.LTR,Q=u.width>n.right-n.left?b?n.left:n.right-u.width:b?Math.min(O.left-(f?14:0)+g.x,n.right-p):Math.max(n.left,O.left-p+(f?14:0)-g.x),S=this.above[a];!l.strictSide&&(S?O.top-(u.bottom-u.top)-g.y<n.top:O.bottom+(u.bottom-u.top)+g.y>n.bottom)&&S==n.bottom-O.bottom>O.top-n.top&&(S=this.above[a]=!S);let v=(S?O.top-n.top:n.bottom-O.bottom)-d;if(v<m&&!1!==h.resize){if(v<this.view.defaultLineHeight){c.style.top=$o;continue}Ao.set(h,m),c.style.height=(m=v)/r+"px"}else c.style.height&&(c.style.height="");let w=S?O.top-m-d-g.y:O.bottom+d+g.y,y=Q+p;if(!0!==h.overlap)for(let t of o)t.left<y&&t.right>Q&&t.top<w+m&&t.bottom>w&&(w=S?t.top-m-2-d:t.bottom+d+2);if("absolute"==this.position?(c.style.top=(w-t.parent.top)/r+"px",c.style.left=(Q-t.parent.left)/s+"px"):(c.style.top=w/r+"px",c.style.left=Q/s+"px"),f){let t=O.left+(b?g.x:-g.x)-(Q+14-7);f.style.left=t/s+"px"}!0!==h.overlap&&o.push({left:Q,top:w,right:y,bottom:w+m}),c.classList.toggle("cm-tooltip-above",S),c.classList.toggle("cm-tooltip-below",!S),h.positioned&&h.positioned(t.space)}}maybeMeasure(){if(this.manager.tooltips.length&&(this.view.inView&&this.view.requestMeasure(this.measureReq),this.inView!=this.view.inView&&(this.inView=this.view.inView,!this.inView)))for(let t of this.manager.tooltipViews)t.dom.style.top=$o}},{eventObservers:{scroll(){this.maybeMeasure()}}}),Mo=zr.baseTheme({".cm-tooltip":{zIndex:100,boxSizing:"border-box"},"&light .cm-tooltip":{border:"1px solid #bbb",backgroundColor:"#f5f5f5"},"&light .cm-tooltip-section:not(:first-child)":{borderTop:"1px solid #bbb"},"&dark .cm-tooltip":{backgroundColor:"#333338",color:"white"},".cm-tooltip-arrow":{height:"7px",width:"14px",position:"absolute",zIndex:-1,overflow:"hidden","&:before, &:after":{content:"''",position:"absolute",width:0,height:0,borderLeft:"7px solid transparent",borderRight:"7px solid transparent"},".cm-tooltip-above &":{bottom:"-7px","&:before":{borderTop:"7px solid #bbb"},"&:after":{borderTop:"7px solid #f5f5f5",bottom:"1px"}},".cm-tooltip-below &":{top:"-7px","&:before":{borderBottom:"7px solid #bbb"},"&:after":{borderBottom:"7px solid #f5f5f5",top:"1px"}}},"&dark .cm-tooltip .cm-tooltip-arrow":{"&:before":{borderTopColor:"#333338",borderBottomColor:"#333338"},"&:after":{borderTopColor:"transparent",borderBottomColor:"transparent"}}}),qo={x:0,y:0},Vo=At.define({enables:[Co,Mo]});function Yo(t,e){let i=t.plugin(Co);if(!i)return null;let n=i.manager.tooltips.indexOf(e);return n<0?null:i.manager.tooltipViews[n]}function zo(t){let e=t.plugin(Co);e&&e.maybeMeasure()}const _o=At.define({combine(t){let e,i;for(let n of t)e=e||n.topContainer,i=i||n.bottomContainer;return{topContainer:e,bottomContainer:i}}}),jo=En.fromClass(class{constructor(t){this.input=t.state.facet(Wo),this.specs=this.input.filter((t=>t)),this.panels=this.specs.map((e=>e(t)));let e=t.state.facet(_o);this.top=new Eo(t,!0,e.topContainer),this.bottom=new Eo(t,!1,e.bottomContainer),this.top.sync(this.panels.filter((t=>t.top))),this.bottom.sync(this.panels.filter((t=>!t.top)));for(let t of this.panels)t.dom.classList.add("cm-panel"),t.mount&&t.mount()}update(t){let e=t.state.facet(_o);this.top.container!=e.topContainer&&(this.top.sync([]),this.top=new Eo(t.view,!0,e.topContainer)),this.bottom.container!=e.bottomContainer&&(this.bottom.sync([]),this.bottom=new Eo(t.view,!1,e.bottomContainer)),this.top.syncClasses(),this.bottom.syncClasses();let i=t.state.facet(Wo);if(i!=this.input){let e=i.filter((t=>t)),n=[],s=[],r=[],o=[];for(let i of e){let e,a=this.specs.indexOf(i);a<0?(e=i(t.view),o.push(e)):(e=this.panels[a],e.update&&e.update(t)),n.push(e),(e.top?s:r).push(e)}this.specs=e,this.panels=n,this.top.sync(s),this.bottom.sync(r);for(let t of o)t.dom.classList.add("cm-panel"),t.mount&&t.mount()}else for(let e of this.panels)e.update&&e.update(t)}destroy(){this.top.sync([]),this.bottom.sync([])}},{provide:t=>zr.scrollMargins.of((e=>{let i=e.plugin(t);return i&&{top:i.top.scrollMargin(),bottom:i.bottom.scrollMargin()}}))});class Eo{constructor(t,e,i){this.view=t,this.top=e,this.container=i,this.dom=void 0,this.classes="",this.panels=[],this.syncClasses()}sync(t){for(let e of this.panels)e.destroy&&t.indexOf(e)<0&&e.destroy();this.panels=t,this.syncDOM()}syncDOM(){if(0==this.panels.length)return void(this.dom&&(this.dom.remove(),this.dom=void 0));if(!this.dom){this.dom=document.createElement("div"),this.dom.className=this.top?"cm-panels cm-panels-top":"cm-panels cm-panels-bottom",this.dom.style[this.top?"top":"bottom"]="0";let t=this.container||this.view.dom;t.insertBefore(this.dom,this.top?t.firstChild:null)}let t=this.dom.firstChild;for(let e of this.panels)if(e.dom.parentNode==this.dom){for(;t!=e.dom;)t=Do(t);t=t.nextSibling}else this.dom.insertBefore(e.dom,t);for(;t;)t=Do(t)}scrollMargin(){return!this.dom||this.container?0:Math.max(0,this.top?this.dom.getBoundingClientRect().bottom-Math.max(0,this.view.scrollDOM.getBoundingClientRect().top):Math.min(innerHeight,this.view.scrollDOM.getBoundingClientRect().bottom)-this.dom.getBoundingClientRect().top)}syncClasses(){if(this.container&&this.classes!=this.view.themeClasses){for(let t of this.classes.split(" "))t&&this.container.classList.remove(t);for(let t of(this.classes=this.view.themeClasses).split(" "))t&&this.container.classList.add(t)}}}function Do(t){let e=t.nextSibling;return t.remove(),e}const Wo=At.define({enables:jo});class Lo extends we{compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}eq(t){return!1}destroy(t){}}Lo.prototype.elementClass="",Lo.prototype.toDOM=void 0,Lo.prototype.mapMode=Qt.TrackBefore,Lo.prototype.startSide=Lo.prototype.endSide=-1,Lo.prototype.point=!0;const Go=At.define(),Bo={class:"",renderEmptyElements:!1,elementStyle:"",markers:()=>Pe.empty,lineMarker:()=>null,widgetMarker:()=>null,lineMarkerChange:null,initialSpacer:null,updateSpacer:null,domEventHandlers:{}},Uo=At.define();function Io(t){return[Ho(),Uo.of(Object.assign(Object.assign({},Bo),t))]}const No=At.define({combine:t=>t.some((t=>t))});function Ho(t){let e=[Fo];return t&&!1===t.fixed&&e.push(No.of(!0)),e}const Fo=En.fromClass(class{constructor(t){this.view=t,this.prevViewport=t.viewport,this.dom=document.createElement("div"),this.dom.className="cm-gutters",this.dom.setAttribute("aria-hidden","true"),this.dom.style.minHeight=this.view.contentHeight/this.view.scaleY+"px",this.gutters=t.state.facet(Uo).map((e=>new ea(t,e)));for(let t of this.gutters)this.dom.appendChild(t.dom);this.fixed=!t.state.facet(No),this.fixed&&(this.dom.style.position="sticky"),this.syncGutters(!1),t.scrollDOM.insertBefore(this.dom,t.contentDOM)}update(t){if(this.updateGutters(t)){let e=this.prevViewport,i=t.view.viewport,n=Math.min(e.to,i.to)-Math.max(e.from,i.from);this.syncGutters(n<.8*(i.to-i.from))}t.geometryChanged&&(this.dom.style.minHeight=this.view.contentHeight+"px"),this.view.state.facet(No)!=!this.fixed&&(this.fixed=!this.fixed,this.dom.style.position=this.fixed?"sticky":""),this.prevViewport=t.view.viewport}syncGutters(t){let e=this.dom.nextSibling;t&&this.dom.remove();let i=Pe.iter(this.view.state.facet(Go),this.view.viewport.from),n=[],s=this.gutters.map((t=>new ta(t,this.view.viewport,-this.view.documentPadding.top)));for(let t of this.view.viewportLineBlocks)if(n.length&&(n=[]),Array.isArray(t.type)){let e=!0;for(let r of t.type)if(r.type==Hi.Text&&e){Jo(i,n,r.from);for(let t of s)t.line(this.view,r,n);e=!1}else if(r.widget)for(let t of s)t.widget(this.view,r)}else if(t.type==Hi.Text){Jo(i,n,t.from);for(let e of s)e.line(this.view,t,n)}else if(t.widget)for(let e of s)e.widget(this.view,t);for(let t of s)t.finish();t&&this.view.scrollDOM.insertBefore(this.dom,e)}updateGutters(t){let e=t.startState.facet(Uo),i=t.state.facet(Uo),n=t.docChanged||t.heightChanged||t.viewportChanged||!Pe.eq(t.startState.facet(Go),t.state.facet(Go),t.view.viewport.from,t.view.viewport.to);if(e==i)for(let e of this.gutters)e.update(t)&&(n=!0);else{n=!0;let s=[];for(let n of i){let i=e.indexOf(n);i<0?s.push(new ea(this.view,n)):(this.gutters[i].update(t),s.push(this.gutters[i]))}for(let t of this.gutters)t.dom.remove(),s.indexOf(t)<0&&t.destroy();for(let t of s)this.dom.appendChild(t.dom);this.gutters=s}return n}destroy(){for(let t of this.gutters)t.destroy();this.dom.remove()}},{provide:t=>zr.scrollMargins.of((e=>{let i=e.plugin(t);return i&&0!=i.gutters.length&&i.fixed?e.textDirection==an.LTR?{left:i.dom.offsetWidth*e.scaleX}:{right:i.dom.offsetWidth*e.scaleX}:null}))});function Ko(t){return Array.isArray(t)?t:[t]}function Jo(t,e,i){for(;t.value&&t.from<=i;)t.from==i&&e.push(t.value),t.next()}class ta{constructor(t,e,i){this.gutter=t,this.height=i,this.i=0,this.cursor=Pe.iter(t.markers,e.from)}addElement(t,e,i){let{gutter:n}=this,s=(e.top-this.height)/t.scaleY,r=e.height/t.scaleY;if(this.i==n.elements.length){let e=new ia(t,r,s,i);n.elements.push(e),n.dom.appendChild(e.dom)}else n.elements[this.i].update(t,r,s,i);this.height=e.bottom,this.i++}line(t,e,i){let n=[];Jo(this.cursor,n,e.from),i.length&&(n=n.concat(i));let s=this.gutter.config.lineMarker(t,e,n);s&&n.unshift(s);let r=this.gutter;(0!=n.length||r.config.renderEmptyElements)&&this.addElement(t,e,n)}widget(t,e){let i=this.gutter.config.widgetMarker(t,e.widget,e);i&&this.addElement(t,e,[i])}finish(){let t=this.gutter;for(;t.elements.length>this.i;){let e=t.elements.pop();t.dom.removeChild(e.dom),e.destroy()}}}class ea{constructor(t,e){this.view=t,this.config=e,this.elements=[],this.spacer=null,this.dom=document.createElement("div"),this.dom.className="cm-gutter"+(this.config.class?" "+this.config.class:"");for(let i in e.domEventHandlers)this.dom.addEventListener(i,(n=>{let s,r=n.target;if(r!=this.dom&&this.dom.contains(r)){for(;r.parentNode!=this.dom;)r=r.parentNode;let t=r.getBoundingClientRect();s=(t.top+t.bottom)/2}else s=n.clientY;let o=t.lineBlockAtHeight(s-t.documentTop);e.domEventHandlers[i](t,o,n)&&n.preventDefault()}));this.markers=Ko(e.markers(t)),e.initialSpacer&&(this.spacer=new ia(t,0,0,[e.initialSpacer(t)]),this.dom.appendChild(this.spacer.dom),this.spacer.dom.style.cssText+="visibility: hidden; pointer-events: none")}update(t){let e=this.markers;if(this.markers=Ko(this.config.markers(t.view)),this.spacer&&this.config.updateSpacer){let e=this.config.updateSpacer(this.spacer.markers[0],t);e!=this.spacer.markers[0]&&this.spacer.update(t.view,0,0,[e])}let i=t.view.viewport;return!Pe.eq(this.markers,e,i.from,i.to)||!!this.config.lineMarkerChange&&this.config.lineMarkerChange(t)}destroy(){for(let t of this.elements)t.destroy()}}class ia{constructor(t,e,i,n){this.height=-1,this.above=0,this.markers=[],this.dom=document.createElement("div"),this.dom.className="cm-gutterElement",this.update(t,e,i,n)}update(t,e,i,n){this.height!=e&&(this.height=e,this.dom.style.height=e+"px"),this.above!=i&&(this.dom.style.marginTop=(this.above=i)?i+"px":""),function(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++)if(!t[i].compare(e[i]))return!1;return!0}(this.markers,n)||this.setMarkers(t,n)}setMarkers(t,e){let i="cm-gutterElement",n=this.dom.firstChild;for(let s=0,r=0;;){let o=r,a=s<e.length?e[s++]:null,l=!1;if(a){let t=a.elementClass;t&&(i+=" "+t);for(let t=r;t<this.markers.length;t++)if(this.markers[t].compare(a)){o=t,l=!0;break}}else o=this.markers.length;for(;r<o;){let t=this.markers[r++];if(t.toDOM){t.destroy(n);let e=n.nextSibling;n.remove(),n=e}}if(!a)break;a.toDOM&&(l?n=n.nextSibling:this.dom.insertBefore(a.toDOM(t),n)),l&&r++}this.dom.className=i,this.markers=e}destroy(){this.setMarkers(null,[])}}const na=At.define(),sa=At.define({combine:t=>ve(t,{formatNumber:String,domEventHandlers:{}},{domEventHandlers(t,e){let i=Object.assign({},t);for(let t in e){let n=i[t],s=e[t];i[t]=n?(t,e,i)=>n(t,e,i)||s(t,e,i):s}return i}})});class ra extends Lo{constructor(t){super(),this.number=t}eq(t){return this.number==t.number}toDOM(){return document.createTextNode(this.number)}}function oa(t,e){return t.state.facet(sa).formatNumber(e,t.state)}const aa=Uo.compute([sa],(t=>({class:"cm-lineNumbers",renderEmptyElements:!1,markers:t=>t.state.facet(na),lineMarker:(t,e,i)=>i.some((t=>t.toDOM))?null:new ra(oa(t,t.state.doc.lineAt(e.from).number)),widgetMarker:()=>null,lineMarkerChange:t=>t.startState.facet(sa)!=t.state.facet(sa),initialSpacer:t=>new ra(oa(t,ha(t.state.doc.lines))),updateSpacer(t,e){let i=oa(e.view,ha(e.view.state.doc.lines));return i==t.number?t:new ra(i)},domEventHandlers:t.facet(sa).domEventHandlers})));function la(t={}){return[sa.of(t),Ho(),aa]}function ha(t){let e=9;for(;e<t;)e=10*e+9;return e}let ca=0;class Oa{constructor(t,e,i,n){this.name=t,this.set=e,this.base=i,this.modified=n,this.id=ca++}toString(){let{name:t}=this;for(let e of this.modified)e.name&&(t=`${e.name}(${t})`);return t}static define(t,e){let i="string"==typeof t?t:"?";if(t instanceof Oa&&(e=t),null==e?void 0:e.base)throw new Error("Can not derive from a modified tag");let n=new Oa(i,[],null,[]);if(n.set.push(n),e)for(let t of e.set)n.set.push(t);return n}static defineModifier(t){let e=new fa(t);return t=>t.modified.indexOf(e)>-1?t:fa.get(t.base||t,t.modified.concat(e).sort(((t,e)=>t.id-e.id)))}}let ua=0;class fa{constructor(t){this.name=t,this.instances=[],this.id=ua++}static get(t,e){if(!e.length)return t;let i=e[0].instances.find((i=>{return i.base==t&&(n=e,s=i.modified,n.length==s.length&&n.every(((t,e)=>t==s[e])));var n,s}));if(i)return i;let n=[],s=new Oa(t.name,n,t,e);for(let t of e)t.instances.push(s);let r=function(t){let e=[[]];for(let i=0;i<t.length;i++)for(let n=0,s=e.length;n<s;n++)e.push(e[n].concat(t[i]));return e.sort(((t,e)=>e.length-t.length))}(e);for(let e of t.set)if(!e.modified.length)for(let t of r)n.push(fa.get(e,t));return s}}function da(t){let e=Object.create(null);for(let i in t){let n=t[i];Array.isArray(n)||(n=[n]);for(let t of i.split(" "))if(t){let i=[],s=2,r=t;for(let e=0;;){if("..."==r&&e>0&&e+3==t.length){s=1;break}let n=/^"(?:[^"\\]|\\.)*?"|[^\/!]+/.exec(r);if(!n)throw new RangeError("Invalid path: "+t);if(i.push("*"==n[0]?"":'"'==n[0][0]?JSON.parse(n[0]):n[0]),e+=n[0].length,e==t.length)break;let o=t[e++];if(e==t.length&&"!"==o){s=0;break}if("/"!=o)throw new RangeError("Invalid path: "+t);r=t.slice(e)}let o=i.length-1,a=i[o];if(!a)throw new RangeError("Invalid path: "+t);let l=new ma(n,s,o>0?i.slice(0,o):null);e[a]=l.sort(e[a])}}return pa.add(e)}const pa=new n;class ma{constructor(t,e,i,n){this.tags=t,this.mode=e,this.context=i,this.next=n}get opaque(){return 0==this.mode}get inherit(){return 1==this.mode}sort(t){return!t||t.depth<this.depth?(this.next=t,this):(t.next=this.sort(t.next),t)}get depth(){return this.context?this.context.length:0}}function ga(t,e){let i=Object.create(null);for(let e of t)if(Array.isArray(e.tag))for(let t of e.tag)i[t.id]=e.class;else i[e.tag.id]=e.class;let{scope:n,all:s=null}=e||{};return{style:t=>{let e=s;for(let n of t)for(let t of n.set){let n=i[t.id];if(n){e=e?e+" "+n:n;break}}return e},scope:n}}function ba(t,e,i,n=0,s=t.length){let r=new Qa(n,Array.isArray(e)?e:[e],i);r.highlightRange(t.cursor(),n,s,"",r.highlighters),r.flush(s)}ma.empty=new ma([],2,null);class Qa{constructor(t,e,i){this.at=t,this.highlighters=e,this.span=i,this.class=""}startSpan(t,e){e!=this.class&&(this.flush(t),t>this.at&&(this.at=t),this.class=e)}flush(t){t>this.at&&this.class&&this.span(this.at,t,this.class)}highlightRange(t,e,i,s,r){let{type:o,from:a,to:l}=t;if(a>=i||l<=e)return;o.isTop&&(r=this.highlighters.filter((t=>!t.scope||t.scope(o))));let h=s,c=function(t){let e=t.type.prop(pa);for(;e&&e.context&&!t.matchContext(e.context);)e=e.next;return e||null}(t)||ma.empty,O=function(t,e){let i=null;for(let n of t){let t=n.style(e);t&&(i=i?i+" "+t:t)}return i}(r,c.tags);if(O&&(h&&(h+=" "),h+=O,1==c.mode&&(s+=(s?" ":"")+O)),this.startSpan(Math.max(e,a),h),c.opaque)return;let u=t.tree&&t.tree.prop(n.mounted);if(u&&u.overlay){let n=t.node.enter(u.overlay[0].from+a,1),o=this.highlighters.filter((t=>!t.scope||t.scope(u.tree.type))),c=t.firstChild();for(let O=0,f=a;;O++){let d=O<u.overlay.length?u.overlay[O]:null,p=d?d.from+a:l,m=Math.max(e,f),g=Math.min(i,p);if(m<g&&c)for(;t.from<g&&(this.highlightRange(t,m,g,s,r),this.startSpan(Math.min(g,t.to),h),!(t.to>=p)&&t.nextSibling()););if(!d||p>i)break;f=d.to+a,f>e&&(this.highlightRange(n.cursor(),Math.max(e,d.from+a),Math.min(i,f),"",o),this.startSpan(Math.min(i,f),h))}c&&t.parent()}else if(t.firstChild()){u&&(s="");do{if(!(t.to<=e)){if(t.from>=i)break;this.highlightRange(t,e,i,s,r),this.startSpan(Math.min(i,t.to),h)}}while(t.nextSibling());t.parent()}}}const Sa=Oa.define,va=Sa(),wa=Sa(),ya=Sa(wa),xa=Sa(wa),ka=Sa(),Pa=Sa(ka),$a=Sa(ka),Za=Sa(),Ta=Sa(Za),Xa=Sa(),Ra=Sa(),Aa=Sa(),Ca=Sa(Aa),Ma=Sa(),qa={comment:va,lineComment:Sa(va),blockComment:Sa(va),docComment:Sa(va),name:wa,variableName:Sa(wa),typeName:ya,tagName:Sa(ya),propertyName:xa,attributeName:Sa(xa),className:Sa(wa),labelName:Sa(wa),namespace:Sa(wa),macroName:Sa(wa),literal:ka,string:Pa,docString:Sa(Pa),character:Sa(Pa),attributeValue:Sa(Pa),number:$a,integer:Sa($a),float:Sa($a),bool:Sa(ka),regexp:Sa(ka),escape:Sa(ka),color:Sa(ka),url:Sa(ka),keyword:Xa,self:Sa(Xa),null:Sa(Xa),atom:Sa(Xa),unit:Sa(Xa),modifier:Sa(Xa),operatorKeyword:Sa(Xa),controlKeyword:Sa(Xa),definitionKeyword:Sa(Xa),moduleKeyword:Sa(Xa),operator:Ra,derefOperator:Sa(Ra),arithmeticOperator:Sa(Ra),logicOperator:Sa(Ra),bitwiseOperator:Sa(Ra),compareOperator:Sa(Ra),updateOperator:Sa(Ra),definitionOperator:Sa(Ra),typeOperator:Sa(Ra),controlOperator:Sa(Ra),punctuation:Aa,separator:Sa(Aa),bracket:Ca,angleBracket:Sa(Ca),squareBracket:Sa(Ca),paren:Sa(Ca),brace:Sa(Ca),content:Za,heading:Ta,heading1:Sa(Ta),heading2:Sa(Ta),heading3:Sa(Ta),heading4:Sa(Ta),heading5:Sa(Ta),heading6:Sa(Ta),contentSeparator:Sa(Za),list:Sa(Za),quote:Sa(Za),emphasis:Sa(Za),strong:Sa(Za),link:Sa(Za),monospace:Sa(Za),strikethrough:Sa(Za),inserted:Sa(),deleted:Sa(),changed:Sa(),invalid:Sa(),meta:Ma,documentMeta:Sa(Ma),annotation:Sa(Ma),processingInstruction:Sa(Ma),definition:Oa.defineModifier("definition"),constant:Oa.defineModifier("constant"),function:Oa.defineModifier("function"),standard:Oa.defineModifier("standard"),local:Oa.defineModifier("local"),special:Oa.defineModifier("special")};for(let t in qa){let e=qa[t];e instanceof Oa&&(e.name=t)}var Va;ga([{tag:qa.link,class:"tok-link"},{tag:qa.heading,class:"tok-heading"},{tag:qa.emphasis,class:"tok-emphasis"},{tag:qa.strong,class:"tok-strong"},{tag:qa.keyword,class:"tok-keyword"},{tag:qa.atom,class:"tok-atom"},{tag:qa.bool,class:"tok-bool"},{tag:qa.url,class:"tok-url"},{tag:qa.labelName,class:"tok-labelName"},{tag:qa.inserted,class:"tok-inserted"},{tag:qa.deleted,class:"tok-deleted"},{tag:qa.literal,class:"tok-literal"},{tag:qa.string,class:"tok-string"},{tag:qa.number,class:"tok-number"},{tag:[qa.regexp,qa.escape,qa.special(qa.string)],class:"tok-string2"},{tag:qa.variableName,class:"tok-variableName"},{tag:qa.local(qa.variableName),class:"tok-variableName tok-local"},{tag:qa.definition(qa.variableName),class:"tok-variableName tok-definition"},{tag:qa.special(qa.variableName),class:"tok-variableName2"},{tag:qa.definition(qa.propertyName),class:"tok-propertyName tok-definition"},{tag:qa.typeName,class:"tok-typeName"},{tag:qa.namespace,class:"tok-namespace"},{tag:qa.className,class:"tok-className"},{tag:qa.macroName,class:"tok-macroName"},{tag:qa.propertyName,class:"tok-propertyName"},{tag:qa.operator,class:"tok-operator"},{tag:qa.comment,class:"tok-comment"},{tag:qa.meta,class:"tok-meta"},{tag:qa.invalid,class:"tok-invalid"},{tag:qa.punctuation,class:"tok-punctuation"}]);const Ya=new n;function za(t){return At.define({combine:t?e=>e.concat(t):void 0})}const _a=new n;class ja{constructor(t,e,i=[],n=""){this.data=t,this.name=n,Se.prototype.hasOwnProperty("tree")||Object.defineProperty(Se.prototype,"tree",{get(){return Wa(this)}}),this.parser=e,this.extension=[el.of(this),Se.languageData.of(((t,e,i)=>{let n=Ea(t,e,i),s=n.type.prop(Ya);if(!s)return[];let r=t.facet(s),o=n.type.prop(_a);if(o){let s=n.resolve(e-n.from,i);for(let e of o)if(e.test(s,t)){let i=t.facet(e.facet);return"replace"==e.type?i:i.concat(r)}}return r}))].concat(i)}isActiveAt(t,e,i=-1){return Ea(t,e,i).type.prop(Ya)==this.data}findRegions(t){let e=t.facet(el);if((null==e?void 0:e.data)==this.data)return[{from:0,to:t.doc.length}];if(!e||!e.allowsNesting)return[];let i=[],s=(t,e)=>{if(t.prop(Ya)==this.data)return void i.push({from:e,to:e+t.length});let r=t.prop(n.mounted);if(r){if(r.tree.prop(Ya)==this.data){if(r.overlay)for(let t of r.overlay)i.push({from:t.from+e,to:t.to+e});else i.push({from:e,to:e+t.length});return}if(r.overlay){let t=i.length;if(s(r.tree,r.overlay[0].from+e),i.length>t)return}}for(let i=0;i<t.children.length;i++){let n=t.children[i];n instanceof O&&s(n,t.positions[i]+e)}};return s(Wa(t),0),i}get allowsNesting(){return!0}}function Ea(t,e,i){let n=t.facet(el),s=Wa(t).topNode;if(!n||n.allowsNesting)for(let t=s;t;t=t.enter(e,i,c.ExcludeBuffers))t.type.isTop&&(s=t);return s}ja.setState=le.define();class Da extends ja{constructor(t,e,i){super(t,e,[],i),this.parser=e}static define(t){let e=za(t.languageData);return new Da(e,t.parser.configure({props:[Ya.add((t=>t.isTop?e:void 0))]}),t.name)}configure(t,e){return new Da(this.data,this.parser.configure(t),e||this.name)}get allowsNesting(){return this.parser.hasWrappers()}}function Wa(t){let e=t.field(ja.state,!1);return e?e.tree:O.empty}function La(t,e,i=50){var n;let s=null===(n=t.field(ja.state,!1))||void 0===n?void 0:n.context;if(!s)return null;let r=s.viewport;s.updateViewport({from:0,to:e});let o=s.isDone(e)||s.work(i,e)?s.tree:null;return s.updateViewport(r),o}function Ga(t,e=t.viewport.to,i=100){let n=La(t.state,e,i);return n!=Wa(t.state)&&t.dispatch({}),!!n}function Ba(t){var e;return(null===(e=t.plugin(tl))||void 0===e?void 0:e.isWorking())||!1}class Ua{constructor(t){this.doc=t,this.cursorPos=0,this.string="",this.cursor=t.iter()}get length(){return this.doc.length}syncTo(t){return this.string=this.cursor.next(t-this.cursorPos).value,this.cursorPos=t+this.string.length,this.cursorPos-this.string.length}chunk(t){return this.syncTo(t),this.string}get lineChunks(){return!0}read(t,e){let i=this.cursorPos-this.string.length;return t<i||e>=this.cursorPos?this.doc.sliceString(t,e):this.string.slice(t-i,e-i)}}let Ia=null;class Na{constructor(t,e,i=[],n,s,r,o,a){this.parser=t,this.state=e,this.fragments=i,this.tree=n,this.treeLen=s,this.viewport=r,this.skipped=o,this.scheduleOn=a,this.parse=null,this.tempSkipped=[]}static create(t,e,i){return new Na(t,e,[],O.empty,0,i,[],null)}startParse(){return this.parser.startParse(new Ua(this.state.doc),this.fragments)}work(t,e){return null!=e&&e>=this.state.doc.length&&(e=void 0),this.tree!=O.empty&&this.isDone(null!=e?e:this.state.doc.length)?(this.takeTree(),!0):this.withContext((()=>{var i;if("number"==typeof t){let e=Date.now()+t;t=()=>Date.now()>e}for(this.parse||(this.parse=this.startParse()),null!=e&&(null==this.parse.stoppedAt||this.parse.stoppedAt>e)&&e<this.state.doc.length&&this.parse.stopAt(e);;){let n=this.parse.advance();if(n){if(this.fragments=this.withoutTempSkipped(X.addTree(n,this.fragments,null!=this.parse.stoppedAt)),this.treeLen=null!==(i=this.parse.stoppedAt)&&void 0!==i?i:this.state.doc.length,this.tree=n,this.parse=null,!(this.treeLen<(null!=e?e:this.state.doc.length)))return!0;this.parse=this.startParse()}if(t())return!1}}))}takeTree(){let t,e;this.parse&&(t=this.parse.parsedPos)>=this.treeLen&&((null==this.parse.stoppedAt||this.parse.stoppedAt>t)&&this.parse.stopAt(t),this.withContext((()=>{for(;!(e=this.parse.advance()););})),this.treeLen=t,this.tree=e,this.fragments=this.withoutTempSkipped(X.addTree(this.tree,this.fragments,!0)),this.parse=null)}withContext(t){let e=Ia;Ia=this;try{return t()}finally{Ia=e}}withoutTempSkipped(t){for(let e;e=this.tempSkipped.pop();)t=Ha(t,e.from,e.to);return t}changes(t,e){let{fragments:i,tree:n,treeLen:s,viewport:r,skipped:o}=this;if(this.takeTree(),!t.empty){let e=[];if(t.iterChangedRanges(((t,i,n,s)=>e.push({fromA:t,toA:i,fromB:n,toB:s}))),i=X.applyChanges(i,e),n=O.empty,s=0,r={from:t.mapPos(r.from,-1),to:t.mapPos(r.to,1)},this.skipped.length){o=[];for(let e of this.skipped){let i=t.mapPos(e.from,1),n=t.mapPos(e.to,-1);i<n&&o.push({from:i,to:n})}}}return new Na(this.parser,e,i,n,s,r,o,this.scheduleOn)}updateViewport(t){if(this.viewport.from==t.from&&this.viewport.to==t.to)return!1;this.viewport=t;let e=this.skipped.length;for(let e=0;e<this.skipped.length;e++){let{from:i,to:n}=this.skipped[e];i<t.to&&n>t.from&&(this.fragments=Ha(this.fragments,i,n),this.skipped.splice(e--,1))}return!(this.skipped.length>=e)&&(this.reset(),!0)}reset(){this.parse&&(this.takeTree(),this.parse=null)}skipUntilInView(t,e){this.skipped.push({from:t,to:e})}static getSkippingParser(t){return new class extends R{createParse(e,i,n){let s=n[0].from,r=n[n.length-1].to;return{parsedPos:s,advance(){let e=Ia;if(e){for(let t of n)e.tempSkipped.push(t);t&&(e.scheduleOn=e.scheduleOn?Promise.all([e.scheduleOn,t]):t)}return this.parsedPos=r,new O(o.none,[],[],r-s)},stoppedAt:null,stopAt(){}}}}}isDone(t){t=Math.min(t,this.state.doc.length);let e=this.fragments;return this.treeLen>=t&&e.length&&0==e[0].from&&e[0].to>=t}static get(){return Ia}}function Ha(t,e,i){return X.applyChanges(t,[{fromA:e,toA:i,fromB:e,toB:i}])}class Fa{constructor(t){this.context=t,this.tree=t.tree}apply(t){if(!t.docChanged&&this.tree==this.context.tree)return this;let e=this.context.changes(t.changes,t.state),i=this.context.treeLen==t.startState.doc.length?void 0:Math.max(t.changes.mapPos(this.context.treeLen),e.viewport.to);return e.work(20,i)||e.takeTree(),new Fa(e)}static init(t){let e=Math.min(3e3,t.doc.length),i=Na.create(t.facet(el).parser,t,{from:0,to:e});return i.work(20,e)||i.takeTree(),new Fa(i)}}ja.state=_t.define({create:Fa.init,update(t,e){for(let t of e.effects)if(t.is(ja.setState))return t.value;return e.startState.facet(el)!=e.state.facet(el)?Fa.init(e.state):t.apply(e)}});let Ka=t=>{let e=setTimeout((()=>t()),500);return()=>clearTimeout(e)};"undefined"!=typeof requestIdleCallback&&(Ka=t=>{let e=-1,i=setTimeout((()=>{e=requestIdleCallback(t,{timeout:400})}),100);return()=>e<0?clearTimeout(i):cancelIdleCallback(e)});const Ja="undefined"!=typeof navigator&&(null===(Va=navigator.scheduling)||void 0===Va?void 0:Va.isInputPending)?()=>navigator.scheduling.isInputPending():null,tl=En.fromClass(class{constructor(t){this.view=t,this.working=null,this.workScheduled=0,this.chunkEnd=-1,this.chunkBudget=-1,this.work=this.work.bind(this),this.scheduleWork()}update(t){let e=this.view.state.field(ja.state).context;(e.updateViewport(t.view.viewport)||this.view.viewport.to>e.treeLen)&&this.scheduleWork(),(t.docChanged||t.selectionSet)&&(this.view.hasFocus&&(this.chunkBudget+=50),this.scheduleWork()),this.checkAsyncSchedule(e)}scheduleWork(){if(this.working)return;let{state:t}=this.view,e=t.field(ja.state);e.tree==e.context.tree&&e.context.isDone(t.doc.length)||(this.working=Ka(this.work))}work(t){this.working=null;let e=Date.now();if(this.chunkEnd<e&&(this.chunkEnd<0||this.view.hasFocus)&&(this.chunkEnd=e+3e4,this.chunkBudget=3e3),this.chunkBudget<=0)return;let{state:i,viewport:{to:n}}=this.view,s=i.field(ja.state);if(s.tree==s.context.tree&&s.context.isDone(n+1e5))return;let r=Date.now()+Math.min(this.chunkBudget,100,t&&!Ja?Math.max(25,t.timeRemaining()-5):1e9),o=s.context.treeLen<n&&i.doc.length>n+1e3,a=s.context.work((()=>Ja&&Ja()||Date.now()>r),n+(o?0:1e5));this.chunkBudget-=Date.now()-e,(a||this.chunkBudget<=0)&&(s.context.takeTree(),this.view.dispatch({effects:ja.setState.of(new Fa(s.context))})),this.chunkBudget>0&&(!a||o)&&this.scheduleWork(),this.checkAsyncSchedule(s.context)}checkAsyncSchedule(t){t.scheduleOn&&(this.workScheduled++,t.scheduleOn.then((()=>this.scheduleWork())).catch((t=>Yn(this.view.state,t))).then((()=>this.workScheduled--)),t.scheduleOn=null)}destroy(){this.working&&this.working()}isWorking(){return!!(this.working||this.workScheduled>0)}},{eventHandlers:{focus(){this.scheduleWork()}}}),el=At.define({combine:t=>t.length?t[0]:null,enables:t=>[ja.state,tl,zr.contentAttributes.compute([t],(e=>{let i=e.facet(t);return i&&i.name?{"data-language":i.name}:{}}))]});class il{constructor(t,e=[]){this.language=t,this.support=e,this.extension=[t,e]}}class nl{constructor(t,e,i,n,s,r=void 0){this.name=t,this.alias=e,this.extensions=i,this.filename=n,this.loadFunc=s,this.support=r,this.loading=null}load(){return this.loading||(this.loading=this.loadFunc().then((t=>this.support=t),(t=>{throw this.loading=null,t})))}static of(t){let{load:e,support:i}=t;if(!e){if(!i)throw new RangeError("Must pass either 'load' or 'support' to LanguageDescription.of");e=()=>Promise.resolve(i)}return new nl(t.name,(t.alias||[]).concat(t.name).map((t=>t.toLowerCase())),t.extensions||[],t.filename,e,i)}static matchFilename(t,e){for(let i of t)if(i.filename&&i.filename.test(e))return i;let i=/\.([^.]+)$/.exec(e);if(i)for(let e of t)if(e.extensions.indexOf(i[1])>-1)return e;return null}static matchLanguageName(t,e,i=!0){e=e.toLowerCase();for(let i of t)if(i.alias.some((t=>t==e)))return i;if(i)for(let i of t)for(let t of i.alias){let n=e.indexOf(t);if(n>-1&&(t.length>2||!/\w/.test(e[n-1])&&!/\w/.test(e[n+t.length])))return i}return null}}const sl=At.define(),rl=At.define({combine:t=>{if(!t.length)return"  ";let e=t[0];if(!e||/\S/.test(e)||Array.from(e).some((t=>t!=e[0])))throw new Error("Invalid indent unit: "+JSON.stringify(t[0]));return e}});function ol(t){let e=t.facet(rl);return 9==e.charCodeAt(0)?t.tabSize*e.length:e.length}function al(t,e){let i="",n=t.tabSize,s=t.facet(rl)[0];if("\t"==s){for(;e>=n;)i+="\t",e-=n;s=" "}for(let t=0;t<e;t++)i+=s;return i}function ll(t,e){t instanceof Se&&(t=new hl(t));for(let i of t.state.facet(sl)){let n=i(t,e);if(void 0!==n)return n}let i=Wa(t.state);return i.length>=e?function(t,e,i){let n=e.resolveStack(i),s=n.node.enterUnfinishedNodesBefore(i);if(s!=n.node){let t=[];for(let e=s;e!=n.node;e=e.parent)t.push(e);for(let e=t.length-1;e>=0;e--)n={node:t[e],next:n}}return Ol(n,t,i)}(t,i,e):null}class hl{constructor(t,e={}){this.state=t,this.options=e,this.unit=ol(t)}lineAt(t,e=1){let i=this.state.doc.lineAt(t),{simulateBreak:n,simulateDoubleBreak:s}=this.options;return null!=n&&n>=i.from&&n<=i.to?s&&n==t?{text:"",from:t}:(e<0?n<t:n<=t)?{text:i.text.slice(n-i.from),from:n}:{text:i.text.slice(0,n-i.from),from:i.from}:i}textAfterPos(t,e=1){if(this.options.simulateDoubleBreak&&t==this.options.simulateBreak)return"";let{text:i,from:n}=this.lineAt(t,e);return i.slice(t-n,Math.min(i.length,t+100-n))}column(t,e=1){let{text:i,from:n}=this.lineAt(t,e),s=this.countColumn(i,t-n),r=this.options.overrideIndentation?this.options.overrideIndentation(n):-1;return r>-1&&(s+=r-this.countColumn(i,i.search(/\S|$/))),s}countColumn(t,e=t.length){return ze(t,this.state.tabSize,e)}lineIndent(t,e=1){let{text:i,from:n}=this.lineAt(t,e),s=this.options.overrideIndentation;if(s){let t=s(n);if(t>-1)return t}return this.countColumn(i,i.search(/\S|$/))}get simulatedBreak(){return this.options.simulateBreak||null}}const cl=new n;function Ol(t,e,i){for(let n=t;n;n=n.next){let t=ul(n.node);if(t)return t(dl.create(e,i,n))}return 0}function ul(t){let e=t.type.prop(cl);if(e)return e;let i,s=t.firstChild;if(s&&(i=s.type.prop(n.closedBy))){let e=t.lastChild,n=e&&i.indexOf(e.name)>-1;return t=>gl(t,!0,1,void 0,n&&!function(t){return t.pos==t.options.simulateBreak&&t.options.simulateDoubleBreak}(t)?e.from:void 0)}return null==t.parent?fl:null}function fl(){return 0}class dl extends hl{constructor(t,e,i){super(t.state,t.options),this.base=t,this.pos=e,this.context=i}get node(){return this.context.node}static create(t,e,i){return new dl(t,e,i)}get textAfter(){return this.textAfterPos(this.pos)}get baseIndent(){return this.baseIndentFor(this.node)}baseIndentFor(t){let e=this.state.doc.lineAt(t.from);for(;;){let i=t.resolve(e.from);for(;i.parent&&i.parent.from==i.from;)i=i.parent;if(pl(i,t))break;e=this.state.doc.lineAt(i.from)}return this.lineIndent(e.from)}continue(){return Ol(this.context.next,this.base,this.pos)}}function pl(t,e){for(let i=e;i;i=i.parent)if(t==i)return!0;return!1}function ml({closing:t,align:e=!0,units:i=1}){return n=>gl(n,e,i,t)}function gl(t,e,i,n,s){let r=t.textAfter,o=r.match(/^\s*/)[0].length,a=n&&r.slice(o,o+n.length)==n||s==t.pos+o,l=e?function(t){let e=t.node,i=e.childAfter(e.from),n=e.lastChild;if(!i)return null;let s=t.options.simulateBreak,r=t.state.doc.lineAt(i.from),o=null==s||s<=r.from?r.to:Math.min(r.to,s);for(let t=i.to;;){let s=e.childAfter(t);if(!s||s==n)return null;if(!s.type.isSkipped)return s.from<o?i:null;t=s.to}}(t):null;return l?a?t.column(l.from):t.column(l.to):t.baseIndent+(a?0:t.unit*i)}const bl=t=>t.baseIndent;function Ql({except:t,units:e=1}={}){return i=>{let n=t&&t.test(i.textAfter);return i.baseIndent+(n?0:e*i.unit)}}function Sl(){return Se.transactionFilter.of((t=>{if(!t.docChanged||!t.isUserEvent("input.type")&&!t.isUserEvent("input.complete"))return t;let e=t.startState.languageDataAt("indentOnInput",t.startState.selection.main.head);if(!e.length)return t;let i=t.newDoc,{head:n}=t.newSelection.main,s=i.lineAt(n);if(n>s.from+200)return t;let r=i.sliceString(s.from,n);if(!e.some((t=>t.test(r))))return t;let{state:o}=t,a=-1,l=[];for(let{head:t}of o.selection.ranges){let e=o.doc.lineAt(t);if(e.from==a)continue;a=e.from;let i=ll(o,e.from);if(null==i)continue;let n=/^\s*/.exec(e.text)[0],s=al(o,i);n!=s&&l.push({from:e.from,to:e.from+n.length,insert:s})}return l.length?[t,{changes:l,sequential:!0}]:t}))}const vl=At.define(),wl=new n;function yl(t){let e=t.firstChild,i=t.lastChild;return e&&e.to<i.from?{from:e.to,to:i.type.isError?t.to:i.from}:null}function xl(t){let e=t.lastChild;return e&&e.to==t.to&&e.type.isError}function kl(t,e,i){for(let n of t.facet(vl)){let s=n(t,e,i);if(s)return s}return function(t,e,i){let n=Wa(t);if(n.length<i)return null;let s=null;for(let r=n.resolveStack(i,1);r;r=r.next){let o=r.node;if(o.to<=i||o.from>i)continue;if(s&&o.from<e)break;let a=o.type.prop(wl);if(a&&(o.to<n.length-50||n.length==t.doc.length||!xl(o))){let n=a(o,t);n&&n.from<=i&&n.from>=e&&n.to>i&&(s=n)}}return s}(t,e,i)}function Pl(t,e){let i=e.mapPos(t.from,1),n=e.mapPos(t.to,-1);return i>=n?void 0:{from:i,to:n}}const $l=le.define({map:Pl}),Zl=le.define({map:Pl});function Tl(t){let e=[];for(let{head:i}of t.state.selection.ranges)e.some((t=>t.from<=i&&t.to>=i))||e.push(t.lineBlockAt(i));return e}const Xl=_t.define({create:()=>Fi.none,update(t,e){t=t.map(e.changes);for(let i of e.effects)if(i.is($l)&&!Al(t,i.value.from,i.value.to)){let{preparePlaceholder:n}=e.state.facet(Yl),s=n?Fi.replace({widget:new El(n(e.state,i.value))}):jl;t=t.update({add:[s.range(i.value.from,i.value.to)]})}else i.is(Zl)&&(t=t.update({filter:(t,e)=>i.value.from!=t||i.value.to!=e,filterFrom:i.value.from,filterTo:i.value.to}));if(e.selection){let i=!1,{head:n}=e.selection.main;t.between(n,n,((t,e)=>{t<n&&e>n&&(i=!0)})),i&&(t=t.update({filterFrom:n,filterTo:n,filter:(t,e)=>e<=n||t>=n}))}return t},provide:t=>zr.decorations.from(t),toJSON(t,e){let i=[];return t.between(0,e.doc.length,((t,e)=>{i.push(t,e)})),i},fromJSON(t){if(!Array.isArray(t)||t.length%2)throw new RangeError("Invalid JSON for fold state");let e=[];for(let i=0;i<t.length;){let n=t[i++],s=t[i++];if("number"!=typeof n||"number"!=typeof s)throw new RangeError("Invalid JSON for fold state");e.push(jl.range(n,s))}return Fi.set(e,!0)}});function Rl(t,e,i){var n;let s=null;return null===(n=t.field(Xl,!1))||void 0===n||n.between(e,i,((t,e)=>{(!s||s.from>t)&&(s={from:t,to:e})})),s}function Al(t,e,i){let n=!1;return t.between(e,e,((t,s)=>{t==e&&s==i&&(n=!0)})),n}function Cl(t,e){return t.field(Xl,!1)?e:e.concat(le.appendConfig.of(zl()))}function Ml(t,e,i=!0){let n=t.state.doc.lineAt(e.from).number,s=t.state.doc.lineAt(e.to).number;return zr.announce.of(`${t.state.phrase(i?"Folded lines":"Unfolded lines")} ${n} ${t.state.phrase("to")} ${s}.`)}const ql=[{key:"Ctrl-Shift-[",mac:"Cmd-Alt-[",run:t=>{for(let e of Tl(t)){let i=kl(t.state,e.from,e.to);if(i)return t.dispatch({effects:Cl(t.state,[$l.of(i),Ml(t,i)])}),!0}return!1}},{key:"Ctrl-Shift-]",mac:"Cmd-Alt-]",run:t=>{if(!t.state.field(Xl,!1))return!1;let e=[];for(let i of Tl(t)){let n=Rl(t.state,i.from,i.to);n&&e.push(Zl.of(n),Ml(t,n,!1))}return e.length&&t.dispatch({effects:e}),e.length>0}},{key:"Ctrl-Alt-[",run:t=>{let{state:e}=t,i=[];for(let n=0;n<e.doc.length;){let s=t.lineBlockAt(n),r=kl(e,s.from,s.to);r&&i.push($l.of(r)),n=(r?t.lineBlockAt(r.to):s).to+1}return i.length&&t.dispatch({effects:Cl(t.state,i)}),!!i.length}},{key:"Ctrl-Alt-]",run:t=>{let e=t.state.field(Xl,!1);if(!e||!e.size)return!1;let i=[];return e.between(0,t.state.doc.length,((t,e)=>{i.push(Zl.of({from:t,to:e}))})),t.dispatch({effects:i}),!0}}],Vl={placeholderDOM:null,preparePlaceholder:null,placeholderText:"…"},Yl=At.define({combine:t=>ve(t,Vl)});function zl(t){let e=[Xl,Gl];return t&&e.push(Yl.of(t)),e}function _l(t,e){let{state:i}=t,n=i.facet(Yl),s=e=>{let i=t.lineBlockAt(t.posAtDOM(e.target)),n=Rl(t.state,i.from,i.to);n&&t.dispatch({effects:Zl.of(n)}),e.preventDefault()};if(n.placeholderDOM)return n.placeholderDOM(t,s,e);let r=document.createElement("span");return r.textContent=n.placeholderText,r.setAttribute("aria-label",i.phrase("folded code")),r.title=i.phrase("unfold"),r.className="cm-foldPlaceholder",r.onclick=s,r}const jl=Fi.replace({widget:new class extends Ni{toDOM(t){return _l(t,null)}}});class El extends Ni{constructor(t){super(),this.value=t}eq(t){return this.value==t.value}toDOM(t){return _l(t,this.value)}}const Dl={openText:"⌄",closedText:"›",markerDOM:null,domEventHandlers:{},foldingChanged:()=>!1};class Wl extends Lo{constructor(t,e){super(),this.config=t,this.open=e}eq(t){return this.config==t.config&&this.open==t.open}toDOM(t){if(this.config.markerDOM)return this.config.markerDOM(this.open);let e=document.createElement("span");return e.textContent=this.open?this.config.openText:this.config.closedText,e.title=t.state.phrase(this.open?"Fold line":"Unfold line"),e}}function Ll(t={}){let e=Object.assign(Object.assign({},Dl),t),i=new Wl(e,!0),n=new Wl(e,!1),s=En.fromClass(class{constructor(t){this.from=t.viewport.from,this.markers=this.buildMarkers(t)}update(t){(t.docChanged||t.viewportChanged||t.startState.facet(el)!=t.state.facet(el)||t.startState.field(Xl,!1)!=t.state.field(Xl,!1)||Wa(t.startState)!=Wa(t.state)||e.foldingChanged(t))&&(this.markers=this.buildMarkers(t.view))}buildMarkers(t){let e=new $e;for(let s of t.viewportLineBlocks){let r=Rl(t.state,s.from,s.to)?n:kl(t.state,s.from,s.to)?i:null;r&&e.add(s.from,s.from,r)}return e.finish()}}),{domEventHandlers:r}=e;return[s,Io({class:"cm-foldGutter",markers(t){var e;return(null===(e=t.plugin(s))||void 0===e?void 0:e.markers)||Pe.empty},initialSpacer:()=>new Wl(e,!1),domEventHandlers:Object.assign(Object.assign({},r),{click:(t,e,i)=>{if(r.click&&r.click(t,e,i))return!0;let n=Rl(t.state,e.from,e.to);if(n)return t.dispatch({effects:Zl.of(n)}),!0;let s=kl(t.state,e.from,e.to);return!!s&&(t.dispatch({effects:$l.of(s)}),!0)}})}),zl()]}const Gl=zr.baseTheme({".cm-foldPlaceholder":{backgroundColor:"#eee",border:"1px solid #ddd",color:"#888",borderRadius:".2em",margin:"0 1px",padding:"0 1px",cursor:"pointer"},".cm-foldGutter span":{padding:"0 1px",cursor:"pointer"}});class Bl{constructor(t,e){let i;function n(t){let e=De.newName();return(i||(i=Object.create(null)))["."+e]=t,e}this.specs=t;const s="string"==typeof e.all?e.all:e.all?n(e.all):void 0,r=e.scope;this.scope=r instanceof ja?t=>t.prop(Ya)==r.data:r?t=>t==r:void 0,this.style=ga(t.map((t=>({tag:t.tag,class:t.class||n(Object.assign({},t,{tag:null}))}))),{all:s}).style,this.module=i?new De(i):null,this.themeType=e.themeType}static define(t,e){return new Bl(t,e||{})}}const Ul=At.define(),Il=At.define({combine:t=>t.length?[t[0]]:null});function Nl(t){let e=t.facet(Ul);return e.length?e:t.facet(Il)}function Hl(t,e){let i,n=[Kl];return t instanceof Bl&&(t.module&&n.push(zr.styleModule.of(t.module)),i=t.themeType),(null==e?void 0:e.fallback)?n.push(Il.of(t)):i?n.push(Ul.computeN([zr.darkTheme],(e=>e.facet(zr.darkTheme)==("dark"==i)?[t]:[]))):n.push(Ul.of(t)),n}class Fl{constructor(t){this.markCache=Object.create(null),this.tree=Wa(t.state),this.decorations=this.buildDeco(t,Nl(t.state)),this.decoratedTo=t.viewport.to}update(t){let e=Wa(t.state),i=Nl(t.state),n=i!=Nl(t.startState),{viewport:s}=t.view,r=t.changes.mapPos(this.decoratedTo,1);e.length<s.to&&!n&&e.type==this.tree.type&&r>=s.to?(this.decorations=this.decorations.map(t.changes),this.decoratedTo=r):(e!=this.tree||t.viewportChanged||n)&&(this.tree=e,this.decorations=this.buildDeco(t.view,i),this.decoratedTo=s.to)}buildDeco(t,e){if(!e||!this.tree.length)return Fi.none;let i=new $e;for(let{from:n,to:s}of t.visibleRanges)ba(this.tree,e,((t,e,n)=>{i.add(t,e,this.markCache[n]||(this.markCache[n]=Fi.mark({class:n})))}),n,s);return i.finish()}}const Kl=Gt.high(En.fromClass(Fl,{decorations:t=>t.decorations}));qa.meta,qa.link,qa.heading,qa.emphasis,qa.strong,qa.strikethrough,qa.keyword,qa.atom,qa.bool,qa.url,qa.contentSeparator,qa.labelName,qa.literal,qa.inserted,qa.string,qa.deleted,qa.regexp,qa.escape,qa.string,qa.variableName,qa.variableName,qa.typeName,qa.namespace,qa.className,qa.variableName,qa.macroName,qa.propertyName,qa.comment,qa.invalid;const Jl=zr.baseTheme({"&.cm-focused .cm-matchingBracket":{backgroundColor:"#328c8252"},"&.cm-focused .cm-nonmatchingBracket":{backgroundColor:"#bb555544"}}),th="()[]{}",eh=At.define({combine:t=>ve(t,{afterCursor:!0,brackets:th,maxScanDistance:1e4,renderMatch:sh})}),ih=Fi.mark({class:"cm-matchingBracket"}),nh=Fi.mark({class:"cm-nonmatchingBracket"});function sh(t){let e=[],i=t.matched?ih:nh;return e.push(i.range(t.start.from,t.start.to)),t.end&&e.push(i.range(t.end.from,t.end.to)),e}const rh=_t.define({create:()=>Fi.none,update(t,e){if(!e.docChanged&&!e.selection)return t;let i=[],n=e.state.facet(eh);for(let t of e.state.selection.ranges){if(!t.empty)continue;let s=Oh(e.state,t.head,-1,n)||t.head>0&&Oh(e.state,t.head-1,1,n)||n.afterCursor&&(Oh(e.state,t.head,1,n)||t.head<e.state.doc.length&&Oh(e.state,t.head+1,-1,n));s&&(i=i.concat(n.renderMatch(s,e.state)))}return Fi.set(i,!0)},provide:t=>zr.decorations.from(t)}),oh=[rh,Jl];function ah(t={}){return[eh.of(t),oh]}const lh=new n;function hh(t,e,i){let s=t.prop(e<0?n.openedBy:n.closedBy);if(s)return s;if(1==t.name.length){let n=i.indexOf(t.name);if(n>-1&&n%2==(e<0?1:0))return[i[n+e]]}return null}function ch(t){let e=t.type.prop(lh);return e?e(t.node):t}function Oh(t,e,i,n={}){let s=n.maxScanDistance||1e4,r=n.brackets||th,o=Wa(t),a=o.resolveInner(e,i);for(let n=a;n;n=n.parent){let s=hh(n.type,i,r);if(s&&n.from<n.to){let o=ch(n);if(o&&(i>0?e>=o.from&&e<o.to:e>o.from&&e<=o.to))return uh(t,e,i,n,o,s,r)}}return function(t,e,i,n,s,r,o){let a=i<0?t.sliceDoc(e-1,e):t.sliceDoc(e,e+1),l=o.indexOf(a);if(l<0||l%2==0!=i>0)return null;let h={from:i<0?e-1:e,to:i>0?e+1:e},c=t.doc.iterRange(e,i>0?t.doc.length:0),O=0;for(let t=0;!c.next().done&&t<=r;){let r=c.value;i<0&&(t+=r.length);let a=e+t*i;for(let t=i>0?0:r.length-1,e=i>0?r.length:-1;t!=e;t+=i){let e=o.indexOf(r[t]);if(!(e<0||n.resolveInner(a+t,1).type!=s))if(e%2==0==i>0)O++;else{if(1==O)return{start:h,end:{from:a+t,to:a+t+1},matched:e>>1==l>>1};O--}}i>0&&(t+=r.length)}return c.done?{start:h,matched:!1}:null}(t,e,i,o,a.type,s,r)}function uh(t,e,i,n,s,r,o){let a=n.parent,l={from:s.from,to:s.to},h=0,c=null==a?void 0:a.cursor();if(c&&(i<0?c.childBefore(n.from):c.childAfter(n.to)))do{if(i<0?c.to<=n.from:c.from>=n.to){if(0==h&&r.indexOf(c.type.name)>-1&&c.from<c.to){let t=ch(c);return{start:l,end:t?{from:t.from,to:t.to}:void 0,matched:!0}}if(hh(c.type,i,o))h++;else if(hh(c.type,-i,o)){if(0==h){let t=ch(c);return{start:l,end:t&&t.from<t.to?{from:t.from,to:t.to}:void 0,matched:!1}}h--}}}while(i<0?c.prevSibling():c.nextSibling());return{start:l,matched:!1}}function fh(t,e,i,n=0,s=0){null==e&&-1==(e=t.search(/[^\s\u00a0]/))&&(e=t.length);let r=s;for(let s=n;s<e;s++)9==t.charCodeAt(s)?r+=i-r%i:r++;return r}class dh{constructor(t,e,i,n){this.string=t,this.tabSize=e,this.indentUnit=i,this.overrideIndent=n,this.pos=0,this.start=0,this.lastColumnPos=0,this.lastColumnValue=0}eol(){return this.pos>=this.string.length}sol(){return 0==this.pos}peek(){return this.string.charAt(this.pos)||void 0}next(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)}eat(t){let e,i=this.string.charAt(this.pos);if(e="string"==typeof t?i==t:i&&(t instanceof RegExp?t.test(i):t(i)),e)return++this.pos,i}eatWhile(t){let e=this.pos;for(;this.eat(t););return this.pos>e}eatSpace(){let t=this.pos;for(;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>t}skipToEnd(){this.pos=this.string.length}skipTo(t){let e=this.string.indexOf(t,this.pos);if(e>-1)return this.pos=e,!0}backUp(t){this.pos-=t}column(){return this.lastColumnPos<this.start&&(this.lastColumnValue=fh(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue}indentation(){var t;return null!==(t=this.overrideIndent)&&void 0!==t?t:fh(this.string,null,this.tabSize)}match(t,e,i){if("string"==typeof t){let n=t=>i?t.toLowerCase():t;return n(this.string.substr(this.pos,t.length))==n(t)?(!1!==e&&(this.pos+=t.length),!0):null}{let i=this.string.slice(this.pos).match(t);return i&&i.index>0?null:(i&&!1!==e&&(this.pos+=i[0].length),i)}}current(){return this.string.slice(this.start,this.pos)}}function ph(t){if("object"!=typeof t)return t;let e={};for(let i in t){let n=t[i];e[i]=n instanceof Array?n.slice():n}return e}const mh=new WeakMap;class gh extends ja{constructor(t){let e,i=za(t.languageData),s={name:(r=t).name||"",token:r.token,blankLine:r.blankLine||(()=>{}),startState:r.startState||(()=>!0),copyState:r.copyState||ph,indent:r.indent||(()=>null),languageData:r.languageData||{},tokenTable:r.tokenTable||wh};var r;super(i,new class extends R{createParse(t,i,n){return new Sh(e,t,i,n)}},[sl.of(((t,e)=>this.getIndent(t,e)))],t.name),this.topNode=function(t){let e=o.define({id:yh.length,name:"Document",props:[Ya.add((()=>t))],top:!0});return yh.push(e),e}(i),e=this,this.streamParser=s,this.stateAfter=new n({perNode:!0}),this.tokenTable=t.tokenTable?new Zh(s.tokenTable):Th}static define(t){return new gh(t)}getIndent(t,e){let i,n=Wa(t.state),s=n.resolve(e);for(;s&&s.type!=this.topNode;)s=s.parent;if(!s)return null;let{overrideIndentation:r}=t.options;r&&(i=mh.get(t.state),null!=i&&i<e-1e4&&(i=void 0));let o,a,l=bh(this,n,0,s.from,null!=i?i:e);if(l?(a=l.state,o=l.pos+1):(a=this.streamParser.startState(t.unit),o=0),e-o>1e4)return null;for(;o<e;){let i=t.state.doc.lineAt(o),n=Math.min(e,i.to);if(i.length){let e=r?r(i.from):-1,s=new dh(i.text,t.state.tabSize,t.unit,e<0?void 0:e);for(;s.pos<n-i.from;)vh(this.streamParser.token,s,a)}else this.streamParser.blankLine(a,t.unit);if(n==e)break;o=i.to+1}let h=t.lineAt(e);return r&&null==i&&mh.set(t.state,h.from),this.streamParser.indent(a,/^\s*(.*)/.exec(h.text)[1],t)}get allowsNesting(){return!1}}function bh(t,e,i,n,s){let r=i>=n&&i+e.length<=s&&e.prop(t.stateAfter);if(r)return{state:t.streamParser.copyState(r),pos:i+e.length};for(let r=e.children.length-1;r>=0;r--){let o=e.children[r],a=i+e.positions[r],l=o instanceof O&&a<s&&bh(t,o,a,n,s);if(l)return l}return null}function Qh(t,e,i,n,s){if(s&&i<=0&&n>=e.length)return e;s||e.type!=t.topNode||(s=!0);for(let r=e.children.length-1;r>=0;r--){let o,a=e.positions[r],l=e.children[r];if(a<n&&l instanceof O){if(!(o=Qh(t,l,i-a,n-a,s)))break;return s?new O(e.type,e.children.slice(0,r).concat(o),e.positions.slice(0,r+1),a+o.length):o}}return null}let Sh=class{constructor(t,e,i,n){this.lang=t,this.input=e,this.fragments=i,this.ranges=n,this.stoppedAt=null,this.chunks=[],this.chunkPos=[],this.chunk=[],this.chunkReused=void 0,this.rangeIndex=0,this.to=n[n.length-1].to;let s=Na.get(),r=n[0].from,{state:o,tree:a}=function(t,e,i,n){for(let n of e){let e,s=n.from+(n.openStart?25:0),r=n.to-(n.openEnd?25:0),o=s<=i&&r>i&&bh(t,n.tree,0-n.offset,i,r);if(o&&(e=Qh(t,n.tree,i+n.offset,o.pos+n.offset,!1)))return{state:o.state,tree:e}}return{state:t.streamParser.startState(n?ol(n):4),tree:O.empty}}(t,i,r,null==s?void 0:s.state);this.state=o,this.parsedPos=this.chunkStart=r+a.length;for(let t=0;t<a.children.length;t++)this.chunks.push(a.children[t]),this.chunkPos.push(a.positions[t]);s&&this.parsedPos<s.viewport.from-1e5&&(this.state=this.lang.streamParser.startState(ol(s.state)),s.skipUntilInView(this.parsedPos,s.viewport.from),this.parsedPos=s.viewport.from),this.moveRangeIndex()}advance(){let t=Na.get(),e=null==this.stoppedAt?this.to:Math.min(this.to,this.stoppedAt),i=Math.min(e,this.chunkStart+2048);for(t&&(i=Math.min(i,t.viewport.to));this.parsedPos<i;)this.parseLine(t);return this.chunkStart<this.parsedPos&&this.finishChunk(),this.parsedPos>=e?this.finish():t&&this.parsedPos>=t.viewport.to?(t.skipUntilInView(this.parsedPos,e),this.finish()):null}stopAt(t){this.stoppedAt=t}lineAfter(t){let e=this.input.chunk(t);if(this.input.lineChunks)"\n"==e&&(e="");else{let t=e.indexOf("\n");t>-1&&(e=e.slice(0,t))}return t+e.length<=this.to?e:e.slice(0,this.to-t)}nextLine(){let t=this.parsedPos,e=this.lineAfter(t),i=t+e.length;for(let t=this.rangeIndex;;){let n=this.ranges[t].to;if(n>=i)break;if(e=e.slice(0,n-(i-e.length)),t++,t==this.ranges.length)break;let s=this.ranges[t].from,r=this.lineAfter(s);e+=r,i=s+r.length}return{line:e,end:i}}skipGapsTo(t,e,i){for(;;){let n=this.ranges[this.rangeIndex].to,s=t+e;if(i>0?n>s:n>=s)break;e+=this.ranges[++this.rangeIndex].from-n}return e}moveRangeIndex(){for(;this.ranges[this.rangeIndex].to<this.parsedPos;)this.rangeIndex++}emitToken(t,e,i,n,s){if(this.ranges.length>1){e+=s=this.skipGapsTo(e,s,1);let t=this.chunk.length;i+=s=this.skipGapsTo(i,s,-1),n+=this.chunk.length-t}return this.chunk.push(t,e,i,n),s}parseLine(t){let{line:e,end:i}=this.nextLine(),n=0,{streamParser:s}=this.lang,r=new dh(e,t?t.state.tabSize:4,t?ol(t.state):2);if(r.eol())s.blankLine(this.state,r.indentUnit);else for(;!r.eol();){let t=vh(s.token,r,this.state);if(t&&(n=this.emitToken(this.lang.tokenTable.resolve(t),this.parsedPos+r.start,this.parsedPos+r.pos,4,n)),r.start>1e4)break}this.parsedPos=i,this.moveRangeIndex(),this.parsedPos<this.to&&this.parsedPos++}finishChunk(){let t=O.build({buffer:this.chunk,start:this.chunkStart,length:this.parsedPos-this.chunkStart,nodeSet:xh,topID:0,maxBufferLength:2048,reused:this.chunkReused});t=new O(t.type,t.children,t.positions,t.length,[[this.lang.stateAfter,this.lang.streamParser.copyState(this.state)]]),this.chunks.push(t),this.chunkPos.push(this.chunkStart-this.ranges[0].from),this.chunk=[],this.chunkReused=void 0,this.chunkStart=this.parsedPos}finish(){return new O(this.lang.topNode,this.chunks,this.chunkPos,this.parsedPos-this.ranges[0].from).balance()}};function vh(t,e,i){e.start=e.pos;for(let n=0;n<10;n++){let n=t(e,i);if(e.pos>e.start)return n}throw new Error("Stream parser failed to advance stream.")}const wh=Object.create(null),yh=[o.none],xh=new a(yh),kh=[],Ph=Object.create(null),$h=Object.create(null);for(let[t,e]of[["variable","variableName"],["variable-2","variableName.special"],["string-2","string.special"],["def","variableName.definition"],["tag","tagName"],["attribute","attributeName"],["type","typeName"],["builtin","variableName.standard"],["qualifier","modifier"],["error","invalid"],["header","heading"],["property","propertyName"]])$h[t]=Rh(wh,e);class Zh{constructor(t){this.extra=t,this.table=Object.assign(Object.create(null),$h)}resolve(t){return t?this.table[t]||(this.table[t]=Rh(this.extra,t)):0}}const Th=new Zh(wh);function Xh(t,e){kh.indexOf(t)>-1||(kh.push(t),console.warn(e))}function Rh(t,e){let i=[];for(let n of e.split(" ")){let e=[];for(let i of n.split(".")){let n=t[i]||qa[i];n?"function"==typeof n?e.length?e=e.map(n):Xh(i,`Modifier ${i} used at start of tag`):e.length?Xh(i,`Tag ${i} used as modifier`):e=Array.isArray(n)?n:[n]:Xh(i,`Unknown highlighting tag ${i}`)}for(let t of e)i.push(t)}if(!i.length)return 0;let n=e.replace(/ /g,"_"),s=n+" "+i.map((t=>t.id)),r=Ph[s];if(r)return r.id;let a=Ph[s]=o.define({id:yh.length,name:n,props:[da({[n]:i})]});return yh.push(a),a.id}function Ah(t){return t.length<=4096&&/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac\ufb50-\ufdff]/.test(t)}function Ch(t){for(let e=t.iter();!e.next().done;)if(Ah(e.value))return!0;return!1}const Mh=At.define({combine:t=>t.some((t=>t))});function qh(t={}){let e=[Vh];return t.alwaysIsolate&&e.push(Mh.of(!0)),e}const Vh=En.fromClass(class{constructor(t){this.always=t.state.facet(Mh)||t.textDirection!=an.LTR||t.state.facet(zr.perLineTextDirection),this.hasRTL=!this.always&&Ch(t.state.doc),this.tree=Wa(t.state),this.decorations=this.always||this.hasRTL?Yh(t,this.tree,this.always):Fi.none}update(t){let e=t.state.facet(Mh)||t.view.textDirection!=an.LTR||t.state.facet(zr.perLineTextDirection);if(e||this.hasRTL||!function(t){let e=!1;return t.iterChanges(((t,i,n,s,r)=>{!e&&Ch(r)&&(e=!0)})),e}(t.changes)||(this.hasRTL=!0),!e&&!this.hasRTL)return;let i=Wa(t.state);(e!=this.always||i!=this.tree||t.docChanged||t.viewportChanged)&&(this.tree=i,this.always=e,this.decorations=Yh(t.view,i,e))}},{provide:t=>{function e(e){var i,n;return null!==(n=null===(i=e.plugin(t))||void 0===i?void 0:i.decorations)&&void 0!==n?n:Fi.none}return[zr.outerDecorations.of(e),Gt.lowest(zr.bidiIsolatedRanges.of(e))]}});function Yh(t,e,i){let s=new $e,r=t.visibleRanges;i||(r=function(t,e){let i=e.iter(),n=0,s=[],r=null;for(let{from:e,to:o}of t)if(!(r&&r.to>e&&(e=r.to,e>=o)))for(n+i.value.length<e&&(i.next(e-(n+i.value.length)),n=e);;){let t=n,e=n+i.value.length;if(!i.lineBreak&&Ah(i.value)&&(r&&r.to>t-10?r.to=Math.min(o,e):s.push(r={from:t,to:Math.min(o,e)})),e>=o)break;n=e,i.next()}return s}(r,t.state.doc));for(let{from:t,to:i}of r)e.iterate({enter:t=>{let e=t.type.prop(n.isolate);e&&s.add(t.from,t.to,zh[e])},from:t,to:i});return s.finish()}const zh={rtl:Fi.mark({class:"cm-iso",inclusive:!0,attributes:{dir:"rtl"},bidiIsolate:an.RTL}),ltr:Fi.mark({class:"cm-iso",inclusive:!0,attributes:{dir:"ltr"},bidiIsolate:an.LTR}),auto:Fi.mark({class:"cm-iso",inclusive:!0,attributes:{dir:"auto"},bidiIsolate:null})};class _h{constructor(t,e,i,n,s,r,o,a,l,h=0,c){this.p=t,this.stack=e,this.state=i,this.reducePos=n,this.pos=s,this.score=r,this.buffer=o,this.bufferBase=a,this.curContext=l,this.lookAhead=h,this.parent=c}toString(){return`[${this.stack.filter(((t,e)=>e%3==0)).concat(this.state)}]@${this.pos}${this.score?"!"+this.score:""}`}static start(t,e,i=0){let n=t.parser.context;return new _h(t,[],e,i,i,0,[],0,n?new jh(n,n.start):null,0,null)}get context(){return this.curContext?this.curContext.context:null}pushState(t,e){this.stack.push(this.state,e,this.bufferBase+this.buffer.length),this.state=t}reduce(t){var e;let i=t>>19,n=65535&t,{parser:s}=this.p,r=this.reducePos<this.pos-25;r&&this.setLookAhead(this.pos);let o=s.dynamicPrecedence(n);if(o&&(this.score+=o),0==i)return this.pushState(s.getGoto(this.state,n,!0),this.reducePos),n<s.minRepeatTerm&&this.storeNode(n,this.reducePos,this.reducePos,r?8:4,!0),void this.reduceContext(n,this.reducePos);let a=this.stack.length-3*(i-1)-(262144&t?6:0),l=a?this.stack[a-2]:this.p.ranges[0].from,h=this.reducePos-l;h>=2e3&&!(null===(e=this.p.parser.nodeSet.types[n])||void 0===e?void 0:e.isAnonymous)&&(l==this.p.lastBigReductionStart?(this.p.bigReductionCount++,this.p.lastBigReductionSize=h):this.p.lastBigReductionSize<h&&(this.p.bigReductionCount=1,this.p.lastBigReductionStart=l,this.p.lastBigReductionSize=h));let c=a?this.stack[a-1]:0,O=this.bufferBase+this.buffer.length-c;if(n<s.minRepeatTerm||131072&t){let t=s.stateFlag(this.state,1)?this.pos:this.reducePos;this.storeNode(n,l,t,O+4,!0)}if(262144&t)this.state=this.stack[a];else{let t=this.stack[a-3];this.state=s.getGoto(t,n,!0)}for(;this.stack.length>a;)this.stack.pop();this.reduceContext(n,l)}storeNode(t,e,i,n=4,s=!1){if(0==t&&(!this.stack.length||this.stack[this.stack.length-1]<this.buffer.length+this.bufferBase)){let t=this,n=this.buffer.length;if(0==n&&t.parent&&(n=t.bufferBase-t.parent.bufferBase,t=t.parent),n>0&&0==t.buffer[n-4]&&t.buffer[n-1]>-1){if(e==i)return;if(t.buffer[n-2]>=e)return void(t.buffer[n-2]=i)}}if(s&&this.pos!=i){let s=this.buffer.length;if(s>0&&0!=this.buffer[s-4]){let t=!1;for(let e=s;e>0&&this.buffer[e-2]>i;e-=4)if(this.buffer[e-1]>=0){t=!0;break}if(t)for(;s>0&&this.buffer[s-2]>i;)this.buffer[s]=this.buffer[s-4],this.buffer[s+1]=this.buffer[s-3],this.buffer[s+2]=this.buffer[s-2],this.buffer[s+3]=this.buffer[s-1],s-=4,n>4&&(n-=4)}this.buffer[s]=t,this.buffer[s+1]=e,this.buffer[s+2]=i,this.buffer[s+3]=n}else this.buffer.push(t,e,i,n)}shift(t,e,i,n){if(131072&t)this.pushState(65535&t,this.pos);else if(262144&t)this.pos=n,this.shiftContext(e,i),e<=this.p.parser.maxNode&&this.buffer.push(e,i,n,4);else{let s=t,{parser:r}=this.p;(n>this.pos||e<=r.maxNode)&&(this.pos=n,r.stateFlag(s,1)||(this.reducePos=n)),this.pushState(s,i),this.shiftContext(e,i),e<=r.maxNode&&this.buffer.push(e,i,n,4)}}apply(t,e,i,n){65536&t?this.reduce(t):this.shift(t,e,i,n)}useNode(t,e){let i=this.p.reused.length-1;(i<0||this.p.reused[i]!=t)&&(this.p.reused.push(t),i++);let n=this.pos;this.reducePos=this.pos=n+t.length,this.pushState(e,n),this.buffer.push(i,n,this.reducePos,-1),this.curContext&&this.updateContext(this.curContext.tracker.reuse(this.curContext.context,t,this,this.p.stream.reset(this.pos-t.length)))}split(){let t=this,e=t.buffer.length;for(;e>0&&t.buffer[e-2]>t.reducePos;)e-=4;let i=t.buffer.slice(e),n=t.bufferBase+e;for(;t&&n==t.bufferBase;)t=t.parent;return new _h(this.p,this.stack.slice(),this.state,this.reducePos,this.pos,this.score,i,n,this.curContext,this.lookAhead,t)}recoverByDelete(t,e){let i=t<=this.p.parser.maxNode;i&&this.storeNode(t,this.pos,e,4),this.storeNode(0,this.pos,e,i?8:4),this.pos=this.reducePos=e,this.score-=190}canShift(t){for(let e=new Eh(this);;){let i=this.p.parser.stateSlot(e.state,4)||this.p.parser.hasAction(e.state,t);if(0==i)return!1;if(!(65536&i))return!0;e.reduce(i)}}recoverByInsert(t){if(this.stack.length>=300)return[];let e=this.p.parser.nextStates(this.state);if(e.length>8||this.stack.length>=120){let i=[];for(let n,s=0;s<e.length;s+=2)(n=e[s+1])!=this.state&&this.p.parser.hasAction(n,t)&&i.push(e[s],n);if(this.stack.length<120)for(let t=0;i.length<8&&t<e.length;t+=2){let n=e[t+1];i.some(((t,e)=>1&e&&t==n))||i.push(e[t],n)}e=i}let i=[];for(let t=0;t<e.length&&i.length<4;t+=2){let n=e[t+1];if(n==this.state)continue;let s=this.split();s.pushState(n,this.pos),s.storeNode(0,s.pos,s.pos,4,!0),s.shiftContext(e[t],this.pos),s.reducePos=this.pos,s.score-=200,i.push(s)}return i}forceReduce(){let{parser:t}=this.p,e=t.stateSlot(this.state,5);if(!(65536&e))return!1;if(!t.validAction(this.state,e)){let i=e>>19,n=65535&e,s=this.stack.length-3*i;if(s<0||t.getGoto(this.stack[s],n,!1)<0){let t=this.findForcedReduction();if(null==t)return!1;e=t}this.storeNode(0,this.pos,this.pos,4,!0),this.score-=100}return this.reducePos=this.pos,this.reduce(e),!0}findForcedReduction(){let{parser:t}=this.p,e=[],i=(n,s)=>{if(!e.includes(n))return e.push(n),t.allActions(n,(e=>{if(393216&e);else if(65536&e){let i=(e>>19)-s;if(i>1){let n=65535&e,s=this.stack.length-3*i;if(s>=0&&t.getGoto(this.stack[s],n,!1)>=0)return i<<19|65536|n}}else{let t=i(e,s+1);if(null!=t)return t}}))};return i(this.state,0)}forceAll(){for(;!this.p.parser.stateFlag(this.state,2);)if(!this.forceReduce()){this.storeNode(0,this.pos,this.pos,4,!0);break}return this}get deadEnd(){if(3!=this.stack.length)return!1;let{parser:t}=this.p;return 65535==t.data[t.stateSlot(this.state,1)]&&!t.stateSlot(this.state,4)}restart(){this.storeNode(0,this.pos,this.pos,4,!0),this.state=this.stack[0],this.stack.length=0}sameState(t){if(this.state!=t.state||this.stack.length!=t.stack.length)return!1;for(let e=0;e<this.stack.length;e+=3)if(this.stack[e]!=t.stack[e])return!1;return!0}get parser(){return this.p.parser}dialectEnabled(t){return this.p.parser.dialect.flags[t]}shiftContext(t,e){this.curContext&&this.updateContext(this.curContext.tracker.shift(this.curContext.context,t,this,this.p.stream.reset(e)))}reduceContext(t,e){this.curContext&&this.updateContext(this.curContext.tracker.reduce(this.curContext.context,t,this,this.p.stream.reset(e)))}emitContext(){let t=this.buffer.length-1;(t<0||-3!=this.buffer[t])&&this.buffer.push(this.curContext.hash,this.pos,this.pos,-3)}emitLookAhead(){let t=this.buffer.length-1;(t<0||-4!=this.buffer[t])&&this.buffer.push(this.lookAhead,this.pos,this.pos,-4)}updateContext(t){if(t!=this.curContext.context){let e=new jh(this.curContext.tracker,t);e.hash!=this.curContext.hash&&this.emitContext(),this.curContext=e}}setLookAhead(t){t>this.lookAhead&&(this.emitLookAhead(),this.lookAhead=t)}close(){this.curContext&&this.curContext.tracker.strict&&this.emitContext(),this.lookAhead>0&&this.emitLookAhead()}}class jh{constructor(t,e){this.tracker=t,this.context=e,this.hash=t.strict?t.hash(e):0}}class Eh{constructor(t){this.start=t,this.state=t.state,this.stack=t.stack,this.base=this.stack.length}reduce(t){let e=65535&t,i=t>>19;0==i?(this.stack==this.start.stack&&(this.stack=this.stack.slice()),this.stack.push(this.state,0,0),this.base+=3):this.base-=3*(i-1);let n=this.start.p.parser.getGoto(this.stack[this.base-3],e,!0);this.state=n}}class Dh{constructor(t,e,i){this.stack=t,this.pos=e,this.index=i,this.buffer=t.buffer,0==this.index&&this.maybeNext()}static create(t,e=t.bufferBase+t.buffer.length){return new Dh(t,e,e-t.bufferBase)}maybeNext(){let t=this.stack.parent;null!=t&&(this.index=this.stack.bufferBase-t.bufferBase,this.stack=t,this.buffer=t.buffer)}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}next(){this.index-=4,this.pos-=4,0==this.index&&this.maybeNext()}fork(){return new Dh(this.stack,this.pos,this.index)}}function Wh(t,e=Uint16Array){if("string"!=typeof t)return t;let i=null;for(let n=0,s=0;n<t.length;){let r=0;for(;;){let e=t.charCodeAt(n++),i=!1;if(126==e){r=65535;break}e>=92&&e--,e>=34&&e--;let s=e-32;if(s>=46&&(s-=46,i=!0),r+=s,i)break;r*=46}i?i[s++]=r:i=new e(r)}return i}class Lh{constructor(){this.start=-1,this.value=-1,this.end=-1,this.extended=-1,this.lookAhead=0,this.mask=0,this.context=0}}const Gh=new Lh;class Bh{constructor(t,e){this.input=t,this.ranges=e,this.chunk="",this.chunkOff=0,this.chunk2="",this.chunk2Pos=0,this.next=-1,this.token=Gh,this.rangeIndex=0,this.pos=this.chunkPos=e[0].from,this.range=e[0],this.end=e[e.length-1].to,this.readNext()}resolveOffset(t,e){let i=this.range,n=this.rangeIndex,s=this.pos+t;for(;s<i.from;){if(!n)return null;let t=this.ranges[--n];s-=i.from-t.to,i=t}for(;e<0?s>i.to:s>=i.to;){if(n==this.ranges.length-1)return null;let t=this.ranges[++n];s+=t.from-i.to,i=t}return s}clipPos(t){if(t>=this.range.from&&t<this.range.to)return t;for(let e of this.ranges)if(e.to>t)return Math.max(t,e.from);return this.end}peek(t){let e,i,n=this.chunkOff+t;if(n>=0&&n<this.chunk.length)e=this.pos+t,i=this.chunk.charCodeAt(n);else{let n=this.resolveOffset(t,1);if(null==n)return-1;if(e=n,e>=this.chunk2Pos&&e<this.chunk2Pos+this.chunk2.length)i=this.chunk2.charCodeAt(e-this.chunk2Pos);else{let t=this.rangeIndex,n=this.range;for(;n.to<=e;)n=this.ranges[++t];this.chunk2=this.input.chunk(this.chunk2Pos=e),e+this.chunk2.length>n.to&&(this.chunk2=this.chunk2.slice(0,n.to-e)),i=this.chunk2.charCodeAt(0)}}return e>=this.token.lookAhead&&(this.token.lookAhead=e+1),i}acceptToken(t,e=0){let i=e?this.resolveOffset(e,-1):this.pos;if(null==i||i<this.token.start)throw new RangeError("Token end out of bounds");this.token.value=t,this.token.end=i}acceptTokenTo(t,e){this.token.value=t,this.token.end=e}getChunk(){if(this.pos>=this.chunk2Pos&&this.pos<this.chunk2Pos+this.chunk2.length){let{chunk:t,chunkPos:e}=this;this.chunk=this.chunk2,this.chunkPos=this.chunk2Pos,this.chunk2=t,this.chunk2Pos=e,this.chunkOff=this.pos-this.chunkPos}else{this.chunk2=this.chunk,this.chunk2Pos=this.chunkPos;let t=this.input.chunk(this.pos),e=this.pos+t.length;this.chunk=e>this.range.to?t.slice(0,this.range.to-this.pos):t,this.chunkPos=this.pos,this.chunkOff=0}}readNext(){return this.chunkOff>=this.chunk.length&&(this.getChunk(),this.chunkOff==this.chunk.length)?this.next=-1:this.next=this.chunk.charCodeAt(this.chunkOff)}advance(t=1){for(this.chunkOff+=t;this.pos+t>=this.range.to;){if(this.rangeIndex==this.ranges.length-1)return this.setDone();t-=this.range.to-this.pos,this.range=this.ranges[++this.rangeIndex],this.pos=this.range.from}return this.pos+=t,this.pos>=this.token.lookAhead&&(this.token.lookAhead=this.pos+1),this.readNext()}setDone(){return this.pos=this.chunkPos=this.end,this.range=this.ranges[this.rangeIndex=this.ranges.length-1],this.chunk="",this.next=-1}reset(t,e){if(e?(this.token=e,e.start=t,e.lookAhead=t+1,e.value=e.extended=-1):this.token=Gh,this.pos!=t){if(this.pos=t,t==this.end)return this.setDone(),this;for(;t<this.range.from;)this.range=this.ranges[--this.rangeIndex];for(;t>=this.range.to;)this.range=this.ranges[++this.rangeIndex];t>=this.chunkPos&&t<this.chunkPos+this.chunk.length?this.chunkOff=t-this.chunkPos:(this.chunk="",this.chunkOff=0),this.readNext()}return this}read(t,e){if(t>=this.chunkPos&&e<=this.chunkPos+this.chunk.length)return this.chunk.slice(t-this.chunkPos,e-this.chunkPos);if(t>=this.chunk2Pos&&e<=this.chunk2Pos+this.chunk2.length)return this.chunk2.slice(t-this.chunk2Pos,e-this.chunk2Pos);if(t>=this.range.from&&e<=this.range.to)return this.input.read(t,e);let i="";for(let n of this.ranges){if(n.from>=e)break;n.to>t&&(i+=this.input.read(Math.max(n.from,t),Math.min(n.to,e)))}return i}}class Uh{constructor(t,e){this.data=t,this.id=e}token(t,e){let{parser:i}=e.p;Hh(this.data,t,e,this.id,i.data,i.tokenPrecTable)}}Uh.prototype.contextual=Uh.prototype.fallback=Uh.prototype.extend=!1;class Ih{constructor(t,e,i){this.precTable=e,this.elseToken=i,this.data="string"==typeof t?Wh(t):t}token(t,e){let i=t.pos,n=0;for(;;){let i=t.next<0,s=t.resolveOffset(1,1);if(Hh(this.data,t,e,0,this.data,this.precTable),t.token.value>-1)break;if(null==this.elseToken)return;if(i||n++,null==s)break;t.reset(s,t.token)}n&&(t.reset(i,t.token),t.acceptToken(this.elseToken,n))}}Ih.prototype.contextual=Uh.prototype.fallback=Uh.prototype.extend=!1;class Nh{constructor(t,e={}){this.token=t,this.contextual=!!e.contextual,this.fallback=!!e.fallback,this.extend=!!e.extend}}function Hh(t,e,i,n,s,r){let o=0,a=1<<n,{dialect:l}=i.p.parser;t:for(;a&t[o];){let i=t[o+1];for(let n=o+3;n<i;n+=2)if((t[n+1]&a)>0){let i=t[n];if(l.allows(i)&&(-1==e.token.value||e.token.value==i||Kh(i,e.token.value,s,r))){e.acceptToken(i);break}}let n=e.next,h=0,c=t[o+2];if(!(e.next<0&&c>h&&65535==t[i+3*c-3])){for(;h<c;){let s=h+c>>1,r=i+s+(s<<1),a=t[r],l=t[r+1]||65536;if(n<a)c=s;else{if(!(n>=l)){o=t[r+2],e.advance();continue t}h=s+1}}break}o=t[i+3*c-1]}}function Fh(t,e,i){for(let n,s=e;65535!=(n=t[s]);s++)if(n==i)return s-e;return-1}function Kh(t,e,i,n){let s=Fh(i,n,e);return s<0||Fh(i,n,t)<s}const Jh="undefined"!=typeof process&&process.env&&/\bparse\b/.test(process.env.LOG);let tc=null;function ec(t,e,i){let n=t.cursor(c.IncludeAnonymous);for(n.moveTo(e);;)if(!(i<0?n.childBefore(e):n.childAfter(e)))for(;;){if((i<0?n.to<e:n.from>e)&&!n.type.isError)return i<0?Math.max(0,Math.min(n.to-1,e-25)):Math.min(t.length,Math.max(n.from+1,e+25));if(i<0?n.prevSibling():n.nextSibling())break;if(!n.parent())return i<0?0:t.length}}class ic{constructor(t,e){this.fragments=t,this.nodeSet=e,this.i=0,this.fragment=null,this.safeFrom=-1,this.safeTo=-1,this.trees=[],this.start=[],this.index=[],this.nextFragment()}nextFragment(){let t=this.fragment=this.i==this.fragments.length?null:this.fragments[this.i++];if(t){for(this.safeFrom=t.openStart?ec(t.tree,t.from+t.offset,1)-t.offset:t.from,this.safeTo=t.openEnd?ec(t.tree,t.to+t.offset,-1)-t.offset:t.to;this.trees.length;)this.trees.pop(),this.start.pop(),this.index.pop();this.trees.push(t.tree),this.start.push(-t.offset),this.index.push(0),this.nextStart=this.safeFrom}else this.nextStart=1e9}nodeAt(t){if(t<this.nextStart)return null;for(;this.fragment&&this.safeTo<=t;)this.nextFragment();if(!this.fragment)return null;for(;;){let e=this.trees.length-1;if(e<0)return this.nextFragment(),null;let i=this.trees[e],s=this.index[e];if(s==i.children.length){this.trees.pop(),this.start.pop(),this.index.pop();continue}let r=i.children[s],o=this.start[e]+i.positions[s];if(o>t)return this.nextStart=o,null;if(r instanceof O){if(o==t){if(o<this.safeFrom)return null;let t=o+r.length;if(t<=this.safeTo){let e=r.prop(n.lookAhead);if(!e||t+e<this.fragment.to)return r}}this.index[e]++,o+r.length>=Math.max(this.safeFrom,t)&&(this.trees.push(r),this.start.push(o),this.index.push(0))}else this.index[e]++,this.nextStart=o+r.length}}}class nc{constructor(t,e){this.stream=e,this.tokens=[],this.mainToken=null,this.actions=[],this.tokens=t.tokenizers.map((t=>new Lh))}getActions(t){let e=0,i=null,{parser:n}=t.p,{tokenizers:s}=n,r=n.stateSlot(t.state,3),o=t.curContext?t.curContext.hash:0,a=0;for(let n=0;n<s.length;n++){if(!(1<<n&r))continue;let l=s[n],h=this.tokens[n];if((!i||l.fallback)&&((l.contextual||h.start!=t.pos||h.mask!=r||h.context!=o)&&(this.updateCachedToken(h,l,t),h.mask=r,h.context=o),h.lookAhead>h.end+25&&(a=Math.max(h.lookAhead,a)),0!=h.value)){let n=e;if(h.extended>-1&&(e=this.addActions(t,h.extended,h.end,e)),e=this.addActions(t,h.value,h.end,e),!l.extend&&(i=h,e>n))break}}for(;this.actions.length>e;)this.actions.pop();return a&&t.setLookAhead(a),i||t.pos!=this.stream.end||(i=new Lh,i.value=t.p.parser.eofTerm,i.start=i.end=t.pos,e=this.addActions(t,i.value,i.end,e)),this.mainToken=i,this.actions}getMainToken(t){if(this.mainToken)return this.mainToken;let e=new Lh,{pos:i,p:n}=t;return e.start=i,e.end=Math.min(i+1,n.stream.end),e.value=i==n.stream.end?n.parser.eofTerm:0,e}updateCachedToken(t,e,i){let n=this.stream.clipPos(i.pos);if(e.token(this.stream.reset(n,t),i),t.value>-1){let{parser:e}=i.p;for(let n=0;n<e.specialized.length;n++)if(e.specialized[n]==t.value){let s=e.specializers[n](this.stream.read(t.start,t.end),i);if(s>=0&&i.p.parser.dialect.allows(s>>1)){1&s?t.extended=s>>1:t.value=s>>1;break}}}else t.value=0,t.end=this.stream.clipPos(n+1)}putAction(t,e,i,n){for(let e=0;e<n;e+=3)if(this.actions[e]==t)return n;return this.actions[n++]=t,this.actions[n++]=e,this.actions[n++]=i,n}addActions(t,e,i,n){let{state:s}=t,{parser:r}=t.p,{data:o}=r;for(let t=0;t<2;t++)for(let a=r.stateSlot(s,t?2:1);;a+=3){if(65535==o[a]){if(1!=o[a+1]){0==n&&2==o[a+1]&&(n=this.putAction(cc(o,a+2),e,i,n));break}a=cc(o,a+2)}o[a]==e&&(n=this.putAction(cc(o,a+1),e,i,n))}return n}}class sc{constructor(t,e,i,n){this.parser=t,this.input=e,this.ranges=n,this.recovering=0,this.nextStackID=9812,this.minStackPos=0,this.reused=[],this.stoppedAt=null,this.lastBigReductionStart=-1,this.lastBigReductionSize=0,this.bigReductionCount=0,this.stream=new Bh(e,n),this.tokens=new nc(t,this.stream),this.topTerm=t.top[1];let{from:s}=n[0];this.stacks=[_h.start(this,t.top[0],s)],this.fragments=i.length&&this.stream.end-s>4*t.bufferLength?new ic(i,t.nodeSet):null}get parsedPos(){return this.minStackPos}advance(){let t,e,i=this.stacks,n=this.minStackPos,s=this.stacks=[];if(this.bigReductionCount>300&&1==i.length){let[t]=i;for(;t.forceReduce()&&t.stack.length&&t.stack[t.stack.length-2]>=this.lastBigReductionStart;);this.bigReductionCount=this.lastBigReductionSize=0}for(let r=0;r<i.length;r++){let o=i[r];for(;;){if(this.tokens.mainToken=null,o.pos>n)s.push(o);else{if(this.advanceStack(o,s,i))continue;{t||(t=[],e=[]),t.push(o);let i=this.tokens.getMainToken(o);e.push(i.value,i.end)}}break}}if(!s.length){let e=t&&function(t){let e=null;for(let i of t){let t=i.p.stoppedAt;(i.pos==i.p.stream.end||null!=t&&i.pos>t)&&i.p.parser.stateFlag(i.state,2)&&(!e||e.score<i.score)&&(e=i)}return e}(t);if(e)return Jh&&console.log("Finish with "+this.stackID(e)),this.stackToTree(e);if(this.parser.strict)throw Jh&&t&&console.log("Stuck with token "+(this.tokens.mainToken?this.parser.getName(this.tokens.mainToken.value):"none")),new SyntaxError("No parse at "+n);this.recovering||(this.recovering=5)}if(this.recovering&&t){let i=null!=this.stoppedAt&&t[0].pos>this.stoppedAt?t[0]:this.runRecovery(t,e,s);if(i)return Jh&&console.log("Force-finish "+this.stackID(i)),this.stackToTree(i.forceAll())}if(this.recovering){let t=1==this.recovering?1:3*this.recovering;if(s.length>t)for(s.sort(((t,e)=>e.score-t.score));s.length>t;)s.pop();s.some((t=>t.reducePos>n))&&this.recovering--}else if(s.length>1){t:for(let t=0;t<s.length-1;t++){let e=s[t];for(let i=t+1;i<s.length;i++){let n=s[i];if(e.sameState(n)||e.buffer.length>500&&n.buffer.length>500){if(!((e.score-n.score||e.buffer.length-n.buffer.length)>0)){s.splice(t--,1);continue t}s.splice(i--,1)}}}s.length>12&&s.splice(12,s.length-12)}this.minStackPos=s[0].pos;for(let t=1;t<s.length;t++)s[t].pos<this.minStackPos&&(this.minStackPos=s[t].pos);return null}stopAt(t){if(null!=this.stoppedAt&&this.stoppedAt<t)throw new RangeError("Can't move stoppedAt forward");this.stoppedAt=t}advanceStack(t,e,i){let s=t.pos,{parser:r}=this,o=Jh?this.stackID(t)+" -> ":"";if(null!=this.stoppedAt&&s>this.stoppedAt)return t.forceReduce()?t:null;if(this.fragments){let e=t.curContext&&t.curContext.tracker.strict,i=e?t.curContext.hash:0;for(let a=this.fragments.nodeAt(s);a;){let s=this.parser.nodeSet.types[a.type.id]==a.type?r.getGoto(t.state,a.type.id):-1;if(s>-1&&a.length&&(!e||(a.prop(n.contextHash)||0)==i))return t.useNode(a,s),Jh&&console.log(o+this.stackID(t)+` (via reuse of ${r.getName(a.type.id)})`),!0;if(!(a instanceof O)||0==a.children.length||a.positions[0]>0)break;let l=a.children[0];if(!(l instanceof O&&0==a.positions[0]))break;a=l}}let a=r.stateSlot(t.state,4);if(a>0)return t.reduce(a),Jh&&console.log(o+this.stackID(t)+` (via always-reduce ${r.getName(65535&a)})`),!0;if(t.stack.length>=8400)for(;t.stack.length>6e3&&t.forceReduce(););let l=this.tokens.getActions(t);for(let n=0;n<l.length;){let a=l[n++],h=l[n++],c=l[n++],O=n==l.length||!i,u=O?t:t.split(),f=this.tokens.mainToken;if(u.apply(a,h,f?f.start:u.pos,c),Jh&&console.log(o+this.stackID(u)+` (via ${65536&a?`reduce of ${r.getName(65535&a)}`:"shift"} for ${r.getName(h)} @ ${s}${u==t?"":", split"})`),O)return!0;u.pos>s?e.push(u):i.push(u)}return!1}advanceFully(t,e){let i=t.pos;for(;;){if(!this.advanceStack(t,null,null))return!1;if(t.pos>i)return rc(t,e),!0}}runRecovery(t,e,i){let n=null,s=!1;for(let r=0;r<t.length;r++){let o=t[r],a=e[r<<1],l=e[1+(r<<1)],h=Jh?this.stackID(o)+" -> ":"";if(o.deadEnd){if(s)continue;if(s=!0,o.restart(),Jh&&console.log(h+this.stackID(o)+" (restarted)"),this.advanceFully(o,i))continue}let c=o.split(),O=h;for(let t=0;c.forceReduce()&&t<10;t++){if(Jh&&console.log(O+this.stackID(c)+" (via force-reduce)"),this.advanceFully(c,i))break;Jh&&(O=this.stackID(c)+" -> ")}for(let t of o.recoverByInsert(a))Jh&&console.log(h+this.stackID(t)+" (via recover-insert)"),this.advanceFully(t,i);this.stream.end>o.pos?(l==o.pos&&(l++,a=0),o.recoverByDelete(a,l),Jh&&console.log(h+this.stackID(o)+` (via recover-delete ${this.parser.getName(a)})`),rc(o,i)):(!n||n.score<o.score)&&(n=o)}return n}stackToTree(t){return t.close(),O.build({buffer:Dh.create(t),nodeSet:this.parser.nodeSet,topID:this.topTerm,maxBufferLength:this.parser.bufferLength,reused:this.reused,start:this.ranges[0].from,length:t.pos-this.ranges[0].from,minRepeatType:this.parser.minRepeatTerm})}stackID(t){let e=(tc||(tc=new WeakMap)).get(t);return e||tc.set(t,e=String.fromCodePoint(this.nextStackID++)),e+t}}function rc(t,e){for(let i=0;i<e.length;i++){let n=e[i];if(n.pos==t.pos&&n.sameState(t))return void(e[i].score<t.score&&(e[i]=t))}e.push(t)}class oc{constructor(t,e,i){this.source=t,this.flags=e,this.disabled=i}allows(t){return!this.disabled||0==this.disabled[t]}}const ac=t=>t;class lc{constructor(t){this.start=t.start,this.shift=t.shift||ac,this.reduce=t.reduce||ac,this.reuse=t.reuse||ac,this.hash=t.hash||(()=>0),this.strict=!1!==t.strict}}class hc extends R{constructor(e){if(super(),this.wrappers=[],14!=e.version)throw new RangeError(`Parser version (${e.version}) doesn't match runtime version (14)`);let i=e.nodeNames.split(" ");this.minRepeatTerm=i.length;for(let t=0;t<e.repeatNodeCount;t++)i.push("");let s=Object.keys(e.topRules).map((t=>e.topRules[t][1])),r=[];for(let t=0;t<i.length;t++)r.push([]);function l(t,e,i){r[t].push([e,e.deserialize(String(i))])}if(e.nodeProps)for(let t of e.nodeProps){let e=t[0];"string"==typeof e&&(e=n[e]);for(let i=1;i<t.length;){let n=t[i++];if(n>=0)l(n,e,t[i++]);else{let s=t[i+-n];for(let r=-n;r>0;r--)l(t[i++],e,s);i++}}}this.nodeSet=new a(i.map(((t,i)=>o.define({name:i>=this.minRepeatTerm?void 0:t,id:i,props:r[i],top:s.indexOf(i)>-1,error:0==i,skipped:e.skippedNodes&&e.skippedNodes.indexOf(i)>-1})))),e.propSources&&(this.nodeSet=this.nodeSet.extend(...e.propSources)),this.strict=!1,this.bufferLength=t;let h=Wh(e.tokenData);this.context=e.context,this.specializerSpecs=e.specialized||[],this.specialized=new Uint16Array(this.specializerSpecs.length);for(let t=0;t<this.specializerSpecs.length;t++)this.specialized[t]=this.specializerSpecs[t].term;this.specializers=this.specializerSpecs.map(Oc),this.states=Wh(e.states,Uint32Array),this.data=Wh(e.stateData),this.goto=Wh(e.goto),this.maxTerm=e.maxTerm,this.tokenizers=e.tokenizers.map((t=>"number"==typeof t?new Uh(h,t):t)),this.topRules=e.topRules,this.dialects=e.dialects||{},this.dynamicPrecedences=e.dynamicPrecedences||null,this.tokenPrecTable=e.tokenPrec,this.termNames=e.termNames||null,this.maxNode=this.nodeSet.types.length-1,this.dialect=this.parseDialect(),this.top=this.topRules[Object.keys(this.topRules)[0]]}createParse(t,e,i){let n=new sc(this,t,e,i);for(let s of this.wrappers)n=s(n,t,e,i);return n}getGoto(t,e,i=!1){let n=this.goto;if(e>=n[0])return-1;for(let s=n[e+1];;){let e=n[s++],r=1&e,o=n[s++];if(r&&i)return o;for(let i=s+(e>>1);s<i;s++)if(n[s]==t)return o;if(r)return-1}}hasAction(t,e){let i=this.data;for(let n=0;n<2;n++)for(let s,r=this.stateSlot(t,n?2:1);;r+=3){if(65535==(s=i[r])){if(1!=i[r+1]){if(2==i[r+1])return cc(i,r+2);break}s=i[r=cc(i,r+2)]}if(s==e||0==s)return cc(i,r+1)}return 0}stateSlot(t,e){return this.states[6*t+e]}stateFlag(t,e){return(this.stateSlot(t,0)&e)>0}validAction(t,e){return!!this.allActions(t,(t=>t==e||null))}allActions(t,e){let i=this.stateSlot(t,4),n=i?e(i):void 0;for(let i=this.stateSlot(t,1);null==n;i+=3){if(65535==this.data[i]){if(1!=this.data[i+1])break;i=cc(this.data,i+2)}n=e(cc(this.data,i+1))}return n}nextStates(t){let e=[];for(let i=this.stateSlot(t,1);;i+=3){if(65535==this.data[i]){if(1!=this.data[i+1])break;i=cc(this.data,i+2)}if(!(1&this.data[i+2])){let t=this.data[i+1];e.some(((e,i)=>1&i&&e==t))||e.push(this.data[i],t)}}return e}configure(t){let e=Object.assign(Object.create(hc.prototype),this);if(t.props&&(e.nodeSet=this.nodeSet.extend(...t.props)),t.top){let i=this.topRules[t.top];if(!i)throw new RangeError(`Invalid top rule name ${t.top}`);e.top=i}return t.tokenizers&&(e.tokenizers=this.tokenizers.map((e=>{let i=t.tokenizers.find((t=>t.from==e));return i?i.to:e}))),t.specializers&&(e.specializers=this.specializers.slice(),e.specializerSpecs=this.specializerSpecs.map(((i,n)=>{let s=t.specializers.find((t=>t.from==i.external));if(!s)return i;let r=Object.assign(Object.assign({},i),{external:s.to});return e.specializers[n]=Oc(r),r}))),t.contextTracker&&(e.context=t.contextTracker),t.dialect&&(e.dialect=this.parseDialect(t.dialect)),null!=t.strict&&(e.strict=t.strict),t.wrap&&(e.wrappers=e.wrappers.concat(t.wrap)),null!=t.bufferLength&&(e.bufferLength=t.bufferLength),e}hasWrappers(){return this.wrappers.length>0}getName(t){return this.termNames?this.termNames[t]:String(t<=this.maxNode&&this.nodeSet.types[t].name||t)}get eofTerm(){return this.maxNode+1}get topNode(){return this.nodeSet.types[this.top[1]]}dynamicPrecedence(t){let e=this.dynamicPrecedences;return null==e?0:e[t]||0}parseDialect(t){let e=Object.keys(this.dialects),i=e.map((()=>!1));if(t)for(let n of t.split(" ")){let t=e.indexOf(n);t>=0&&(i[t]=!0)}let n=null;for(let t=0;t<e.length;t++)if(!i[t])for(let i,s=this.dialects[e[t]];65535!=(i=this.data[s++]);)(n||(n=new Uint8Array(this.maxTerm+1)))[i]=1;return new oc(t,i,n)}static deserialize(t){return new hc(t)}}function cc(t,e){return t[e]|t[e+1]<<16}function Oc(t){if(t.external){let e=t.extend?1:0;return(i,n)=>t.external(i,n)<<1|e}return t.get}const uc=[9,10,11,12,13,32,133,160,5760,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8232,8233,8239,8287,12288];function fc(t){return t>=65&&t<=90||t>=97&&t<=122||t>=161}function dc(t){return t>=48&&t<=57}const pc=new Nh(((t,e)=>{for(let i=!1,n=0,s=0;;s++){let{next:r}=t;if(fc(r)||45==r||95==r||i&&dc(r))!i&&(45!=r||s>0)&&(i=!0),n===s&&45==r&&n++,t.advance();else{if(92!=r||10==t.peek(1)){i&&t.acceptToken(40==r?101:2==n&&e.canShift(2)?2:102);break}t.advance(),t.next>-1&&t.advance(),i=!0}}})),mc=new Nh((t=>{if(uc.includes(t.peek(-1))){let{next:e}=t;(fc(e)||95==e||35==e||46==e||91==e||58==e&&fc(t.peek(1))||45==e||38==e)&&t.acceptToken(100)}})),gc=new Nh((t=>{if(!uc.includes(t.peek(-1))){let{next:e}=t;if(37==e&&(t.advance(),t.acceptToken(1)),fc(e)){do{t.advance()}while(fc(t.next)||dc(t.next));t.acceptToken(1)}}})),bc=da({"AtKeyword import charset namespace keyframes media supports":qa.definitionKeyword,"from to selector":qa.keyword,NamespaceName:qa.namespace,KeyframeName:qa.labelName,KeyframeRangeName:qa.operatorKeyword,TagName:qa.tagName,ClassName:qa.className,PseudoClassName:qa.constant(qa.className),IdName:qa.labelName,"FeatureName PropertyName":qa.propertyName,AttributeName:qa.attributeName,NumberLiteral:qa.number,KeywordQuery:qa.keyword,UnaryQueryOp:qa.operatorKeyword,"CallTag ValueName":qa.atom,VariableName:qa.variableName,Callee:qa.operatorKeyword,Unit:qa.unit,"UniversalSelector NestingSelector":qa.definitionOperator,MatchOp:qa.compareOperator,"ChildOp SiblingOp, LogicOp":qa.logicOperator,BinOp:qa.arithmeticOperator,Important:qa.modifier,Comment:qa.blockComment,ColorLiteral:qa.color,"ParenthesizedContent StringLiteral":qa.string,":":qa.punctuation,"PseudoOp #":qa.derefOperator,"; ,":qa.separator,"( )":qa.paren,"[ ]":qa.squareBracket,"{ }":qa.brace}),Qc={__proto__:null,lang:34,"nth-child":34,"nth-last-child":34,"nth-of-type":34,"nth-last-of-type":34,dir:34,"host-context":34,url:62,"url-prefix":62,domain:62,regexp:62,selector:140},Sc={__proto__:null,"@import":120,"@media":144,"@charset":148,"@namespace":152,"@keyframes":158,"@supports":170},vc={__proto__:null,not:134,only:134},wc=hc.deserialize({version:14,states:":jQYQ[OOO#_Q[OOP#fOWOOOOQP'#Cd'#CdOOQP'#Cc'#CcO#kQ[O'#CfO$_QXO'#CaO$fQ[O'#CiO$qQ[O'#DUO$vQ[O'#DXOOQP'#En'#EnO${QdO'#DhO%jQ[O'#DuO${QdO'#DwO%{Q[O'#DyO&WQ[O'#D|O&`Q[O'#ESO&nQ[O'#EUOOQS'#Em'#EmOOQS'#EX'#EXQYQ[OOO&uQXO'#CdO'jQWO'#DdO'oQWO'#EsO'zQ[O'#EsQOQWOOP(UO#tO'#C_POOO)C@])C@]OOQP'#Ch'#ChOOQP,59Q,59QO#kQ[O,59QO(aQ[O'#E]O({QWO,58{O)TQ[O,59TO$qQ[O,59pO$vQ[O,59sO(aQ[O,59vO(aQ[O,59xO(aQ[O,59yO)`Q[O'#DcOOQS,58{,58{OOQP'#Cl'#ClOOQO'#DS'#DSOOQP,59T,59TO)gQWO,59TO)lQWO,59TOOQP'#DW'#DWOOQP,59p,59pOOQO'#DY'#DYO)qQ`O,59sOOQS'#Cq'#CqO${QdO'#CrO)yQvO'#CtO+ZQtO,5:SOOQO'#Cy'#CyO)lQWO'#CxO+oQWO'#CzO+tQ[O'#DPOOQS'#Ep'#EpOOQO'#Dk'#DkO+|Q[O'#DrO,[QWO'#EtO&`Q[O'#DpO,jQWO'#DsOOQO'#Eu'#EuO)OQWO,5:aO,oQpO,5:cOOQS'#D{'#D{O,wQWO,5:eO,|Q[O,5:eOOQO'#EO'#EOO-UQWO,5:hO-ZQWO,5:nO-cQWO,5:pOOQS-E8V-E8VO-kQdO,5:OO-{Q[O'#E_O.YQWO,5;_O.YQWO,5;_POOO'#EW'#EWP.eO#tO,58yPOOO,58y,58yOOQP1G.l1G.lO/[QXO,5:wOOQO-E8Z-E8ZOOQS1G.g1G.gOOQP1G.o1G.oO)gQWO1G.oO)lQWO1G.oOOQP1G/[1G/[O/iQ`O1G/_O0SQXO1G/bO0jQXO1G/dO1QQXO1G/eO1hQWO,59}O1mQ[O'#DTO1tQdO'#CpOOQP1G/_1G/_O${QdO1G/_O1{QpO,59^OOQS,59`,59`O${QdO,59bO2TQWO1G/nOOQS,59d,59dO2YQ!bO,59fOOQS'#DQ'#DQOOQS'#EZ'#EZO2eQ[O,59kOOQS,59k,59kO2mQWO'#DkO2xQWO,5:WO2}QWO,5:^O&`Q[O,5:YO&`Q[O'#E`O3VQWO,5;`O3bQWO,5:[O(aQ[O,5:_OOQS1G/{1G/{OOQS1G/}1G/}OOQS1G0P1G0PO3sQWO1G0PO3xQdO'#EPOOQS1G0S1G0SOOQS1G0Y1G0YOOQS1G0[1G0[O4TQtO1G/jOOQO1G/j1G/jOOQO,5:y,5:yO4kQ[O,5:yOOQO-E8]-E8]O4xQWO1G0yPOOO-E8U-E8UPOOO1G.e1G.eOOQP7+$Z7+$ZOOQP7+$y7+$yO${QdO7+$yOOQS1G/i1G/iO5TQXO'#ErO5[QWO,59oO5aQtO'#EYO6XQdO'#EoO6cQWO,59[O6hQpO7+$yOOQS1G.x1G.xOOQS1G.|1G.|OOQS7+%Y7+%YOOQS1G/Q1G/QO6pQWO1G/QOOQS-E8X-E8XOOQS1G/V1G/VO${QdO1G/rOOQO1G/x1G/xOOQO1G/t1G/tO6uQWO,5:zOOQO-E8^-E8^O7TQXO1G/yOOQS7+%k7+%kO7[QYO'#CtOOQO'#ER'#ERO7gQ`O'#EQOOQO'#EQ'#EQO7rQWO'#EaO7zQdO,5:kOOQS,5:k,5:kO8VQtO'#E^O${QdO'#E^O9WQdO7+%UOOQO7+%U7+%UOOQO1G0e1G0eO9kQpO<<HeO9sQWO,5;^OOQP1G/Z1G/ZOOQS-E8W-E8WO${QdO'#E[O9{QWO,5;ZOOQT1G.v1G.vOOQP<<He<<HeOOQS7+$l7+$lO:TQdO7+%^OOQO7+%e7+%eOOQO,5:l,5:lO3{QdO'#EbO7rQWO,5:{OOQS,5:{,5:{OOQS-E8_-E8_OOQS1G0V1G0VO:[QtO,5:xOOQS-E8[-E8[OOQO<<Hp<<HpOOQPAN>PAN>PO;]QdO,5:vOOQO-E8Y-E8YOOQO<<Hx<<HxOOQO,5:|,5:|OOQO-E8`-E8`OOQS1G0g1G0g",stateData:";o~O#[OS#]QQ~OUYOXYOZTO^VO_VOrXOyWO!]aO!^ZO!j[O!l]O!n^O!q_O!w`O#YRO~OQfOUYOXYOZTO^VO_VOrXOyWO!]aO!^ZO!j[O!l]O!n^O!q_O!w`O#YeO~O#V#gP~P!ZO#]jO~O#YlO~OZnO^qO_qOrsOuoOyrO!PtO!SvO#WuO~O!UwO~P#pOa}O#XzO#YyO~O#Y!OO~O#Y!QO~OQ![Oc!TOg![Oi![Oo!YOr!ZO#X!WO#Y!SO#e!UO~Oc!^O!e!`O!h!aO#Y!]O!U#hP~Oi!fOo!YO#Y!eO~Oi!hO#Y!hO~Oc!^O!e!`O!h!aO#Y!]O~O!Z#hP~P%jOZWX^WX^!XX_WXrWXuWXyWX!PWX!SWX!UWX#WWX~O^!mO~O!Z!nO#V#gX!T#gX~O#V#gX!T#gX~P!ZO#^!qO#_!qO#`!sO~OUYOXYOZTO^VO_VOrXOyWO#YRO~OuoO!UwO~Oa!zO#XzO#YyO~O!T#gP~P!ZOc#RO~Oc#SO~Oq#TO}#UO~OP#WOchXkhX!ZhX!ehX!hhX#YhXbhXQhXghXihXohXrhXuhX!YhX#VhX#XhX#ehXqhX!ThX~Oc!^Ok#XO!e!`O!h!aO#Y!]O!Z#hP~Oc#[O~Oq#`O#Y#]O~Oc!^O!e!`O!h!aO#Y#aO~Ou#eO!c#dO!U#hX!Z#hX~Oc#hO~Ok#XO!Z#jO~O!Z#kO~Oi#lOo!YO~O!U#mO~O!UwO!c#dO~O!UwO!Z#pO~O!Y#rO!Z!Wa#V!Wa!T!Wa~P${O!Z#RX#V#RX!T#RX~P!ZO!Z!nO#V#ga!T#ga~O#^!qO#_!qO#`#xO~OZnO^qO_qOrsOyrO!PtO!SvO#WuO~Ou#Pa!U#Pab#Pa~P.pOq#zO}#{O~OZnO^qO_qOrsOyrO~Ou!Oi!P!Oi!S!Oi!U!Oi#W!Oib!Oi~P/qOu!Qi!P!Qi!S!Qi!U!Qi#W!Qib!Qi~P/qOu!Ri!P!Ri!S!Ri!U!Ri#W!Rib!Ri~P/qO!T#|O~Ob#fP~P(aOb#cP~P${Ob$TOk#XO~O!Z$VO~Ob$WOi$XOp$XO~Oq$ZO#Y#]O~O^!aXb!_X!c!_X~O^$[O~Ob$]O!c#dO~Ou#eO!U#ha!Z#ha~O!c#dOu!da!U!da!Z!dab!da~O!Z$bO~O!T$iO#Y$dO#e$cO~Ok#XOu$kO!Y$mO!Z!Wi#V!Wi!T!Wi~P${O!Z#Ra#V#Ra!T#Ra~P!ZO!Z!nO#V#gi!T#gi~Ob#fX~P#pOb$qO~Ok#XOQ!|Xb!|Xc!|Xg!|Xi!|Xo!|Xr!|Xu!|X#X!|X#Y!|X#e!|X~Ou$sOb#cX~P${Ob$uO~Ok#XOq$vO~Ob$wO~O!c#dOu#Sa!U#Sa!Z#Sa~Ob$yO~P.pOP#WOuhX!UhX~O#e$cOu!tX!U!tX~Ou${O!UwO~O!T%PO#Y$dO#e$cO~Ok#XOQ#QXc#QXg#QXi#QXo#QXr#QXu#QX!Y#QX!Z#QX#V#QX#X#QX#Y#QX#e#QX!T#QX~Ou$kO!Y%SO!Z!Wq#V!Wq!T!Wq~P${Ok#XOq%TO~OuoOb#fa~Ou$sOb#ca~Ob%WO~P${Ok#XOQ#Qac#Qag#Qai#Qao#Qar#Qau#Qa!Y#Qa!Z#Qa#V#Qa#X#Qa#Y#Qa#e#Qa!T#Qa~Ob#Oau#Oa~P${O#[p#]#ek!S#e~",goto:"-g#jPPP#kP#nP#w$WP#wP$g#wPP$mPPP$s$|$|P%`P$|P$|%z&^PPPP$|&vP&z'Q#wP'W#w'^P#wP#w#wPPP'd'y(WPP#nPP(_(_(i(_P(_P(_(_P#nP#nP#nP(l#nP(o(r(u(|#nP#nP)R)X)h)v)|*S*^*d*n*t*zPPPPPPPPPP+Q+Z+v+yP,o,r,x-RRkQ_bOPdhw!n#tkYOPdhotuvw!n#R#h#tkSOPdhotuvw!n#R#h#tQmTR!tnQ{VR!xqQ!x}Q#Z!XR#y!zq![Z]!T!m#S#U#X#q#{$Q$[$k$l$s$x%Up![Z]!T!m#S#U#X#q#{$Q$[$k$l$s$x%UU$f#m$h${R$z$eq!XZ]!T!m#S#U#X#q#{$Q$[$k$l$s$x%Up![Z]!T!m#S#U#X#q#{$Q$[$k$l$s$x%UQ!f^R#l!gT#^!Z#_Q|VR!yqQ!x|R#y!yQ!PWR!{rQ!RXR!|sQxUQ!wpQ#i!cQ#o!jQ#p!kQ$}$gR%Z$|SgPwQ!phQ#s!nR$n#tZfPhw!n#ta!b[`a!V!^!`#d#eR#b!^R!g^R!i_R#n!iS$g#m$hR%X${V$e#m$h${Q!rjR#w!rQdOShPwU!ldh#tR#t!nQ$Q#SU$r$Q$x%UQ$x$[R%U$sQ#_!ZR$Y#_Q$t$QR%V$tQpUS!vp$pR$p#}Q$l#qR%R$lQ!ogS#u!o#vR#v!pQ#f!_R$`#fQ$h#mR%O$hQ$|$gR%Y$|_cOPdhw!n#t^UOPdhw!n#tQ!uoQ!}tQ#OuQ#PvQ#}#RR$a#hR$R#SQ!VZQ!d]Q#V!TQ#q!m[$P#S$Q$[$s$x%UQ$S#UQ$U#XS$j#q$lQ$o#{R%Q$kR$O#RQiPR#QwQ!c[Q!kaR#Y!VU!_[a!VQ!j`Q#c!^Q#g!`Q$^#dR$_#e",nodeNames:"⚠ Unit VariableName Comment StyleSheet RuleSet UniversalSelector TagSelector TagName NestingSelector ClassSelector . ClassName PseudoClassSelector : :: PseudoClassName PseudoClassName ) ( ArgList ValueName ParenthesizedValue ColorLiteral NumberLiteral StringLiteral BinaryExpression BinOp CallExpression Callee CallLiteral CallTag ParenthesizedContent ] [ LineNames LineName , PseudoClassName ArgList IdSelector # IdName AttributeSelector AttributeName MatchOp ChildSelector ChildOp DescendantSelector SiblingSelector SiblingOp } { Block Declaration PropertyName Important ; ImportStatement AtKeyword import KeywordQuery FeatureQuery FeatureName BinaryQuery LogicOp UnaryQuery UnaryQueryOp ParenthesizedQuery SelectorQuery selector MediaStatement media CharsetStatement charset NamespaceStatement namespace NamespaceName KeyframesStatement keyframes KeyframeName KeyframeList KeyframeSelector KeyframeRangeName SupportsStatement supports AtRule Styles",maxTerm:117,nodeProps:[["isolate",-2,3,25,""],["openedBy",18,"(",33,"[",51,"{"],["closedBy",19,")",34,"]",52,"}"]],propSources:[bc],skippedNodes:[0,3,88],repeatNodeCount:11,tokenData:"J^~R!^OX$}X^%u^p$}pq%uqr)Xrs.Rst/utu6duv$}vw7^wx7oxy9^yz9oz{9t{|:_|}?Q}!O?c!O!P@Q!P!Q@i!Q![Ab![!]B]!]!^CX!^!_$}!_!`Cj!`!aC{!a!b$}!b!cDw!c!}$}!}#OFa#O#P$}#P#QFr#Q#R6d#R#T$}#T#UGT#U#c$}#c#dHf#d#o$}#o#pH{#p#q6d#q#rI^#r#sIo#s#y$}#y#z%u#z$f$}$f$g%u$g#BY$}#BY#BZ%u#BZ$IS$}$IS$I_%u$I_$I|$}$I|$JO%u$JO$JT$}$JT$JU%u$JU$KV$}$KV$KW%u$KW&FU$}&FU&FV%u&FV;'S$};'S;=`JW<%lO$}`%QSOy%^z;'S%^;'S;=`%o<%lO%^`%cSp`Oy%^z;'S%^;'S;=`%o<%lO%^`%rP;=`<%l%^~%zh#[~OX%^X^'f^p%^pq'fqy%^z#y%^#y#z'f#z$f%^$f$g'f$g#BY%^#BY#BZ'f#BZ$IS%^$IS$I_'f$I_$I|%^$I|$JO'f$JO$JT%^$JT$JU'f$JU$KV%^$KV$KW'f$KW&FU%^&FU&FV'f&FV;'S%^;'S;=`%o<%lO%^~'mh#[~p`OX%^X^'f^p%^pq'fqy%^z#y%^#y#z'f#z$f%^$f$g'f$g#BY%^#BY#BZ'f#BZ$IS%^$IS$I_'f$I_$I|%^$I|$JO'f$JO$JT%^$JT$JU'f$JU$KV%^$KV$KW'f$KW&FU%^&FU&FV'f&FV;'S%^;'S;=`%o<%lO%^l)[UOy%^z#]%^#]#^)n#^;'S%^;'S;=`%o<%lO%^l)sUp`Oy%^z#a%^#a#b*V#b;'S%^;'S;=`%o<%lO%^l*[Up`Oy%^z#d%^#d#e*n#e;'S%^;'S;=`%o<%lO%^l*sUp`Oy%^z#c%^#c#d+V#d;'S%^;'S;=`%o<%lO%^l+[Up`Oy%^z#f%^#f#g+n#g;'S%^;'S;=`%o<%lO%^l+sUp`Oy%^z#h%^#h#i,V#i;'S%^;'S;=`%o<%lO%^l,[Up`Oy%^z#T%^#T#U,n#U;'S%^;'S;=`%o<%lO%^l,sUp`Oy%^z#b%^#b#c-V#c;'S%^;'S;=`%o<%lO%^l-[Up`Oy%^z#h%^#h#i-n#i;'S%^;'S;=`%o<%lO%^l-uS!Y[p`Oy%^z;'S%^;'S;=`%o<%lO%^~.UWOY.RZr.Rrs.ns#O.R#O#P.s#P;'S.R;'S;=`/o<%lO.R~.sOi~~.vRO;'S.R;'S;=`/P;=`O.R~/SXOY.RZr.Rrs.ns#O.R#O#P.s#P;'S.R;'S;=`/o;=`<%l.R<%lO.R~/rP;=`<%l.Rn/zYyQOy%^z!Q%^!Q![0j![!c%^!c!i0j!i#T%^#T#Z0j#Z;'S%^;'S;=`%o<%lO%^l0oYp`Oy%^z!Q%^!Q![1_![!c%^!c!i1_!i#T%^#T#Z1_#Z;'S%^;'S;=`%o<%lO%^l1dYp`Oy%^z!Q%^!Q![2S![!c%^!c!i2S!i#T%^#T#Z2S#Z;'S%^;'S;=`%o<%lO%^l2ZYg[p`Oy%^z!Q%^!Q![2y![!c%^!c!i2y!i#T%^#T#Z2y#Z;'S%^;'S;=`%o<%lO%^l3QYg[p`Oy%^z!Q%^!Q![3p![!c%^!c!i3p!i#T%^#T#Z3p#Z;'S%^;'S;=`%o<%lO%^l3uYp`Oy%^z!Q%^!Q![4e![!c%^!c!i4e!i#T%^#T#Z4e#Z;'S%^;'S;=`%o<%lO%^l4lYg[p`Oy%^z!Q%^!Q![5[![!c%^!c!i5[!i#T%^#T#Z5[#Z;'S%^;'S;=`%o<%lO%^l5aYp`Oy%^z!Q%^!Q![6P![!c%^!c!i6P!i#T%^#T#Z6P#Z;'S%^;'S;=`%o<%lO%^l6WSg[p`Oy%^z;'S%^;'S;=`%o<%lO%^d6gUOy%^z!_%^!_!`6y!`;'S%^;'S;=`%o<%lO%^d7QS}Sp`Oy%^z;'S%^;'S;=`%o<%lO%^b7cSXQOy%^z;'S%^;'S;=`%o<%lO%^~7rWOY7oZw7owx.nx#O7o#O#P8[#P;'S7o;'S;=`9W<%lO7o~8_RO;'S7o;'S;=`8h;=`O7o~8kXOY7oZw7owx.nx#O7o#O#P8[#P;'S7o;'S;=`9W;=`<%l7o<%lO7o~9ZP;=`<%l7on9cSc^Oy%^z;'S%^;'S;=`%o<%lO%^~9tOb~n9{UUQkWOy%^z!_%^!_!`6y!`;'S%^;'S;=`%o<%lO%^n:fWkW!SQOy%^z!O%^!O!P;O!P!Q%^!Q![>T![;'S%^;'S;=`%o<%lO%^l;TUp`Oy%^z!Q%^!Q![;g![;'S%^;'S;=`%o<%lO%^l;nYp`#e[Oy%^z!Q%^!Q![;g![!g%^!g!h<^!h#X%^#X#Y<^#Y;'S%^;'S;=`%o<%lO%^l<cYp`Oy%^z{%^{|=R|}%^}!O=R!O!Q%^!Q![=j![;'S%^;'S;=`%o<%lO%^l=WUp`Oy%^z!Q%^!Q![=j![;'S%^;'S;=`%o<%lO%^l=qUp`#e[Oy%^z!Q%^!Q![=j![;'S%^;'S;=`%o<%lO%^l>[[p`#e[Oy%^z!O%^!O!P;g!P!Q%^!Q![>T![!g%^!g!h<^!h#X%^#X#Y<^#Y;'S%^;'S;=`%o<%lO%^n?VSu^Oy%^z;'S%^;'S;=`%o<%lO%^l?hWkWOy%^z!O%^!O!P;O!P!Q%^!Q![>T![;'S%^;'S;=`%o<%lO%^n@VUZQOy%^z!Q%^!Q![;g![;'S%^;'S;=`%o<%lO%^~@nTkWOy%^z{@}{;'S%^;'S;=`%o<%lO%^~AUSp`#]~Oy%^z;'S%^;'S;=`%o<%lO%^lAg[#e[Oy%^z!O%^!O!P;g!P!Q%^!Q![>T![!g%^!g!h<^!h#X%^#X#Y<^#Y;'S%^;'S;=`%o<%lO%^bBbU^QOy%^z![%^![!]Bt!];'S%^;'S;=`%o<%lO%^bB{S_Qp`Oy%^z;'S%^;'S;=`%o<%lO%^nC^S!Z^Oy%^z;'S%^;'S;=`%o<%lO%^dCoS}SOy%^z;'S%^;'S;=`%o<%lO%^bDQU!PQOy%^z!`%^!`!aDd!a;'S%^;'S;=`%o<%lO%^bDkS!PQp`Oy%^z;'S%^;'S;=`%o<%lO%^bDzWOy%^z!c%^!c!}Ed!}#T%^#T#oEd#o;'S%^;'S;=`%o<%lO%^bEk[!]Qp`Oy%^z}%^}!OEd!O!Q%^!Q![Ed![!c%^!c!}Ed!}#T%^#T#oEd#o;'S%^;'S;=`%o<%lO%^nFfSr^Oy%^z;'S%^;'S;=`%o<%lO%^nFwSq^Oy%^z;'S%^;'S;=`%o<%lO%^bGWUOy%^z#b%^#b#cGj#c;'S%^;'S;=`%o<%lO%^bGoUp`Oy%^z#W%^#W#XHR#X;'S%^;'S;=`%o<%lO%^bHYS!cQp`Oy%^z;'S%^;'S;=`%o<%lO%^bHiUOy%^z#f%^#f#gHR#g;'S%^;'S;=`%o<%lO%^fIQS!UUOy%^z;'S%^;'S;=`%o<%lO%^nIcS!T^Oy%^z;'S%^;'S;=`%o<%lO%^fItU!SQOy%^z!_%^!_!`6y!`;'S%^;'S;=`%o<%lO%^`JZP;=`<%l$}",tokenizers:[mc,gc,pc,1,2,3,4,new Ih("m~RRYZ[z{a~~g~aO#_~~dP!P!Qg~lO#`~~",28,106)],topRules:{StyleSheet:[0,4],Styles:[1,87]},specialized:[{term:101,get:t=>Qc[t]||-1},{term:59,get:t=>Sc[t]||-1},{term:102,get:t=>vc[t]||-1}],tokenPrec:1219});let yc=null;function xc(){if(!yc&&"object"==typeof document&&document.body){let{style:t}=document.body,e=[],i=new Set;for(let n in t)"cssText"!=n&&"cssFloat"!=n&&"string"==typeof t[n]&&(/[A-Z]/.test(n)&&(n=n.replace(/[A-Z]/g,(t=>"-"+t.toLowerCase()))),i.has(n)||(e.push(n),i.add(n)));yc=e.sort().map((t=>({type:"property",label:t})))}return yc||[]}const kc=["active","after","any-link","autofill","backdrop","before","checked","cue","default","defined","disabled","empty","enabled","file-selector-button","first","first-child","first-letter","first-line","first-of-type","focus","focus-visible","focus-within","fullscreen","has","host","host-context","hover","in-range","indeterminate","invalid","is","lang","last-child","last-of-type","left","link","marker","modal","not","nth-child","nth-last-child","nth-last-of-type","nth-of-type","only-child","only-of-type","optional","out-of-range","part","placeholder","placeholder-shown","read-only","read-write","required","right","root","scope","selection","slotted","target","target-text","valid","visited","where"].map((t=>({type:"class",label:t}))),Pc=["above","absolute","activeborder","additive","activecaption","after-white-space","ahead","alias","all","all-scroll","alphabetic","alternate","always","antialiased","appworkspace","asterisks","attr","auto","auto-flow","avoid","avoid-column","avoid-page","avoid-region","axis-pan","background","backwards","baseline","below","bidi-override","blink","block","block-axis","bold","bolder","border","border-box","both","bottom","break","break-all","break-word","bullets","button","button-bevel","buttonface","buttonhighlight","buttonshadow","buttontext","calc","capitalize","caps-lock-indicator","caption","captiontext","caret","cell","center","checkbox","circle","cjk-decimal","clear","clip","close-quote","col-resize","collapse","color","color-burn","color-dodge","column","column-reverse","compact","condensed","contain","content","contents","content-box","context-menu","continuous","copy","counter","counters","cover","crop","cross","crosshair","currentcolor","cursive","cyclic","darken","dashed","decimal","decimal-leading-zero","default","default-button","dense","destination-atop","destination-in","destination-out","destination-over","difference","disc","discard","disclosure-closed","disclosure-open","document","dot-dash","dot-dot-dash","dotted","double","down","e-resize","ease","ease-in","ease-in-out","ease-out","element","ellipse","ellipsis","embed","end","ethiopic-abegede-gez","ethiopic-halehame-aa-er","ethiopic-halehame-gez","ew-resize","exclusion","expanded","extends","extra-condensed","extra-expanded","fantasy","fast","fill","fill-box","fixed","flat","flex","flex-end","flex-start","footnotes","forwards","from","geometricPrecision","graytext","grid","groove","hand","hard-light","help","hidden","hide","higher","highlight","highlighttext","horizontal","hsl","hsla","hue","icon","ignore","inactiveborder","inactivecaption","inactivecaptiontext","infinite","infobackground","infotext","inherit","initial","inline","inline-axis","inline-block","inline-flex","inline-grid","inline-table","inset","inside","intrinsic","invert","italic","justify","keep-all","landscape","large","larger","left","level","lighter","lighten","line-through","linear","linear-gradient","lines","list-item","listbox","listitem","local","logical","loud","lower","lower-hexadecimal","lower-latin","lower-norwegian","lowercase","ltr","luminosity","manipulation","match","matrix","matrix3d","medium","menu","menutext","message-box","middle","min-intrinsic","mix","monospace","move","multiple","multiple_mask_images","multiply","n-resize","narrower","ne-resize","nesw-resize","no-close-quote","no-drop","no-open-quote","no-repeat","none","normal","not-allowed","nowrap","ns-resize","numbers","numeric","nw-resize","nwse-resize","oblique","opacity","open-quote","optimizeLegibility","optimizeSpeed","outset","outside","outside-shape","overlay","overline","padding","padding-box","painted","page","paused","perspective","pinch-zoom","plus-darker","plus-lighter","pointer","polygon","portrait","pre","pre-line","pre-wrap","preserve-3d","progress","push-button","radial-gradient","radio","read-only","read-write","read-write-plaintext-only","rectangle","region","relative","repeat","repeating-linear-gradient","repeating-radial-gradient","repeat-x","repeat-y","reset","reverse","rgb","rgba","ridge","right","rotate","rotate3d","rotateX","rotateY","rotateZ","round","row","row-resize","row-reverse","rtl","run-in","running","s-resize","sans-serif","saturation","scale","scale3d","scaleX","scaleY","scaleZ","screen","scroll","scrollbar","scroll-position","se-resize","self-start","self-end","semi-condensed","semi-expanded","separate","serif","show","single","skew","skewX","skewY","skip-white-space","slide","slider-horizontal","slider-vertical","sliderthumb-horizontal","sliderthumb-vertical","slow","small","small-caps","small-caption","smaller","soft-light","solid","source-atop","source-in","source-out","source-over","space","space-around","space-between","space-evenly","spell-out","square","start","static","status-bar","stretch","stroke","stroke-box","sub","subpixel-antialiased","svg_masks","super","sw-resize","symbolic","symbols","system-ui","table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group","text","text-bottom","text-top","textarea","textfield","thick","thin","threeddarkshadow","threedface","threedhighlight","threedlightshadow","threedshadow","to","top","transform","translate","translate3d","translateX","translateY","translateZ","transparent","ultra-condensed","ultra-expanded","underline","unidirectional-pan","unset","up","upper-latin","uppercase","url","var","vertical","vertical-text","view-box","visible","visibleFill","visiblePainted","visibleStroke","visual","w-resize","wait","wave","wider","window","windowframe","windowtext","words","wrap","wrap-reverse","x-large","x-small","xor","xx-large","xx-small"].map((t=>({type:"keyword",label:t}))).concat(["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","grey","green","greenyellow","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"].map((t=>({type:"constant",label:t})))),$c=["a","abbr","address","article","aside","b","bdi","bdo","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","dd","del","details","dfn","dialog","div","dl","dt","em","figcaption","figure","footer","form","header","hgroup","h1","h2","h3","h4","h5","h6","hr","html","i","iframe","img","input","ins","kbd","label","legend","li","main","meter","nav","ol","output","p","pre","ruby","section","select","small","source","span","strong","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","tr","u","ul"].map((t=>({type:"type",label:t}))),Zc=["@charset","@color-profile","@container","@counter-style","@font-face","@font-feature-values","@font-palette-values","@import","@keyframes","@layer","@media","@namespace","@page","@position-try","@property","@scope","@starting-style","@supports","@view-transition"].map((t=>({type:"keyword",label:t}))),Tc=/^(\w[\w-]*|-\w[\w-]*|)$/,Xc=/^-(-[\w-]*)?$/;const Rc=new T,Ac=["Declaration"];function Cc(t){for(let e=t;;){if(e.type.isTop)return e;if(!(e=e.parent))return t}}function Mc(t,e,i){if(e.to-e.from>4096){let n=Rc.get(e);if(n)return n;let s=[],r=new Set,o=e.cursor(c.IncludeAnonymous);if(o.firstChild())do{for(let e of Mc(t,o.node,i))r.has(e.label)||(r.add(e.label),s.push(e))}while(o.nextSibling());return Rc.set(e,s),s}{let n=[],s=new Set;return e.cursor().iterate((e=>{var r;if(i(e)&&e.matchContext(Ac)&&":"==(null===(r=e.node.nextSibling)||void 0===r?void 0:r.name)){let i=t.sliceString(e.from,e.to);s.has(i)||(s.add(i),n.push({label:i,type:"variable"}))}})),n}}const qc=t=>e=>{let{state:i,pos:n}=e,s=Wa(i).resolveInner(n,-1),r=s.type.isError&&s.from==s.to-1&&"-"==i.doc.sliceString(s.from,s.to);if("PropertyName"==s.name||(r||"TagName"==s.name)&&/^(Block|Styles)$/.test(s.resolve(s.to).name))return{from:s.from,options:xc(),validFor:Tc};if("ValueName"==s.name)return{from:s.from,options:Pc,validFor:Tc};if("PseudoClassName"==s.name)return{from:s.from,options:kc,validFor:Tc};if(t(s)||(e.explicit||r)&&function(t,e){var i;if(("("==t.name||t.type.isError)&&(t=t.parent||t),"ArgList"!=t.name)return!1;let n=null===(i=t.parent)||void 0===i?void 0:i.firstChild;return"Callee"==(null==n?void 0:n.name)&&"var"==e.sliceString(n.from,n.to)}(s,i.doc))return{from:t(s)||r?s.from:n,options:Mc(i.doc,Cc(s),t),validFor:Xc};if("TagName"==s.name){for(let{parent:t}=s;t;t=t.parent)if("Block"==t.name)return{from:s.from,options:xc(),validFor:Tc};return{from:s.from,options:$c,validFor:Tc}}if("AtKeyword"==s.name)return{from:s.from,options:Zc,validFor:Tc};if(!e.explicit)return null;let o=s.resolve(n),a=o.childBefore(n);return a&&":"==a.name&&"PseudoClassSelector"==o.name?{from:n,options:kc,validFor:Tc}:a&&":"==a.name&&"Declaration"==o.name||"ArgList"==o.name?{from:n,options:Pc,validFor:Tc}:"Block"==o.name||"Styles"==o.name?{from:n,options:xc(),validFor:Tc}:null},Vc=qc((t=>"VariableName"==t.name)),Yc=Da.define({name:"css",parser:wc.configure({props:[cl.add({Declaration:Ql()}),wl.add({"Block KeyframeList":yl})]}),languageData:{commentTokens:{block:{open:"/*",close:"*/"}},indentOnInput:/^\s*\}$/,wordChars:"-"}});function zc(){return new il(Yc,Yc.data.of({autocomplete:Vc}))}class _c{constructor(t,e,i,n){this.state=t,this.pos=e,this.explicit=i,this.view=n,this.abortListeners=[],this.abortOnDocChange=!1}tokenBefore(t){let e=Wa(this.state).resolveInner(this.pos,-1);for(;e&&t.indexOf(e.name)<0;)e=e.parent;return e?{from:e.from,to:this.pos,text:this.state.sliceDoc(e.from,this.pos),type:e.type}:null}matchBefore(t){let e=this.state.doc.lineAt(this.pos),i=Math.max(e.from,this.pos-250),n=e.text.slice(i-e.from,this.pos-e.from),s=n.search(Gc(t,!1));return s<0?null:{from:i+s,to:this.pos,text:n.slice(s)}}get aborted(){return null==this.abortListeners}addEventListener(t,e,i){"abort"==t&&this.abortListeners&&(this.abortListeners.push(e),i&&i.onDocChange&&(this.abortOnDocChange=!0))}}function jc(t){let e=Object.keys(t).join(""),i=/\w/.test(e);return i&&(e=e.replace(/\w/g,"")),`[${i?"\\w":""}${e.replace(/[^\w\s]/g,"\\$&")}]`}function Ec(t){let e=t.map((t=>"string"==typeof t?{label:t}:t)),[i,n]=e.every((t=>/^\w+$/.test(t.label)))?[/\w*$/,/\w+$/]:function(t){let e=Object.create(null),i=Object.create(null);for(let{label:n}of t){e[n[0]]=!0;for(let t=1;t<n.length;t++)i[n[t]]=!0}let n=jc(e)+jc(i)+"*$";return[new RegExp("^"+n),new RegExp(n)]}(e);return t=>{let s=t.matchBefore(n);return s||t.explicit?{from:s?s.from:t.pos,options:e,validFor:i}:null}}function Dc(t,e){return i=>{for(let e=Wa(i.state).resolveInner(i.pos,-1);e;e=e.parent){if(t.indexOf(e.name)>-1)return null;if(e.type.isTop)break}return e(i)}}class Wc{constructor(t,e,i,n){this.completion=t,this.source=e,this.match=i,this.score=n}}function Lc(t){return t.selection.main.from}function Gc(t,e){var i;let{source:n}=t,s=e&&"^"!=n[0],r="$"!=n[n.length-1];return s||r?new RegExp(`${s?"^":""}(?:${n})${r?"$":""}`,null!==(i=t.flags)&&void 0!==i?i:t.ignoreCase?"i":""):t}const Bc=re.define();const Uc=new WeakMap;function Ic(t){if(!Array.isArray(t))return t;let e=Uc.get(t);return e||Uc.set(t,e=Ec(t)),e}const Nc=le.define(),Hc=le.define();class Fc{constructor(t){this.pattern=t,this.chars=[],this.folded=[],this.any=[],this.precise=[],this.byWord=[],this.score=0,this.matched=[];for(let e=0;e<t.length;){let i=pt(t,e),n=gt(i);this.chars.push(i);let s=t.slice(e,e+n),r=s.toUpperCase();this.folded.push(pt(r==s?s.toLowerCase():r,0)),e+=n}this.astral=t.length!=this.chars.length}ret(t,e){return this.score=t,this.matched=e,this}match(t){if(0==this.pattern.length)return this.ret(-100,[]);if(t.length<this.pattern.length)return null;let{chars:e,folded:i,any:n,precise:s,byWord:r}=this;if(1==e.length){let n=pt(t,0),s=gt(n),r=s==t.length?0:-100;if(n==e[0]);else{if(n!=i[0])return null;r+=-200}return this.ret(r,[0,s])}let o=t.indexOf(this.pattern);if(0==o)return this.ret(t.length==this.pattern.length?0:-100,[0,this.pattern.length]);let a=e.length,l=0;if(o<0){for(let s=0,r=Math.min(t.length,200);s<r&&l<a;){let r=pt(t,s);r!=e[l]&&r!=i[l]||(n[l++]=s),s+=gt(r)}if(l<a)return null}let h=0,c=0,O=!1,u=0,f=-1,d=-1,p=/[a-z]/.test(t),m=!0;for(let n=0,l=Math.min(t.length,200),g=0;n<l&&c<a;){let l=pt(t,n);o<0&&(h<a&&l==e[h]&&(s[h++]=n),u<a&&(l==e[u]||l==i[u]?(0==u&&(f=n),d=n+1,u++):u=0));let b,Q=l<255?l>=48&&l<=57||l>=97&&l<=122?2:l>=65&&l<=90?1:0:(b=mt(l))!=b.toLowerCase()?1:b!=b.toUpperCase()?2:0;(!n||1==Q&&p||0==g&&0!=Q)&&(e[c]==l||i[c]==l&&(O=!0)?r[c++]=n:r.length&&(m=!1)),g=Q,n+=gt(l)}return c==a&&0==r[0]&&m?this.result((O?-200:0)-100,r,t):u==a&&0==f?this.ret(-200-t.length+(d==t.length?0:-100),[0,d]):o>-1?this.ret(-700-t.length,[o,o+this.pattern.length]):u==a?this.ret(-900-t.length,[f,d]):c==a?this.result((O?-200:0)-100-700+(m?0:-1100),r,t):2==e.length?null:this.result((n[0]?-700:0)-200-1100,n,t)}result(t,e,i){let n=[],s=0;for(let t of e){let e=t+(this.astral?gt(pt(i,t)):1);s&&n[s-1]==t?n[s-1]=e:(n[s++]=t,n[s++]=e)}return this.ret(t-i.length,n)}}class Kc{constructor(t){this.pattern=t,this.matched=[],this.score=0,this.folded=t.toLowerCase()}match(t){if(t.length<this.pattern.length)return null;let e=t.slice(0,this.pattern.length),i=e==this.pattern?0:e.toLowerCase()==this.folded?-200:null;return null==i?null:(this.matched=[0,e.length],this.score=i+(t.length==this.pattern.length?0:-100),this)}}const Jc=At.define({combine:t=>ve(t,{activateOnTyping:!0,activateOnCompletion:()=>!1,activateOnTypingDelay:100,selectOnOpen:!0,override:null,closeOnBlur:!0,maxRenderedOptions:100,defaultKeymap:!0,tooltipClass:()=>"",optionClass:()=>"",aboveCursor:!1,icons:!0,addToOptions:[],positionInfo:eO,filterStrict:!1,compareCompletions:(t,e)=>t.label.localeCompare(e.label),interactionDelay:75,updateSyncTime:100},{defaultKeymap:(t,e)=>t&&e,closeOnBlur:(t,e)=>t&&e,icons:(t,e)=>t&&e,tooltipClass:(t,e)=>i=>tO(t(i),e(i)),optionClass:(t,e)=>i=>tO(t(i),e(i)),addToOptions:(t,e)=>t.concat(e),filterStrict:(t,e)=>t||e})});function tO(t,e){return t?e?t+" "+e:t:e}function eO(t,e,i,n,s,r){let o,a,l=t.textDirection==an.RTL,h=l,c=!1,O="top",u=e.left-s.left,f=s.right-e.right,d=n.right-n.left,p=n.bottom-n.top;if(h&&u<Math.min(d,f)?h=!1:!h&&f<Math.min(d,u)&&(h=!0),d<=(h?u:f))o=Math.max(s.top,Math.min(i.top,s.bottom-p))-e.top,a=Math.min(400,h?u:f);else{c=!0,a=Math.min(400,(l?e.right:s.right-e.left)-30);let t=s.bottom-e.bottom;t>=p||t>e.top?o=i.bottom-e.top:(O="bottom",o=e.bottom-i.top)}return{style:`${O}: ${o/((e.bottom-e.top)/r.offsetHeight)}px; max-width: ${a/((e.right-e.left)/r.offsetWidth)}px`,class:"cm-completionInfo-"+(c?l?"left-narrow":"right-narrow":h?"left":"right")}}function iO(t,e,i){if(t<=i)return{from:0,to:t};if(e<0&&(e=0),e<=t>>1){let t=Math.floor(e/i);return{from:t*i,to:(t+1)*i}}let n=Math.floor((t-e)/i);return{from:t-(n+1)*i,to:t-n*i}}class nO{constructor(t,e,i){this.view=t,this.stateField=e,this.applyCompletion=i,this.info=null,this.infoDestroy=null,this.placeInfoReq={read:()=>this.measureInfo(),write:t=>this.placeInfo(t),key:this},this.space=null,this.currentClass="";let n=t.state.field(e),{options:s,selected:r}=n.open,o=t.state.facet(Jc);this.optionContent=function(t){let e=t.addToOptions.slice();return t.icons&&e.push({render(t){let e=document.createElement("div");return e.classList.add("cm-completionIcon"),t.type&&e.classList.add(...t.type.split(/\s+/g).map((t=>"cm-completionIcon-"+t))),e.setAttribute("aria-hidden","true"),e},position:20}),e.push({render(t,e,i,n){let s=document.createElement("span");s.className="cm-completionLabel";let r=t.displayLabel||t.label,o=0;for(let t=0;t<n.length;){let e=n[t++],i=n[t++];e>o&&s.appendChild(document.createTextNode(r.slice(o,e)));let a=s.appendChild(document.createElement("span"));a.appendChild(document.createTextNode(r.slice(e,i))),a.className="cm-completionMatchedText",o=i}return o<r.length&&s.appendChild(document.createTextNode(r.slice(o))),s},position:50},{render(t){if(!t.detail)return null;let e=document.createElement("span");return e.className="cm-completionDetail",e.textContent=t.detail,e},position:80}),e.sort(((t,e)=>t.position-e.position)).map((t=>t.render))}(o),this.optionClass=o.optionClass,this.tooltipClass=o.tooltipClass,this.range=iO(s.length,r,o.maxRenderedOptions),this.dom=document.createElement("div"),this.dom.className="cm-tooltip-autocomplete",this.updateTooltipClass(t.state),this.dom.addEventListener("mousedown",(i=>{let{options:n}=t.state.field(e).open;for(let e,s=i.target;s&&s!=this.dom;s=s.parentNode)if("LI"==s.nodeName&&(e=/-(\d+)$/.exec(s.id))&&+e[1]<n.length)return this.applyCompletion(t,n[+e[1]]),void i.preventDefault()})),this.dom.addEventListener("focusout",(e=>{let i=t.state.field(this.stateField,!1);i&&i.tooltip&&t.state.facet(Jc).closeOnBlur&&e.relatedTarget!=t.contentDOM&&t.dispatch({effects:Hc.of(null)})})),this.showOptions(s,n.id)}mount(){this.updateSel()}showOptions(t,e){this.list&&this.list.remove(),this.list=this.dom.appendChild(this.createListBox(t,e,this.range)),this.list.addEventListener("scroll",(()=>{this.info&&this.view.requestMeasure(this.placeInfoReq)}))}update(t){var e;let i=t.state.field(this.stateField),n=t.startState.field(this.stateField);if(this.updateTooltipClass(t.state),i!=n){let{options:s,selected:r,disabled:o}=i.open;n.open&&n.open.options==s||(this.range=iO(s.length,r,t.state.facet(Jc).maxRenderedOptions),this.showOptions(s,i.id)),this.updateSel(),o!=(null===(e=n.open)||void 0===e?void 0:e.disabled)&&this.dom.classList.toggle("cm-tooltip-autocomplete-disabled",!!o)}}updateTooltipClass(t){let e=this.tooltipClass(t);if(e!=this.currentClass){for(let t of this.currentClass.split(" "))t&&this.dom.classList.remove(t);for(let t of e.split(" "))t&&this.dom.classList.add(t);this.currentClass=e}}positioned(t){this.space=t,this.info&&this.view.requestMeasure(this.placeInfoReq)}updateSel(){let t=this.view.state.field(this.stateField),e=t.open;if((e.selected>-1&&e.selected<this.range.from||e.selected>=this.range.to)&&(this.range=iO(e.options.length,e.selected,this.view.state.facet(Jc).maxRenderedOptions),this.showOptions(e.options,t.id)),this.updateSelectedOption(e.selected)){this.destroyInfo();let{completion:i}=e.options[e.selected],{info:n}=i;if(!n)return;let s="string"==typeof n?document.createTextNode(n):n(i);if(!s)return;"then"in s?s.then((e=>{e&&this.view.state.field(this.stateField,!1)==t&&this.addInfoPane(e,i)})).catch((t=>Yn(this.view.state,t,"completion info"))):this.addInfoPane(s,i)}}addInfoPane(t,e){this.destroyInfo();let i=this.info=document.createElement("div");if(i.className="cm-tooltip cm-completionInfo",null!=t.nodeType)i.appendChild(t),this.infoDestroy=null;else{let{dom:e,destroy:n}=t;i.appendChild(e),this.infoDestroy=n||null}this.dom.appendChild(i),this.view.requestMeasure(this.placeInfoReq)}updateSelectedOption(t){let e=null;for(let i=this.list.firstChild,n=this.range.from;i;i=i.nextSibling,n++)"LI"==i.nodeName&&i.id?n==t?i.hasAttribute("aria-selected")||(i.setAttribute("aria-selected","true"),e=i):i.hasAttribute("aria-selected")&&i.removeAttribute("aria-selected"):n--;return e&&function(t,e){let i=t.getBoundingClientRect(),n=e.getBoundingClientRect(),s=i.height/t.offsetHeight;n.top<i.top?t.scrollTop-=(i.top-n.top)/s:n.bottom>i.bottom&&(t.scrollTop+=(n.bottom-i.bottom)/s)}(this.list,e),e}measureInfo(){let t=this.dom.querySelector("[aria-selected]");if(!t||!this.info)return null;let e=this.dom.getBoundingClientRect(),i=this.info.getBoundingClientRect(),n=t.getBoundingClientRect(),s=this.space;if(!s){let t=this.dom.ownerDocument.defaultView||window;s={left:0,top:0,right:t.innerWidth,bottom:t.innerHeight}}return n.top>Math.min(s.bottom,e.bottom)-10||n.bottom<Math.max(s.top,e.top)+10?null:this.view.state.facet(Jc).positionInfo(this.view,e,n,i,s,this.dom)}placeInfo(t){this.info&&(t?(t.style&&(this.info.style.cssText=t.style),this.info.className="cm-tooltip cm-completionInfo "+(t.class||"")):this.info.style.cssText="top: -1e6px")}createListBox(t,e,i){const n=document.createElement("ul");n.id=e,n.setAttribute("role","listbox"),n.setAttribute("aria-expanded","true"),n.setAttribute("aria-label",this.view.state.phrase("Completions"));let s=null;for(let r=i.from;r<i.to;r++){let{completion:o,match:a}=t[r],{section:l}=o;if(l){let t="string"==typeof l?l:l.name;if(t!=s&&(r>i.from||0==i.from))if(s=t,"string"!=typeof l&&l.header)n.appendChild(l.header(l));else{n.appendChild(document.createElement("completion-section")).textContent=t}}const h=n.appendChild(document.createElement("li"));h.id=e+"-"+r,h.setAttribute("role","option");let c=this.optionClass(o);c&&(h.className=c);for(let t of this.optionContent){let e=t(o,this.view.state,this.view,a);e&&h.appendChild(e)}}return i.from&&n.classList.add("cm-completionListIncompleteTop"),i.to<t.length&&n.classList.add("cm-completionListIncompleteBottom"),n}destroyInfo(){this.info&&(this.infoDestroy&&this.infoDestroy(),this.info.remove(),this.info=null)}destroy(){this.destroyInfo()}}function sO(t,e){return i=>new nO(i,t,e)}function rO(t){return 100*(t.boost||0)+(t.apply?10:0)+(t.info?5:0)+(t.type?1:0)}class oO{constructor(t,e,i,n,s,r){this.options=t,this.attrs=e,this.tooltip=i,this.timestamp=n,this.selected=s,this.disabled=r}setSelected(t,e){return t==this.selected||t>=this.options.length?this:new oO(this.options,cO(e,t),this.tooltip,this.timestamp,t,this.disabled)}static build(t,e,i,n,s,r){if(n&&!r&&t.some((t=>1==t.state)))return n.setDisabled();let o=function(t,e){let i=[],n=null,s=t=>{i.push(t);let{section:e}=t.completion;if(e){n||(n=[]);let t="string"==typeof e?e:e.name;n.some((e=>e.name==t))||n.push("string"==typeof e?{name:t}:e)}},r=e.facet(Jc);for(let n of t)if(n.hasResult()){let t=n.result.getMatch;if(!1===n.result.filter)for(let e of n.result.options)s(new Wc(e,n.source,t?t(e):[],1e9-i.length));else{let i,o=e.sliceDoc(n.from,n.to),a=r.filterStrict?new Kc(o):new Fc(o);for(let e of n.result.options)if(i=a.match(e.label)){let r=e.displayLabel?t?t(e,i.matched):[]:i.matched;s(new Wc(e,n.source,r,i.score+(e.boost||0)))}}}if(n){let t=Object.create(null),e=0,s=(t,e)=>{var i,n;return(null!==(i=t.rank)&&void 0!==i?i:1e9)-(null!==(n=e.rank)&&void 0!==n?n:1e9)||(t.name<e.name?-1:1)};for(let i of n.sort(s))e-=1e5,t[i.name]=e;for(let e of i){let{section:i}=e.completion;i&&(e.score+=t["string"==typeof i?i:i.name])}}let o=[],a=null,l=r.compareCompletions;for(let t of i.sort(((t,e)=>e.score-t.score||l(t.completion,e.completion)))){let e=t.completion;!a||a.label!=e.label||a.detail!=e.detail||null!=a.type&&null!=e.type&&a.type!=e.type||a.apply!=e.apply||a.boost!=e.boost?o.push(t):rO(t.completion)>rO(a)&&(o[o.length-1]=t),a=t.completion}return o}(t,e);if(!o.length)return n&&t.some((t=>1==t.state))?n.setDisabled():null;let a=e.facet(Jc).selectOnOpen?0:-1;if(n&&n.selected!=a&&-1!=n.selected){let t=n.options[n.selected].completion;for(let e=0;e<o.length;e++)if(o[e].completion==t){a=e;break}}return new oO(o,cO(i,a),{pos:t.reduce(((t,e)=>e.hasResult()?Math.min(t,e.from):t),1e8),create:QO,above:s.aboveCursor},n?n.timestamp:Date.now(),a,!1)}map(t){return new oO(this.options,this.attrs,Object.assign(Object.assign({},this.tooltip),{pos:t.mapPos(this.tooltip.pos)}),this.timestamp,this.selected,this.disabled)}setDisabled(){return new oO(this.options,this.attrs,this.tooltip,this.timestamp,this.selected,!0)}}class aO{constructor(t,e,i){this.active=t,this.id=e,this.open=i}static start(){return new aO(OO,"cm-ac-"+Math.floor(2e6*Math.random()).toString(36),null)}update(t){let{state:e}=t,i=e.facet(Jc),n=(i.override||e.languageDataAt("autocomplete",Lc(e)).map(Ic)).map((e=>(this.active.find((t=>t.source==e))||new fO(e,this.active.some((t=>0!=t.state))?1:0)).update(t,i)));n.length==this.active.length&&n.every(((t,e)=>t==this.active[e]))&&(n=this.active);let s=this.open,r=t.effects.some((t=>t.is(pO)));s&&t.docChanged&&(s=s.map(t.changes)),t.selection||n.some((e=>e.hasResult()&&t.changes.touchesRange(e.from,e.to)))||!function(t,e){if(t==e)return!0;for(let i=0,n=0;;){for(;i<t.length&&!t[i].hasResult;)i++;for(;n<e.length&&!e[n].hasResult;)n++;let s=i==t.length,r=n==e.length;if(s||r)return s==r;if(t[i++].result!=e[n++].result)return!1}}(n,this.active)||r?s=oO.build(n,e,this.id,s,i,r):s&&s.disabled&&!n.some((t=>1==t.state))&&(s=null),!s&&n.every((t=>1!=t.state))&&n.some((t=>t.hasResult()))&&(n=n.map((t=>t.hasResult()?new fO(t.source,0):t)));for(let e of t.effects)e.is(mO)&&(s=s&&s.setSelected(e.value,this.id));return n==this.active&&s==this.open?this:new aO(n,this.id,s)}get tooltip(){return this.open?this.open.tooltip:null}get attrs(){return this.open?this.open.attrs:this.active.length?lO:hO}}const lO={"aria-autocomplete":"list"},hO={};function cO(t,e){let i={"aria-autocomplete":"list","aria-haspopup":"listbox","aria-controls":t};return e>-1&&(i["aria-activedescendant"]=t+"-"+e),i}const OO=[];function uO(t,e){if(t.isUserEvent("input.complete")){let i=t.annotation(Bc);if(i&&e.activateOnCompletion(i))return 12}let i=t.isUserEvent("input.type");return i&&e.activateOnTyping?5:i?1:t.isUserEvent("delete.backward")?2:t.selection?8:t.docChanged?16:0}class fO{constructor(t,e,i=-1){this.source=t,this.state=e,this.explicitPos=i}hasResult(){return!1}update(t,e){let i=uO(t,e),n=this;(8&i||16&i&&this.touches(t))&&(n=new fO(n.source,0)),4&i&&0==n.state&&(n=new fO(this.source,1)),n=n.updateFor(t,i);for(let e of t.effects)if(e.is(Nc))n=new fO(n.source,1,e.value?Lc(t.state):-1);else if(e.is(Hc))n=new fO(n.source,0);else if(e.is(pO))for(let t of e.value)t.source==n.source&&(n=t);return n}updateFor(t,e){return this.map(t.changes)}map(t){return t.empty||this.explicitPos<0?this:new fO(this.source,this.state,t.mapPos(this.explicitPos))}touches(t){return t.changes.touchesRange(Lc(t.state))}}class dO extends fO{constructor(t,e,i,n,s){super(t,2,e),this.result=i,this.from=n,this.to=s}hasResult(){return!0}updateFor(t,e){var i;if(!(3&e))return this.map(t.changes);let n=this.result;n.map&&!t.changes.empty&&(n=n.map(n,t.changes));let s=t.changes.mapPos(this.from),r=t.changes.mapPos(this.to,1),o=Lc(t.state);if((this.explicitPos<0?o<=s:o<this.from)||o>r||!n||2&e&&Lc(t.startState)==this.from)return new fO(this.source,4&e?1:0);let a=this.explicitPos<0?-1:t.changes.mapPos(this.explicitPos);return function(t,e,i,n){if(!t)return!1;let s=e.sliceDoc(i,n);return"function"==typeof t?t(s,i,n,e):Gc(t,!0).test(s)}(n.validFor,t.state,s,r)?new dO(this.source,a,n,s,r):n.update&&(n=n.update(n,s,r,new _c(t.state,o,a>=0)))?new dO(this.source,a,n,n.from,null!==(i=n.to)&&void 0!==i?i:Lc(t.state)):new fO(this.source,1,a)}map(t){if(t.empty)return this;return(this.result.map?this.result.map(this.result,t):this.result)?new dO(this.source,this.explicitPos<0?-1:t.mapPos(this.explicitPos),this.result,t.mapPos(this.from),t.mapPos(this.to,1)):new fO(this.source,0)}touches(t){return t.changes.touchesRange(this.from,this.to)}}const pO=le.define({map:(t,e)=>t.map((t=>t.map(e)))}),mO=le.define(),gO=_t.define({create:()=>aO.start(),update:(t,e)=>t.update(e),provide:t=>[Vo.from(t,(t=>t.tooltip)),zr.contentAttributes.from(t,(t=>t.attrs))]});function bO(t,e){const i=e.completion.apply||e.completion.label;let n=t.state.field(gO).active.find((t=>t.source==e.source));return n instanceof dO&&("string"==typeof i?t.dispatch(Object.assign(Object.assign({},function(t,e,i,n){let{main:s}=t.selection,r=i-s.from,o=n-s.from;return Object.assign(Object.assign({},t.changeByRange((a=>{if(a!=s&&i!=n&&t.sliceDoc(a.from+r,a.from+o)!=t.sliceDoc(i,n))return{range:a};let l=t.toText(e);return{changes:{from:a.from+r,to:n==s.from?a.to:a.from+o,insert:l},range:Tt.cursor(a.from+r+l.length)}}))),{scrollIntoView:!0,userEvent:"input.complete"})}(t.state,i,n.from,n.to)),{annotations:Bc.of(e.completion)})):i(t,e.completion,n.from,n.to),!0)}const QO=sO(gO,bO);function SO(t,e="option"){return i=>{let n=i.state.field(gO,!1);if(!n||!n.open||n.open.disabled||Date.now()-n.open.timestamp<i.state.facet(Jc).interactionDelay)return!1;let s,r=1;"page"==e&&(s=Yo(i,n.open.tooltip))&&(r=Math.max(2,Math.floor(s.dom.offsetHeight/s.dom.querySelector("li").offsetHeight)-1));let{length:o}=n.open.options,a=n.open.selected>-1?n.open.selected+r*(t?1:-1):t?0:o-1;return a<0?a="page"==e?0:o-1:a>=o&&(a="page"==e?o-1:0),i.dispatch({effects:mO.of(a)}),!0}}const vO=t=>{let e=t.state.field(gO,!1);return!(t.state.readOnly||!e||!e.open||e.open.selected<0||e.open.disabled||Date.now()-e.open.timestamp<t.state.facet(Jc).interactionDelay)&&bO(t,e.open.options[e.open.selected])},wO=t=>!!t.state.field(gO,!1)&&(t.dispatch({effects:Nc.of(!0)}),!0),yO=t=>{let e=t.state.field(gO,!1);return!(!e||!e.active.some((t=>0!=t.state)))&&(t.dispatch({effects:Hc.of(null)}),!0)};class xO{constructor(t,e){this.active=t,this.context=e,this.time=Date.now(),this.updates=[],this.done=void 0}}const kO=En.fromClass(class{constructor(t){this.view=t,this.debounceUpdate=-1,this.running=[],this.debounceAccept=-1,this.pendingStart=!1,this.composing=0;for(let e of t.state.field(gO).active)1==e.state&&this.startQuery(e)}update(t){let e=t.state.field(gO),i=t.state.facet(Jc);if(!t.selectionSet&&!t.docChanged&&t.startState.field(gO)==e)return;let n=t.transactions.some((t=>{let e=uO(t,i);return 8&e||(t.selection||t.docChanged)&&!(3&e)}));for(let e=0;e<this.running.length;e++){let i=this.running[e];if(n||i.context.abortOnDocChange&&t.docChanged||i.updates.length+t.transactions.length>50&&Date.now()-i.time>1e3){for(let t of i.context.abortListeners)try{t()}catch(t){Yn(this.view.state,t)}i.context.abortListeners=null,this.running.splice(e--,1)}else i.updates.push(...t.transactions)}this.debounceUpdate>-1&&clearTimeout(this.debounceUpdate),t.transactions.some((t=>t.effects.some((t=>t.is(Nc)))))&&(this.pendingStart=!0);let s=this.pendingStart?50:i.activateOnTypingDelay;if(this.debounceUpdate=e.active.some((t=>1==t.state&&!this.running.some((e=>e.active.source==t.source))))?setTimeout((()=>this.startUpdate()),s):-1,0!=this.composing)for(let e of t.transactions)e.isUserEvent("input.type")?this.composing=2:2==this.composing&&e.selection&&(this.composing=3)}startUpdate(){this.debounceUpdate=-1,this.pendingStart=!1;let{state:t}=this.view,e=t.field(gO);for(let t of e.active)1!=t.state||this.running.some((e=>e.active.source==t.source))||this.startQuery(t);this.running.length&&e.open&&e.open.disabled&&(this.debounceAccept=setTimeout((()=>this.accept()),this.view.state.facet(Jc).updateSyncTime))}startQuery(t){let{state:e}=this.view,i=Lc(e),n=new _c(e,i,t.explicitPos==i,this.view),s=new xO(t,n);this.running.push(s),Promise.resolve(t.source(n)).then((t=>{s.context.aborted||(s.done=t||null,this.scheduleAccept())}),(t=>{this.view.dispatch({effects:Hc.of(null)}),Yn(this.view.state,t)}))}scheduleAccept(){this.running.every((t=>void 0!==t.done))?this.accept():this.debounceAccept<0&&(this.debounceAccept=setTimeout((()=>this.accept()),this.view.state.facet(Jc).updateSyncTime))}accept(){var t;this.debounceAccept>-1&&clearTimeout(this.debounceAccept),this.debounceAccept=-1;let e=[],i=this.view.state.facet(Jc),n=this.view.state.field(gO);for(let s=0;s<this.running.length;s++){let r=this.running[s];if(void 0===r.done)continue;if(this.running.splice(s--,1),r.done){let n=new dO(r.active.source,r.active.explicitPos,r.done,r.done.from,null!==(t=r.done.to)&&void 0!==t?t:Lc(r.updates.length?r.updates[0].startState:this.view.state));for(let t of r.updates)n=n.update(t,i);if(n.hasResult()){e.push(n);continue}}let o=n.active.find((t=>t.source==r.active.source));if(o&&1==o.state)if(null==r.done){let t=new fO(r.active.source,0);for(let e of r.updates)t=t.update(e,i);1!=t.state&&e.push(t)}else this.startQuery(o)}(e.length||n.open&&n.open.disabled)&&this.view.dispatch({effects:pO.of(e)})}},{eventHandlers:{blur(t){let e=this.view.state.field(gO,!1);if(e&&e.tooltip&&this.view.state.facet(Jc).closeOnBlur){let i=e.open&&Yo(this.view,e.open.tooltip);i&&i.dom.contains(t.relatedTarget)||setTimeout((()=>this.view.dispatch({effects:Hc.of(null)})),10)}},compositionstart(){this.composing=1},compositionend(){3==this.composing&&setTimeout((()=>this.view.dispatch({effects:Nc.of(!1)})),20),this.composing=0}}}),PO="object"==typeof navigator&&/Win/.test(navigator.platform),$O=Gt.highest(zr.domEventHandlers({keydown(t,e){let i=e.state.field(gO,!1);if(!i||!i.open||i.open.disabled||i.open.selected<0||t.key.length>1||t.ctrlKey&&(!PO||!t.altKey)||t.metaKey)return!1;let n=i.open.options[i.open.selected],s=i.active.find((t=>t.source==n.source)),r=n.completion.commitCharacters||s.result.commitCharacters;return r&&r.indexOf(t.key)>-1&&bO(e,n),!1}})),ZO=zr.baseTheme({".cm-tooltip.cm-tooltip-autocomplete":{"& > ul":{fontFamily:"monospace",whiteSpace:"nowrap",overflow:"hidden auto",maxWidth_fallback:"700px",maxWidth:"min(700px, 95vw)",minWidth:"250px",maxHeight:"10em",height:"100%",listStyle:"none",margin:0,padding:0,"& > li, & > completion-section":{padding:"1px 3px",lineHeight:1.2},"& > li":{overflowX:"hidden",textOverflow:"ellipsis",cursor:"pointer"},"& > completion-section":{display:"list-item",borderBottom:"1px solid silver",paddingLeft:"0.5em",opacity:.7}}},"&light .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#17c",color:"white"},"&light .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#777"},"&dark .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#347",color:"white"},"&dark .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#444"},".cm-completionListIncompleteTop:before, .cm-completionListIncompleteBottom:after":{content:'"···"',opacity:.5,display:"block",textAlign:"center"},".cm-tooltip.cm-completionInfo":{position:"absolute",padding:"3px 9px",width:"max-content",maxWidth:"400px",boxSizing:"border-box",whiteSpace:"pre-line"},".cm-completionInfo.cm-completionInfo-left":{right:"100%"},".cm-completionInfo.cm-completionInfo-right":{left:"100%"},".cm-completionInfo.cm-completionInfo-left-narrow":{right:"30px"},".cm-completionInfo.cm-completionInfo-right-narrow":{left:"30px"},"&light .cm-snippetField":{backgroundColor:"#00000022"},"&dark .cm-snippetField":{backgroundColor:"#ffffff22"},".cm-snippetFieldPosition":{verticalAlign:"text-top",width:0,height:"1.15em",display:"inline-block",margin:"0 -0.7px -.7em",borderLeft:"1.4px dotted #888"},".cm-completionMatchedText":{textDecoration:"underline"},".cm-completionDetail":{marginLeft:"0.5em",fontStyle:"italic"},".cm-completionIcon":{fontSize:"90%",width:".8em",display:"inline-block",textAlign:"center",paddingRight:".6em",opacity:"0.6",boxSizing:"content-box"},".cm-completionIcon-function, .cm-completionIcon-method":{"&:after":{content:"'ƒ'"}},".cm-completionIcon-class":{"&:after":{content:"'○'"}},".cm-completionIcon-interface":{"&:after":{content:"'◌'"}},".cm-completionIcon-variable":{"&:after":{content:"'𝑥'"}},".cm-completionIcon-constant":{"&:after":{content:"'𝐶'"}},".cm-completionIcon-type":{"&:after":{content:"'𝑡'"}},".cm-completionIcon-enum":{"&:after":{content:"'∪'"}},".cm-completionIcon-property":{"&:after":{content:"'□'"}},".cm-completionIcon-keyword":{"&:after":{content:"'🔑︎'"}},".cm-completionIcon-namespace":{"&:after":{content:"'▢'"}},".cm-completionIcon-text":{"&:after":{content:"'abc'",fontSize:"50%",verticalAlign:"middle"}}});class TO{constructor(t,e,i,n){this.field=t,this.line=e,this.from=i,this.to=n}}class XO{constructor(t,e,i){this.field=t,this.from=e,this.to=i}map(t){let e=t.mapPos(this.from,-1,Qt.TrackDel),i=t.mapPos(this.to,1,Qt.TrackDel);return null==e||null==i?null:new XO(this.field,e,i)}}class RO{constructor(t,e){this.lines=t,this.fieldPositions=e}instantiate(t,e){let i=[],n=[e],s=t.doc.lineAt(e),r=/^\s*/.exec(s.text)[0];for(let s of this.lines){if(i.length){let i=r,o=/^\t*/.exec(s)[0].length;for(let e=0;e<o;e++)i+=t.facet(rl);n.push(e+i.length-o),s=i+s.slice(o)}i.push(s),e+=s.length+1}let o=this.fieldPositions.map((t=>new XO(t.field,n[t.line]+t.from,n[t.line]+t.to)));return{text:i,ranges:o}}static parse(t){let e,i=[],n=[],s=[];for(let r of t.split(/\r\n?|\n/)){for(;e=/[#$]\{(?:(\d+)(?::([^}]*))?|((?:\\[{}]|[^}])*))\}/.exec(r);){let t=e[1]?+e[1]:null,o=e[2]||e[3]||"",a=-1,l=o.replace(/\\[{}]/g,(t=>t[1]));for(let e=0;e<i.length;e++)(null!=t?i[e].seq==t:l&&i[e].name==l)&&(a=e);if(a<0){let e=0;for(;e<i.length&&(null==t||null!=i[e].seq&&i[e].seq<t);)e++;i.splice(e,0,{seq:t,name:l}),a=e;for(let t of s)t.field>=a&&t.field++}s.push(new TO(a,n.length,e.index,e.index+l.length)),r=r.slice(0,e.index)+o+r.slice(e.index+e[0].length)}r=r.replace(/\\([{}])/g,((t,e,i)=>{for(let t of s)t.line==n.length&&t.from>i&&(t.from--,t.to--);return e})),n.push(r)}return new RO(n,s)}}let AO=Fi.widget({widget:new class extends Ni{toDOM(){let t=document.createElement("span");return t.className="cm-snippetFieldPosition",t}ignoreEvent(){return!1}}}),CO=Fi.mark({class:"cm-snippetField"});class MO{constructor(t,e){this.ranges=t,this.active=e,this.deco=Fi.set(t.map((t=>(t.from==t.to?AO:CO).range(t.from,t.to))))}map(t){let e=[];for(let i of this.ranges){let n=i.map(t);if(!n)return null;e.push(n)}return new MO(e,this.active)}selectionInsideField(t){return t.ranges.every((t=>this.ranges.some((e=>e.field==this.active&&e.from<=t.from&&e.to>=t.to))))}}const qO=le.define({map:(t,e)=>t&&t.map(e)}),VO=le.define(),YO=_t.define({create:()=>null,update(t,e){for(let i of e.effects){if(i.is(qO))return i.value;if(i.is(VO)&&t)return new MO(t.ranges,i.value)}return t&&e.docChanged&&(t=t.map(e.changes)),t&&e.selection&&!t.selectionInsideField(e.selection)&&(t=null),t},provide:t=>zr.decorations.from(t,(t=>t?t.deco:Fi.none))});function zO(t,e){return Tt.create(t.filter((t=>t.field==e)).map((t=>Tt.range(t.from,t.to))))}function _O(t){let e=RO.parse(t);return(t,i,n,s)=>{let{text:r,ranges:o}=e.instantiate(t.state,n),a={changes:{from:n,to:s,insert:st.of(r)},scrollIntoView:!0,annotations:i?[Bc.of(i),he.userEvent.of("input.complete")]:void 0};if(o.length&&(a.selection=zO(o,0)),o.some((t=>t.field>0))){let e=new MO(o,0),i=a.effects=[qO.of(e)];void 0===t.state.field(YO,!1)&&i.push(le.appendConfig.of([YO,WO,GO,ZO]))}t.dispatch(t.state.update(a))}}function jO(t){return({state:e,dispatch:i})=>{let n=e.field(YO,!1);if(!n||t<0&&0==n.active)return!1;let s=n.active+t,r=t>0&&!n.ranges.some((e=>e.field==s+t));return i(e.update({selection:zO(n.ranges,s),effects:qO.of(r?null:new MO(n.ranges,s)),scrollIntoView:!0})),!0}}const EO=[{key:"Tab",run:jO(1),shift:jO(-1)},{key:"Escape",run:({state:t,dispatch:e})=>!!t.field(YO,!1)&&(e(t.update({effects:qO.of(null)})),!0)}],DO=At.define({combine:t=>t.length?t[0]:EO}),WO=Gt.highest(Br.compute([DO],(t=>t.facet(DO))));function LO(t,e){return Object.assign(Object.assign({},e),{apply:_O(t)})}const GO=zr.domEventHandlers({mousedown(t,e){let i,n=e.state.field(YO,!1);if(!n||null==(i=e.posAtCoords({x:t.clientX,y:t.clientY})))return!1;let s=n.ranges.find((t=>t.from<=i&&t.to>=i));return!(!s||s.field==n.active)&&(e.dispatch({selection:zO(n.ranges,s.field),effects:qO.of(n.ranges.some((t=>t.field>s.field))?new MO(n.ranges,s.field):null),scrollIntoView:!0}),!0)}});function BO(t,e){return new RegExp(e(t.source),t.unicode?"u":"")}const UO=Object.create(null);function IO(t,e,i,n,s){for(let r=t.iterLines(),o=0;!r.next().done;){let t,{value:a}=r;for(e.lastIndex=0;t=e.exec(a);)if(!n[t[0]]&&o+t.index!=s&&(i.push({type:"text",label:t[0]}),n[t[0]]=!0,i.length>=2e3))return;o+=a.length+1}}function NO(t,e,i,n,s){let r=t.length>=1e3,o=r&&e.get(t);if(o)return o;let a=[],l=Object.create(null);if(t.children){let r=0;for(let o of t.children){if(o.length>=1e3)for(let t of NO(o,e,i,n-r,s-r))l[t.label]||(l[t.label]=!0,a.push(t));else IO(o,i,a,l,s-r);r+=o.length+1}}else IO(t,i,a,l,s);return r&&a.length<2e3&&e.set(t,a),a}const HO=t=>{let e=t.state.languageDataAt("wordChars",t.pos).join(""),i=function(t){let e=t.replace(/[\]\-\\]/g,"\\$&");try{return new RegExp(`[\\p{Alphabetic}\\p{Number}_${e}]+`,"ug")}catch(t){return new RegExp(`[w${e}]`,"g")}}(e),n=t.matchBefore(BO(i,(t=>t+"$")));if(!n&&!t.explicit)return null;let s=n?n.from:t.pos,r=NO(t.state.doc,function(t){return UO[t]||(UO[t]=new WeakMap)}(e),i,5e4,s);return{from:s,options:r,validFor:BO(i,(t=>"^"+t))}},FO={brackets:["(","[","{","'",'"'],before:")]}:;>",stringPrefixes:[]},KO=le.define({map(t,e){let i=e.mapPos(t,-1,Qt.TrackAfter);return null==i?void 0:i}}),JO=new class extends we{};JO.startSide=1,JO.endSide=-1;const tu=_t.define({create:()=>Pe.empty,update(t,e){if(t=t.map(e.changes),e.selection){let i=e.state.doc.lineAt(e.selection.main.head);t=t.update({filter:t=>t>=i.from&&t<=i.to})}for(let i of e.effects)i.is(KO)&&(t=t.update({add:[JO.range(i.value,i.value+1)]}));return t}});function eu(){return[ou,tu]}const iu="()[]{}<>";function nu(t){for(let e=0;e<8;e+=2)if(iu.charCodeAt(e)==t)return iu.charAt(e+1);return mt(t<128?t:t+1)}function su(t,e){return t.languageDataAt("closeBrackets",e)[0]||FO}const ru="object"==typeof navigator&&/Android\b/.test(navigator.userAgent),ou=zr.inputHandler.of(((t,e,i,n)=>{if((ru?t.composing:t.compositionStarted)||t.state.readOnly)return!1;let s=t.state.selection.main;if(n.length>2||2==n.length&&1==gt(pt(n,0))||e!=s.from||i!=s.to)return!1;let r=function(t,e){let i=su(t,t.selection.main.head),n=i.brackets||FO.brackets;for(let s of n){let r=nu(pt(s,0));if(e==s)return r==s?uu(t,s,n.indexOf(s+s+s)>-1,i):cu(t,s,r,i.before||FO.before);if(e==r&&lu(t,t.selection.main.from))return Ou(t,s,r)}return null}(t.state,n);return!!r&&(t.dispatch(r),!0)})),au=[{key:"Backspace",run:({state:t,dispatch:e})=>{if(t.readOnly)return!1;let i=su(t,t.selection.main.head).brackets||FO.brackets,n=null,s=t.changeByRange((e=>{if(e.empty){let n=function(t,e){let i=t.sliceString(e-2,e);return gt(pt(i,0))==i.length?i:i.slice(1)}(t.doc,e.head);for(let s of i)if(s==n&&hu(t.doc,e.head)==nu(pt(s,0)))return{changes:{from:e.head-s.length,to:e.head+s.length},range:Tt.cursor(e.head-s.length)}}return{range:n=e}}));return n||e(t.update(s,{scrollIntoView:!0,userEvent:"delete.backward"})),!n}}];function lu(t,e){let i=!1;return t.field(tu).between(0,t.doc.length,(t=>{t==e&&(i=!0)})),i}function hu(t,e){let i=t.sliceString(e,e+2);return i.slice(0,gt(pt(i,0)))}function cu(t,e,i,n){let s=null,r=t.changeByRange((r=>{if(!r.empty)return{changes:[{insert:e,from:r.from},{insert:i,from:r.to}],effects:KO.of(r.to+e.length),range:Tt.range(r.anchor+e.length,r.head+e.length)};let o=hu(t.doc,r.head);return!o||/\s/.test(o)||n.indexOf(o)>-1?{changes:{insert:e+i,from:r.head},effects:KO.of(r.head+e.length),range:Tt.cursor(r.head+e.length)}:{range:s=r}}));return s?null:t.update(r,{scrollIntoView:!0,userEvent:"input.type"})}function Ou(t,e,i){let n=null,s=t.changeByRange((e=>e.empty&&hu(t.doc,e.head)==i?{changes:{from:e.head,to:e.head+i.length,insert:i},range:Tt.cursor(e.head+i.length)}:n={range:e}));return n?null:t.update(s,{scrollIntoView:!0,userEvent:"input.type"})}function uu(t,e,i,n){let s=n.stringPrefixes||FO.stringPrefixes,r=null,o=t.changeByRange((n=>{if(!n.empty)return{changes:[{insert:e,from:n.from},{insert:e,from:n.to}],effects:KO.of(n.to+e.length),range:Tt.range(n.anchor+e.length,n.head+e.length)};let o,a=n.head,l=hu(t.doc,a);if(l==e){if(fu(t,a))return{changes:{insert:e+e,from:a},effects:KO.of(a+e.length),range:Tt.cursor(a+e.length)};if(lu(t,a)){let n=i&&t.sliceDoc(a,a+3*e.length)==e+e+e?e+e+e:e;return{changes:{from:a,to:a+n.length,insert:n},range:Tt.cursor(a+n.length)}}}else{if(i&&t.sliceDoc(a-2*e.length,a)==e+e&&(o=du(t,a-2*e.length,s))>-1&&fu(t,o))return{changes:{insert:e+e+e+e,from:a},effects:KO.of(a+e.length),range:Tt.cursor(a+e.length)};if(t.charCategorizer(a)(l)!=me.Word&&du(t,a,s)>-1&&!function(t,e,i,n){let s=Wa(t).resolveInner(e,-1),r=n.reduce(((t,e)=>Math.max(t,e.length)),0);for(let o=0;o<5;o++){let o=t.sliceDoc(s.from,Math.min(s.to,s.from+i.length+r)),a=o.indexOf(i);if(!a||a>-1&&n.indexOf(o.slice(0,a))>-1){let e=s.firstChild;for(;e&&e.from==s.from&&e.to-e.from>i.length+a;){if(t.sliceDoc(e.to-i.length,e.to)==i)return!1;e=e.firstChild}return!0}let l=s.to==e&&s.parent;if(!l)break;s=l}return!1}(t,a,e,s))return{changes:{insert:e+e,from:a},effects:KO.of(a+e.length),range:Tt.cursor(a+e.length)}}return{range:r=n}}));return r?null:t.update(o,{scrollIntoView:!0,userEvent:"input.type"})}function fu(t,e){let i=Wa(t).resolveInner(e+1);return i.parent&&i.from==e}function du(t,e,i){let n=t.charCategorizer(e);if(n(t.sliceDoc(e-1,e))!=me.Word)return e;for(let s of i){let i=e-s.length;if(t.sliceDoc(i,e)==s&&n(t.sliceDoc(i-1,i))!=me.Word)return i}return-1}function pu(t={}){return[$O,gO,Jc.of(t),kO,gu,ZO]}const mu=[{key:"Ctrl-Space",run:wO},{mac:"Alt-`",run:wO},{key:"Escape",run:yO},{key:"ArrowDown",run:SO(!0)},{key:"ArrowUp",run:SO(!1)},{key:"PageDown",run:SO(!0,"page")},{key:"PageUp",run:SO(!1,"page")},{key:"Enter",run:vO}],gu=Gt.highest(Br.computeN([Jc],(t=>t.facet(Jc).defaultKeymap?[mu]:[])));function bu(t){let e=t.field(gO,!1);return e&&e.active.some((t=>1==t.state))?"pending":e&&e.active.some((t=>0!=t.state))?"active":null}const Qu=new WeakMap;function Su(t){var e;let i=null===(e=t.field(gO,!1))||void 0===e?void 0:e.open;if(!i||i.disabled)return[];let n=Qu.get(i.options);return n||Qu.set(i.options,n=i.options.map((t=>t.completion))),n}function vu(t){var e;let i=null===(e=t.field(gO,!1))||void 0===e?void 0:e.open;return i&&!i.disabled&&i.selected>=0?i.options[i.selected].completion:null}function wu(t){var e;let i=null===(e=t.field(gO,!1))||void 0===e?void 0:e.open;return i&&!i.disabled&&i.selected>=0?i.selected:null}const yu=t=>{let{state:e}=t,i=e.doc.lineAt(e.selection.main.from),n=$u(t.state,i.from);return n.line?ku(t):!!n.block&&Pu(t)};function xu(t,e){return({state:i,dispatch:n})=>{if(i.readOnly)return!1;let s=t(e,i);return!!s&&(n(i.update(s)),!0)}}const ku=xu(Tu,0),Pu=xu(((t,e)=>function(t,e,i=e.selection.ranges){let n=i.map((t=>$u(e,t.from).block));if(!n.every((t=>t)))return null;let s=i.map(((t,i)=>function(t,{open:e,close:i},n,s){let r,o,a=t.sliceDoc(n-Zu,n),l=t.sliceDoc(s,s+Zu),h=/\s*$/.exec(a)[0].length,c=/^\s*/.exec(l)[0].length,O=a.length-h;if(a.slice(O-e.length,O)==e&&l.slice(c,c+i.length)==i)return{open:{pos:n-h,margin:h&&1},close:{pos:s+c,margin:c&&1}};s-n<=2*Zu?r=o=t.sliceDoc(n,s):(r=t.sliceDoc(n,n+Zu),o=t.sliceDoc(s-Zu,s));let u=/^\s*/.exec(r)[0].length,f=/\s*$/.exec(o)[0].length,d=o.length-f-i.length;if(r.slice(u,u+e.length)==e&&o.slice(d,d+i.length)==i)return{open:{pos:n+u+e.length,margin:/\s/.test(r.charAt(u+e.length))?1:0},close:{pos:s-f-i.length,margin:/\s/.test(o.charAt(d-1))?1:0}};return null}(e,n[i],t.from,t.to)));if(2!=t&&!s.every((t=>t)))return{changes:e.changes(i.map(((t,e)=>s[e]?[]:[{from:t.from,insert:n[e].open+" "},{from:t.to,insert:" "+n[e].close}])))};if(1!=t&&s.some((t=>t))){let t=[];for(let e,i=0;i<s.length;i++)if(e=s[i]){let s=n[i],{open:r,close:o}=e;t.push({from:r.pos-s.open.length,to:r.pos+r.margin},{from:o.pos-o.margin,to:o.pos+s.close.length})}return{changes:t}}return null}(t,e,function(t){let e=[];for(let i of t.selection.ranges){let n=t.doc.lineAt(i.from),s=i.to<=n.to?n:t.doc.lineAt(i.to),r=e.length-1;r>=0&&e[r].to>n.from?e[r].to=s.to:e.push({from:n.from+/^\s*/.exec(n.text)[0].length,to:s.to})}return e}(e))),0);function $u(t,e){let i=t.languageDataAt("commentTokens",e);return i.length?i[0]:{}}const Zu=50;function Tu(t,e,i=e.selection.ranges){let n=[],s=-1;for(let{from:t,to:r}of i){let i=n.length,o=1e9,a=$u(e,t).line;if(a){for(let i=t;i<=r;){let l=e.doc.lineAt(i);if(l.from>s&&(t==r||r>l.from)){s=l.from;let t=/^\s*/.exec(l.text)[0].length,e=t==l.length,i=l.text.slice(t,t+a.length)==a?t:-1;t<l.text.length&&t<o&&(o=t),n.push({line:l,comment:i,token:a,indent:t,empty:e,single:!1})}i=l.to+1}if(o<1e9)for(let t=i;t<n.length;t++)n[t].indent<n[t].line.text.length&&(n[t].indent=o);n.length==i+1&&(n[i].single=!0)}}if(2!=t&&n.some((t=>t.comment<0&&(!t.empty||t.single)))){let t=[];for(let{line:e,token:i,indent:s,empty:r,single:o}of n)!o&&r||t.push({from:e.from+s,insert:i+" "});let i=e.changes(t);return{changes:i,selection:e.selection.map(i,1)}}if(1!=t&&n.some((t=>t.comment>=0))){let t=[];for(let{line:e,comment:i,token:s}of n)if(i>=0){let n=e.from+i,r=n+s.length;" "==e.text[r-e.from]&&r++,t.push({from:n,to:r})}return{changes:t}}return null}const Xu=re.define(),Ru=re.define(),Au=At.define(),Cu=At.define({combine:t=>ve(t,{minDepth:100,newGroupDelay:500,joinToEvent:(t,e)=>e},{minDepth:Math.max,newGroupDelay:Math.min,joinToEvent:(t,e)=>(i,n)=>t(i,n)||e(i,n)})});const Mu=_t.define({create:()=>Fu.empty,update(t,e){let i=e.state.facet(Cu),n=e.annotation(Xu);if(n){let s=e.docChanged?Tt.single(function(t){let e=0;return t.iterChangedRanges(((t,i)=>e=i)),e}(e.changes)):void 0,r=Eu.fromTransaction(e,s),o=n.side,a=0==o?t.undone:t.done;return a=r?Du(a,a.length,i.minDepth,r):Bu(a,e.startState.selection),new Fu(0==o?n.rest:a,0==o?a:n.rest)}let s=e.annotation(Ru);if("full"!=s&&"before"!=s||(t=t.isolate()),!1===e.annotation(he.addToHistory))return e.changes.empty?t:t.addMapping(e.changes.desc);let r=Eu.fromTransaction(e),o=e.annotation(he.time),a=e.annotation(he.userEvent);return r?t=t.addChanges(r,o,a,i,e):e.selection&&(t=t.addSelection(e.startState.selection,o,a,i.newGroupDelay)),"full"!=s&&"after"!=s||(t=t.isolate()),t},toJSON:t=>({done:t.done.map((t=>t.toJSON())),undone:t.undone.map((t=>t.toJSON()))}),fromJSON:t=>new Fu(t.done.map(Eu.fromJSON),t.undone.map(Eu.fromJSON))});function qu(t={}){return[Mu,Cu.of(t),zr.domEventHandlers({beforeinput(t,e){let i="historyUndo"==t.inputType?Yu:"historyRedo"==t.inputType?zu:null;return!!i&&(t.preventDefault(),i(e))}})]}function Vu(t,e){return function({state:i,dispatch:n}){if(!e&&i.readOnly)return!1;let s=i.field(Mu,!1);if(!s)return!1;let r=s.pop(t,i,e);return!!r&&(n(r),!0)}}const Yu=Vu(0,!1),zu=Vu(1,!1),_u=Vu(0,!0),ju=Vu(1,!0);class Eu{constructor(t,e,i,n,s){this.changes=t,this.effects=e,this.mapped=i,this.startSelection=n,this.selectionsAfter=s}setSelAfter(t){return new Eu(this.changes,this.effects,this.mapped,this.startSelection,t)}toJSON(){var t,e,i;return{changes:null===(t=this.changes)||void 0===t?void 0:t.toJSON(),mapped:null===(e=this.mapped)||void 0===e?void 0:e.toJSON(),startSelection:null===(i=this.startSelection)||void 0===i?void 0:i.toJSON(),selectionsAfter:this.selectionsAfter.map((t=>t.toJSON()))}}static fromJSON(t){return new Eu(t.changes&&vt.fromJSON(t.changes),[],t.mapped&&St.fromJSON(t.mapped),t.startSelection&&Tt.fromJSON(t.startSelection),t.selectionsAfter.map(Tt.fromJSON))}static fromTransaction(t,e){let i=Lu;for(let e of t.startState.facet(Au)){let n=e(t);n.length&&(i=i.concat(n))}return!i.length&&t.changes.empty?null:new Eu(t.changes.invert(t.startState.doc),i,void 0,e||t.startState.selection,Lu)}static selection(t){return new Eu(void 0,Lu,void 0,void 0,t)}}function Du(t,e,i,n){let s=e+1>i+20?e-i-1:0,r=t.slice(s,e);return r.push(n),r}function Wu(t,e){return t.length?e.length?t.concat(e):t:e}const Lu=[],Gu=200;function Bu(t,e){if(t.length){let i=t[t.length-1],n=i.selectionsAfter.slice(Math.max(0,i.selectionsAfter.length-Gu));return n.length&&n[n.length-1].eq(e)?t:(n.push(e),Du(t,t.length-1,1e9,i.setSelAfter(n)))}return[Eu.selection([e])]}function Uu(t){let e=t[t.length-1],i=t.slice();return i[t.length-1]=e.setSelAfter(e.selectionsAfter.slice(0,e.selectionsAfter.length-1)),i}function Iu(t,e){if(!t.length)return t;let i=t.length,n=Lu;for(;i;){let s=Nu(t[i-1],e,n);if(s.changes&&!s.changes.empty||s.effects.length){let e=t.slice(0,i);return e[i-1]=s,e}e=s.mapped,i--,n=s.selectionsAfter}return n.length?[Eu.selection(n)]:Lu}function Nu(t,e,i){let n=Wu(t.selectionsAfter.length?t.selectionsAfter.map((t=>t.map(e))):Lu,i);if(!t.changes)return Eu.selection(n);let s=t.changes.map(e),r=e.mapDesc(t.changes,!0),o=t.mapped?t.mapped.composeDesc(r):r;return new Eu(s,le.mapEffects(t.effects,e),o,t.startSelection.map(r),n)}const Hu=/^(input\.type|delete)($|\.)/;class Fu{constructor(t,e,i=0,n=void 0){this.done=t,this.undone=e,this.prevTime=i,this.prevUserEvent=n}isolate(){return this.prevTime?new Fu(this.done,this.undone):this}addChanges(t,e,i,n,s){let r=this.done,o=r[r.length-1];return r=o&&o.changes&&!o.changes.empty&&t.changes&&(!i||Hu.test(i))&&(!o.selectionsAfter.length&&e-this.prevTime<n.newGroupDelay&&n.joinToEvent(s,function(t,e){let i=[],n=!1;return t.iterChangedRanges(((t,e)=>i.push(t,e))),e.iterChangedRanges(((t,e,s,r)=>{for(let t=0;t<i.length;){let e=i[t++],o=i[t++];r>=e&&s<=o&&(n=!0)}})),n}(o.changes,t.changes))||"input.type.compose"==i)?Du(r,r.length-1,n.minDepth,new Eu(t.changes.compose(o.changes),Wu(t.effects,o.effects),o.mapped,o.startSelection,Lu)):Du(r,r.length,n.minDepth,t),new Fu(r,Lu,e,i)}addSelection(t,e,i,n){let s=this.done.length?this.done[this.done.length-1].selectionsAfter:Lu;return s.length>0&&e-this.prevTime<n&&i==this.prevUserEvent&&i&&/^select($|\.)/.test(i)&&(r=s[s.length-1],o=t,r.ranges.length==o.ranges.length&&0===r.ranges.filter(((t,e)=>t.empty!=o.ranges[e].empty)).length)?this:new Fu(Bu(this.done,t),this.undone,e,i);var r,o}addMapping(t){return new Fu(Iu(this.done,t),Iu(this.undone,t),this.prevTime,this.prevUserEvent)}pop(t,e,i){let n=0==t?this.done:this.undone;if(0==n.length)return null;let s=n[n.length-1];if(i&&s.selectionsAfter.length)return e.update({selection:s.selectionsAfter[s.selectionsAfter.length-1],annotations:Xu.of({side:t,rest:Uu(n)}),userEvent:0==t?"select.undo":"select.redo",scrollIntoView:!0});if(s.changes){let i=1==n.length?Lu:n.slice(0,n.length-1);return s.mapped&&(i=Iu(i,s.mapped)),e.update({changes:s.changes,selection:s.startSelection,effects:s.effects,annotations:Xu.of({side:t,rest:i}),filter:!1,userEvent:0==t?"undo":"redo",scrollIntoView:!0})}return null}}Fu.empty=new Fu(Lu,Lu);const Ku=[{key:"Mod-z",run:Yu,preventDefault:!0},{key:"Mod-y",mac:"Mod-Shift-z",run:zu,preventDefault:!0},{linux:"Ctrl-Shift-z",run:zu,preventDefault:!0},{key:"Mod-u",run:_u,preventDefault:!0},{key:"Alt-u",mac:"Mod-Shift-u",run:ju,preventDefault:!0}];function Ju(t,e){return Tt.create(t.ranges.map(e),t.mainIndex)}function tf(t,e){return t.update({selection:e,scrollIntoView:!0,userEvent:"select"})}function ef({state:t,dispatch:e},i){let n=Ju(t.selection,i);return!n.eq(t.selection)&&(e(tf(t,n)),!0)}function nf(t,e){return Tt.cursor(e?t.to:t.from)}function sf(t,e){return ef(t,(i=>i.empty?t.moveByChar(i,e):nf(i,e)))}function rf(t){return t.textDirectionAt(t.state.selection.main.head)==an.LTR}const of=t=>sf(t,!rf(t)),af=t=>sf(t,rf(t));function lf(t,e){return ef(t,(i=>i.empty?t.moveByGroup(i,e):nf(i,e)))}const hf=t=>lf(t,!rf(t)),cf=t=>lf(t,rf(t));function Of(t,e,i){if(e.type.prop(i))return!0;let n=e.to-e.from;return n&&(n>2||/[^\s,.;:]/.test(t.sliceDoc(e.from,e.to)))||e.firstChild}function uf(t,e,i){let s,r,o=Wa(t).resolveInner(e.head),a=i?n.closedBy:n.openedBy;for(let n=e.head;;){let e=i?o.childAfter(n):o.childBefore(n);if(!e)break;Of(t,e,a)?o=e:n=i?e.to:e.from}return r=o.type.prop(a)&&(s=i?Oh(t,o.from,1):Oh(t,o.to,-1))&&s.matched?i?s.end.to:s.end.from:i?o.to:o.from,Tt.cursor(r,i?-1:1)}const ff=t=>ef(t,(e=>uf(t.state,e,!rf(t)))),df=t=>ef(t,(e=>uf(t.state,e,rf(t))));function pf(t,e){return ef(t,(i=>{if(!i.empty)return nf(i,e);let n=t.moveVertically(i,e);return n.head!=i.head?n:t.moveToLineBoundary(i,e)}))}const mf=t=>pf(t,!1),gf=t=>pf(t,!0);function bf(t){let e,i=t.scrollDOM.clientHeight<t.scrollDOM.scrollHeight-2,n=0,s=0;if(i){for(let e of t.state.facet(zr.scrollMargins)){let i=e(t);(null==i?void 0:i.top)&&(n=Math.max(null==i?void 0:i.top,n)),(null==i?void 0:i.bottom)&&(s=Math.max(null==i?void 0:i.bottom,s))}e=t.scrollDOM.clientHeight-n-s}else e=(t.dom.ownerDocument.defaultView||window).innerHeight;return{marginTop:n,marginBottom:s,selfScroll:i,height:Math.max(t.defaultLineHeight,e-5)}}function Qf(t,e){let i,n=bf(t),{state:s}=t,r=Ju(s.selection,(i=>i.empty?t.moveVertically(i,e,n.height):nf(i,e)));if(r.eq(s.selection))return!1;if(n.selfScroll){let e=t.coordsAtPos(s.selection.main.head),o=t.scrollDOM.getBoundingClientRect(),a=o.top+n.marginTop,l=o.bottom-n.marginBottom;e&&e.top>a&&e.bottom<l&&(i=zr.scrollIntoView(r.main.head,{y:"start",yMargin:e.top-a}))}return t.dispatch(tf(s,r),{effects:i}),!0}const Sf=t=>Qf(t,!1),vf=t=>Qf(t,!0);function wf(t,e,i){let n=t.lineBlockAt(e.head),s=t.moveToLineBoundary(e,i);if(s.head==e.head&&s.head!=(i?n.to:n.from)&&(s=t.moveToLineBoundary(e,i,!1)),!i&&s.head==n.from&&n.length){let i=/^\s*/.exec(t.state.sliceDoc(n.from,Math.min(n.from+100,n.to)))[0].length;i&&e.head!=n.from+i&&(s=Tt.cursor(n.from+i))}return s}function yf(t,e,i){let n=!1,s=Ju(t.selection,(e=>{let s=Oh(t,e.head,-1)||Oh(t,e.head,1)||e.head>0&&Oh(t,e.head-1,1)||e.head<t.doc.length&&Oh(t,e.head+1,-1);if(!s||!s.end)return e;n=!0;let r=s.start.from==e.head?s.end.to:s.end.from;return i?Tt.range(e.anchor,r):Tt.cursor(r)}));return!!n&&(e(tf(t,s)),!0)}const xf=({state:t,dispatch:e})=>yf(t,e,!1),kf=({state:t,dispatch:e})=>yf(t,e,!0);function Pf(t,e){let i=Ju(t.state.selection,(t=>{let i=e(t);return Tt.range(t.anchor,i.head,i.goalColumn,i.bidiLevel||void 0)}));return!i.eq(t.state.selection)&&(t.dispatch(tf(t.state,i)),!0)}function $f(t,e){return Pf(t,(i=>t.moveByChar(i,e)))}const Zf=t=>$f(t,!rf(t)),Tf=t=>$f(t,rf(t));function Xf(t,e){return Pf(t,(i=>t.moveByGroup(i,e)))}const Rf=t=>Xf(t,!rf(t)),Af=t=>Xf(t,rf(t)),Cf=t=>Pf(t,(e=>uf(t.state,e,!rf(t)))),Mf=t=>Pf(t,(e=>uf(t.state,e,rf(t))));function qf(t,e){return Pf(t,(i=>t.moveVertically(i,e)))}const Vf=t=>qf(t,!1),Yf=t=>qf(t,!0);function zf(t,e){return Pf(t,(i=>t.moveVertically(i,e,bf(t).height)))}const _f=t=>zf(t,!1),jf=t=>zf(t,!0),Ef=({state:t,dispatch:e})=>(e(tf(t,{anchor:0})),!0),Df=({state:t,dispatch:e})=>(e(tf(t,{anchor:t.doc.length})),!0),Wf=({state:t,dispatch:e})=>(e(tf(t,{anchor:t.selection.main.anchor,head:0})),!0),Lf=({state:t,dispatch:e})=>(e(tf(t,{anchor:t.selection.main.anchor,head:t.doc.length})),!0);function Gf(t,e){if(t.state.readOnly)return!1;let i="delete.selection",{state:n}=t,s=n.changeByRange((n=>{let{from:s,to:r}=n;if(s==r){let o=e(n);o<s?(i="delete.backward",o=Bf(t,o,!1)):o>s&&(i="delete.forward",o=Bf(t,o,!0)),s=Math.min(s,o),r=Math.max(r,o)}else s=Bf(t,s,!1),r=Bf(t,r,!0);return s==r?{range:n}:{changes:{from:s,to:r},range:Tt.cursor(s,s<n.head?-1:1)}}));return!s.changes.empty&&(t.dispatch(n.update(s,{scrollIntoView:!0,userEvent:i,effects:"delete.selection"==i?zr.announce.of(n.phrase("Selection deleted")):void 0})),!0)}function Bf(t,e,i){if(t instanceof zr)for(let n of t.state.facet(zr.atomicRanges).map((e=>e(t))))n.between(e,e,((t,n)=>{t<e&&n>e&&(e=i?n:t)}));return e}const Uf=(t,e)=>Gf(t,(i=>{let n,s,r=i.from,{state:o}=t,a=o.doc.lineAt(r);if(!e&&r>a.from&&r<a.from+200&&!/[^ \t]/.test(n=a.text.slice(0,r-a.from))){if("\t"==n[n.length-1])return r-1;let t=ze(n,o.tabSize)%ol(o)||ol(o);for(let e=0;e<t&&" "==n[n.length-1-e];e++)r--;s=r}else s=dt(a.text,r-a.from,e,e)+a.from,s==r&&a.number!=(e?o.doc.lines:1)&&(s+=e?1:-1);return s})),If=t=>Uf(t,!1),Nf=t=>Uf(t,!0),Hf=(t,e)=>Gf(t,(i=>{let n=i.head,{state:s}=t,r=s.doc.lineAt(n),o=s.charCategorizer(n);for(let t=null;;){if(n==(e?r.to:r.from)){n==i.head&&r.number!=(e?s.doc.lines:1)&&(n+=e?1:-1);break}let a=dt(r.text,n-r.from,e)+r.from,l=r.text.slice(Math.min(n,a)-r.from,Math.max(n,a)-r.from),h=o(l);if(null!=t&&h!=t)break;" "==l&&n==i.head||(t=h),n=a}return n})),Ff=t=>Hf(t,!1);const Kf=Jf(!1);function Jf(t){return({state:e,dispatch:i})=>{if(e.readOnly)return!1;let s=e.changeByRange((i=>{let{from:s,to:r}=i,o=e.doc.lineAt(s),a=!t&&s==r&&function(t,e){if(/\(\)|\[\]|\{\}/.test(t.sliceDoc(e-1,e+1)))return{from:e,to:e};let i,s=Wa(t).resolveInner(e),r=s.childBefore(e),o=s.childAfter(e);return r&&o&&r.to<=e&&o.from>=e&&(i=r.type.prop(n.closedBy))&&i.indexOf(o.name)>-1&&t.doc.lineAt(r.to).from==t.doc.lineAt(o.from).from&&!/\S/.test(t.sliceDoc(r.to,o.from))?{from:r.to,to:o.from}:null}(e,s);t&&(s=r=(r<=o.to?o:e.doc.lineAt(r)).to);let l=new hl(e,{simulateBreak:s,simulateDoubleBreak:!!a}),h=ll(l,s);for(null==h&&(h=ze(/^\s*/.exec(e.doc.lineAt(s).text)[0],e.tabSize));r<o.to&&/\s/.test(o.text[r-o.from]);)r++;a?({from:s,to:r}=a):s>o.from&&s<o.from+100&&!/\S/.test(o.text.slice(0,s))&&(s=o.from);let c=["",al(e,h)];return a&&c.push(al(e,l.lineIndent(o.from,-1))),{changes:{from:s,to:r,insert:st.of(c)},range:Tt.cursor(s+1+c[1].length)}}));return i(e.update(s,{scrollIntoView:!0,userEvent:"input"})),!0}}function td(t,e){let i=-1;return t.changeByRange((n=>{let s=[];for(let r=n.from;r<=n.to;){let o=t.doc.lineAt(r);o.number>i&&(n.empty||n.to>o.from)&&(e(o,s,n),i=o.number),r=o.to+1}let r=t.changes(s);return{changes:s,range:Tt.range(r.mapPos(n.anchor,1),r.mapPos(n.head,1))}}))}const ed=({state:t,dispatch:e})=>!t.readOnly&&(e(t.update(td(t,((e,i)=>{i.push({from:e.from,insert:t.facet(rl)})})),{userEvent:"input.indent"})),!0),id=({state:t,dispatch:e})=>!t.readOnly&&(e(t.update(td(t,((e,i)=>{let n=/^\s*/.exec(e.text)[0];if(!n)return;let s=ze(n,t.tabSize),r=0,o=al(t,Math.max(0,s-ol(t)));for(;r<n.length&&r<o.length&&n.charCodeAt(r)==o.charCodeAt(r);)r++;i.push({from:e.from+r,to:e.from+n.length,insert:o.slice(r)})})),{userEvent:"delete.dedent"})),!0),nd=[{key:"ArrowLeft",run:of,shift:Zf,preventDefault:!0},{key:"Mod-ArrowLeft",mac:"Alt-ArrowLeft",run:hf,shift:Rf,preventDefault:!0},{mac:"Cmd-ArrowLeft",run:t=>ef(t,(e=>wf(t,e,!rf(t)))),shift:t=>Pf(t,(e=>wf(t,e,!rf(t)))),preventDefault:!0},{key:"ArrowRight",run:af,shift:Tf,preventDefault:!0},{key:"Mod-ArrowRight",mac:"Alt-ArrowRight",run:cf,shift:Af,preventDefault:!0},{mac:"Cmd-ArrowRight",run:t=>ef(t,(e=>wf(t,e,rf(t)))),shift:t=>Pf(t,(e=>wf(t,e,rf(t)))),preventDefault:!0},{key:"ArrowUp",run:mf,shift:Vf,preventDefault:!0},{mac:"Cmd-ArrowUp",run:Ef,shift:Wf},{mac:"Ctrl-ArrowUp",run:Sf,shift:_f},{key:"ArrowDown",run:gf,shift:Yf,preventDefault:!0},{mac:"Cmd-ArrowDown",run:Df,shift:Lf},{mac:"Ctrl-ArrowDown",run:vf,shift:jf},{key:"PageUp",run:Sf,shift:_f},{key:"PageDown",run:vf,shift:jf},{key:"Home",run:t=>ef(t,(e=>wf(t,e,!1))),shift:t=>Pf(t,(e=>wf(t,e,!1))),preventDefault:!0},{key:"Mod-Home",run:Ef,shift:Wf},{key:"End",run:t=>ef(t,(e=>wf(t,e,!0))),shift:t=>Pf(t,(e=>wf(t,e,!0))),preventDefault:!0},{key:"Mod-End",run:Df,shift:Lf},{key:"Enter",run:Kf},{key:"Mod-a",run:({state:t,dispatch:e})=>(e(t.update({selection:{anchor:0,head:t.doc.length},userEvent:"select"})),!0)},{key:"Backspace",run:If,shift:If},{key:"Delete",run:Nf},{key:"Mod-Backspace",mac:"Alt-Backspace",run:Ff},{key:"Mod-Delete",mac:"Alt-Delete",run:t=>Hf(t,!0)},{mac:"Mod-Backspace",run:t=>Gf(t,(e=>{let i=t.moveToLineBoundary(e,!1).head;return e.head>i?i:Math.max(0,e.head-1)}))},{mac:"Mod-Delete",run:t=>Gf(t,(e=>{let i=t.moveToLineBoundary(e,!0).head;return e.head<i?i:Math.min(t.state.doc.length,e.head+1)}))}].concat([{key:"Ctrl-b",run:of,shift:Zf,preventDefault:!0},{key:"Ctrl-f",run:af,shift:Tf},{key:"Ctrl-p",run:mf,shift:Vf},{key:"Ctrl-n",run:gf,shift:Yf},{key:"Ctrl-a",run:t=>ef(t,(e=>Tt.cursor(t.lineBlockAt(e.head).from,1))),shift:t=>Pf(t,(e=>Tt.cursor(t.lineBlockAt(e.head).from)))},{key:"Ctrl-e",run:t=>ef(t,(e=>Tt.cursor(t.lineBlockAt(e.head).to,-1))),shift:t=>Pf(t,(e=>Tt.cursor(t.lineBlockAt(e.head).to)))},{key:"Ctrl-d",run:Nf},{key:"Ctrl-h",run:If},{key:"Ctrl-k",run:t=>Gf(t,(e=>{let i=t.lineBlockAt(e.head).to;return e.head<i?i:Math.min(t.state.doc.length,e.head+1)}))},{key:"Ctrl-Alt-h",run:Ff},{key:"Ctrl-o",run:({state:t,dispatch:e})=>{if(t.readOnly)return!1;let i=t.changeByRange((t=>({changes:{from:t.from,to:t.to,insert:st.of(["",""])},range:Tt.cursor(t.from)})));return e(t.update(i,{scrollIntoView:!0,userEvent:"input"})),!0}},{key:"Ctrl-t",run:({state:t,dispatch:e})=>{if(t.readOnly)return!1;let i=t.changeByRange((e=>{if(!e.empty||0==e.from||e.from==t.doc.length)return{range:e};let i=e.from,n=t.doc.lineAt(i),s=i==n.from?i-1:dt(n.text,i-n.from,!1)+n.from,r=i==n.to?i+1:dt(n.text,i-n.from,!0)+n.from;return{changes:{from:s,to:r,insert:t.doc.slice(i,r).append(t.doc.slice(s,i))},range:Tt.cursor(r)}}));return!i.changes.empty&&(e(t.update(i,{scrollIntoView:!0,userEvent:"move.character"})),!0)}},{key:"Ctrl-v",run:vf}].map((t=>({mac:t.key,run:t.run,shift:t.shift})))),sd={area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0,menuitem:!0},rd={dd:!0,li:!0,optgroup:!0,option:!0,p:!0,rp:!0,rt:!0,tbody:!0,td:!0,tfoot:!0,th:!0,tr:!0},od={dd:{dd:!0,dt:!0},dt:{dd:!0,dt:!0},li:{li:!0},option:{option:!0,optgroup:!0},optgroup:{optgroup:!0},p:{address:!0,article:!0,aside:!0,blockquote:!0,dir:!0,div:!0,dl:!0,fieldset:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,menu:!0,nav:!0,ol:!0,p:!0,pre:!0,section:!0,table:!0,ul:!0},rp:{rp:!0,rt:!0},rt:{rp:!0,rt:!0},tbody:{tbody:!0,tfoot:!0},td:{td:!0,th:!0},tfoot:{tbody:!0},th:{td:!0,th:!0},thead:{tbody:!0,tfoot:!0},tr:{tr:!0}};function ad(t){return 9==t||10==t||13==t||32==t}let ld=null,hd=null,cd=0;function Od(t,e){let i=t.pos+e;if(cd==i&&hd==t)return ld;let n=t.peek(e);for(;ad(n);)n=t.peek(++e);let s="";for(;45==(r=n)||46==r||58==r||r>=65&&r<=90||95==r||r>=97&&r<=122||r>=161;)s+=String.fromCharCode(n),n=t.peek(++e);var r;return hd=t,cd=i,ld=s?s.toLowerCase():n==ud||n==fd?void 0:null}const ud=63,fd=33;function dd(t,e){this.name=t,this.parent=e}const pd=[6,10,7,8,9],md=new lc({start:null,shift:(t,e,i,n)=>pd.indexOf(e)>-1?new dd(Od(n,1)||"",t):t,reduce:(t,e)=>20==e&&t?t.parent:t,reuse(t,e,i,n){let s=e.type.id;return 6==s||36==s?new dd(Od(n,1)||"",t):t},strict:!1}),gd=new Nh(((t,e)=>{if(60!=t.next)return void(t.next<0&&e.context&&t.acceptToken(57));t.advance();let i=47==t.next;i&&t.advance();let n=Od(t,0);if(void 0===n)return;if(!n)return t.acceptToken(i?14:6);let s=e.context?e.context.name:null;if(i){if(n==s)return t.acceptToken(11);if(s&&rd[s])return t.acceptToken(57,-2);if(e.dialectEnabled(0))return t.acceptToken(12);for(let t=e.context;t;t=t.parent)if(t.name==n)return;t.acceptToken(13)}else{if("script"==n)return t.acceptToken(7);if("style"==n)return t.acceptToken(8);if("textarea"==n)return t.acceptToken(9);if(sd.hasOwnProperty(n))return t.acceptToken(10);s&&od[s]&&od[s][n]?t.acceptToken(57,-1):t.acceptToken(6)}}),{contextual:!0}),bd=new Nh((t=>{for(let e=0,i=0;;i++){if(t.next<0){i&&t.acceptToken(58);break}if(45==t.next)e++;else{if(62==t.next&&e>=2){i>=3&&t.acceptToken(58,-2);break}e=0}t.advance()}}));const Qd=new Nh(((t,e)=>{if(47==t.next&&62==t.peek(1)){let i=e.dialectEnabled(1)||function(t){for(;t;t=t.parent)if("svg"==t.name||"math"==t.name)return!0;return!1}(e.context);t.acceptToken(i?5:4,2)}else 62==t.next&&t.acceptToken(4,1)}));function Sd(t,e,i){let n=2+t.length;return new Nh((s=>{for(let r=0,o=0,a=0;;a++){if(s.next<0){a&&s.acceptToken(e);break}if(0==r&&60==s.next||1==r&&47==s.next||r>=2&&r<n&&s.next==t.charCodeAt(r-2))r++,o++;else if(2!=r&&r!=n||!ad(s.next)){if(r==n&&62==s.next){a>o?s.acceptToken(e,-o):s.acceptToken(i,-(o-2));break}if((10==s.next||13==s.next)&&a){s.acceptToken(e,1);break}r=o=0}else o++;s.advance()}}))}const vd=Sd("script",54,1),wd=Sd("style",55,2),yd=Sd("textarea",56,3),xd=da({"Text RawText":qa.content,"StartTag StartCloseTag SelfClosingEndTag EndTag":qa.angleBracket,TagName:qa.tagName,"MismatchedCloseTag/TagName":[qa.tagName,qa.invalid],AttributeName:qa.attributeName,"AttributeValue UnquotedAttributeValue":qa.attributeValue,Is:qa.definitionOperator,"EntityReference CharacterReference":qa.character,Comment:qa.blockComment,ProcessingInst:qa.processingInstruction,DoctypeDecl:qa.documentMeta}),kd=hc.deserialize({version:14,states:",xOVO!rOOO!WQ#tO'#CqO!]Q#tO'#CzO!bQ#tO'#C}O!gQ#tO'#DQO!lQ#tO'#DSO!qOaO'#CpO!|ObO'#CpO#XOdO'#CpO$eO!rO'#CpOOO`'#Cp'#CpO$lO$fO'#DTO$tQ#tO'#DVO$yQ#tO'#DWOOO`'#Dk'#DkOOO`'#DY'#DYQVO!rOOO%OQ&rO,59]O%ZQ&rO,59fO%fQ&rO,59iO%qQ&rO,59lO%|Q&rO,59nOOOa'#D^'#D^O&XOaO'#CxO&dOaO,59[OOOb'#D_'#D_O&lObO'#C{O&wObO,59[OOOd'#D`'#D`O'POdO'#DOO'[OdO,59[OOO`'#Da'#DaO'dO!rO,59[O'kQ#tO'#DROOO`,59[,59[OOOp'#Db'#DbO'pO$fO,59oOOO`,59o,59oO'xQ#|O,59qO'}Q#|O,59rOOO`-E7W-E7WO(SQ&rO'#CsOOQW'#DZ'#DZO(bQ&rO1G.wOOOa1G.w1G.wOOO`1G/Y1G/YO(mQ&rO1G/QOOOb1G/Q1G/QO(xQ&rO1G/TOOOd1G/T1G/TO)TQ&rO1G/WOOO`1G/W1G/WO)`Q&rO1G/YOOOa-E7[-E7[O)kQ#tO'#CyOOO`1G.v1G.vOOOb-E7]-E7]O)pQ#tO'#C|OOOd-E7^-E7^O)uQ#tO'#DPOOO`-E7_-E7_O)zQ#|O,59mOOOp-E7`-E7`OOO`1G/Z1G/ZOOO`1G/]1G/]OOO`1G/^1G/^O*PQ,UO,59_OOQW-E7X-E7XOOOa7+$c7+$cOOO`7+$t7+$tOOOb7+$l7+$lOOOd7+$o7+$oOOO`7+$r7+$rO*[Q#|O,59eO*aQ#|O,59hO*fQ#|O,59kOOO`1G/X1G/XO*kO7[O'#CvO*|OMhO'#CvOOQW1G.y1G.yOOO`1G/P1G/POOO`1G/S1G/SOOO`1G/V1G/VOOOO'#D['#D[O+_O7[O,59bOOQW,59b,59bOOOO'#D]'#D]O+pOMhO,59bOOOO-E7Y-E7YOOQW1G.|1G.|OOOO-E7Z-E7Z",stateData:",]~O!^OS~OUSOVPOWQOXROYTO[]O][O^^O`^Oa^Ob^Oc^Ox^O{_O!dZO~OfaO~OfbO~OfcO~OfdO~OfeO~O!WfOPlP!ZlP~O!XiOQoP!ZoP~O!YlORrP!ZrP~OUSOVPOWQOXROYTOZqO[]O][O^^O`^Oa^Ob^Oc^Ox^O!dZO~O!ZrO~P#dO![sO!euO~OfvO~OfwO~OS|OT}OhyO~OS!POT}OhyO~OS!ROT}OhyO~OS!TOT}OhyO~OS}OT}OhyO~O!WfOPlX!ZlX~OP!WO!Z!XO~O!XiOQoX!ZoX~OQ!ZO!Z!XO~O!YlORrX!ZrX~OR!]O!Z!XO~O!Z!XO~P#dOf!_O~O![sO!e!aO~OS!bO~OS!cO~Oi!dOSgXTgXhgX~OS!fOT!gOhyO~OS!hOT!gOhyO~OS!iOT!gOhyO~OS!jOT!gOhyO~OS!gOT!gOhyO~Of!kO~Of!lO~Of!mO~OS!nO~Ok!qO!`!oO!b!pO~OS!rO~OS!sO~OS!tO~Oa!uOb!uOc!uO!`!wO!a!uO~Oa!xOb!xOc!xO!b!wO!c!xO~Oa!uOb!uOc!uO!`!{O!a!uO~Oa!xOb!xOc!xO!b!{O!c!xO~OT~bac!dx{!d~",goto:"%p!`PPPPPPPPPPPPPPPPPPPP!a!gP!mPP!yP!|#P#S#Y#]#`#f#i#l#r#x!aP!a!aP$O$U$l$r$x%O%U%[%bPPPPPPPP%hX^OX`pXUOX`pezabcde{!O!Q!S!UR!q!dRhUR!XhXVOX`pRkVR!XkXWOX`pRnWR!XnXXOX`pQrXR!XpXYOX`pQ`ORx`Q{aQ!ObQ!QcQ!SdQ!UeZ!e{!O!Q!S!UQ!v!oR!z!vQ!y!pR!|!yQgUR!VgQjVR!YjQmWR![mQpXR!^pQtZR!`tS_O`ToXp",nodeNames:"⚠ StartCloseTag StartCloseTag StartCloseTag EndTag SelfClosingEndTag StartTag StartTag StartTag StartTag StartTag StartCloseTag StartCloseTag StartCloseTag IncompleteCloseTag Document Text EntityReference CharacterReference InvalidEntity Element OpenTag TagName Attribute AttributeName Is AttributeValue UnquotedAttributeValue ScriptText CloseTag OpenTag StyleText CloseTag OpenTag TextareaText CloseTag OpenTag CloseTag SelfClosingTag Comment ProcessingInst MismatchedCloseTag CloseTag DoctypeDecl",maxTerm:67,context:md,nodeProps:[["closedBy",-10,1,2,3,7,8,9,10,11,12,13,"EndTag",6,"EndTag SelfClosingEndTag",-4,21,30,33,36,"CloseTag"],["openedBy",4,"StartTag StartCloseTag",5,"StartTag",-4,29,32,35,37,"OpenTag"],["group",-9,14,17,18,19,20,39,40,41,42,"Entity",16,"Entity TextContent",-3,28,31,34,"TextContent Entity"],["isolate",-11,21,29,30,32,33,35,36,37,38,41,42,"ltr",-3,26,27,39,""]],propSources:[xd],skippedNodes:[0],repeatNodeCount:9,tokenData:"!<p!aR!YOX$qXY,QYZ,QZ[$q[]&X]^,Q^p$qpq,Qqr-_rs3_sv-_vw3}wxHYx}-_}!OH{!O!P-_!P!Q$q!Q![-_![!]Mz!]!^-_!^!_!$S!_!`!;x!`!a&X!a!c-_!c!}Mz!}#R-_#R#SMz#S#T1k#T#oMz#o#s-_#s$f$q$f%W-_%W%oMz%o%p-_%p&aMz&a&b-_&b1pMz1p4U-_4U4dMz4d4e-_4e$ISMz$IS$I`-_$I`$IbMz$Ib$Kh-_$Kh%#tMz%#t&/x-_&/x&EtMz&Et&FV-_&FV;'SMz;'S;:j!#|;:j;=`3X<%l?&r-_?&r?AhMz?Ah?BY$q?BY?MnMz?MnO$q!Z$|c`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr$qrs&}sv$qvw+Pwx(tx!^$q!^!_*V!_!a&X!a#S$q#S#T&X#T;'S$q;'S;=`+z<%lO$q!R&bX`P!a`!cpOr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&Xq'UV`P!cpOv&}wx'kx!^&}!^!_(V!_;'S&};'S;=`(n<%lO&}P'pT`POv'kw!^'k!_;'S'k;'S;=`(P<%lO'kP(SP;=`<%l'kp([S!cpOv(Vx;'S(V;'S;=`(h<%lO(Vp(kP;=`<%l(Vq(qP;=`<%l&}a({W`P!a`Or(trs'ksv(tw!^(t!^!_)e!_;'S(t;'S;=`*P<%lO(t`)jT!a`Or)esv)ew;'S)e;'S;=`)y<%lO)e`)|P;=`<%l)ea*SP;=`<%l(t!Q*^V!a`!cpOr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!Q*vP;=`<%l*V!R*|P;=`<%l&XW+UYkWOX+PZ[+P^p+Pqr+Psw+Px!^+P!a#S+P#T;'S+P;'S;=`+t<%lO+PW+wP;=`<%l+P!Z+}P;=`<%l$q!a,]``P!a`!cp!^^OX&XXY,QYZ,QZ]&X]^,Q^p&Xpq,Qqr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&X!_-ljhS`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx!P-_!P!Q$q!Q!^-_!^!_*V!_!a&X!a#S-_#S#T1k#T#s-_#s$f$q$f;'S-_;'S;=`3X<%l?Ah-_?Ah?BY$q?BY?Mn-_?MnO$q[/ebhSkWOX+PZ[+P^p+Pqr/^sw/^x!P/^!P!Q+P!Q!^/^!a#S/^#S#T0m#T#s/^#s$f+P$f;'S/^;'S;=`1e<%l?Ah/^?Ah?BY+P?BY?Mn/^?MnO+PS0rXhSqr0msw0mx!P0m!Q!^0m!a#s0m$f;'S0m;'S;=`1_<%l?Ah0m?BY?Mn0mS1bP;=`<%l0m[1hP;=`<%l/^!V1vchS`P!a`!cpOq&Xqr1krs&}sv1kvw0mwx(tx!P1k!P!Q&X!Q!^1k!^!_*V!_!a&X!a#s1k#s$f&X$f;'S1k;'S;=`3R<%l?Ah1k?Ah?BY&X?BY?Mn1k?MnO&X!V3UP;=`<%l1k!_3[P;=`<%l-_!Z3hV!`h`P!cpOv&}wx'kx!^&}!^!_(V!_;'S&};'S;=`(n<%lO&}!_4WihSkWc!ROX5uXZ7SZ[5u[^7S^p5uqr8trs7Sst>]tw8twx7Sx!P8t!P!Q5u!Q!]8t!]!^/^!^!a7S!a#S8t#S#T;{#T#s8t#s$f5u$f;'S8t;'S;=`>V<%l?Ah8t?Ah?BY5u?BY?Mn8t?MnO5u!Z5zbkWOX5uXZ7SZ[5u[^7S^p5uqr5urs7Sst+Ptw5uwx7Sx!]5u!]!^7w!^!a7S!a#S5u#S#T7S#T;'S5u;'S;=`8n<%lO5u!R7VVOp7Sqs7St!]7S!]!^7l!^;'S7S;'S;=`7q<%lO7S!R7qOa!R!R7tP;=`<%l7S!Z8OYkWa!ROX+PZ[+P^p+Pqr+Psw+Px!^+P!a#S+P#T;'S+P;'S;=`+t<%lO+P!Z8qP;=`<%l5u!_8{ihSkWOX5uXZ7SZ[5u[^7S^p5uqr8trs7Sst/^tw8twx7Sx!P8t!P!Q5u!Q!]8t!]!^:j!^!a7S!a#S8t#S#T;{#T#s8t#s$f5u$f;'S8t;'S;=`>V<%l?Ah8t?Ah?BY5u?BY?Mn8t?MnO5u!_:sbhSkWa!ROX+PZ[+P^p+Pqr/^sw/^x!P/^!P!Q+P!Q!^/^!a#S/^#S#T0m#T#s/^#s$f+P$f;'S/^;'S;=`1e<%l?Ah/^?Ah?BY+P?BY?Mn/^?MnO+P!V<QchSOp7Sqr;{rs7Sst0mtw;{wx7Sx!P;{!P!Q7S!Q!];{!]!^=]!^!a7S!a#s;{#s$f7S$f;'S;{;'S;=`>P<%l?Ah;{?Ah?BY7S?BY?Mn;{?MnO7S!V=dXhSa!Rqr0msw0mx!P0m!Q!^0m!a#s0m$f;'S0m;'S;=`1_<%l?Ah0m?BY?Mn0m!V>SP;=`<%l;{!_>YP;=`<%l8t!_>dhhSkWOX@OXZAYZ[@O[^AY^p@OqrBwrsAYswBwwxAYx!PBw!P!Q@O!Q!]Bw!]!^/^!^!aAY!a#SBw#S#TE{#T#sBw#s$f@O$f;'SBw;'S;=`HS<%l?AhBw?Ah?BY@O?BY?MnBw?MnO@O!Z@TakWOX@OXZAYZ[@O[^AY^p@Oqr@OrsAYsw@OwxAYx!]@O!]!^Az!^!aAY!a#S@O#S#TAY#T;'S@O;'S;=`Bq<%lO@O!RA]UOpAYq!]AY!]!^Ao!^;'SAY;'S;=`At<%lOAY!RAtOb!R!RAwP;=`<%lAY!ZBRYkWb!ROX+PZ[+P^p+Pqr+Psw+Px!^+P!a#S+P#T;'S+P;'S;=`+t<%lO+P!ZBtP;=`<%l@O!_COhhSkWOX@OXZAYZ[@O[^AY^p@OqrBwrsAYswBwwxAYx!PBw!P!Q@O!Q!]Bw!]!^Dj!^!aAY!a#SBw#S#TE{#T#sBw#s$f@O$f;'SBw;'S;=`HS<%l?AhBw?Ah?BY@O?BY?MnBw?MnO@O!_DsbhSkWb!ROX+PZ[+P^p+Pqr/^sw/^x!P/^!P!Q+P!Q!^/^!a#S/^#S#T0m#T#s/^#s$f+P$f;'S/^;'S;=`1e<%l?Ah/^?Ah?BY+P?BY?Mn/^?MnO+P!VFQbhSOpAYqrE{rsAYswE{wxAYx!PE{!P!QAY!Q!]E{!]!^GY!^!aAY!a#sE{#s$fAY$f;'SE{;'S;=`G|<%l?AhE{?Ah?BYAY?BY?MnE{?MnOAY!VGaXhSb!Rqr0msw0mx!P0m!Q!^0m!a#s0m$f;'S0m;'S;=`1_<%l?Ah0m?BY?Mn0m!VHPP;=`<%lE{!_HVP;=`<%lBw!ZHcW!bx`P!a`Or(trs'ksv(tw!^(t!^!_)e!_;'S(t;'S;=`*P<%lO(t!aIYlhS`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx}-_}!OKQ!O!P-_!P!Q$q!Q!^-_!^!_*V!_!a&X!a#S-_#S#T1k#T#s-_#s$f$q$f;'S-_;'S;=`3X<%l?Ah-_?Ah?BY$q?BY?Mn-_?MnO$q!aK_khS`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx!P-_!P!Q$q!Q!^-_!^!_*V!_!`&X!`!aMS!a#S-_#S#T1k#T#s-_#s$f$q$f;'S-_;'S;=`3X<%l?Ah-_?Ah?BY$q?BY?Mn-_?MnO$q!TM_X`P!a`!cp!eQOr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&X!aNZ!ZhSfQ`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx}-_}!OMz!O!PMz!P!Q$q!Q![Mz![!]Mz!]!^-_!^!_*V!_!a&X!a!c-_!c!}Mz!}#R-_#R#SMz#S#T1k#T#oMz#o#s-_#s$f$q$f$}-_$}%OMz%O%W-_%W%oMz%o%p-_%p&aMz&a&b-_&b1pMz1p4UMz4U4dMz4d4e-_4e$ISMz$IS$I`-_$I`$IbMz$Ib$Je-_$Je$JgMz$Jg$Kh-_$Kh%#tMz%#t&/x-_&/x&EtMz&Et&FV-_&FV;'SMz;'S;:j!#|;:j;=`3X<%l?&r-_?&r?AhMz?Ah?BY$q?BY?MnMz?MnO$q!a!$PP;=`<%lMz!R!$ZY!a`!cpOq*Vqr!$yrs(Vsv*Vwx)ex!a*V!a!b!4t!b;'S*V;'S;=`*s<%lO*V!R!%Q]!a`!cpOr*Vrs(Vsv*Vwx)ex}*V}!O!%y!O!f*V!f!g!']!g#W*V#W#X!0`#X;'S*V;'S;=`*s<%lO*V!R!&QX!a`!cpOr*Vrs(Vsv*Vwx)ex}*V}!O!&m!O;'S*V;'S;=`*s<%lO*V!R!&vV!a`!cp!dPOr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!R!'dX!a`!cpOr*Vrs(Vsv*Vwx)ex!q*V!q!r!(P!r;'S*V;'S;=`*s<%lO*V!R!(WX!a`!cpOr*Vrs(Vsv*Vwx)ex!e*V!e!f!(s!f;'S*V;'S;=`*s<%lO*V!R!(zX!a`!cpOr*Vrs(Vsv*Vwx)ex!v*V!v!w!)g!w;'S*V;'S;=`*s<%lO*V!R!)nX!a`!cpOr*Vrs(Vsv*Vwx)ex!{*V!{!|!*Z!|;'S*V;'S;=`*s<%lO*V!R!*bX!a`!cpOr*Vrs(Vsv*Vwx)ex!r*V!r!s!*}!s;'S*V;'S;=`*s<%lO*V!R!+UX!a`!cpOr*Vrs(Vsv*Vwx)ex!g*V!g!h!+q!h;'S*V;'S;=`*s<%lO*V!R!+xY!a`!cpOr!+qrs!,hsv!+qvw!-Swx!.[x!`!+q!`!a!/j!a;'S!+q;'S;=`!0Y<%lO!+qq!,mV!cpOv!,hvx!-Sx!`!,h!`!a!-q!a;'S!,h;'S;=`!.U<%lO!,hP!-VTO!`!-S!`!a!-f!a;'S!-S;'S;=`!-k<%lO!-SP!-kO{PP!-nP;=`<%l!-Sq!-xS!cp{POv(Vx;'S(V;'S;=`(h<%lO(Vq!.XP;=`<%l!,ha!.aX!a`Or!.[rs!-Ssv!.[vw!-Sw!`!.[!`!a!.|!a;'S!.[;'S;=`!/d<%lO!.[a!/TT!a`{POr)esv)ew;'S)e;'S;=`)y<%lO)ea!/gP;=`<%l!.[!R!/sV!a`!cp{POr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!R!0]P;=`<%l!+q!R!0gX!a`!cpOr*Vrs(Vsv*Vwx)ex#c*V#c#d!1S#d;'S*V;'S;=`*s<%lO*V!R!1ZX!a`!cpOr*Vrs(Vsv*Vwx)ex#V*V#V#W!1v#W;'S*V;'S;=`*s<%lO*V!R!1}X!a`!cpOr*Vrs(Vsv*Vwx)ex#h*V#h#i!2j#i;'S*V;'S;=`*s<%lO*V!R!2qX!a`!cpOr*Vrs(Vsv*Vwx)ex#m*V#m#n!3^#n;'S*V;'S;=`*s<%lO*V!R!3eX!a`!cpOr*Vrs(Vsv*Vwx)ex#d*V#d#e!4Q#e;'S*V;'S;=`*s<%lO*V!R!4XX!a`!cpOr*Vrs(Vsv*Vwx)ex#X*V#X#Y!+q#Y;'S*V;'S;=`*s<%lO*V!R!4{Y!a`!cpOr!4trs!5ksv!4tvw!6Vwx!8]x!a!4t!a!b!:]!b;'S!4t;'S;=`!;r<%lO!4tq!5pV!cpOv!5kvx!6Vx!a!5k!a!b!7W!b;'S!5k;'S;=`!8V<%lO!5kP!6YTO!a!6V!a!b!6i!b;'S!6V;'S;=`!7Q<%lO!6VP!6lTO!`!6V!`!a!6{!a;'S!6V;'S;=`!7Q<%lO!6VP!7QOxPP!7TP;=`<%l!6Vq!7]V!cpOv!5kvx!6Vx!`!5k!`!a!7r!a;'S!5k;'S;=`!8V<%lO!5kq!7yS!cpxPOv(Vx;'S(V;'S;=`(h<%lO(Vq!8YP;=`<%l!5ka!8bX!a`Or!8]rs!6Vsv!8]vw!6Vw!a!8]!a!b!8}!b;'S!8];'S;=`!:V<%lO!8]a!9SX!a`Or!8]rs!6Vsv!8]vw!6Vw!`!8]!`!a!9o!a;'S!8];'S;=`!:V<%lO!8]a!9vT!a`xPOr)esv)ew;'S)e;'S;=`)y<%lO)ea!:YP;=`<%l!8]!R!:dY!a`!cpOr!4trs!5ksv!4tvw!6Vwx!8]x!`!4t!`!a!;S!a;'S!4t;'S;=`!;r<%lO!4t!R!;]V!a`!cpxPOr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!R!;uP;=`<%l!4t!V!<TXiS`P!a`!cpOr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&X",tokenizers:[vd,wd,yd,Qd,gd,bd,0,1,2,3,4,5],topRules:{Document:[0,15]},dialects:{noMatch:0,selfClosing:509},tokenPrec:511});function Pd(t,e){let i=Object.create(null);for(let n of t.getChildren(23)){let t=n.getChild(24),s=n.getChild(26)||n.getChild(27);t&&(i[e.read(t.from,t.to)]=s?26==s.type.id?e.read(s.from+1,s.to-1):e.read(s.from,s.to):"")}return i}function $d(t,e){let i=t.getChild(22);return i?e.read(i.from,i.to):" "}function Zd(t,e,i){let n;for(let s of i)if(!s.attrs||s.attrs(n||(n=Pd(t.node.parent.firstChild,e))))return{parser:s.parser};return null}function Td(t=[],e=[]){let i=[],n=[],s=[],r=[];for(let e of t){("script"==e.tag?i:"style"==e.tag?n:"textarea"==e.tag?s:r).push(e)}let o=e.length?Object.create(null):null;for(let t of e)(o[t.name]||(o[t.name]=[])).push(t);return C(((t,e)=>{let a=t.type.id;if(28==a)return Zd(t,e,i);if(31==a)return Zd(t,e,n);if(34==a)return Zd(t,e,s);if(20==a&&r.length){let i,n=t.node,s=n.firstChild,o=s&&$d(s,e);if(o)for(let t of r)if(t.tag==o&&(!t.attrs||t.attrs(i||(i=Pd(s,e))))){let e=n.lastChild,i=37==e.type.id?e.from:n.to;if(i>s.to)return{parser:t.parser,overlay:[{from:s.to,to:i}]}}}if(o&&23==a){let i,n=t.node;if(i=n.firstChild){let t=o[e.read(i.from,i.to)];if(t)for(let i of t){if(i.tagName&&i.tagName!=$d(n.parent,e))continue;let t=n.lastChild;if(26==t.type.id){let e=t.from+1,n=t.lastChild,s=t.to-(n&&n.isError?0:1);if(s>e)return{parser:i.parser,overlay:[{from:e,to:s}]}}else if(27==t.type.id)return{parser:i.parser,overlay:[{from:t.from,to:t.to}]}}}}return null}))}const Xd=[9,10,11,12,13,32,133,160,5760,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8232,8233,8239,8287,12288],Rd=new lc({start:!1,shift:(t,e)=>5==e||6==e||316==e?t:317==e,strict:!1}),Ad=new Nh(((t,e)=>{let{next:i}=t;(125==i||-1==i||e.context)&&t.acceptToken(314)}),{contextual:!0,fallback:!0}),Cd=new Nh(((t,e)=>{let i,{next:n}=t;Xd.indexOf(n)>-1||(47!=n||47!=(i=t.peek(1))&&42!=i)&&(125==n||59==n||-1==n||e.context||t.acceptToken(312))}),{contextual:!0}),Md=new Nh(((t,e)=>{91!=t.next||e.context||t.acceptToken(313)}),{contextual:!0}),qd=new Nh(((t,e)=>{let{next:i}=t;if(43==i||45==i){if(t.advance(),i==t.next){t.advance();let i=!e.context&&e.canShift(1);t.acceptToken(i?1:2)}}else 63==i&&46==t.peek(1)&&(t.advance(),t.advance(),(t.next<48||t.next>57)&&t.acceptToken(3))}),{contextual:!0});function Vd(t,e){return t>=65&&t<=90||t>=97&&t<=122||95==t||t>=192||!e&&t>=48&&t<=57}const Yd=new Nh(((t,e)=>{if(60!=t.next||!e.dialectEnabled(0))return;if(t.advance(),47==t.next)return;let i=0;for(;Xd.indexOf(t.next)>-1;)t.advance(),i++;if(Vd(t.next,!0)){for(t.advance(),i++;Vd(t.next,!1);)t.advance(),i++;for(;Xd.indexOf(t.next)>-1;)t.advance(),i++;if(44==t.next)return;for(let e=0;;e++){if(7==e){if(!Vd(t.next,!0))return;break}if(t.next!="extends".charCodeAt(e))break;t.advance(),i++}}t.acceptToken(4,-i)})),zd=da({"get set async static":qa.modifier,"for while do if else switch try catch finally return throw break continue default case":qa.controlKeyword,"in of await yield void typeof delete instanceof":qa.operatorKeyword,"let var const using function class extends":qa.definitionKeyword,"import export from":qa.moduleKeyword,"with debugger as new":qa.keyword,TemplateString:qa.special(qa.string),super:qa.atom,BooleanLiteral:qa.bool,this:qa.self,null:qa.null,Star:qa.modifier,VariableName:qa.variableName,"CallExpression/VariableName TaggedTemplateExpression/VariableName":qa.function(qa.variableName),VariableDefinition:qa.definition(qa.variableName),Label:qa.labelName,PropertyName:qa.propertyName,PrivatePropertyName:qa.special(qa.propertyName),"CallExpression/MemberExpression/PropertyName":qa.function(qa.propertyName),"FunctionDeclaration/VariableDefinition":qa.function(qa.definition(qa.variableName)),"ClassDeclaration/VariableDefinition":qa.definition(qa.className),PropertyDefinition:qa.definition(qa.propertyName),PrivatePropertyDefinition:qa.definition(qa.special(qa.propertyName)),UpdateOp:qa.updateOperator,"LineComment Hashbang":qa.lineComment,BlockComment:qa.blockComment,Number:qa.number,String:qa.string,Escape:qa.escape,ArithOp:qa.arithmeticOperator,LogicOp:qa.logicOperator,BitOp:qa.bitwiseOperator,CompareOp:qa.compareOperator,RegExp:qa.regexp,Equals:qa.definitionOperator,Arrow:qa.function(qa.punctuation),": Spread":qa.punctuation,"( )":qa.paren,"[ ]":qa.squareBracket,"{ }":qa.brace,"InterpolationStart InterpolationEnd":qa.special(qa.brace),".":qa.derefOperator,", ;":qa.separator,"@":qa.meta,TypeName:qa.typeName,TypeDefinition:qa.definition(qa.typeName),"type enum interface implements namespace module declare":qa.definitionKeyword,"abstract global Privacy readonly override":qa.modifier,"is keyof unique infer":qa.operatorKeyword,JSXAttributeValue:qa.attributeValue,JSXText:qa.content,"JSXStartTag JSXStartCloseTag JSXSelfCloseEndTag JSXEndTag":qa.angleBracket,"JSXIdentifier JSXNameSpacedName":qa.tagName,"JSXAttribute/JSXIdentifier JSXAttribute/JSXNameSpacedName":qa.attributeName,"JSXBuiltin/JSXIdentifier":qa.standard(qa.tagName)}),_d={__proto__:null,export:20,as:25,from:33,default:36,async:41,function:42,extends:54,this:58,true:66,false:66,null:78,void:82,typeof:86,super:102,new:136,delete:148,yield:157,await:161,class:166,public:229,private:229,protected:229,readonly:231,instanceof:250,satisfies:253,in:254,const:256,import:290,keyof:345,unique:349,infer:355,is:391,abstract:411,implements:413,type:415,let:418,var:420,using:423,interface:429,enum:433,namespace:439,module:441,declare:445,global:449,for:468,of:477,while:480,with:484,do:488,if:492,else:494,switch:498,case:504,try:510,catch:514,finally:518,return:522,throw:526,break:530,continue:534,debugger:538},jd={__proto__:null,async:123,get:125,set:127,declare:189,public:191,private:191,protected:191,static:193,abstract:195,override:197,readonly:203,accessor:205,new:395},Ed={__proto__:null,"<":187},Dd=hc.deserialize({version:14,states:"$CdQ%TQlOOO%[QlOOO'_QpOOP(lO`OOO*zQ!0MxO'#CiO+RO#tO'#CjO+aO&jO'#CjO+oO#@ItO'#D^O.QQlO'#DdO.bQlO'#DoO%[QlO'#DwO0fQlO'#EPOOQ!0Lf'#EX'#EXO1PQ`O'#EUOOQO'#Em'#EmOOQO'#Ih'#IhO1XQ`O'#GpO1dQ`O'#ElO1iQ`O'#ElO3hQ!0MxO'#JnO6[Q!0MxO'#JoO6uQ`O'#F[O6zQ,UO'#FsOOQ!0Lf'#Fe'#FeO7VO7dO'#FeO7eQMhO'#FzO9RQ`O'#FyOOQ!0Lf'#Jo'#JoOOQ!0Lb'#Jn'#JnO9WQ`O'#GtOOQ['#K['#K[O9cQ`O'#IUO9hQ!0LrO'#IVOOQ['#J['#J[OOQ['#IZ'#IZQ`QlOOQ`QlOOO9pQ!L^O'#DsO9wQlO'#D{O:OQlO'#D}O9^Q`O'#GpO:VQMhO'#CoO:eQ`O'#EkO:pQ`O'#EvO:uQMhO'#FdO;dQ`O'#GpOOQO'#K]'#K]O;iQ`O'#K]O;wQ`O'#GxO;wQ`O'#GyO;wQ`O'#G{O9^Q`O'#HOO<nQ`O'#HRO>VQ`O'#CeO>gQ`O'#H_O>oQ`O'#HeO>oQ`O'#HgO`QlO'#HiO>oQ`O'#HkO>oQ`O'#HnO>tQ`O'#HtO>yQ!0LsO'#HzO%[QlO'#H|O?UQ!0LsO'#IOO?aQ!0LsO'#IQO9hQ!0LrO'#ISO?lQ!0MxO'#CiO@nQpO'#DiQOQ`OOO%[QlO'#D}OAUQ`O'#EQO:VQMhO'#EkOAaQ`O'#EkOAlQ!bO'#FdOOQ['#Cg'#CgOOQ!0Lb'#Dn'#DnOOQ!0Lb'#Jr'#JrO%[QlO'#JrOOQO'#Ju'#JuOOQO'#Id'#IdOBlQpO'#EdOOQ!0Lb'#Ec'#EcOOQ!0Lb'#Jy'#JyOChQ!0MSO'#EdOCrQpO'#ETOOQO'#Jt'#JtODWQpO'#JuOEeQpO'#ETOCrQpO'#EdPErO&2DjO'#CbPOOO)CDy)CDyOOOO'#I['#I[OE}O#tO,59UOOQ!0Lh,59U,59UOOOO'#I]'#I]OF]O&jO,59UOFkQ!L^O'#D`OOOO'#I_'#I_OFrO#@ItO,59xOOQ!0Lf,59x,59xOGQQlO'#I`OGeQ`O'#JpOIdQ!fO'#JpO+}QlO'#JpOIkQ`O,5:OOJRQ`O'#EmOJ`Q`O'#KPOJkQ`O'#KOOJkQ`O'#KOOJsQ`O,5;ZOJxQ`O'#J}OOQ!0Ln,5:Z,5:ZOKPQlO,5:ZOL}Q!0MxO,5:cOMnQ`O,5:kONXQ!0LrO'#J|ON`Q`O'#J{O9WQ`O'#J{ONtQ`O'#J{ON|Q`O,5;YO! RQ`O'#J{O!#WQ!fO'#JoOOQ!0Lh'#Ci'#CiO%[QlO'#EPO!#vQ!fO,5:pOOQS'#Jv'#JvOOQO-E<f-E<fO9^Q`O,5=[O!$^Q`O,5=[O!$cQlO,5;WO!&fQMhO'#EhO!(PQ`O,5;WO!(UQlO'#DvO!(`QpO,5;aO!(hQpO,5;aO%[QlO,5;aOOQ['#FS'#FSOOQ['#FU'#FUO%[QlO,5;bO%[QlO,5;bO%[QlO,5;bO%[QlO,5;bO%[QlO,5;bO%[QlO,5;bO%[QlO,5;bO%[QlO,5;bO%[QlO,5;bO%[QlO,5;bOOQ['#FY'#FYO!(vQlO,5;sOOQ!0Lf,5;x,5;xOOQ!0Lf,5;y,5;yOOQ!0Lf,5;{,5;{O%[QlO'#IlO!*yQ!0LrO,5<gO%[QlO,5;bO!&fQMhO,5;bO!+hQMhO,5;bO!-YQMhO'#EZO%[QlO,5;vOOQ!0Lf,5;z,5;zO!-aQ,UO'#FiO!.^Q,UO'#KTO!-xQ,UO'#KTO!.eQ,UO'#KTOOQO'#KT'#KTO!.yQ,UO,5<ROOOW,5<_,5<_O!/[QlO'#FuOOOW'#Ik'#IkO7VO7dO,5<PO!/cQ,UO'#FwOOQ!0Lf,5<P,5<PO!0SQ$IUO'#CvOOQ!0Lh'#Cz'#CzO!0gO#@ItO'#DOO!1TQMjO,5<dO!1[Q`O,5<fO!2wQ(CWO'#GVO!3UQ`O'#GWO!3ZQ`O'#GWO!4yQ(CWO'#G[O!6OQpO'#G`OOQO'#Gk'#GkO!+oQMhO'#GjOOQO'#Gm'#GmO!+oQMhO'#GlO!6qQ$IUO'#JhOOQ!0Lh'#Jh'#JhO!6{Q`O'#JgO!7ZQ`O'#JfO!7cQ`O'#CuOOQ!0Lh'#Cx'#CxO!7kQ`O'#CzOOQ!0Lh'#DS'#DSOOQ!0Lh'#DU'#DUO1SQ`O'#DWO!+oQMhO'#F}O!+oQMhO'#GPO!7pQ`O'#GRO!7uQ`O'#GSO!3ZQ`O'#GYO!+oQMhO'#G_O!7zQ`O'#EnO!8iQ`O,5<eOOQ!0Lb'#Cr'#CrO!8qQ`O'#EoO!9kQpO'#EpOOQ!0Lb'#J}'#J}O!9rQ!0LrO'#K^O9hQ!0LrO,5=`O`QlO,5>pOOQ['#Jd'#JdOOQ[,5>q,5>qOOQ[-E<X-E<XO!;qQ!0MxO,5:_O!9fQpO,5:]O!>[Q!0MxO,5:gO%[QlO,5:gO!@rQ!0MxO,5:iOOQO,5@w,5@wO!AcQMhO,5=[O!AqQ!0LrO'#JeO9RQ`O'#JeO!BSQ!0LrO,59ZO!B_QpO,59ZO!BgQMhO,59ZO:VQMhO,59ZO!BrQ`O,5;WO!BzQ`O'#H^O!C`Q`O'#KaO%[QlO,5;|O!9fQpO,5<OO!ChQ`O,5=wO!CmQ`O,5=wO!CrQ`O,5=wO9hQ!0LrO,5=wO;wQ`O,5=gOOQO'#Cv'#CvO!DQQpO,5=dO!DYQMhO,5=eO!DeQ`O,5=gO!DjQ!bO,5=jO!DrQ`O'#K]O>tQ`O'#HTO9^Q`O'#HVO!DwQ`O'#HVO:VQMhO'#HXO!D|Q`O'#HXOOQ[,5=m,5=mO!ERQ`O'#HYO!EdQ`O'#CoO!EiQ`O,59PO!EsQ`O,59PO!GxQlO,59POOQ[,59P,59PO!HYQ!0LrO,59PO%[QlO,59PO!JeQlO'#HaOOQ['#Hb'#HbOOQ['#Hc'#HcO`QlO,5=yO!J{Q`O,5=yO`QlO,5>PO`QlO,5>RO!KQQ`O,5>TO`QlO,5>VO!KVQ`O,5>YO!K[QlO,5>`OOQ[,5>f,5>fO%[QlO,5>fO9hQ!0LrO,5>hOOQ[,5>j,5>jO# fQ`O,5>jOOQ[,5>l,5>lO# fQ`O,5>lOOQ[,5>n,5>nO#!SQpO'#D[O%[QlO'#JrO#!uQpO'#JrO##PQpO'#DjO##bQpO'#DjO#%sQlO'#DjO#%zQ`O'#JqO#&SQ`O,5:TO#&XQ`O'#EqO#&gQ`O'#KQO#&oQ`O,5;[O#&tQpO'#DjO#'RQpO'#ESOOQ!0Lf,5:l,5:lO%[QlO,5:lO#'YQ`O,5:lO>tQ`O,5;VO!B_QpO,5;VO!BgQMhO,5;VO:VQMhO,5;VO#'bQ`O,5@^O#'gQ07dO,5:pOOQO-E<b-E<bO#(mQ!0MSO,5;OOCrQpO,5:oO#(wQpO,5:oOCrQpO,5;OO!BSQ!0LrO,5:oOOQ!0Lb'#Eg'#EgOOQO,5;O,5;OO%[QlO,5;OO#)UQ!0LrO,5;OO#)aQ!0LrO,5;OO!B_QpO,5:oOOQO,5;U,5;UO#)oQ!0LrO,5;OPOOO'#IY'#IYP#*TO&2DjO,58|POOO,58|,58|OOOO-E<Y-E<YOOQ!0Lh1G.p1G.pOOOO-E<Z-E<ZOOOO,59z,59zO#*`Q!bO,59zOOOO-E<]-E<]OOQ!0Lf1G/d1G/dO#*eQ!fO,5>zO+}QlO,5>zOOQO,5?Q,5?QO#*oQlO'#I`OOQO-E<^-E<^O#*|Q`O,5@[O#+UQ!fO,5@[O#+]Q`O,5@jOOQ!0Lf1G/j1G/jO%[QlO,5@kO#+eQ`O'#IfOOQO-E<d-E<dO#+]Q`O,5@jOOQ!0Lb1G0u1G0uOOQ!0Ln1G/u1G/uOOQ!0Ln1G0V1G0VO%[QlO,5@hO#+yQ!0LrO,5@hO#,[Q!0LrO,5@hO#,cQ`O,5@gO9WQ`O,5@gO#,kQ`O,5@gO#,yQ`O'#IiO#,cQ`O,5@gOOQ!0Lb1G0t1G0tO!(`QpO,5:rO!(kQpO,5:rOOQS,5:t,5:tO#-kQdO,5:tO#-sQMhO1G2vO9^Q`O1G2vOOQ!0Lf1G0r1G0rO#.RQ!0MxO1G0rO#/WQ!0MvO,5;SOOQ!0Lh'#GU'#GUO#/tQ!0MzO'#JhO!$cQlO1G0rO#2PQ!fO'#JsO%[QlO'#JsO#2ZQ`O,5:bOOQ!0Lh'#D['#D[OOQ!0Lf1G0{1G0{O%[QlO1G0{OOQ!0Lf1G1e1G1eO#2`Q`O1G0{O#4tQ!0MxO1G0|O#4{Q!0MxO1G0|O#7cQ!0MxO1G0|O#7jQ!0MxO1G0|O#:QQ!0MxO1G0|O#<hQ!0MxO1G0|O#<oQ!0MxO1G0|O#<vQ!0MxO1G0|O#?^Q!0MxO1G0|O#?eQ!0MxO1G0|O#ArQ?MtO'#CiO#CmQ?MtO1G1_O#CtQ?MtO'#JoO#DXQ!0MxO,5?WOOQ!0Lb-E<j-E<jO#FfQ!0MxO1G0|O#GcQ!0MzO1G0|OOQ!0Lf1G0|1G0|O#HfQMjO'#JxO#HpQ`O,5:uO#HuQ!0MxO1G1bO#IiQ,UO,5<VO#IqQ,UO,5<WO#IyQ,UO'#FnO#JbQ`O'#FmOOQO'#KU'#KUOOQO'#Ij'#IjO#JgQ,UO1G1mOOQ!0Lf1G1m1G1mOOOW1G1x1G1xO#JxQ?MtO'#JnO#KSQ`O,5<aO!(vQlO,5<aOOOW-E<i-E<iOOQ!0Lf1G1k1G1kO#KXQpO'#KTOOQ!0Lf,5<c,5<cO#KaQpO,5<cO#KfQMhO'#DQOOOO'#I^'#I^O#KmO#@ItO,59jOOQ!0Lh,59j,59jO%[QlO1G2OO!7uQ`O'#InO#KxQ`O,5<xOOQ!0Lh,5<u,5<uO!+oQMhO'#IqO#LfQMjO,5=UO!+oQMhO'#IsO#MXQMjO,5=WO!&fQMhO,5=YOOQO1G2Q1G2QO#McQ!dO'#CrO#MvQ(CWO'#EoO#N{QpO'#G`O$ cQ!dO,5<qO$ jQ`O'#KXO9WQ`O'#KXO$ xQ`O,5<sO!+oQMhO,5<rO$ }Q`O'#GXO$!`Q`O,5<rO$!eQ!dO'#GUO$!rQ!dO'#KYO$!|Q`O'#KYO!&fQMhO'#KYO$#RQ`O,5<vO$#WQlO'#JrO$#bQpO'#GaO##bQpO'#GaO$#sQ`O'#GeO!3ZQ`O'#GhO$#xQ!0LrO'#IpO$$TQpO,5<zOOQ!0Lp,5<z,5<zO$$[QpO'#GaO$$iQpO'#GbO$$zQpO'#GbO$%PQMjO,5=UO$%aQMjO,5=WOOQ!0Lh,5=Z,5=ZO!+oQMhO,5@RO!+oQMhO,5@RO$%qQ`O'#IuO$%|Q`O,5@QO$&UQ`O,59aOOQ!0Lh,59f,59fO$&{Q$IYO,59rOOQ!0Lh'#Jl'#JlO$'nQMjO,5<iO$(aQMjO,5<kO@fQ`O,5<mOOQ!0Lh,5<n,5<nO$(kQ`O,5<tO$(pQMjO,5<yO$)QQ`O'#J{O!$cQlO1G2PO$)VQ`O1G2PO9WQ`O'#KOO9WQ`O'#EqO%[QlO'#EqO9WQ`O'#IwO$)[Q!0LrO,5@xOOQ[1G2z1G2zOOQ[1G4[1G4[OOQ!0Lf1G/y1G/yOOQ!0Lf1G/w1G/wO$+^Q!0MxO1G0ROOQ[1G2v1G2vO!&fQMhO1G2vO%[QlO1G2vO#-vQ`O1G2vO$-bQMhO'#EhOOQ!0Lb,5@P,5@PO$-lQ!0LrO,5@POOQ[1G.u1G.uO!BSQ!0LrO1G.uO!B_QpO1G.uO!BgQMhO1G.uO$-}Q`O1G0rO$.SQ`O'#CiO$._Q`O'#KbO$.gQ`O,5=xO$.lQ`O'#KbO$.qQ`O'#KbO$/PQ`O'#I}O$/_Q`O,5@{O$/gQ!fO1G1hOOQ!0Lf1G1j1G1jO9^Q`O1G3cO@fQ`O1G3cO$/nQ`O1G3cO$/sQ`O1G3cOOQ[1G3c1G3cO!DeQ`O1G3RO!&fQMhO1G3OO$/xQ`O1G3OOOQ[1G3P1G3PO!&fQMhO1G3PO$/}Q`O1G3PO$0VQpO'#G}OOQ[1G3R1G3RO!5yQpO'#IyO!DjQ!bO1G3UOOQ[1G3U1G3UOOQ[,5=o,5=oO$0_QMhO,5=qO9^Q`O,5=qO$#sQ`O,5=sO9RQ`O,5=sO!B_QpO,5=sO!BgQMhO,5=sO:VQMhO,5=sO$0mQ`O'#K`O$0xQ`O,5=tOOQ[1G.k1G.kO$0}Q!0LrO1G.kO@fQ`O1G.kO$1YQ`O1G.kO9hQ!0LrO1G.kO$3bQ!fO,5@}O$3oQ`O,5@}O9WQ`O,5@}O$3zQlO,5={O$4RQ`O,5={OOQ[1G3e1G3eO`QlO1G3eOOQ[1G3k1G3kOOQ[1G3m1G3mO>oQ`O1G3oO$4WQlO1G3qO$8[QlO'#HpOOQ[1G3t1G3tO$8iQ`O'#HvO>tQ`O'#HxOOQ[1G3z1G3zO$8qQlO1G3zO9hQ!0LrO1G4QOOQ[1G4S1G4SOOQ!0Lb'#G]'#G]O9hQ!0LrO1G4UO9hQ!0LrO1G4WO$<xQ`O,5@^O!(vQlO,5;]O9WQ`O,5;]O>tQ`O,5:UO!(vQlO,5:UO!B_QpO,5:UO$<}Q?MtO,5:UOOQO,5;],5;]O$=XQpO'#IaO$=oQ`O,5@]OOQ!0Lf1G/o1G/oO$=wQpO'#IgO$>RQ`O,5@lOOQ!0Lb1G0v1G0vO##bQpO,5:UOOQO'#Ic'#IcO$>ZQpO,5:nOOQ!0Ln,5:n,5:nO#']Q`O1G0WOOQ!0Lf1G0W1G0WO%[QlO1G0WOOQ!0Lf1G0q1G0qO>tQ`O1G0qO!B_QpO1G0qO!BgQMhO1G0qOOQ!0Lb1G5x1G5xO!BSQ!0LrO1G0ZOOQO1G0j1G0jO%[QlO1G0jO$>bQ!0LrO1G0jO$>mQ!0LrO1G0jO!B_QpO1G0ZOCrQpO1G0ZO$>{Q!0LrO1G0jOOQO1G0Z1G0ZO$?aQ!0MxO1G0jPOOO-E<W-E<WPOOO1G.h1G.hOOOO1G/f1G/fO$?kQ!bO,5<gO$?sQ!fO1G4fOOQO1G4l1G4lO%[QlO,5>zO$?}Q`O1G5vO$@VQ`O1G6UO$@_Q!fO1G6VO9WQ`O,5?QO$@iQ!0MxO1G6SO%[QlO1G6SO$@yQ!0LrO1G6SO$A[Q`O1G6RO$A[Q`O1G6RO9WQ`O1G6RO$AdQ`O,5?TO9WQ`O,5?TOOQO,5?T,5?TO$AxQ`O,5?TO$)QQ`O,5?TOOQO-E<g-E<gOOQS1G0^1G0^OOQS1G0`1G0`O#-nQ`O1G0`OOQ[7+(b7+(bO!&fQMhO7+(bO%[QlO7+(bO$BWQ`O7+(bO$BcQMhO7+(bO$BqQ!0MzO,5=UO$D|Q!0MzO,5=WO$GXQ!0MzO,5=UO$IjQ!0MzO,5=WO$K{Q!0MzO,59rO$NQQ!0MzO,5<iO%!]Q!0MzO,5<kO%$hQ!0MzO,5<yOOQ!0Lf7+&^7+&^O%&yQ!0MxO7+&^O%'mQlO'#IbO%'zQ`O,5@_O%(SQ!fO,5@_OOQ!0Lf1G/|1G/|O%(^Q`O7+&gOOQ!0Lf7+&g7+&gO%(cQ?MtO,5:cO%[QlO7+&yO%(mQ?MtO,5:_O%(zQ?MtO,5:gO%)UQ?MtO,5:iO%)`QMhO'#IeO%)jQ`O,5@dOOQ!0Lh1G0a1G0aOOQO1G1q1G1qOOQO1G1r1G1rO%)rQ!jO,5<YO!(vQlO,5<XOOQO-E<h-E<hOOQ!0Lf7+'X7+'XOOOW7+'d7+'dOOOW1G1{1G1{O%)}Q`O1G1{OOQ!0Lf1G1}1G1}OOOO,59l,59lO%*SQ!dO,59lOOOO-E<[-E<[OOQ!0Lh1G/U1G/UO%*ZQ!0MxO7+'jOOQ!0Lh,5?Y,5?YO%*}QMhO1G2dP%+UQ`O'#InPOQ!0Lh-E<l-E<lO%+rQMjO,5?]OOQ!0Lh-E<o-E<oO%,eQMjO,5?_OOQ!0Lh-E<q-E<qO%,oQ!dO1G2tO%,vQ!dO'#CrO%-^QMhO'#KOO$#WQlO'#JrOOQ!0Lh1G2]1G2]O%-eQ`O'#ImO%-yQ`O,5@sO%-yQ`O,5@sO%.RQ`O,5@sO%.^Q`O,5@sOOQO1G2_1G2_O%.lQMjO1G2^O!+oQMhO1G2^O%.|Q(CWO'#IoO%/ZQ`O,5@tO!&fQMhO,5@tO%/cQ!dO,5@tOOQ!0Lh1G2b1G2bO%1sQ!fO'#CiO%1}Q`O,5<}OOQ!0Lb,5<{,5<{O%2VQpO,5<{OOQ!0Lb,5<|,5<|OCcQ`O,5<{O%2bQpO,5<{OOQ!0Lb,5=P,5=PO$)QQ`O,5=SOOQO,5?[,5?[OOQO-E<n-E<nOOQ!0Lp1G2f1G2fO##bQpO,5<{O$#WQlO,5<}O%2pQ`O,5<|O%2{QpO,5<|O!+oQMhO'#IqO%3uQMjO1G2pO!+oQMhO'#IsO%4hQMjO1G2rO%4rQMjO1G5mO%4|QMjO1G5mOOQO,5?a,5?aOOQO-E<s-E<sOOQO1G.{1G.{O!9fQpO,59tO%[QlO,59tOOQ!0Lh,5<h,5<hO%5ZQ`O1G2XO!+oQMhO1G2`O%5`Q!0MxO7+'kOOQ!0Lf7+'k7+'kO!$cQlO7+'kO%6SQ`O,5;]OOQ!0Lb,5?c,5?cOOQ!0Lb-E<u-E<uO%6XQ!dO'#KZO#']Q`O7+(bO4UQ!fO7+(bO$BZQ`O7+(bO%6cQ!0MvO'#CiO%6vQ!0MvO,5=QO%7hQ`O,5=QOOQ!0Lb1G5k1G5kOOQ[7+$a7+$aO!BSQ!0LrO7+$aO!B_QpO7+$aO!$cQlO7+&^O%7mQ`O'#I|O%8UQ`O,5@|OOQO1G3d1G3dO9^Q`O,5@|O%8UQ`O,5@|O%8^Q`O,5@|OOQO,5?i,5?iOOQO-E<{-E<{OOQ!0Lf7+'S7+'SO%8cQ`O7+(}O9hQ!0LrO7+(}O9^Q`O7+(}O@fQ`O7+(}OOQ[7+(m7+(mO%8hQ!0MvO7+(jO!&fQMhO7+(jO!D`Q`O7+(kOOQ[7+(k7+(kO!&fQMhO7+(kO%8rQ`O'#K_O%8}Q`O,5=iOOQO,5?e,5?eOOQO-E<w-E<wOOQ[7+(p7+(pO%:aQpO'#HWOOQ[1G3]1G3]O!&fQMhO1G3]O%[QlO1G3]O%:hQ`O1G3]O%:sQMhO1G3]O9hQ!0LrO1G3_O$#sQ`O1G3_O9RQ`O1G3_O!B_QpO1G3_O!BgQMhO1G3_O%;RQ`O'#I{O%;gQ`O,5@zO%;oQpO,5@zOOQ!0Lb1G3`1G3`OOQ[7+$V7+$VO@fQ`O7+$VO9hQ!0LrO7+$VO%;zQ`O7+$VO%[QlO1G6iO%[QlO1G6jO%<PQ!0LrO1G6iO%<ZQlO1G3gO%<bQ`O1G3gO%<gQlO1G3gOOQ[7+)P7+)PO9hQ!0LrO7+)ZO`QlO7+)]OOQ['#Ke'#KeOOQ['#JO'#JOO%<nQlO,5>[OOQ[,5>[,5>[O%[QlO'#HqO%<{Q`O'#HsOOQ[,5>b,5>bO9WQ`O,5>bOOQ[,5>d,5>dOOQ[7+)f7+)fOOQ[7+)l7+)lOOQ[7+)p7+)pOOQ[7+)r7+)rO%=QQpO1G5xO%=lQ?MtO1G0wO%=vQ`O1G0wOOQO1G/p1G/pO%>RQ?MtO1G/pO>tQ`O1G/pO!(vQlO'#DjOOQO,5>{,5>{OOQO-E<_-E<_OOQO,5?R,5?ROOQO-E<e-E<eO!B_QpO1G/pOOQO-E<a-E<aOOQ!0Ln1G0Y1G0YOOQ!0Lf7+%r7+%rO#']Q`O7+%rOOQ!0Lf7+&]7+&]O>tQ`O7+&]O!B_QpO7+&]OOQO7+%u7+%uO$?aQ!0MxO7+&UOOQO7+&U7+&UO%[QlO7+&UO%>]Q!0LrO7+&UO!BSQ!0LrO7+%uO!B_QpO7+%uO%>hQ!0LrO7+&UO%>vQ!0MxO7++nO%[QlO7++nO%?WQ`O7++mO%?WQ`O7++mOOQO1G4o1G4oO9WQ`O1G4oO%?`Q`O1G4oOOQS7+%z7+%zO#']Q`O<<K|O4UQ!fO<<K|O%?nQ`O<<K|OOQ[<<K|<<K|O!&fQMhO<<K|O%[QlO<<K|O%?vQ`O<<K|O%@RQ!0MzO,5?]O%B^Q!0MzO,5?_O%DiQ!0MzO1G2^O%FzQ!0MzO1G2pO%IVQ!0MzO1G2rO%KbQ!fO,5>|O%[QlO,5>|OOQO-E<`-E<`O%KlQ`O1G5yOOQ!0Lf<<JR<<JRO%KtQ?MtO1G0rO%M{Q?MtO1G0|O%NSQ?MtO1G0|O&!TQ?MtO1G0|O&![Q?MtO1G0|O&$]Q?MtO1G0|O&&^Q?MtO1G0|O&&eQ?MtO1G0|O&&lQ?MtO1G0|O&(mQ?MtO1G0|O&(tQ?MtO1G0|O&({Q!0MxO<<JeO&*sQ?MtO1G0|O&+pQ?MvO1G0|O&,sQ?MvO'#JhO&.yQ?MtO1G1bO&/WQ?MtO1G0RO&/bQMjO,5?POOQO-E<c-E<cO!(vQlO'#FpOOQO'#KV'#KVOOQO1G1t1G1tO&/lQ`O1G1sO&/qQ?MtO,5?WOOOW7+'g7+'gOOOO1G/W1G/WO&/{Q!dO1G4tOOQ!0Lh7+(O7+(OP!&fQMhO,5?YO!+oQMhO7+(`O&0SQ`O,5?XO9WQ`O,5?XOOQO-E<k-E<kO&0bQ`O1G6_O&0bQ`O1G6_O&0jQ`O1G6_O&0uQMjO7+'xO&1VQ!dO,5?ZO&1aQ`O,5?ZO!&fQMhO,5?ZOOQO-E<m-E<mO&1fQ!dO1G6`O&1pQ`O1G6`O&1xQ`O1G2iO!&fQMhO1G2iOOQ!0Lb1G2g1G2gOOQ!0Lb1G2h1G2hO%2VQpO1G2gO!B_QpO1G2gOCcQ`O1G2gOOQ!0Lb1G2n1G2nO&1}QpO1G2gO&2]Q`O1G2iO$)QQ`O1G2hOCcQ`O1G2hO$#WQlO1G2iO&2eQ`O1G2hO&3XQMjO,5?]OOQ!0Lh-E<p-E<pO&3zQMjO,5?_OOQ!0Lh-E<r-E<rO!+oQMhO7++XOOQ!0Lh1G/`1G/`O&4UQ`O1G/`OOQ!0Lh7+'s7+'sO&4ZQMjO7+'zO&4kQ!0MxO<<KVOOQ!0Lf<<KV<<KVO&5_Q`O1G0wO!&fQMhO'#IvO&5dQ`O,5@uO&7fQ!fO<<K|O!&fQMhO1G2lOOQ[<<G{<<G{O!BSQ!0LrO<<G{O&7mQ!0MxO<<IxOOQ!0Lf<<Ix<<IxOOQO,5?h,5?hO&8aQ`O,5?hO&8fQ`O,5?hOOQO-E<z-E<zO&8tQ`O1G6hO&8tQ`O1G6hO9^Q`O1G6hO@fQ`O<<LiOOQ[<<Li<<LiO&8|Q`O<<LiO9hQ!0LrO<<LiOOQ[<<LU<<LUO%8hQ!0MvO<<LUOOQ[<<LV<<LVO!D`Q`O<<LVO&9RQpO'#IxO&9^Q`O,5@yO!(vQlO,5@yOOQ[1G3T1G3TOOQO'#Iz'#IzO9hQ!0LrO'#IzO&9fQpO,5=rOOQ[,5=r,5=rO&9mQpO'#EdO&9tQpO'#GcO&9yQ`O7+(wO&:OQ`O7+(wOOQ[7+(w7+(wO!&fQMhO7+(wO%[QlO7+(wO&:WQ`O7+(wOOQ[7+(y7+(yO9hQ!0LrO7+(yO$#sQ`O7+(yO9RQ`O7+(yO!B_QpO7+(yO&:cQ`O,5?gOOQO-E<y-E<yOOQO'#HZ'#HZO&:nQ`O1G6fO9hQ!0LrO<<GqOOQ[<<Gq<<GqO@fQ`O<<GqO&:vQ`O7+,TO&:{Q`O7+,UO%[QlO7+,TO%[QlO7+,UOOQ[7+)R7+)RO&;QQ`O7+)RO&;VQlO7+)RO&;^Q`O7+)ROOQ[<<Lu<<LuOOQ[<<Lw<<LwOOQ[-E<|-E<|OOQ[1G3v1G3vO&;cQ`O,5>]OOQ[,5>_,5>_O&;hQ`O1G3|O9WQ`O7+&cO!(vQlO7+&cOOQO7+%[7+%[O&;mQ?MtO1G6VO>tQ`O7+%[OOQ!0Lf<<I^<<I^OOQ!0Lf<<Iw<<IwO>tQ`O<<IwOOQO<<Ip<<IpO$?aQ!0MxO<<IpO%[QlO<<IpOOQO<<Ia<<IaO!BSQ!0LrO<<IaO&;wQ!0LrO<<IpO&<SQ!0MxO<= YO&<dQ`O<= XOOQO7+*Z7+*ZO9WQ`O7+*ZOOQ[ANAhANAhO&<lQ!fOANAhO!&fQMhOANAhO#']Q`OANAhO4UQ!fOANAhO&<sQ`OANAhO%[QlOANAhO&<{Q!0MzO7+'xO&?^Q!0MzO,5?]O&AiQ!0MzO,5?_O&CtQ!0MzO7+'zO&FVQ!fO1G4hO&FaQ?MtO7+&^O&HeQ?MvO,5=UO&JlQ?MvO,5=WO&J|Q?MvO,5=UO&K^Q?MvO,5=WO&KnQ?MvO,59rO&MtQ?MvO,5<iO' wQ?MvO,5<kO'$]Q?MvO,5<yO'&RQ?MtO7+'jO'&`Q?MtO7+'kO'&mQ`O,5<[OOQO7+'_7+'_OOQ!0Lh7+*`7+*`O'&rQMjO<<KzOOQO1G4s1G4sO'&yQ`O1G4sO''UQ`O1G4sO''dQ`O7++yO''dQ`O7++yO!&fQMhO1G4uO''lQ!dO1G4uO''vQ`O7++zO'(OQ`O7+(TO'(ZQ!dO7+(TOOQ!0Lb7+(R7+(ROOQ!0Lb7+(S7+(SO!B_QpO7+(ROCcQ`O7+(RO'(eQ`O7+(TO!&fQMhO7+(TO$)QQ`O7+(SO'(jQ`O7+(TOCcQ`O7+(SO'(rQMjO<<NsOOQ!0Lh7+$z7+$zO'(|Q!dO,5?bOOQO-E<t-E<tO')WQ!0MvO7+(WOOQ[AN=gAN=gO9^Q`O1G5SOOQO1G5S1G5SO')hQ`O1G5SO')mQ`O7+,SO')mQ`O7+,SO9hQ!0LrOANBTO@fQ`OANBTOOQ[ANBTANBTOOQ[ANApANApOOQ[ANAqANAqO')uQ`O,5?dOOQO-E<v-E<vO'*QQ?MtO1G6eOOQO,5?f,5?fOOQO-E<x-E<xOOQ[1G3^1G3^O'*[Q`O,5<}OOQ[<<Lc<<LcO!&fQMhO<<LcO&9yQ`O<<LcO'*aQ`O<<LcO%[QlO<<LcOOQ[<<Le<<LeO9hQ!0LrO<<LeO$#sQ`O<<LeO9RQ`O<<LeO'*iQpO1G5RO'*tQ`O7+,QOOQ[AN=]AN=]O9hQ!0LrOAN=]OOQ[<= o<= oOOQ[<= p<= pO'*|Q`O<= oO'+RQ`O<= pOOQ[<<Lm<<LmO'+WQ`O<<LmO'+]QlO<<LmOOQ[1G3w1G3wO>tQ`O7+)hO'+dQ`O<<I}O'+oQ?MtO<<I}OOQO<<Hv<<HvOOQ!0LfAN?cAN?cOOQOAN?[AN?[O$?aQ!0MxOAN?[OOQOAN>{AN>{O%[QlOAN?[OOQO<<Mu<<MuOOQ[G27SG27SO!&fQMhOG27SO#']Q`OG27SO'+yQ!fOG27SO4UQ!fOG27SO',QQ`OG27SO',YQ?MtO<<JeO',gQ?MvO1G2^O'.]Q?MvO,5?]O'0`Q?MvO,5?_O'2cQ?MvO1G2pO'4fQ?MvO1G2rO'6iQ?MtO<<KVO'6vQ?MtO<<IxOOQO1G1v1G1vO!+oQMhOANAfOOQO7+*_7+*_O'7TQ`O7+*_O'7`Q`O<= eO'7hQ!dO7+*aOOQ!0Lb<<Ko<<KoO$)QQ`O<<KoOCcQ`O<<KoO'7rQ`O<<KoO!&fQMhO<<KoOOQ!0Lb<<Km<<KmO!B_QpO<<KmO'7}Q!dO<<KoOOQ!0Lb<<Kn<<KnO'8XQ`O<<KoO!&fQMhO<<KoO$)QQ`O<<KnOOQO7+*n7+*nO9^Q`O7+*nO'8^Q`O<= nOOQ[G27oG27oO9hQ!0LrOG27oO!(vQlO1G5OO'8fQ`O7+,PO'8nQ`O1G2iO&9yQ`OANA}OOQ[ANA}ANA}O!&fQMhOANA}O'8sQ`OANA}OOQ[ANBPANBPO9hQ!0LrOANBPO$#sQ`OANBPOOQO'#H['#H[OOQO7+*m7+*mOOQ[G22wG22wOOQ[ANEZANEZOOQ[ANE[ANE[OOQ[ANBXANBXO'8{Q`OANBXOOQ[<<MS<<MSO!(vQlOAN?iOOQOG24vG24vO$?aQ!0MxOG24vO#']Q`OLD,nOOQ[LD,nLD,nO!&fQMhOLD,nO'9QQ!fOLD,nO'9XQ?MvO7+'xO':}Q?MvO,5?]O'=QQ?MvO,5?_O'?TQ?MvO7+'zO'@yQMjOG27QOOQO<<My<<MyOOQ!0LbANAZANAZO$)QQ`OANAZOCcQ`OANAZO'AZQ!dOANAZOOQ!0LbANAXANAXO'AbQ`OANAZO!&fQMhOANAZO'AmQ!dOANAZOOQ!0LbANAYANAYOOQO<<NY<<NYOOQ[LD-ZLD-ZO'AwQ?MtO7+*jOOQO'#Gd'#GdOOQ[G27iG27iO&9yQ`OG27iO!&fQMhOG27iOOQ[G27kG27kO9hQ!0LrOG27kOOQ[G27sG27sO'BRQ?MtOG25TOOQOLD*bLD*bOOQ[!$(!Y!$(!YO#']Q`O!$(!YO!&fQMhO!$(!YO'B]Q!0MzOG27QOOQ!0LbG26uG26uO$)QQ`OG26uO'DnQ`OG26uOCcQ`OG26uO'DyQ!dOG26uO!&fQMhOG26uOOQ[LD-TLD-TO&9yQ`OLD-TOOQ[LD-VLD-VOOQ[!)9Et!)9EtO#']Q`O!)9EtOOQ!0LbLD,aLD,aO$)QQ`OLD,aOCcQ`OLD,aO'EQQ`OLD,aO'E]Q!dOLD,aOOQ[!$(!o!$(!oOOQ[!.K;`!.K;`O'EdQ?MvOG27QOOQ!0Lb!$( {!$( {O$)QQ`O!$( {OCcQ`O!$( {O'GYQ`O!$( {OOQ!0Lb!)9Eg!)9EgO$)QQ`O!)9EgOCcQ`O!)9EgOOQ!0Lb!.K;R!.K;RO$)QQ`O!.K;ROOQ!0Lb!4/0m!4/0mO!(vQlO'#DwO1PQ`O'#EUO'GeQ!fO'#JnO'GlQ!L^O'#DsO'GsQlO'#D{O'GzQ!fO'#CiO'JbQ!fO'#CiO!(vQlO'#D}O'JrQlO,5;WO!(vQlO,5;bO!(vQlO,5;bO!(vQlO,5;bO!(vQlO,5;bO!(vQlO,5;bO!(vQlO,5;bO!(vQlO,5;bO!(vQlO,5;bO!(vQlO,5;bO!(vQlO,5;bO!(vQlO'#IlO'LuQ`O,5<gO!(vQlO,5;bO'L}QMhO,5;bO'NhQMhO,5;bO!(vQlO,5;vO!&fQMhO'#GjO'L}QMhO'#GjO!&fQMhO'#GlO'L}QMhO'#GlO1SQ`O'#DWO1SQ`O'#DWO!&fQMhO'#F}O'L}QMhO'#F}O!&fQMhO'#GPO'L}QMhO'#GPO!&fQMhO'#G_O'L}QMhO'#G_O!(vQlO,5:gO'NoQpO'#D[O'NyQpO'#JrO!(vQlO,5@kO'JrQlO1G0rO( TQ?MtO'#CiO!(vQlO1G2OO!&fQMhO'#IqO'L}QMhO'#IqO!&fQMhO'#IsO'L}QMhO'#IsO( _Q!dO'#CrO!&fQMhO,5<rO'L}QMhO,5<rO'JrQlO1G2PO!(vQlO7+&yO!&fQMhO1G2^O'L}QMhO1G2^O!&fQMhO'#IqO'L}QMhO'#IqO!&fQMhO'#IsO'L}QMhO'#IsO!&fQMhO1G2`O'L}QMhO1G2`O'JrQlO7+'kO'JrQlO7+&^O!&fQMhOANAfO'L}QMhOANAfO( rQ`O'#ElO( wQ`O'#ElO(!PQ`O'#F[O(!UQ`O'#EvO(!ZQ`O'#KPO(!fQ`O'#J}O(!qQ`O,5;WO(!vQMjO,5<dO(!}Q`O'#GWO(#SQ`O'#GWO(#XQ`O,5<eO(#aQ`O,5;WO(#iQ?MtO1G1_O(#pQ`O,5<rO(#uQ`O,5<rO(#zQ`O,5<tO($PQ`O,5<tO($UQ`O1G2PO($ZQ`O1G0rO($`QMjO<<KzO($gQMjO<<KzO7eQMhO'#FzO9RQ`O'#FyOAaQ`O'#EkO!(vQlO,5;sO!3ZQ`O'#GWO!3ZQ`O'#GWO!3ZQ`O'#GYO!3ZQ`O'#GYO!+oQMhO7+(`O!+oQMhO7+(`O%,oQ!dO1G2tO%,oQ!dO1G2tO!&fQMhO,5=YO!&fQMhO,5=Y",stateData:"(%k~O'xOS'yOSTOS'zRQ~OPYOQYOSfOY!VOaqOdzOeyOmkOoYOpkOqkOwkOyYO{YO!PWO!TkO!UkO![XO!fuO!iZO!lYO!mYO!nYO!pvO!rwO!uxO!y]O#t!PO$V|O%e}O%g!QO%i!OO%j!OO%k!OO%n!RO%p!SO%s!TO%t!TO%v!UO&S!WO&Y!XO&[!YO&^!ZO&`![O&c!]O&i!^O&o!_O&q!`O&s!aO&u!bO&w!cO(PSO(RTO(UUO(]VO(k[O(ziO~OWtO~P`OPYOQYOSfOd!jOe!iOmkOoYOpkOqkOwkOyYO{YO!PWO!TkO!UkO![!eO!fuO!iZO!lYO!mYO!nYO!pvO!r!gO!u!hO$V!kO(P!dO(RTO(UUO(]VO(k[O(ziO~Oa!wOp!nO!P!oO!_!yO!`!vO!a!vO!y;kO#Q!pO#R!pO#S!xO#T!pO#U!pO#X!zO#Y!zO(Q!lO(RTO(UUO(a!mO(k!sO~O'z!{O~OP]XR]X[]Xa]Xo]X}]X!P]X!Y]X!i]X!m]X#O]X#P]X#]]X#hfX#k]X#l]X#m]X#n]X#o]X#p]X#q]X#r]X#s]X#u]X#w]X#y]X#z]X$P]X'v]X(]]X(n]X(u]X(v]X~O!d%PX~P(qO_!}O(R#PO(S!}O(T#PO~O_#QO(T#PO(U#PO(V#QO~Ou#SO!R#TO(^#TO(_#VO~OPYOQYOSfOd!jOe!iOmkOoYOpkOqkOwkOyYO{YO!PWO!TkO!UkO![!eO!fuO!iZO!lYO!mYO!nYO!pvO!r!gO!u!hO$V!kO(P;oO(RTO(UUO(]VO(k[O(ziO~O!X#ZO!Y#WO!V(dP!V(rP~P+}O!Z#cO~P`OPYOQYOSfOd!jOe!iOoYOpkOqkOwkOyYO{YO!PWO!TkO!UkO![!eO!fuO!iZO!lYO!mYO!nYO!pvO!r!gO!u!hO$V!kO(RTO(UUO(]VO(k[O(ziO~Om#mO!X#iO!y]O#f#lO#g#iO(P;pO!h(oP~P.iO!i#oO(P#nO~O!u#sO!y]O%e#tO~O#h#uO~O!d#vO#h#uO~OP$[OR#zO[$cOo$aO}#yO!P#{O!Y$_O!i#xO!m$[O#O$RO#k$OO#l$PO#m$PO#n$PO#o$QO#p$RO#q$RO#r$bO#s$RO#u$SO#w$UO#y$WO#z$XO(]VO(n$YO(u#|O(v#}O~Oa(bX'v(bX's(bX!h(bX!V(bX![(bX%f(bX!d(bX~P1qO#P$dO#]$eO$P$eOP(cXR(cX[(cXo(cX}(cX!P(cX!Y(cX!i(cX!m(cX#O(cX#k(cX#l(cX#m(cX#n(cX#o(cX#p(cX#q(cX#r(cX#s(cX#u(cX#w(cX#y(cX#z(cX(](cX(n(cX(u(cX(v(cX![(cX%f(cX~Oa(cX'v(cX's(cX!V(cX!h(cXs(cX!d(cX~P4UO#]$eO~O$[$hO$^$gO$e$mO~OSfO![$nO$h$oO$j$qO~Oh%VOm%WOo%XOp$tOq$tOw%YOy%ZO{%[O!P${O![$|O!f%aO!i$xO#g%bO$V%_O$r%]O$t%^O$w%`O(P$sO(RTO(UUO(]$uO(u$}O(v%POg(YP~O!i%cO~O!P%fO![%gO(P%eO~O!d%kO~Oa%lO'v%lO~O}%pO~P%[O(Q!lO~P%[O%k%tO~P%[Oh%VO!i%cO(P%eO(Q!lO~Oe%{O!i%cO(P%eO~O#s$RO~O}&QO![%}O!i&PO%g&TO(P%eO(Q!lO(RTO(UUO`)TP~O!u#sO~O%p&VO!P)PX![)PX(P)PX~O(P&WO~O!r&]O#t!PO%g!QO%i!OO%j!OO%k!OO%n!RO%p!SO%s!TO%t!TO~Od&bOe&aO!u&_O%e&`O%x&^O~P;|Od&eOeyO![&dO!r&]O!uxO!y]O#t!PO%e}O%i!OO%j!OO%k!OO%n!RO%p!SO%s!TO%t!TO%v!UO~Ob&hO#]&kO%g&fO(Q!lO~P=RO!i&lO!r&pO~O!i#oO~O![XO~Oa%lO't&xO'v%lO~Oa%lO't&{O'v%lO~Oa%lO't&}O'v%lO~O's]X!V]Xs]X!h]X&W]X![]X%f]X!d]X~P(qO!_'[O!`'TO!a'TO(Q!lO(RTO(UUO~Op'RO!P'QO!X'UO(a'PO!Z(eP!Z(tP~P@YOk'_O![']O(P%eO~Oe'dO!i%cO(P%eO~O}&QO!i&PO~Op!nO!P!oO!y;kO#Q!pO#R!pO#T!pO#U!pO(Q!lO(RTO(UUO(a!mO(k!sO~O!_'jO!`'iO!a'iO#S!pO#X'kO#Y'kO~PAtOa%lOh%VO!d#vO!i%cO'v%lO(n'mO~O!m'qO#]'oO~PCSOp!nO!P!oO(RTO(UUO(a!mO(k!sO~O![XOp(iX!P(iX!_(iX!`(iX!a(iX!y(iX#Q(iX#R(iX#S(iX#T(iX#U(iX#X(iX#Y(iX(Q(iX(R(iX(U(iX(a(iX(k(iX~O!`'iO!a'iO(Q!lO~PCrO'{'uO'|'uO'}'wO~O_!}O(R'yO(S!}O(T'yO~O_#QO(T'yO(U'yO(V#QO~Os'{O~P%[Ou#SO!R#TO(^#TO(_(OO~O!X(QO!V'SX!V'YX!Y'SX!Y'YX~P+}O!Y(SO!V(dX~OP$[OR#zO[$cOo$aO}#yO!P#{O!Y(SO!i#xO!m$[O#O$RO#k$OO#l$PO#m$PO#n$PO#o$QO#p$RO#q$RO#r$bO#s$RO#u$SO#w$UO#y$WO#z$XO(]VO(n$YO(u#|O(v#}O~O!V(dX~PGmO!V(XO~O!V(qX!Y(qX!d(qX!h(qX(n(qX~O#](qX#h#aX!Z(qX~PIpO#](YO!V(sX!Y(sX~O!Y(ZO!V(rX~O!V(^O~O#]$eO~PIpO!Z(_O~P`OR#zO}#yO!P#{O!i#xO(]VOP!ka[!kao!ka!Y!ka!m!ka#O!ka#k!ka#l!ka#m!ka#n!ka#o!ka#p!ka#q!ka#r!ka#s!ka#u!ka#w!ka#y!ka#z!ka(n!ka(u!ka(v!ka~Oa!ka'v!ka's!ka!V!ka!h!kas!ka![!ka%f!ka!d!ka~PKWO!h(`O~O!d#vO#](aO(n'mO!Y(pXa(pX'v(pX~O!h(pX~PMsO!P%fO![%gO!y]O#f(fO#g(eO(P%eO~O!Y(gO!h(oX~O!h(iO~O!P%fO![%gO#g(eO(P%eO~OP(cXR(cX[(cXo(cX}(cX!P(cX!Y(cX!i(cX!m(cX#O(cX#k(cX#l(cX#m(cX#n(cX#o(cX#p(cX#q(cX#r(cX#s(cX#u(cX#w(cX#y(cX#z(cX(](cX(n(cX(u(cX(v(cX~O!d#vO!h(cX~P! aOR(kO}(jO!i#xO#P$dO!y!xa!P!xa~O!u!xa%e!xa![!xa#f!xa#g!xa(P!xa~P!#bO!u(oO~OPYOQYOSfOd!jOe!iOmkOoYOpkOqkOwkOyYO{YO!PWO!TkO!UkO![XO!fuO!iZO!lYO!mYO!nYO!pvO!r!gO!u!hO$V!kO(P!dO(RTO(UUO(]VO(k[O(ziO~Oh%VOm%WOo%XOp$tOq$tOw%YOy%ZO{<XO!P${O![$|O!f=jO!i$xO#g<_O$V%_O$r<ZO$t<]O$w%`O(P(sO(RTO(UUO(]$uO(u$}O(v%PO~O#h(uO~O!X(wO!h(gP~P%[O(a(yO(k[O~O!P({O!i#xO(a(yO(k[O~OP;jOQ;jOSfOd=fOe!iOmkOo;jOpkOqkOwkOy;jO{;jO!PWO!TkO!UkO![!eO!f;mO!iZO!l;jO!m;jO!n;jO!p;nO!r;qO!u!hO$V!kO(P)YO(RTO(UUO(]VO(k[O(z=dO~O!Y$_Oa$oa'v$oa's$oa!h$oa!V$oa![$oa%f$oa!d$oa~O#t)aO~P!&fOh%VOm%WOo%XOp$tOq$tOw%YOy%ZO{%[O!P${O![$|O!f%aO!i$xO#g%bO$V%_O$r%]O$t%^O$w%`O(P(sO(RTO(UUO(]$uO(u$}O(v%PO~Og(lP~P!+oO})fO!d)eO![$]X$Y$]X$[$]X$^$]X$e$]X~O!d)eO![(wX$Y(wX$[(wX$^(wX$e(wX~O})fO~P!-xO})fO![(wX$Y(wX$[(wX$^(wX$e(wX~O![)hO$Y)lO$[)gO$^)gO$e)mO~O!X)pO~P!(vO$[$hO$^$gO$e)tO~Ok$xX}$xX#P$xX'u$xX(u$xX(v$xX~OgjXg$xXkjX!YjX#]jX~P!/nOu)vO(^)wO(_)yO~Ok*SO}){O'u)|O(u$}O(v%PO~Og)zO~P!0rOg*TO~Oh%VOm%WOo%XOp$tOq$tOw%YOy%ZO{<XO!P*VO![*WO!f=jO!i$xO#g<_O$V%_O$r<ZO$t<]O$w%`O(RTO(UUO(]$uO(u$}O(v%PO~O!X*ZO(P*UO!h({P~P!1aO#h*]O~O!i*^O~Oh%VOm%WOo%XOp$tOq$tOw%YOy%ZO{<XO!P${O![$|O!f=jO!i$xO#g<_O$V%_O$r<ZO$t<]O$w%`O(P*`O(RTO(UUO(]$uO(u$}O(v%PO~O!X*cO!V(|P~P!3`Oo*oOp!nO!P*eO!_*mO!`*gO!a*gO!i*^O#X*nO%]*iO(Q!lO(RTO(UUO(a!mO~O!Z*lO~P!5TO#P$dOk([X}([X'u([X(u([X(v([X!Y([X#]([X~Og([X#}([X~P!6VOk*tO#]*sOg(ZX!Y(ZX~O!Y*uOg(YX~O(P&WOg(YP~Op*xO~O!i*}O~O(P(sO~Om+RO!P%fO!X#iO![%gO!y]O#f#lO#g#iO(P%eO!h(oP~O!d#vO#h+SO~O!P%fO!X+UO!Y(ZO![%gO(P%eO!V(rP~Op'XO!P+WO!X+VO(RTO(UUO(a(yO~O!Z(tP~P!9VO!Y+XOa)QX'v)QX~OP$[OR#zO[$cOo$aO}#yO!P#{O!i#xO!m$[O#O$RO#k$OO#l$PO#m$PO#n$PO#o$QO#p$RO#q$RO#r$bO#s$RO#u$SO#w$UO#y$WO#z$XO(]VO(n$YO(u#|O(v#}O~Oa!ga!Y!ga'v!ga's!ga!V!ga!h!gas!ga![!ga%f!ga!d!ga~P!9}OR#zO}#yO!P#{O!i#xO(]VOP!oa[!oao!oa!Y!oa!m!oa#O!oa#k!oa#l!oa#m!oa#n!oa#o!oa#p!oa#q!oa#r!oa#s!oa#u!oa#w!oa#y!oa#z!oa(n!oa(u!oa(v!oa~Oa!oa'v!oa's!oa!V!oa!h!oas!oa![!oa%f!oa!d!oa~P!<eOR#zO}#yO!P#{O!i#xO(]VOP!qa[!qao!qa!Y!qa!m!qa#O!qa#k!qa#l!qa#m!qa#n!qa#o!qa#p!qa#q!qa#r!qa#s!qa#u!qa#w!qa#y!qa#z!qa(n!qa(u!qa(v!qa~Oa!qa'v!qa's!qa!V!qa!h!qas!qa![!qa%f!qa!d!qa~P!>{Oh%VOk+bO![']O%f+aO~O!d+dOa(XX![(XX'v(XX!Y(XX~Oa%lO![XO'v%lO~Oh%VO!i%cO~Oh%VO!i%cO(P%eO~O!d#vO#h(uO~Ob+oO%g+pO(P+lO(RTO(UUO!Z)UP~O!Y+qO`)TX~O[+uO~O`+vO~O![%}O(P%eO(Q!lO`)TP~Oh%VO#]+{O~Oh%VOk,OO![$|O~O![,QO~O},SO![XO~O%k%tO~O!u,XO~Oe,^O~Ob,_O(P#nO(RTO(UUO!Z)SP~Oe%{O~O%g!QO(P&WO~P=RO[,dO`,cO~OPYOQYOSfOdzOeyOmkOoYOpkOqkOwkOyYO{YO!PWO!TkO!UkO!fuO!iZO!lYO!mYO!nYO!pvO!uxO!y]O%e}O(RTO(UUO(]VO(k[O(ziO~O![!eO!r!gO$V!kO(P!dO~P!E{O`,cOa%lO'v%lO~OPYOQYOSfOd!jOe!iOmkOoYOpkOqkOwkOyYO{YO!PWO!TkO!UkO![!eO!fuO!iZO!lYO!mYO!nYO!pvO!u!hO$V!kO(P!dO(RTO(UUO(]VO(k[O(ziO~Oa,iO!rwO#t!OO%i!OO%j!OO%k!OO~P!HeO!i&lO~O&Y,oO~O![,qO~O&k,sO&m,tOP&haQ&haS&haY&haa&had&hae&ham&hao&hap&haq&haw&hay&ha{&ha!P&ha!T&ha!U&ha![&ha!f&ha!i&ha!l&ha!m&ha!n&ha!p&ha!r&ha!u&ha!y&ha#t&ha$V&ha%e&ha%g&ha%i&ha%j&ha%k&ha%n&ha%p&ha%s&ha%t&ha%v&ha&S&ha&Y&ha&[&ha&^&ha&`&ha&c&ha&i&ha&o&ha&q&ha&s&ha&u&ha&w&ha's&ha(P&ha(R&ha(U&ha(]&ha(k&ha(z&ha!Z&ha&a&hab&ha&f&ha~O(P,yO~Oh!bX!Y!OX!Z!OX!d!OX!d!bX!i!bX#]!OX~O!Y!bX!Z!bX~P# kO!d-OO#],}Oh(fX!Y#eX!Z#eX!d(fX!i(fX~O!Y(fX!Z(fX~P#!^Oh%VO!d-QO!i%cO!Y!^X!Z!^X~Op!nO!P!oO(RTO(UUO(a!mO~OP;jOQ;jOSfOd=fOe!iOmkOo;jOpkOqkOwkOy;jO{;jO!PWO!TkO!UkO![!eO!f;mO!iZO!l;jO!m;jO!n;jO!p;nO!r;qO!u!hO$V!kO(RTO(UUO(]VO(k[O(z=dO~O(P<fO~P##sO!Y-UO!Z(eX~O!Z-WO~O!d-OO#],}O!Y#eX!Z#eX~O!Y-XO!Z(tX~O!Z-ZO~O!`-[O!a-[O(Q!lO~P##bO!Z-_O~P'_Ok-bO![']O~O!V-gO~Op!xa!_!xa!`!xa!a!xa#Q!xa#R!xa#S!xa#T!xa#U!xa#X!xa#Y!xa(Q!xa(R!xa(U!xa(a!xa(k!xa~P!#bO!m-lO#]-jO~PCSO!`-nO!a-nO(Q!lO~PCrOa%lO#]-jO'v%lO~Oa%lO!d#vO#]-jO'v%lO~Oa%lO!d#vO!m-lO#]-jO'v%lO(n'mO~O'{'uO'|'uO'}-sO~Os-tO~O!V'Sa!Y'Sa~P!9}O!X-xO!V'SX!Y'SX~P%[O!Y(SO!V(da~O!V(da~PGmO!Y(ZO!V(ra~O!P%fO!X-|O![%gO(P%eO!V'YX!Y'YX~O#].OO!Y(pa!h(paa(pa'v(pa~O!d#vO~P#+yO!Y(gO!h(oa~O!P%fO![%gO#g.SO(P%eO~Om.XO!P%fO!X.UO![%gO!y]O#f.WO#g.UO(P%eO!Y']X!h']X~OR.]O!i#xO~Oh%VOk.`O![']O%f._O~Oa#`i!Y#`i'v#`i's#`i!V#`i!h#`is#`i![#`i%f#`i!d#`i~P!9}Ok=pO}){O'u)|O(u$}O(v%PO~O#h#[aa#[a#]#[a'v#[a!Y#[a!h#[a![#[a!V#[a~P#.uO#h([XP([XR([X[([Xa([Xo([X!P([X!i([X!m([X#O([X#k([X#l([X#m([X#n([X#o([X#p([X#q([X#r([X#s([X#u([X#w([X#y([X#z([X'v([X(]([X(n([X!h([X!V([X's([Xs([X![([X%f([X!d([X~P!6VO!Y.mO!h(gX~P!9}O!h.pO~O!V.rO~OP$[OR#zO}#yO!P#{O!i#xO!m$[O(]VO[#jia#jio#ji!Y#ji#O#ji#l#ji#m#ji#n#ji#o#ji#p#ji#q#ji#r#ji#s#ji#u#ji#w#ji#y#ji#z#ji'v#ji(n#ji(u#ji(v#ji's#ji!V#ji!h#jis#ji![#ji%f#ji!d#ji~O#k#ji~P#2eO#k$OO~P#2eOP$[OR#zOo$aO}#yO!P#{O!i#xO!m$[O#k$OO#l$PO#m$PO#n$PO(]VO[#jia#ji!Y#ji#O#ji#p#ji#q#ji#r#ji#s#ji#u#ji#w#ji#y#ji#z#ji'v#ji(n#ji(u#ji(v#ji's#ji!V#ji!h#jis#ji![#ji%f#ji!d#ji~O#o#ji~P#5SO#o$QO~P#5SOP$[OR#zO[$cOo$aO}#yO!P#{O!i#xO!m$[O#O$RO#k$OO#l$PO#m$PO#n$PO#o$QO#p$RO#q$RO#r$bO#s$RO(]VOa#ji!Y#ji#w#ji#y#ji#z#ji'v#ji(n#ji(u#ji(v#ji's#ji!V#ji!h#jis#ji![#ji%f#ji!d#ji~O#u#ji~P#7qOP$[OR#zO[$cOo$aO}#yO!P#{O!i#xO!m$[O#O$RO#k$OO#l$PO#m$PO#n$PO#o$QO#p$RO#q$RO#r$bO#s$RO#u$SO(]VO(v#}Oa#ji!Y#ji#y#ji#z#ji'v#ji(n#ji(u#ji's#ji!V#ji!h#jis#ji![#ji%f#ji!d#ji~O#w$UO~P#:XO#w#ji~P#:XO#u$SO~P#7qOP$[OR#zO[$cOo$aO}#yO!P#{O!i#xO!m$[O#O$RO#k$OO#l$PO#m$PO#n$PO#o$QO#p$RO#q$RO#r$bO#s$RO#u$SO#w$UO(]VO(u#|O(v#}Oa#ji!Y#ji#z#ji'v#ji(n#ji's#ji!V#ji!h#jis#ji![#ji%f#ji!d#ji~O#y#ji~P#<}O#y$WO~P#<}OP]XR]X[]Xo]X}]X!P]X!i]X!m]X#O]X#P]X#]]X#hfX#k]X#l]X#m]X#n]X#o]X#p]X#q]X#r]X#s]X#u]X#w]X#y]X#z]X$P]X(]]X(n]X(u]X(v]X!Y]X!Z]X~O#}]X~P#?lOP$[OR#zO[<ROo<PO}#yO!P#{O!i#xO!m$[O#O;vO#k;sO#l;tO#m;tO#n;tO#o;uO#p;vO#q;vO#r<QO#s;vO#u;wO#w;yO#y;{O#z;|O(]VO(n$YO(u#|O(v#}O~O#}.tO~P#AyO#P$dO#]<SO$P<SO#}(cX!Z(cX~P! aOa'`a!Y'`a'v'`a's'`a!h'`a!V'`as'`a!['`a%f'`a!d'`a~P!9}O[#jia#jio#ji!Y#ji#O#ji#o#ji#p#ji#q#ji#r#ji#s#ji#u#ji#w#ji#y#ji#z#ji'v#ji(n#ji's#ji!V#ji!h#jis#ji![#ji%f#ji!d#ji~OP$[OR#zO}#yO!P#{O!i#xO!m$[O#k$OO#l$PO#m$PO#n$PO(]VO(u#ji(v#ji~P#D{Ok=pO}){O'u)|O(u$}O(v%POP#jiR#ji!P#ji!i#ji!m#ji#k#ji#l#ji#m#ji#n#ji(]#ji~P#D{O!Y.xOg(lX~P!0rOg.zO~Oa$Oi!Y$Oi'v$Oi's$Oi!V$Oi!h$Ois$Oi![$Oi%f$Oi!d$Oi~P!9}O$[.{O$^.{O~O$[.|O$^.|O~O!d)eO#].}O![$bX$Y$bX$[$bX$^$bX$e$bX~O!X/OO~O![)hO$Y/QO$[)gO$^)gO$e/RO~O!Y;}O!Z(bX~P#AyO!Z/SO~O!d)eO$e(wX~O$e/UO~Os/VO~P!&fOu)vO(^)wO(_/YO~O!P/]O~O(u$}Ok%^a}%^a'u%^a(v%^a!Y%^a#]%^a~Og%^a#}%^a~P#K}O(v%POk%`a}%`a'u%`a(u%`a!Y%`a#]%`a~Og%`a#}%`a~P#LpO!YfX!dfX!hfX!h$xX(nfX~P!/nO!X/fO!Y(ZO(P/eO!V(rP!V(|P~P!1aOo*oO!_*mO!`*gO!a*gO!i*^O#X*nO%]*iO(Q!lO(RTO(UUO~Op<cO!P/gO!X+VO!Z*lO(a<bO!Z(tP~P#NZO!h/hO~P#.uO!Y/iO!d#vO(n'mO!h({X~O!h/nO~O!P%fO!X*ZO![%gO(P%eO!h({P~O#h/pO~O!V$xX!Y$xX!d%PX~P!/nO!Y/qO!V(|X~P#.uO!d/sO~O!V/uO~OmkO(P/vO~P.iOh%VOo/{O!d#vO!i%cO(n'mO~O!d+dO~Oa%lO!Y0PO'v%lO~O!Z0RO~P!5TO!`0SO!a0SO(Q!lO~P##bOp!nO!P0TO(RTO(UUO(a!mO~O#X0VO~Og%^a!Y%^a#]%^a#}%^a~P!0rOg%`a!Y%`a#]%`a#}%`a~P!0rO(P&WOg'iX!Y'iX~O!Y*uOg(Ya~Og0`O~OR0aO}0aO!P0bO#P$dOkza'uza(uza(vza!Yza#]za~Ogza#}za~P$&ZO}){O'u)|Ok$qa(u$qa(v$qa!Y$qa#]$qa~Og$qa#}$qa~P$'VO}){O'u)|Ok$sa(u$sa(v$sa!Y$sa#]$sa~Og$sa#}$sa~P$'xO#h0eO~Og%Ra!Y%Ra#]%Ra#}%Ra~P!0rO!d#vO~O#h0hO~O!Y+XOa)Qa'v)Qa~OR#zO}#yO!P#{O!i#xO(]VOP!oi[!oio!oi!Y!oi!m!oi#O!oi#k!oi#l!oi#m!oi#n!oi#o!oi#p!oi#q!oi#r!oi#s!oi#u!oi#w!oi#y!oi#z!oi(n!oi(u!oi(v!oi~Oa!oi'v!oi's!oi!V!oi!h!ois!oi![!oi%f!oi!d!oi~P$)gOh%VOo%XOp$tOq$tOw%YOy%ZO{<XO!P${O![$|O!f=jO!i$xO#g<_O$V%_O$r<ZO$t<]O$w%`O(RTO(UUO(]$uO(u$}O(v%PO~Om0qO(P0pO~P$+}O!d+dOa(Xa![(Xa'v(Xa!Y(Xa~O#h0wO~O[]X!YfX!ZfX~O!Y0xO!Z)UX~O!Z0zO~O[0{O~Ob0}O(P+lO(RTO(UUO~O![%}O(P%eO`'qX!Y'qX~O!Y+qO`)Ta~O!h1QO~P!9}O[1TO~O`1UO~O#]1XO~Ok1[O![$|O~O(a(yO!Z)RP~Oh%VOk1eO![1bO%f1dO~O[1oO!Y1mO!Z)SX~O!Z1pO~O`1rOa%lO'v%lO~O(P#nO(RTO(UUO~O#P$dO#]$eO$P$eOP(cXR(cX[(cXo(cX}(cX!P(cX!Y(cX!i(cX!m(cX#O(cX#k(cX#l(cX#m(cX#n(cX#o(cX#p(cX#q(cX#r(cX#u(cX#w(cX#y(cX#z(cX(](cX(n(cX(u(cX(v(cX~O#s1uO&W1vOa(cX~P$1eO#]$eO#s1uO&W1vO~Oa1xO~P%[Oa1zO~O&a1}OP&_iQ&_iS&_iY&_ia&_id&_ie&_im&_io&_ip&_iq&_iw&_iy&_i{&_i!P&_i!T&_i!U&_i![&_i!f&_i!i&_i!l&_i!m&_i!n&_i!p&_i!r&_i!u&_i!y&_i#t&_i$V&_i%e&_i%g&_i%i&_i%j&_i%k&_i%n&_i%p&_i%s&_i%t&_i%v&_i&S&_i&Y&_i&[&_i&^&_i&`&_i&c&_i&i&_i&o&_i&q&_i&s&_i&u&_i&w&_i's&_i(P&_i(R&_i(U&_i(]&_i(k&_i(z&_i!Z&_ib&_i&f&_i~Ob2TO!Z2RO&f2SO~P`O![XO!i2VO~O&m,tOP&hiQ&hiS&hiY&hia&hid&hie&him&hio&hip&hiq&hiw&hiy&hi{&hi!P&hi!T&hi!U&hi![&hi!f&hi!i&hi!l&hi!m&hi!n&hi!p&hi!r&hi!u&hi!y&hi#t&hi$V&hi%e&hi%g&hi%i&hi%j&hi%k&hi%n&hi%p&hi%s&hi%t&hi%v&hi&S&hi&Y&hi&[&hi&^&hi&`&hi&c&hi&i&hi&o&hi&q&hi&s&hi&u&hi&w&hi's&hi(P&hi(R&hi(U&hi(]&hi(k&hi(z&hi!Z&hi&a&hib&hi&f&hi~O!V2]O~O!Y!^a!Z!^a~P#AyOp!nO!P!oO!X2cO(a!mO!Y'TX!Z'TX~P@YO!Y-UO!Z(ea~O!Y'ZX!Z'ZX~P!9VO!Y-XO!Z(ta~O!Z2jO~P'_Oa%lO#]2sO'v%lO~Oa%lO!d#vO#]2sO'v%lO~Oa%lO!d#vO!m2wO#]2sO'v%lO(n'mO~Oa%lO'v%lO~P!9}O!Y$_Os$oa~O!V'Si!Y'Si~P!9}O!Y(SO!V(di~O!Y(ZO!V(ri~O!V(si!Y(si~P!9}O!Y(pi!h(pia(pi'v(pi~P!9}O#]2yO!Y(pi!h(pia(pi'v(pi~O!Y(gO!h(oi~O!P%fO![%gO!y]O#f3OO#g2}O(P%eO~O!P%fO![%gO#g2}O(P%eO~Ok3VO![']O%f3UO~Oh%VOk3VO![']O%f3UO~O#h%^aP%^aR%^a[%^aa%^ao%^a!P%^a!i%^a!m%^a#O%^a#k%^a#l%^a#m%^a#n%^a#o%^a#p%^a#q%^a#r%^a#s%^a#u%^a#w%^a#y%^a#z%^a'v%^a(]%^a(n%^a!h%^a!V%^a's%^as%^a![%^a%f%^a!d%^a~P#K}O#h%`aP%`aR%`a[%`aa%`ao%`a!P%`a!i%`a!m%`a#O%`a#k%`a#l%`a#m%`a#n%`a#o%`a#p%`a#q%`a#r%`a#s%`a#u%`a#w%`a#y%`a#z%`a'v%`a(]%`a(n%`a!h%`a!V%`a's%`as%`a![%`a%f%`a!d%`a~P#LpO#h%^aP%^aR%^a[%^aa%^ao%^a!P%^a!Y%^a!i%^a!m%^a#O%^a#k%^a#l%^a#m%^a#n%^a#o%^a#p%^a#q%^a#r%^a#s%^a#u%^a#w%^a#y%^a#z%^a'v%^a(]%^a(n%^a!h%^a!V%^a's%^a#]%^as%^a![%^a%f%^a!d%^a~P#.uO#h%`aP%`aR%`a[%`aa%`ao%`a!P%`a!Y%`a!i%`a!m%`a#O%`a#k%`a#l%`a#m%`a#n%`a#o%`a#p%`a#q%`a#r%`a#s%`a#u%`a#w%`a#y%`a#z%`a'v%`a(]%`a(n%`a!h%`a!V%`a's%`a#]%`as%`a![%`a%f%`a!d%`a~P#.uO#hzaPza[zaazaoza!iza!mza#Oza#kza#lza#mza#nza#oza#pza#qza#rza#sza#uza#wza#yza#zza'vza(]za(nza!hza!Vza'szasza![za%fza!dza~P$&ZO#h$qaP$qaR$qa[$qaa$qao$qa!P$qa!i$qa!m$qa#O$qa#k$qa#l$qa#m$qa#n$qa#o$qa#p$qa#q$qa#r$qa#s$qa#u$qa#w$qa#y$qa#z$qa'v$qa(]$qa(n$qa!h$qa!V$qa's$qas$qa![$qa%f$qa!d$qa~P$'VO#h$saP$saR$sa[$saa$sao$sa!P$sa!i$sa!m$sa#O$sa#k$sa#l$sa#m$sa#n$sa#o$sa#p$sa#q$sa#r$sa#s$sa#u$sa#w$sa#y$sa#z$sa'v$sa(]$sa(n$sa!h$sa!V$sa's$sas$sa![$sa%f$sa!d$sa~P$'xO#h%RaP%RaR%Ra[%Raa%Rao%Ra!P%Ra!Y%Ra!i%Ra!m%Ra#O%Ra#k%Ra#l%Ra#m%Ra#n%Ra#o%Ra#p%Ra#q%Ra#r%Ra#s%Ra#u%Ra#w%Ra#y%Ra#z%Ra'v%Ra(]%Ra(n%Ra!h%Ra!V%Ra's%Ra#]%Ras%Ra![%Ra%f%Ra!d%Ra~P#.uOa#`q!Y#`q'v#`q's#`q!V#`q!h#`qs#`q![#`q%f#`q!d#`q~P!9}O!X3_O!Y'UX!h'UX~P%[O!Y.mO!h(ga~O!Y.mO!h(ga~P!9}O!V3bO~O#}!ka!Z!ka~PKWO#}!ga!Y!ga!Z!ga~P#AyO#}!oa!Z!oa~P!<eO#}!qa!Z!qa~P!>{Og'XX!Y'XX~P!+oO!Y.xOg(la~OSfO![3vO$c3wO~O!Z3{O~Os3|O~P#.uOa$lq!Y$lq'v$lq's$lq!V$lq!h$lqs$lq![$lq%f$lq!d$lq~P!9}O!V4OO~P!&fO!P4PO~O}){O'u)|O(v%POk'ea(u'ea!Y'ea#]'ea~Og'ea#}'ea~P%+ZO}){O'u)|Ok'ga(u'ga(v'ga!Y'ga#]'ga~Og'ga#}'ga~P%+|O(n$YO~P#.uO!VfX!V$xX!YfX!Y$xX!d%PX#]fX~P!/nO(P<lO~P!1aO!P%fO!X4SO![%gO(P%eO!Y'aX!h'aX~O!Y/iO!h({a~O!Y/iO!d#vO!h({a~O!Y/iO!d#vO(n'mO!h({a~Og$zi!Y$zi#]$zi#}$zi~P!0rO!X4[O!V'cX!Y'cX~P!3`O!Y/qO!V(|a~O!Y/qO!V(|a~P#.uOP]XR]X[]Xo]X}]X!P]X!V]X!Y]X!i]X!m]X#O]X#P]X#]]X#hfX#k]X#l]X#m]X#n]X#o]X#p]X#q]X#r]X#s]X#u]X#w]X#y]X#z]X$P]X(]]X(n]X(u]X(v]X~O!d%WX#s%WX~P%/mO!d#vO#s4aO~Oh%VO!d#vO!i%cO~Oh%VOo4fO!i%cO(n'mO~Oo4kO!d#vO(n'mO~Op!nO!P4lO(RTO(UUO(a!mO~O(u$}Ok%^i}%^i'u%^i(v%^i!Y%^i#]%^i~Og%^i#}%^i~P%3^O(v%POk%`i}%`i'u%`i(u%`i!Y%`i#]%`i~Og%`i#}%`i~P%4POg(Zi!Y(Zi~P!0rO#]4rOg(Zi!Y(Zi~P!0rO!h4uO~Oa$mq!Y$mq'v$mq's$mq!V$mq!h$mqs$mq![$mq%f$mq!d$mq~P!9}O!V4yO~O!Y4zO![(}X~P#.uOa$xX![$xX%Z]X'v$xX!Y$xX~P!/nO%Z4}OalXklX}lX![lX'ulX'vlX(ulX(vlX!YlX~O%Z4}O~Ob5TO%g5UO(P+lO(RTO(UUO!Y'pX!Z'pX~O!Y0xO!Z)Ua~O[5YO~O`5ZO~Oa%lO'v%lO~P#.uO!Y5cO#]5eO!Z)RX~O!Z5fO~Oo5lOp!nO!P*eO!_!yO!`!vO!a!vO!y;kO#Q!pO#R!pO#S!pO#T!pO#U!pO#X5kO#Y!zO(Q!lO(RTO(UUO(a!mO(k!sO~O!Z5jO~P%9SOk5qO![1bO%f5pO~Oh%VOk5qO![1bO%f5pO~Ob5xO(P#nO(RTO(UUO!Y'oX!Z'oX~O!Y1mO!Z)Sa~O(RTO(UUO(a5zO~O`6OO~O#s6RO&W6SO~PMsO!h6TO~P%[Oa6VO~Oa6VO~P%[Ob2TO!Z6[O&f2SO~P`O!d6^O~O!d6`Oh(fi!Y(fi!Z(fi!d(fi!i(fio(fi(n(fi~O!Y#ei!Z#ei~P#AyO#]6aO!Y#ei!Z#ei~O!Y!^i!Z!^i~P#AyOa%lO#]6jO'v%lO~Oa%lO!d#vO#]6jO'v%lO~O!Y(pq!h(pqa(pq'v(pq~P!9}O!Y(gO!h(oq~O!P%fO![%gO#g6qO(P%eO~O![']O%f6tO~Ok6xO![']O%f6tO~O#h'eaP'eaR'ea['eaa'eao'ea!P'ea!i'ea!m'ea#O'ea#k'ea#l'ea#m'ea#n'ea#o'ea#p'ea#q'ea#r'ea#s'ea#u'ea#w'ea#y'ea#z'ea'v'ea(]'ea(n'ea!h'ea!V'ea's'eas'ea!['ea%f'ea!d'ea~P%+ZO#h'gaP'gaR'ga['gaa'gao'ga!P'ga!i'ga!m'ga#O'ga#k'ga#l'ga#m'ga#n'ga#o'ga#p'ga#q'ga#r'ga#s'ga#u'ga#w'ga#y'ga#z'ga'v'ga(]'ga(n'ga!h'ga!V'ga's'gas'ga!['ga%f'ga!d'ga~P%+|O#h$ziP$ziR$zi[$zia$zio$zi!P$zi!Y$zi!i$zi!m$zi#O$zi#k$zi#l$zi#m$zi#n$zi#o$zi#p$zi#q$zi#r$zi#s$zi#u$zi#w$zi#y$zi#z$zi'v$zi(]$zi(n$zi!h$zi!V$zi's$zi#]$zis$zi![$zi%f$zi!d$zi~P#.uO#h%^iP%^iR%^i[%^ia%^io%^i!P%^i!i%^i!m%^i#O%^i#k%^i#l%^i#m%^i#n%^i#o%^i#p%^i#q%^i#r%^i#s%^i#u%^i#w%^i#y%^i#z%^i'v%^i(]%^i(n%^i!h%^i!V%^i's%^is%^i![%^i%f%^i!d%^i~P%3^O#h%`iP%`iR%`i[%`ia%`io%`i!P%`i!i%`i!m%`i#O%`i#k%`i#l%`i#m%`i#n%`i#o%`i#p%`i#q%`i#r%`i#s%`i#u%`i#w%`i#y%`i#z%`i'v%`i(]%`i(n%`i!h%`i!V%`i's%`is%`i![%`i%f%`i!d%`i~P%4PO!Y'Ua!h'Ua~P!9}O!Y.mO!h(gi~O#}#`i!Y#`i!Z#`i~P#AyOP$[OR#zO}#yO!P#{O!i#xO!m$[O(]VO[#jio#ji#O#ji#l#ji#m#ji#n#ji#o#ji#p#ji#q#ji#r#ji#s#ji#u#ji#w#ji#y#ji#z#ji#}#ji(n#ji(u#ji(v#ji!Y#ji!Z#ji~O#k#ji~P%LRO#k;sO~P%LROP$[OR#zOo<PO}#yO!P#{O!i#xO!m$[O#k;sO#l;tO#m;tO#n;tO(]VO[#ji#O#ji#p#ji#q#ji#r#ji#s#ji#u#ji#w#ji#y#ji#z#ji#}#ji(n#ji(u#ji(v#ji!Y#ji!Z#ji~O#o#ji~P%NZO#o;uO~P%NZOP$[OR#zO[<ROo<PO}#yO!P#{O!i#xO!m$[O#O;vO#k;sO#l;tO#m;tO#n;tO#o;uO#p;vO#q;vO#r<QO#s;vO(]VO#w#ji#y#ji#z#ji#}#ji(n#ji(u#ji(v#ji!Y#ji!Z#ji~O#u#ji~P&!cOP$[OR#zO[<ROo<PO}#yO!P#{O!i#xO!m$[O#O;vO#k;sO#l;tO#m;tO#n;tO#o;uO#p;vO#q;vO#r<QO#s;vO#u;wO(]VO(v#}O#y#ji#z#ji#}#ji(n#ji(u#ji!Y#ji!Z#ji~O#w;yO~P&$dO#w#ji~P&$dO#u;wO~P&!cOP$[OR#zO[<ROo<PO}#yO!P#{O!i#xO!m$[O#O;vO#k;sO#l;tO#m;tO#n;tO#o;uO#p;vO#q;vO#r<QO#s;vO#u;wO#w;yO(]VO(u#|O(v#}O#z#ji#}#ji(n#ji!Y#ji!Z#ji~O#y#ji~P&&sO#y;{O~P&&sOa#{y!Y#{y'v#{y's#{y!V#{y!h#{ys#{y![#{y%f#{y!d#{y~P!9}O[#jio#ji#O#ji#o#ji#p#ji#q#ji#r#ji#s#ji#u#ji#w#ji#y#ji#z#ji#}#ji(n#ji!Y#ji!Z#ji~OP$[OR#zO}#yO!P#{O!i#xO!m$[O#k;sO#l;tO#m;tO#n;tO(]VO(u#ji(v#ji~P&)oOk=qO}){O'u)|O(u$}O(v%POP#jiR#ji!P#ji!i#ji!m#ji#k#ji#l#ji#m#ji#n#ji(]#ji~P&)oO#P$dOP([XR([X[([Xk([Xo([X}([X!P([X!i([X!m([X#O([X#k([X#l([X#m([X#n([X#o([X#p([X#q([X#r([X#s([X#u([X#w([X#y([X#z([X#}([X'u([X(]([X(n([X(u([X(v([X!Y([X!Z([X~O#}$Oi!Y$Oi!Z$Oi~P#AyO#}!oi!Z!oi~P$)gOg'Xa!Y'Xa~P!0rO!Z7[O~O!Y'`a!Z'`a~P#AyO!V7]O~P#.uO!d#vO(n'mO!Y'aa!h'aa~O!Y/iO!h({i~O!Y/iO!d#vO!h({i~Og$zq!Y$zq#]$zq#}$zq~P!0rO!V'ca!Y'ca~P#.uO!d7dO~O!Y/qO!V(|i~P#.uO!Y/qO!V(|i~O!V7gO~Oh%VOo7lO!i%cO(n'mO~O!d#vO#s7nO~Oo7qO!d#vO(n'mO~O}){O'u)|O(v%POk'fa(u'fa!Y'fa#]'fa~Og'fa#}'fa~P&2pO}){O'u)|Ok'ha(u'ha(v'ha!Y'ha#]'ha~Og'ha#}'ha~P&3cO!V7sO~Og$|q!Y$|q#]$|q#}$|q~P!0rOa$my!Y$my'v$my's$my!V$my!h$mys$my![$my%f$my!d$my~P!9}O!d6`O~O!Y4zO![(}a~O![']OP$SaR$Sa[$Sao$Sa}$Sa!P$Sa!Y$Sa!i$Sa!m$Sa#O$Sa#k$Sa#l$Sa#m$Sa#n$Sa#o$Sa#p$Sa#q$Sa#r$Sa#s$Sa#u$Sa#w$Sa#y$Sa#z$Sa(]$Sa(n$Sa(u$Sa(v$Sa~O%f6tO~P&5lOa#`y!Y#`y'v#`y's#`y!V#`y!h#`ys#`y![#`y%f#`y!d#`y~P!9}O[7xO~Ob7zO(P+lO(RTO(UUO~O!Y0xO!Z)Ui~O`8OO~O(a(yO!Y'lX!Z'lX~O!Y5cO!Z)Ra~O!Z8XO~P%9SO(k!sO~P$$iO#X8YO~O![1bO~O![1bO%f8[O~Ok8_O![1bO%f8[O~O[8dO!Y'oa!Z'oa~O!Y1mO!Z)Si~O!h8hO~O!h8iO~O!h8lO~O!h8lO~P%[Oa8nO~O!d8oO~O!h8pO~O!Y(si!Z(si~P#AyOa%lO#]8xO'v%lO~O!Y(py!h(pya(py'v(py~P!9}O!Y(gO!h(oy~O%f8{O~P&5lO![']O%f8{O~O#h$zqP$zqR$zq[$zqa$zqo$zq!P$zq!Y$zq!i$zq!m$zq#O$zq#k$zq#l$zq#m$zq#n$zq#o$zq#p$zq#q$zq#r$zq#s$zq#u$zq#w$zq#y$zq#z$zq'v$zq(]$zq(n$zq!h$zq!V$zq's$zq#]$zqs$zq![$zq%f$zq!d$zq~P#.uO#h'faP'faR'fa['faa'fao'fa!P'fa!i'fa!m'fa#O'fa#k'fa#l'fa#m'fa#n'fa#o'fa#p'fa#q'fa#r'fa#s'fa#u'fa#w'fa#y'fa#z'fa'v'fa(]'fa(n'fa!h'fa!V'fa's'fas'fa!['fa%f'fa!d'fa~P&2pO#h'haP'haR'ha['haa'hao'ha!P'ha!i'ha!m'ha#O'ha#k'ha#l'ha#m'ha#n'ha#o'ha#p'ha#q'ha#r'ha#s'ha#u'ha#w'ha#y'ha#z'ha'v'ha(]'ha(n'ha!h'ha!V'ha's'has'ha!['ha%f'ha!d'ha~P&3cO#h$|qP$|qR$|q[$|qa$|qo$|q!P$|q!Y$|q!i$|q!m$|q#O$|q#k$|q#l$|q#m$|q#n$|q#o$|q#p$|q#q$|q#r$|q#s$|q#u$|q#w$|q#y$|q#z$|q'v$|q(]$|q(n$|q!h$|q!V$|q's$|q#]$|qs$|q![$|q%f$|q!d$|q~P#.uO!Y'Ui!h'Ui~P!9}O#}#`q!Y#`q!Z#`q~P#AyO(u$}OP%^aR%^a[%^ao%^a!P%^a!i%^a!m%^a#O%^a#k%^a#l%^a#m%^a#n%^a#o%^a#p%^a#q%^a#r%^a#s%^a#u%^a#w%^a#y%^a#z%^a#}%^a(]%^a(n%^a!Y%^a!Z%^a~Ok%^a}%^a'u%^a(v%^a~P&FnO(v%POP%`aR%`a[%`ao%`a!P%`a!i%`a!m%`a#O%`a#k%`a#l%`a#m%`a#n%`a#o%`a#p%`a#q%`a#r%`a#s%`a#u%`a#w%`a#y%`a#z%`a#}%`a(]%`a(n%`a!Y%`a!Z%`a~Ok%`a}%`a'u%`a(u%`a~P&HuOk=qO}){O'u)|O(v%PO~P&FnOk=qO}){O'u)|O(u$}O~P&HuOR0aO}0aO!P0bO#P$dOPza[zakzaoza!iza!mza#Oza#kza#lza#mza#nza#oza#pza#qza#rza#sza#uza#wza#yza#zza#}za'uza(]za(nza(uza(vza!Yza!Zza~O}){O'u)|OP$qaR$qa[$qak$qao$qa!P$qa!i$qa!m$qa#O$qa#k$qa#l$qa#m$qa#n$qa#o$qa#p$qa#q$qa#r$qa#s$qa#u$qa#w$qa#y$qa#z$qa#}$qa(]$qa(n$qa(u$qa(v$qa!Y$qa!Z$qa~O}){O'u)|OP$saR$sa[$sak$sao$sa!P$sa!i$sa!m$sa#O$sa#k$sa#l$sa#m$sa#n$sa#o$sa#p$sa#q$sa#r$sa#s$sa#u$sa#w$sa#y$sa#z$sa#}$sa(]$sa(n$sa(u$sa(v$sa!Y$sa!Z$sa~Ok=qO}){O'u)|O(u$}O(v%PO~OP%RaR%Ra[%Rao%Ra!P%Ra!i%Ra!m%Ra#O%Ra#k%Ra#l%Ra#m%Ra#n%Ra#o%Ra#p%Ra#q%Ra#r%Ra#s%Ra#u%Ra#w%Ra#y%Ra#z%Ra#}%Ra(]%Ra(n%Ra!Y%Ra!Z%Ra~P'#zO#}$lq!Y$lq!Z$lq~P#AyO#}$mq!Y$mq!Z$mq~P#AyO!Z9YO~O#}9ZO~P!0rO!d#vO!Y'ai!h'ai~O!d#vO(n'mO!Y'ai!h'ai~O!Y/iO!h({q~O!V'ci!Y'ci~P#.uO!Y/qO!V(|q~Oo9bO!d#vO(n'mO~O[9dO!V9cO~P#.uO!V9cO~O!d#vO#s9jO~Og(Zy!Y(Zy~P!0rO!Y'ja!['ja~P#.uOa%Yq![%Yq'v%Yq!Y%Yq~P#.uO[9mO~O!Y0xO!Z)Uq~O#]9qO!Y'la!Z'la~O!Y5cO!Z)Ri~P#AyO!P9sO~O![1bO%f9vO~O(RTO(UUO(a9{O~O!Y1mO!Z)Sq~O!h:OO~O!h:PO~O!h:QO~O!h:QO~P%[O#]:TO!Y#ey!Z#ey~O!Y#ey!Z#ey~P#AyO%f:YO~P&5lO![']O%f:YO~O#}#{y!Y#{y!Z#{y~P#AyOP$ziR$zi[$zio$zi!P$zi!i$zi!m$zi#O$zi#k$zi#l$zi#m$zi#n$zi#o$zi#p$zi#q$zi#r$zi#s$zi#u$zi#w$zi#y$zi#z$zi#}$zi(]$zi(n$zi!Y$zi!Z$zi~P'#zO}){O'u)|O(v%POP'eaR'ea['eak'eao'ea!P'ea!i'ea!m'ea#O'ea#k'ea#l'ea#m'ea#n'ea#o'ea#p'ea#q'ea#r'ea#s'ea#u'ea#w'ea#y'ea#z'ea#}'ea(]'ea(n'ea(u'ea!Y'ea!Z'ea~O}){O'u)|OP'gaR'ga['gak'gao'ga!P'ga!i'ga!m'ga#O'ga#k'ga#l'ga#m'ga#n'ga#o'ga#p'ga#q'ga#r'ga#s'ga#u'ga#w'ga#y'ga#z'ga#}'ga(]'ga(n'ga(u'ga(v'ga!Y'ga!Z'ga~O(u$}OP%^iR%^i[%^ik%^io%^i}%^i!P%^i!i%^i!m%^i#O%^i#k%^i#l%^i#m%^i#n%^i#o%^i#p%^i#q%^i#r%^i#s%^i#u%^i#w%^i#y%^i#z%^i#}%^i'u%^i(]%^i(n%^i(v%^i!Y%^i!Z%^i~O(v%POP%`iR%`i[%`ik%`io%`i}%`i!P%`i!i%`i!m%`i#O%`i#k%`i#l%`i#m%`i#n%`i#o%`i#p%`i#q%`i#r%`i#s%`i#u%`i#w%`i#y%`i#z%`i#}%`i'u%`i(]%`i(n%`i(u%`i!Y%`i!Z%`i~O#}$my!Y$my!Z$my~P#AyO#}#`y!Y#`y!Z#`y~P#AyO!d#vO!Y'aq!h'aq~O!Y/iO!h({y~O!V'cq!Y'cq~P#.uOo:dO!d#vO(n'mO~O[:hO!V:gO~P#.uO!V:gO~O!Y0xO!Z)Uy~O!Y5cO!Z)Rq~O(P:nO~O![1bO%f:qO~O!h:tO~O%f:yO~P&5lOP$zqR$zq[$zqo$zq!P$zq!i$zq!m$zq#O$zq#k$zq#l$zq#m$zq#n$zq#o$zq#p$zq#q$zq#r$zq#s$zq#u$zq#w$zq#y$zq#z$zq#}$zq(]$zq(n$zq!Y$zq!Z$zq~P'#zO}){O'u)|O(v%POP'faR'fa['fak'fao'fa!P'fa!i'fa!m'fa#O'fa#k'fa#l'fa#m'fa#n'fa#o'fa#p'fa#q'fa#r'fa#s'fa#u'fa#w'fa#y'fa#z'fa#}'fa(]'fa(n'fa(u'fa!Y'fa!Z'fa~O}){O'u)|OP'haR'ha['hak'hao'ha!P'ha!i'ha!m'ha#O'ha#k'ha#l'ha#m'ha#n'ha#o'ha#p'ha#q'ha#r'ha#s'ha#u'ha#w'ha#y'ha#z'ha#}'ha(]'ha(n'ha(u'ha(v'ha!Y'ha!Z'ha~OP$|qR$|q[$|qo$|q!P$|q!i$|q!m$|q#O$|q#k$|q#l$|q#m$|q#n$|q#o$|q#p$|q#q$|q#r$|q#s$|q#u$|q#w$|q#y$|q#z$|q#}$|q(]$|q(n$|q!Y$|q!Z$|q~P'#zOg%b!Z!Y%b!Z#]%b!Z#}%b!Z~P!0rO!V:}O~P#.uOo;OO!d#vO(n'mO~O[;QO!V:}O~P#.uO!Y'lq!Z'lq~P#AyO!Y#e!Z!Z#e!Z~P#AyO#h%b!ZP%b!ZR%b!Z[%b!Za%b!Zo%b!Z!P%b!Z!Y%b!Z!i%b!Z!m%b!Z#O%b!Z#k%b!Z#l%b!Z#m%b!Z#n%b!Z#o%b!Z#p%b!Z#q%b!Z#r%b!Z#s%b!Z#u%b!Z#w%b!Z#y%b!Z#z%b!Z'v%b!Z(]%b!Z(n%b!Z!h%b!Z!V%b!Z's%b!Z#]%b!Zs%b!Z![%b!Z%f%b!Z!d%b!Z~P#.uOo;YO!d#vO(n'mO~O!V;ZO~P#.uOo;bO!d#vO(n'mO~O!V;cO~P#.uOP%b!ZR%b!Z[%b!Zo%b!Z!P%b!Z!i%b!Z!m%b!Z#O%b!Z#k%b!Z#l%b!Z#m%b!Z#n%b!Z#o%b!Z#p%b!Z#q%b!Z#r%b!Z#s%b!Z#u%b!Z#w%b!Z#y%b!Z#z%b!Z#}%b!Z(]%b!Z(n%b!Z!Y%b!Z!Z%b!Z~P'#zOo;fO!d#vO(n'mO~Os(bX~P1qO}%pO~P!(vO(Q!lO~P!(vO!VfX!YfX#]fX~P%/mOP]XR]X[]Xo]X}]X!P]X!Y]X!YfX!i]X!m]X#O]X#P]X#]]X#]fX#hfX#k]X#l]X#m]X#n]X#o]X#p]X#q]X#r]X#s]X#u]X#w]X#y]X#z]X$P]X(]]X(n]X(u]X(v]X~O!dfX!h]X!hfX(nfX~P'HXOP;jOQ;jOSfOd=fOe!iOmkOo;jOpkOqkOwkOy;jO{;jO!PWO!TkO!UkO![XO!f;mO!iZO!l;jO!m;jO!n;jO!p;nO!r;qO!u!hO$V!kO(P)YO(RTO(UUO(]VO(k[O(z=dO~O!Y;}O!Z$oa~Oh%VOm%WOo%XOp$tOq$tOw%YOy%ZO{<YO!P${O![$|O!f=kO!i$xO#g<`O$V%_O$r<[O$t<^O$w%`O(P(sO(RTO(UUO(]$uO(u$}O(v%PO~O#t)aO~P'L}Oo!bX(n!bX~P# kOo(fX(n(fX~P#!^O!Z]X!ZfX~P'HXO!VfX!V$xX!YfX!Y$xX#]fX~P!/nO#h;rO~O!d#vO#h;rO~O#]<SO~O#s;vO~O#]<dO!Y(sX!Z(sX~O#]<SO!Y(qX!Z(qX~O#h<eO~Og<gO~P!0rO#h<mO~O#h<nO~O!d#vO#h<oO~O!d#vO#h<eO~O#}<pO~P#AyO#h<qO~O#h<rO~O#h<wO~O#h<xO~O#h<yO~O#h<zO~O#}<{O~P!0rO#}<|O~P!0rO#P#Q#R#T#U#X#f#g#r(z$r$t$w%Z%e%f%g%n%p%s%t%v%x~'zT#l!U'x(Q#mp#k#no}'y$['y(P$^(a~",goto:"$6w)YPPPPPP)ZPP)^P)oP+P/RPPPP6XPP6oPP<g@VP@jP@jPPP@jPBnP@jP@jP@jPBrPBwPCfPH`PPPHdPPPPHdKgPPPKmL_PHdPHdPPNmHdPPPHdPHdP!!tHdP!&[!'a!'jP!(^!(b!(^!+oPPPPPPP!,`!'aPP!,p!.bP!1nHdHd!1s!5P!9m!9m!=lPPP!=tHdPPPPPPPPPPP!ATP!BbPPHd!CsPHdPHdHdHdHdHdPHd!EVP!HaP!KgP!Kk!Ku!Ky!KyP!H^P!K}!K}P# TP# XHdHd# _#$dBr@jP@jP@j@jP#%q@j@j#(P@j#*s@j#,{@j@j#-k#/{#/{#0Q#0Z#/{#0fP#/{P@j#1O@j#4u@j@j6XPPP#8rPPP#9]#9]P#9]P#9s#9]PP#9yP#9pP#9p#:^#9p#:x#;O#;R)^#;U)^P#;]#;]#;]P)^P)^P)^P)^PP)^P#;c#;fP#;f)^P#;jP#;mP)^P)^P)^P)^P)^P)^)^PP#;s#;y#<U#<[#<b#<h#<n#<|#=S#=^#=d#=n#=t#>U#>[#>|#?`#?f#?l#?z#@a#BQ#B`#Bg#C}#D]#Ey#FX#F_#Fe#Fk#Fu#F{#GR#G]#Go#GuPPPPPPPPPPP#G{PPPPPPP#Hp#Kw#Ma#Mh#MpPPP$%OP$%X$(Q$.k$.n$.q$/p$/s$/z$0SP$0Y$0]P$0y$0}$1u$3T$3Y$3pPP$3u$3{$4PP$4S$4W$4[$5W$5o$6W$6[$6_$6b$6h$6k$6o$6sR!|RoqOXst!Z#d%k&o&q&r&t,l,q1}2QY!vQ']-^1b5iQ%rvQ%zyQ&R|Q&g!VS'T!e-UQ'c!iS'i!r!yU*g$|*W*kQ+j%{Q+w&TQ,]&aQ-['[Q-f'dQ-n'jQ0S*mQ1l,^R<a;n%SdOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%k%r&P&h&k&o&q&r&t&x'Q'_'o(Q(S(Y(a(u(w({)z*e+S+W,i,l,q-b-j-x.O.m.t/g0T0b0h0w1e1u1v1x1z1}2Q2S2s2y3_4l5q6R6S6V6j8_8n8xS#q];k!r)[$Z$n'U)p,}-Q/O2c3v5e6a9q:T;j;m;n;q;r;s;t;u;v;w;x;y;z;{;|;}<P<S<a<d<e<g<o<p<y<z=gU*z%[<X<YQ+o%}Q,_&dQ,f&lQ0n+bQ0r+dQ0}+pQ1t,dQ3R.`Q5T0xQ5x1mQ6v3VQ7z5UR9O6x'QkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%k%r&P&h&k&l&o&q&r&t&x'Q'U'_'o(Q(S(Y(a(u(w({)p)z*e+S+W+b,i,l,q,}-Q-b-j-x.O.`.m.t/O/g0T0b0h0w1e1u1v1x1z1}2Q2S2c2s2y3V3_3v4l5e5q6R6S6V6a6j6x8_8n8x9q:T;j;m;n;q;r;s;t;u;v;w;x;y;z;{;|;}<P<S<a<d<e<g<o<p<y<z=g!S!nQ!r!v!y!z$|'T'[']'i'j'k*g*k*m*n-U-[-^-n0S0V1b5i5k%S$ti#v$b$c$d$x${%O%Q%]%^%b)v*O*Q*S*V*]*c*s*t+a+d+{,O._.x/]/f/p/q/s0W0Y0e1X1[1d3U4P4Q4[4a4r4z4}5p6t7d7n8[8{9Z9d9j9v:Y:h:q:y;Q<Q<R<T<U<V<W<Z<[<]<^<_<`<h<i<j<k<m<n<q<r<s<t<u<v<w<x<{<|=d=l=m=p=qQ&U|Q'R!eS'X%g-XQ+o%}Q,_&dQ0d*}Q0}+pQ1S+vQ1s,cQ1t,dQ5T0xQ5^1UQ5x1mQ5{1oQ5|1rQ7z5UQ7}5ZQ8g6OQ9p8OQ9|8dR<c*WrnOXst!V!Z#d%k&f&o&q&r&t,l,q1}2QR,a&h&z^OPXYstuvwz!Z!`!g!j!o#S#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%k%r&P&h&k&l&o&q&r&t&x'Q'_'o(S(Y(a(u(w({)p)z*e+S+W+b,i,l,q,}-Q-b-j-x.O.`.m.t/O/g0T0b0h0w1e1u1v1x1z1}2Q2S2c2s2y3V3_3v4l5e5q6R6S6V6a6j6x8_8n8x9q:T;j;m;n;q;r;s;t;u;v;w;x;y;z;{;|;}<P<S<a<d<e<g<o<p<y<z=f=g[#]WZ#W#Z'U(Q!b%hm#h#i#l$x%c%f(Z(e(f(g*V*Z*^+U+V+X,h-O-|.S.T.U.W/f/i2V2}3O4S6`6qQ%uxQ%yyS&O|&TQ&[!TQ'`!hQ'b!iQ(n#sS+i%z%{Q+m%}Q,W&_Q,[&aS-e'c'dQ.b(oQ0v+jQ0|+pQ1O+qQ1R+uQ1g,XS1k,],^Q2o-fQ5S0xQ5W0{Q5]1TQ5w1lQ7y5UQ7|5YQ9l7xR:k9m!O$zi$d%O%Q%]%^%b*O*Q*]*s*t.x/p0W0Y0e4Q4r9Z=d=l=m!^%wy!i!u%y%z%{'S'b'c'd'h'r*f+i+j-R-e-f-m/y/|0v2h2o2v4d4e4h7k9fQ+c%uQ+|&XQ,P&YQ,Z&aQ.a(nQ1f,WU1j,[,],^Q3W.bQ5r1gS5v1k1lQ8c5w#d=h#v$b$c$x${)v*S*V*c+a+d+{,O._/]/f/q/s1X1[1d3U4P4[4a4z4}5p6t7d7n8[8{9d9j9v:Y:h:q:y;Q<T<V<Z<]<_<h<j<m<q<s<u<w<{=p=qo=i<Q<R<U<W<[<^<`<i<k<n<r<t<v<x<|W%Ti%V*u=dS&X!Q&fQ&Y!RQ&Z!SR+z&V%T%Si#v$b$c$d$x${%O%Q%]%^%b)v*O*Q*S*V*]*c*s*t+a+d+{,O._.x/]/f/p/q/s0W0Y0e1X1[1d3U4P4Q4[4a4r4z4}5p6t7d7n8[8{9Z9d9j9v:Y:h:q:y;Q<Q<R<T<U<V<W<Z<[<]<^<_<`<h<i<j<k<m<n<q<r<s<t<u<v<w<x<{<|=d=l=m=p=qT)w$u)xV*z%[<X<YW'X!e%g*W-XS(z#y#zQ+^%pQ+t&QS.Z(j(kQ1],QQ4s0aR8S5c'QkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%k%r&P&h&k&l&o&q&r&t&x'Q'U'_'o(Q(S(Y(a(u(w({)p)z*e+S+W+b,i,l,q,}-Q-b-j-x.O.`.m.t/O/g0T0b0h0w1e1u1v1x1z1}2Q2S2c2s2y3V3_3v4l5e5q6R6S6V6a6j6x8_8n8x9q:T;j;m;n;q;r;s;t;u;v;w;x;y;z;{;|;}<P<S<a<d<e<g<o<p<y<z=g$i$^c#Y#e%o%q%s(P(V(q(v)O)P)Q)R)S)T)U)V)W)X)Z)])_)d)n+_+s-S-q-v-{-}.l.o.s.u.v.w/Z0f2^2a2q2x3^3c3d3e3f3g3h3i3j3k3l3m3n3o3r3s3z4w5Q6c6i6n6}7O7X7Y8U8r8v9Q9W9X:V:m:u;l=ZT#TV#U'RkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%k%r&P&h&k&l&o&q&r&t&x'Q'U'_'o(Q(S(Y(a(u(w({)p)z*e+S+W+b,i,l,q,}-Q-b-j-x.O.`.m.t/O/g0T0b0h0w1e1u1v1x1z1}2Q2S2c2s2y3V3_3v4l5e5q6R6S6V6a6j6x8_8n8x9q:T;j;m;n;q;r;s;t;u;v;w;x;y;z;{;|;}<P<S<a<d<e<g<o<p<y<z=gQ'V!eR2d-U!W!nQ!e!r!v!y!z$|'T'[']'i'j'k*W*g*k*m*n-U-[-^-n0S0V1b5i5kR1_,SnqOXst!Z#d%k&o&q&r&t,l,q1}2QQ&v!^Q's!xS(p#u;rQ+g%xQ,U&[Q,V&^Q-c'aQ-p'lS.k(u<eS0g+S<oQ0t+hQ1a,TQ2U,sQ2W,tQ2`-PQ2m-dQ2p-hS4x0h<yQ5O0uS5R0w<zQ6b2bQ6f2nQ6k2uQ7w5PQ8s6dQ8t6gQ8w6lR:S8p$d$]c#Y#e%q%s(P(V(q(v)O)P)Q)R)S)T)U)V)W)X)Z)])_)d)n+_+s-S-q-v-{-}.l.o.s.v.w/Z0f2^2a2q2x3^3c3d3e3f3g3h3i3j3k3l3m3n3o3r3s3z4w5Q6c6i6n6}7O7X7Y8U8r8v9Q9W9X:V:m:u;l=ZS(l#p'fQ(|#zS+]%o.uS.[(k(mR3P.]'QkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%k%r&P&h&k&l&o&q&r&t&x'Q'U'_'o(Q(S(Y(a(u(w({)p)z*e+S+W+b,i,l,q,}-Q-b-j-x.O.`.m.t/O/g0T0b0h0w1e1u1v1x1z1}2Q2S2c2s2y3V3_3v4l5e5q6R6S6V6a6j6x8_8n8x9q:T;j;m;n;q;r;s;t;u;v;w;x;y;z;{;|;}<P<S<a<d<e<g<o<p<y<z=gS#q];kQ&q!XQ&r!YQ&t![Q&u!]R1|,oQ'^!hQ+`%uQ-a'`S.^(n+cQ2k-`W3T.a.b0m0oQ6e2lW6r3Q3S3W4|U8z6s6u6wU:X8|8}9PS:w:W:ZQ;U:xR;^;VU!wQ']-^T5g1b5i!Q_OXZ`st!V!Z#d#h%c%k&f&h&o&q&r&t(g,l,q.T1}2Q]!pQ!r']-^1b5iT#q];k%^{OPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%k%r&P&h&k&l&o&q&r&t&x'Q'_'o(Q(S(Y(a(u(w({)z*e+S+W+b,i,l,q-b-j-x.O.`.m.t/g0T0b0h0w1e1u1v1x1z1}2Q2S2s2y3V3_4l5q6R6S6V6j6x8_8n8xS(z#y#zS.Z(j(k!s=Q$Z$n'U)p,}-Q/O2c3v5e6a9q:T;j;m;n;q;r;s;t;u;v;w;x;y;z;{;|;}<P<S<a<d<e<g<o<p<y<z=gU$fd)[,fS(m#p'fU*r%R(t3qU0c*y.g7TQ4|0nQ6s3RQ8}6vR:Z9Om!tQ!r!v!y!z']'i'j'k-^-n1b5i5kQ'q!uS(c#g1wS-l'h'tQ/l*YQ/y*fQ2w-oQ4W/mQ4d/{Q4e/|Q4j0UQ7`4RS7k4f4hS7o4k4mQ9]7aQ9a7gQ9f7lQ9k7qS:c9b9cS:|:d:gS;X:};OS;a;Y;ZS;e;b;cR;h;fQ#wbQ'p!uS(b#g1wS(d#m+RQ+T%dQ+e%vQ+k%|U-k'h'q'tQ.P(cQ/k*YQ/z*fQ/}*hQ0s+fQ1h,YS2t-l-oQ2|.XS4V/l/mQ4`/wS4c/y0UQ4g0OQ5t1iQ6m2wQ7_4RQ7c4WU7j4d4j4mQ7m4iQ8a5uS9[7`7aQ9`7gQ9h7oQ9i7pQ9y8bQ:a9]S:b9a9cQ:j9kQ:s9zS:{:c:gS;W:|:}S;`;X;ZS;d;a;cQ;g;eQ;i;hQ=T=OQ=`=XR=a=YV!wQ']-^%^aOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%k%r&P&h&k&l&o&q&r&t&x'Q'_'o(Q(S(Y(a(u(w({)z*e+S+W+b,i,l,q-b-j-x.O.`.m.t/g0T0b0h0w1e1u1v1x1z1}2Q2S2s2y3V3_4l5q6R6S6V6j6x8_8n8xS#wz!j!r<}$Z$n'U)p,}-Q/O2c3v5e6a9q:T;j;m;n;q;r;s;t;u;v;w;x;y;z;{;|;}<P<S<a<d<e<g<o<p<y<z=gR=T=f%^bOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%k%r&P&h&k&l&o&q&r&t&x'Q'_'o(Q(S(Y(a(u(w({)z*e+S+W+b,i,l,q-b-j-x.O.`.m.t/g0T0b0h0w1e1u1v1x1z1}2Q2S2s2y3V3_4l5q6R6S6V6j6x8_8n8xQ%dj!^%vy!i!u%y%z%{'S'b'c'd'h'r*f+i+j-R-e-f-m/y/|0v2h2o2v4d4e4h7k9fS%|z!jQ+f%wQ,Y&aW1i,Z,[,],^U5u1j1k1lS8b5v5wQ9z8c!r=O$Z$n'U)p,}-Q/O2c3v5e6a9q:T;j;m;n;q;r;s;t;u;v;w;x;y;z;{;|;}<P<S<a<d<e<g<o<p<y<z=gQ=X=eR=Y=f%QeOPXYstuvw!Z!`!g!o#S#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%k%r&P&h&k&o&q&r&t&x'Q'_'o(S(Y(a(u(w({)z*e+S+W+b,i,l,q-b-j-x.O.`.m.t/g0T0b0h0w1e1u1v1x1z1}2Q2S2s2y3V3_4l5q6R6S6V6j6x8_8n8xY#bWZ#W#Z(Q!b%hm#h#i#l$x%c%f(Z(e(f(g*V*Z*^+U+V+X,h-O-|.S.T.U.W/f/i2V2}3O4S6`6qQ,g&l!p=P$Z$n)p,}-Q/O2c3v5e6a9q:T;j;m;n;q;r;s;t;u;v;w;x;y;z;{;|;}<P<S<a<d<e<g<o<p<y<z=gR=S'UU'Y!e%g*WR2f-X%SdOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%k%r&P&h&k&o&q&r&t&x'Q'_'o(Q(S(Y(a(u(w({)z*e+S+W,i,l,q-b-j-x.O.m.t/g0T0b0h0w1e1u1v1x1z1}2Q2S2s2y3_4l5q6R6S6V6j8_8n8x!r)[$Z$n'U)p,}-Q/O2c3v5e6a9q:T;j;m;n;q;r;s;t;u;v;w;x;y;z;{;|;}<P<S<a<d<e<g<o<p<y<z=gQ,f&lQ0n+bQ3R.`Q6v3VR9O6x!b$Tc#Y%o(P(V(q(v)W)X)])d+s-q-v-{-}.l.o/Z0f2q2x3^3n4w5Q6i6n6}8v:V;l!P;x)Z)n-S.u2^2a3c3l3m3r3z6c7O7X7Y8U8r9Q9W9X:m:u=Z!f$Vc#Y%o(P(V(q(v)T)U)W)X)])d+s-q-v-{-}.l.o/Z0f2q2x3^3n4w5Q6i6n6}8v:V;l!T;z)Z)n-S.u2^2a3c3i3j3l3m3r3z6c7O7X7Y8U8r9Q9W9X:m:u=Z!^$Zc#Y%o(P(V(q(v)])d+s-q-v-{-}.l.o/Z0f2q2x3^3n4w5Q6i6n6}8v:V;lQ4Q/dz=g)Z)n-S.u2^2a3c3r3z6c7O7X7Y8U8r9Q9W9X:m:u=ZQ=l=nR=m=o'QkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%k%r&P&h&k&l&o&q&r&t&x'Q'U'_'o(Q(S(Y(a(u(w({)p)z*e+S+W+b,i,l,q,}-Q-b-j-x.O.`.m.t/O/g0T0b0h0w1e1u1v1x1z1}2Q2S2c2s2y3V3_3v4l5e5q6R6S6V6a6j6x8_8n8x9q:T;j;m;n;q;r;s;t;u;v;w;x;y;z;{;|;}<P<S<a<d<e<g<o<p<y<z=gS$oh$pR3w.}'XgOPWXYZhstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n$p%k%r&P&h&k&l&o&q&r&t&x'Q'U'_'o(Q(S(Y(a(u(w({)p)z*e+S+W+b,i,l,q,}-Q-b-j-x.O.`.m.t.}/O/g0T0b0h0w1e1u1v1x1z1}2Q2S2c2s2y3V3_3v4l5e5q6R6S6V6a6j6x8_8n8x9q:T;j;m;n;q;r;s;t;u;v;w;x;y;z;{;|;}<P<S<a<d<e<g<o<p<y<z=gT$kf$qQ$ifS)g$l)kR)s$qT$jf$qT)i$l)k'XhOPWXYZhstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n$p%k%r&P&h&k&l&o&q&r&t&x'Q'U'_'o(Q(S(Y(a(u(w({)p)z*e+S+W+b,i,l,q,}-Q-b-j-x.O.`.m.t.}/O/g0T0b0h0w1e1u1v1x1z1}2Q2S2c2s2y3V3_3v4l5e5q6R6S6V6a6j6x8_8n8x9q:T;j;m;n;q;r;s;t;u;v;w;x;y;z;{;|;}<P<S<a<d<e<g<o<p<y<z=gT$oh$pQ$rhR)r$p%^jOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%k%r&P&h&k&l&o&q&r&t&x'Q'_'o(Q(S(Y(a(u(w({)z*e+S+W+b,i,l,q-b-j-x.O.`.m.t/g0T0b0h0w1e1u1v1x1z1}2Q2S2s2y3V3_4l5q6R6S6V6j6x8_8n8x!s=e$Z$n'U)p,}-Q/O2c3v5e6a9q:T;j;m;n;q;r;s;t;u;v;w;x;y;z;{;|;}<P<S<a<d<e<g<o<p<y<z=g#glOPXZst!Z!`!o#S#d#o#{$n%k&h&k&l&o&q&r&t&x'Q'_({)p*e+W+b,i,l,q-b.`/O/g0T0b1e1u1v1x1z1}2Q2S3V3v4l5q6R6S6V6x8_8n!O%Ri$d%O%Q%]%^%b*O*Q*]*s*t.x/p0W0Y0e4Q4r9Z=d=l=m#d(t#v$b$c$x${)v*S*V*c+a+d+{,O._/]/f/q/s1X1[1d3U4P4[4a4z4}5p6t7d7n8[8{9d9j9v:Y:h:q:y;Q<T<V<Z<]<_<h<j<m<q<s<u<w<{=p=qQ+O%`Q/[){o3q<Q<R<U<W<[<^<`<i<k<n<r<t<v<x<|!O$yi$d%O%Q%]%^%b*O*Q*]*s*t.x/p0W0Y0e4Q4r9Z=d=l=mQ*_$zU*h$|*W*kQ+P%aQ0O*i#d=V#v$b$c$x${)v*S*V*c+a+d+{,O._/]/f/q/s1X1[1d3U4P4[4a4z4}5p6t7d7n8[8{9d9j9v:Y:h:q:y;Q<T<V<Z<]<_<h<j<m<q<s<u<w<{=p=qn=W<Q<R<U<W<[<^<`<i<k<n<r<t<v<x<|Q=[=hQ=]=iQ=^=jR=_=k!O%Ri$d%O%Q%]%^%b*O*Q*]*s*t.x/p0W0Y0e4Q4r9Z=d=l=m#d(t#v$b$c$x${)v*S*V*c+a+d+{,O._/]/f/q/s1X1[1d3U4P4[4a4z4}5p6t7d7n8[8{9d9j9v:Y:h:q:y;Q<T<V<Z<]<_<h<j<m<q<s<u<w<{=p=qo3q<Q<R<U<W<[<^<`<i<k<n<r<t<v<x<|noOXst!Z#d%k&o&q&r&t,l,q1}2QS*b${*VQ,z&{Q,{&}R4Z/q%S%Si#v$b$c$d$x${%O%Q%]%^%b)v*O*Q*S*V*]*c*s*t+a+d+{,O._.x/]/f/p/q/s0W0Y0e1X1[1d3U4P4Q4[4a4r4z4}5p6t7d7n8[8{9Z9d9j9v:Y:h:q:y;Q<Q<R<T<U<V<W<Z<[<]<^<_<`<h<i<j<k<m<n<q<r<s<t<u<v<w<x<{<|=d=l=m=p=qQ+}&YQ1Z,PQ5a1YR8R5bV*j$|*W*kU*j$|*W*kT5h1b5iS/w*e/gQ4i0TT7p4l9sQ+e%vQ/}*hQ0s+fQ1h,YQ5t1iQ8a5uQ9y8bR:s9z!O%Oi$d%O%Q%]%^%b*O*Q*]*s*t.x/p0W0Y0e4Q4r9Z=d=l=mr*O$v)b*P*q+Q/o0[0]3t4X4v7^7r:`=U=b=cS0W*p0X#d<T#v$b$c$x${)v*S*V*c+a+d+{,O._/]/f/q/s1X1[1d3U4P4[4a4z4}5p6t7d7n8[8{9d9j9v:Y:h:q:y;Q<T<V<Z<]<_<h<j<m<q<s<u<w<{=p=qn<U<Q<R<U<W<[<^<`<i<k<n<r<t<v<x<|!b<h(r)`*X*a.c.f.j/W/d/t0l1W3Z3}4Y4^5`6y6|7e7h7t7v9_9g:e:i:z;P;[=n=o`<i3p7P7S7W9R:[:_;_S<s.e3[T<t7R9U!O%Qi$d%O%Q%]%^%b*O*Q*]*s*t.x/p0W0Y0e4Q4r9Z=d=l=mv*Q$v)b*R*p+Q/`/o0[0]3t4X4n4v7^7r:`=U=b=cS0Y*q0Z#d<V#v$b$c$x${)v*S*V*c+a+d+{,O._/]/f/q/s1X1[1d3U4P4[4a4z4}5p6t7d7n8[8{9d9j9v:Y:h:q:y;Q<T<V<Z<]<_<h<j<m<q<s<u<w<{=p=qn<W<Q<R<U<W<[<^<`<i<k<n<r<t<v<x<|!f<j(r)`*X*a.d.e.j/W/d/t0l1W3X3Z3}4Y4^5`6y6z6|7e7h7t7v9_9g:e:i:z;P;[=n=od<k3p7Q7R7W9R9S:[:]:_;_S<u.f3]T<v7S9VrnOXst!V!Z#d%k&f&o&q&r&t,l,q1}2QQ&c!UR,i&lrnOXst!V!Z#d%k&f&o&q&r&t,l,q1}2QR&c!UQ,R&ZR1V+zsnOXst!V!Z#d%k&f&o&q&r&t,l,q1}2QQ1c,WS5o1f1gU8Z5m5n5rS9u8]8^S:o9t9wQ;R:pR;];SQ&j!VR,b&fR5{1oR9|8dS&O|&TR1O+qQ&o!WR,l&pR,r&uT2O,q2QR,v&vQ,u&vR2X,vQ'v!{R-r'vSsOtQ#dXT%ns#dQ#OTR'x#OQ#RUR'z#RQ)x$uR/X)xQ#UVR'}#UQ#XWU(T#X(U-yQ(U#YR-y(VQ-V'VR2e-VQ.n(vS3`.n3aR3a.oQ-^']R2i-^Y!rQ']-^1b5iR'g!rQ.y)bR3u.yU#_W%f*VU([#_(]-zQ(]#`R-z(WQ-Y'YR2g-Yt`OXst!V!Z#d%k&f&h&o&q&r&t,l,q1}2QS#hZ%cU#r`#h.TR.T(gQ(h#jQ.Q(dW.Y(h.Q2z6oQ2z.RR6o2{Q)k$lR/P)kQ$phR)q$pQ$`cU)^$`-u<OQ-u;lR<O)nQ/j*YW4T/j4U7b9^U4U/k/l/mS7b4V4WR9^7c$])}$v(r)`)b*X*a*p*q*{*|+Q.e.f.h.i.j/W/`/b/d/o/t0[0]0l1W3X3Y3Z3p3t3}4X4Y4^4n4p4v5`6y6z6{6|7R7S7U7V7W7^7e7h7r7t7v9R9S9T9_9g:[:]:^:_:`:e:i:z;P;[;_=U=b=c=n=oQ/r*aU4]/r4_7fQ4_/tR7f4^S*k$|*WR0Q*kr*P$v)b*p*q+Q/o0[0]3t4X4v7^7r:`=U=b=c!b.c(r)`*X*a.e.f.j/W/d/t0l1W3Z3}4Y4^5`6y6|7e7h7t7v9_9g:e:i:z;P;[=n=oU/a*P.c7Pa7P3p7R7S7W9R:[:_;_Q0X*pQ3[.eU4o0X3[9UR9U7Rv*R$v)b*p*q+Q/`/o0[0]3t4X4n4v7^7r:`=U=b=c!f.d(r)`*X*a.e.f.j/W/d/t0l1W3X3Z3}4Y4^5`6y6z6|7e7h7t7v9_9g:e:i:z;P;[=n=oU/c*R.d7Qe7Q3p7R7S7W9R9S:[:]:_;_Q0Z*qQ3].fU4q0Z3]9VR9V7SQ*v%UR0_*vQ4{0lR7u4{Q+Y%iR0k+YQ5d1]S8T5d9rR9r8UQ,T&[R1`,TQ5i1bR8W5iQ1n,_S5y1n8eR8e5{Q0y+mW5V0y5X7{9nQ5X0|Q7{5WR9n7|Q+r&OR1P+rQ2Q,qR6Z2QYrOXst#dQ&s!ZQ+[%kQ,k&oQ,m&qQ,n&rQ,p&tQ1{,lS2O,q2QR6Y1}Q%mpQ&w!_Q&z!aQ&|!bQ'O!cQ'n!uQ+Z%jQ+g%xQ+y&UQ,a&jQ,x&yW-i'h'p'q'tQ-p'lQ0P*jQ0t+hS1q,b,eQ2Y,wQ2Z,zQ2[,{Q2p-hW2r-k-l-o-qQ5O0uQ5[1SQ5_1WQ5s1hQ5}1sQ6X1|U6h2q2t2wQ6k2uQ7w5PQ8P5^Q8Q5`Q8V5hQ8`5tQ8f5|S8u6i6mQ8w6lQ9o7}Q9x8aQ9}8gQ:U8vQ:l9pQ:r9yQ:v:VR;T:sQ%xyQ'a!iQ'l!uU+h%y%z%{Q-P'SU-d'b'c'dS-h'h'rQ/x*fS0u+i+jQ2b-RS2n-e-fQ2u-mS4b/y/|Q5P0vQ6d2hQ6g2oQ6l2vU7i4d4e4hQ9e7kR:f9fS$wi=dR*w%VU%Ui%V=dR0^*uQ$viS(r#v+dS)`$b$cQ)b$dQ*X$xS*a${*VQ*p%OQ*q%QQ*{%]Q*|%^Q+Q%bQ.e<TQ.f<VQ.h<ZQ.i<]Q.j<_Q/W)vQ/`*OQ/b*QQ/d*SQ/o*]S/t*c/fQ0[*sQ0]*tl0l+a,O._1[1d3U5p6t8[8{9v:Y:q:yQ1W+{Q3X<hQ3Y<jQ3Z<mS3p<Q<RQ3t.xS3}/]4PQ4X/pQ4Y/qQ4^/sQ4n0WQ4p0YQ4v0eQ5`1XQ6y<qQ6z<sQ6{<uQ6|<wQ7R<UQ7S<WQ7U<[Q7V<^Q7W<`Q7^4QQ7e4[Q7h4aQ7r4rQ7t4zQ7v4}Q9R<nQ9S<iQ9T<kQ9_7dQ9g7nQ:[<rQ:]<tQ:^<vQ:_<xQ:`9ZQ:e9dQ:i9jQ:z<{Q;P:hQ;[;QQ;_<|Q=U=dQ=b=lQ=c=mQ=n=pR=o=qQ*y%[Q.g<XR7T<YnpOXst!Z#d%k&o&q&r&t,l,q1}2QQ!fPS#fZ#oQ&y!`W'e!o*e0T4lQ'|#SQ(}#{Q)o$nS,e&h&kQ,j&lQ,w&xS,|'Q/gQ-`'_Q.q({Q/T)pQ0i+WQ0o+bQ1y,iQ2l-bQ3S.`Q3y/OQ4t0bQ5n1eQ6P1uQ6Q1vQ6U1xQ6W1zQ6]2SQ6w3VQ7Z3vQ8^5qQ8j6RQ8k6SQ8m6VQ9P6xQ9w8_R:R8n#[cOPXZst!Z!`!o#d#o#{%k&h&k&l&o&q&r&t&x'Q'_({*e+W+b,i,l,q-b.`/g0T0b1e1u1v1x1z1}2Q2S3V4l5q6R6S6V6x8_8nQ#YWQ#eYQ%ouQ%qvS%sw!gS(P#W(SQ(V#ZQ(q#uQ(v#xQ)O$OQ)P$PQ)Q$QQ)R$RQ)S$SQ)T$TQ)U$UQ)V$VQ)W$WQ)X$XQ)Z$ZQ)]$_Q)_$aQ)d$eW)n$n)p/O3vQ+_%rQ+s&PS-S'U2cQ-q'oS-v(Q-xQ-{(YQ-}(aQ.l(uQ.o(wQ.s;jQ.u;mQ.v;nQ.w;qQ/Z)zQ0f+SQ2^,}Q2a-QQ2q-jQ2x.OQ3^.mQ3c;rQ3d;sQ3e;tQ3f;uQ3g;vQ3h;wQ3i;xQ3j;yQ3k;zQ3l;{Q3m;|Q3n.tQ3o<PQ3r<SQ3s<aQ3z;}Q4w0hQ5Q0wQ6c<dQ6i2sQ6n2yQ6}3_Q7O<eQ7X<gQ7Y<oQ8U5eQ8r6aQ8v6jQ9Q<pQ9W<yQ9X<zQ:V8xQ:m9qQ:u:TQ;l#SR=Z=gR#[WR'W!el!tQ!r!v!y!z']'i'j'k-^-n1b5i5kS'S!e-UU*f$|*W*kS-R'T'[S/|*g*mQ0U*nQ2h-[Q4h0SR4m0VR(x#xQ!fQT-]']-^]!qQ!r']-^1b5iQ#p]R'f;kR)c$dY!uQ']-^1b5iQ'h!rS'r!v!yS't!z5kS-m'i'jQ-o'kR2v-nT#kZ%cS#jZ%cS%im,hU(d#h#i#lS.R(e(fQ.V(gQ0j+XQ2{.SU2|.T.U.WS6p2}3OR8y6qd#^W#W#Z%f(Q(Z*V+U-|/fr#gZm#h#i#l%c(e(f(g+X.S.T.U.W2}3O6qS*Y$x*^Q/m*ZQ1w,hQ2_-OQ4R/iQ6_2VQ7a4SQ8q6`T=R'U+VV#aW%f*VU#`W%f*VS(R#W(ZU(W#Z+U/fS-T'U+VT-w(Q-|V'Z!e%g*WQ$lfR)u$qT)j$l)kR3x.}T*[$x*^T*d${*VQ0m+aQ1Y,OQ3Q._Q5b1[Q5m1dQ6u3UQ8]5pQ8|6tQ9t8[Q:W8{Q:p9vQ:x:YQ;S:qR;V:ynqOXst!Z#d%k&o&q&r&t,l,q1}2QQ&i!VR,a&ftmOXst!U!V!Z#d%k&f&o&q&r&t,l,q1}2QR,h&lT%jm,hR1^,QR,`&dQ&S|R+x&TR+n%}T&m!W&pT&n!W&pT2P,q2Q",nodeNames:"⚠ ArithOp ArithOp ?. JSXStartTag LineComment BlockComment Script Hashbang ExportDeclaration export Star as VariableName String Escape from ; default FunctionDeclaration async function VariableDefinition > < TypeParamList TypeDefinition extends ThisType this LiteralType ArithOp Number BooleanLiteral TemplateType InterpolationEnd Interpolation InterpolationStart NullType null VoidType void TypeofType typeof MemberExpression . PropertyName [ TemplateString Escape Interpolation super RegExp ] ArrayExpression Spread , } { ObjectExpression Property async get set PropertyDefinition Block : NewTarget new NewExpression ) ( ArgList UnaryExpression delete LogicOp BitOp YieldExpression yield AwaitExpression await ParenthesizedExpression ClassExpression class ClassBody MethodDeclaration Decorator @ MemberExpression PrivatePropertyName CallExpression TypeArgList CompareOp < declare Privacy static abstract override PrivatePropertyDefinition PropertyDeclaration readonly accessor Optional TypeAnnotation Equals StaticBlock FunctionExpression ArrowFunction ParamList ParamList ArrayPattern ObjectPattern PatternProperty Privacy readonly Arrow MemberExpression BinaryExpression ArithOp ArithOp ArithOp ArithOp BitOp CompareOp instanceof satisfies in const CompareOp BitOp BitOp BitOp LogicOp LogicOp ConditionalExpression LogicOp LogicOp AssignmentExpression UpdateOp PostfixExpression CallExpression InstantiationExpression TaggedTemplateExpression DynamicImport import ImportMeta JSXElement JSXSelfCloseEndTag JSXSelfClosingTag JSXIdentifier JSXBuiltin JSXIdentifier JSXNamespacedName JSXMemberExpression JSXSpreadAttribute JSXAttribute JSXAttributeValue JSXEscape JSXEndTag JSXOpenTag JSXFragmentTag JSXText JSXEscape JSXStartCloseTag JSXCloseTag PrefixCast ArrowFunction TypeParamList SequenceExpression InstantiationExpression KeyofType keyof UniqueType unique ImportType InferredType infer TypeName ParenthesizedType FunctionSignature ParamList NewSignature IndexedType TupleType Label ArrayType ReadonlyType ObjectType MethodType PropertyType IndexSignature PropertyDefinition CallSignature TypePredicate is NewSignature new UnionType LogicOp IntersectionType LogicOp ConditionalType ParameterizedType ClassDeclaration abstract implements type VariableDeclaration let var using TypeAliasDeclaration InterfaceDeclaration interface EnumDeclaration enum EnumBody NamespaceDeclaration namespace module AmbientDeclaration declare GlobalDeclaration global ClassDeclaration ClassBody AmbientFunctionDeclaration ExportGroup VariableName VariableName ImportDeclaration ImportGroup ForStatement for ForSpec ForInSpec ForOfSpec of WhileStatement while WithStatement with DoStatement do IfStatement if else SwitchStatement switch SwitchBody CaseLabel case DefaultLabel TryStatement try CatchClause catch FinallyClause finally ReturnStatement return ThrowStatement throw BreakStatement break ContinueStatement continue DebuggerStatement debugger LabeledStatement ExpressionStatement SingleExpression SingleClassItem",maxTerm:377,context:Rd,nodeProps:[["isolate",-8,5,6,14,34,36,48,50,52,""],["group",-26,9,17,19,65,204,208,212,213,215,218,221,231,233,239,241,243,245,248,254,260,262,264,266,268,270,271,"Statement",-34,13,14,29,32,33,39,48,51,52,54,59,67,69,73,77,79,81,82,107,108,117,118,135,138,140,141,142,143,144,146,147,166,167,169,"Expression",-23,28,30,34,38,40,42,171,173,175,176,178,179,180,182,183,184,186,187,188,198,200,202,203,"Type",-3,85,100,106,"ClassItem"],["openedBy",23,"<",35,"InterpolationStart",53,"[",57,"{",70,"(",159,"JSXStartCloseTag"],["closedBy",24,">",37,"InterpolationEnd",47,"]",58,"}",71,")",164,"JSXEndTag"]],propSources:[zd],skippedNodes:[0,5,6,274],repeatNodeCount:37,tokenData:"$Fq07[R!bOX%ZXY+gYZ-yZ[+g[]%Z]^.c^p%Zpq+gqr/mrs3cst:_tuEruvJSvwLkwx! Yxy!'iyz!(sz{!)}{|!,q|}!.O}!O!,q!O!P!/Y!P!Q!9j!Q!R#:O!R![#<_![!]#I_!]!^#Jk!^!_#Ku!_!`$![!`!a$$v!a!b$*T!b!c$,r!c!}Er!}#O$-|#O#P$/W#P#Q$4o#Q#R$5y#R#SEr#S#T$7W#T#o$8b#o#p$<r#p#q$=h#q#r$>x#r#s$@U#s$f%Z$f$g+g$g#BYEr#BY#BZ$A`#BZ$ISEr$IS$I_$A`$I_$I|Er$I|$I}$Dk$I}$JO$Dk$JO$JTEr$JT$JU$A`$JU$KVEr$KV$KW$A`$KW&FUEr&FU&FV$A`&FV;'SEr;'S;=`I|<%l?HTEr?HT?HU$A`?HUOEr(n%d_$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z&j&hT$h&jO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c&j&zP;=`<%l&c'|'U]$h&j(V!bOY&}YZ&cZw&}wx&cx!^&}!^!_'}!_#O&}#O#P&c#P#o&}#o#p'}#p;'S&};'S;=`(l<%lO&}!b(SU(V!bOY'}Zw'}x#O'}#P;'S'};'S;=`(f<%lO'}!b(iP;=`<%l'}'|(oP;=`<%l&}'[(y]$h&j(SpOY(rYZ&cZr(rrs&cs!^(r!^!_)r!_#O(r#O#P&c#P#o(r#o#p)r#p;'S(r;'S;=`*a<%lO(rp)wU(SpOY)rZr)rs#O)r#P;'S)r;'S;=`*Z<%lO)rp*^P;=`<%l)r'[*dP;=`<%l(r#S*nX(Sp(V!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g#S+^P;=`<%l*g(n+dP;=`<%l%Z07[+rq$h&j(Sp(V!b'x0/lOX%ZXY+gYZ&cZ[+g[p%Zpq+gqr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p$f%Z$f$g+g$g#BY%Z#BY#BZ+g#BZ$IS%Z$IS$I_+g$I_$JT%Z$JT$JU+g$JU$KV%Z$KV$KW+g$KW&FU%Z&FU&FV+g&FV;'S%Z;'S;=`+a<%l?HT%Z?HT?HU+g?HUO%Z07[.ST(T#S$h&j'y0/lO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c07[.n_$h&j(Sp(V!b'y0/lOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z)3p/x`$h&j!m),Q(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`0z!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW1V`#u(Ch$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`2X!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW2d_#u(Ch$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'At3l_(R':f$h&j(V!bOY4kYZ5qZr4krs7nsw4kwx5qx!^4k!^!_8p!_#O4k#O#P5q#P#o4k#o#p8p#p;'S4k;'S;=`:X<%lO4k(^4r_$h&j(V!bOY4kYZ5qZr4krs7nsw4kwx5qx!^4k!^!_8p!_#O4k#O#P5q#P#o4k#o#p8p#p;'S4k;'S;=`:X<%lO4k&z5vX$h&jOr5qrs6cs!^5q!^!_6y!_#o5q#o#p6y#p;'S5q;'S;=`7h<%lO5q&z6jT$c`$h&jO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c`6|TOr6yrs7]s;'S6y;'S;=`7b<%lO6y`7bO$c``7eP;=`<%l6y&z7kP;=`<%l5q(^7w]$c`$h&j(V!bOY&}YZ&cZw&}wx&cx!^&}!^!_'}!_#O&}#O#P&c#P#o&}#o#p'}#p;'S&};'S;=`(l<%lO&}!r8uZ(V!bOY8pYZ6yZr8prs9hsw8pwx6yx#O8p#O#P6y#P;'S8p;'S;=`:R<%lO8p!r9oU$c`(V!bOY'}Zw'}x#O'}#P;'S'};'S;=`(f<%lO'}!r:UP;=`<%l8p(^:[P;=`<%l4k%9[:hh$h&j(Sp(V!bOY%ZYZ&cZq%Zqr<Srs&}st%ZtuCruw%Zwx(rx!^%Z!^!_*g!_!c%Z!c!}Cr!}#O%Z#O#P&c#P#R%Z#R#SCr#S#T%Z#T#oCr#o#p*g#p$g%Z$g;'SCr;'S;=`El<%lOCr(r<__WS$h&j(Sp(V!bOY<SYZ&cZr<Srs=^sw<Swx@nx!^<S!^!_Bm!_#O<S#O#P>`#P#o<S#o#pBm#p;'S<S;'S;=`Cl<%lO<S(Q=g]WS$h&j(V!bOY=^YZ&cZw=^wx>`x!^=^!^!_?q!_#O=^#O#P>`#P#o=^#o#p?q#p;'S=^;'S;=`@h<%lO=^&n>gXWS$h&jOY>`YZ&cZ!^>`!^!_?S!_#o>`#o#p?S#p;'S>`;'S;=`?k<%lO>`S?XSWSOY?SZ;'S?S;'S;=`?e<%lO?SS?hP;=`<%l?S&n?nP;=`<%l>`!f?xWWS(V!bOY?qZw?qwx?Sx#O?q#O#P?S#P;'S?q;'S;=`@b<%lO?q!f@eP;=`<%l?q(Q@kP;=`<%l=^'`@w]WS$h&j(SpOY@nYZ&cZr@nrs>`s!^@n!^!_Ap!_#O@n#O#P>`#P#o@n#o#pAp#p;'S@n;'S;=`Bg<%lO@ntAwWWS(SpOYApZrAprs?Ss#OAp#O#P?S#P;'SAp;'S;=`Ba<%lOAptBdP;=`<%lAp'`BjP;=`<%l@n#WBvYWS(Sp(V!bOYBmZrBmrs?qswBmwxApx#OBm#O#P?S#P;'SBm;'S;=`Cf<%lOBm#WCiP;=`<%lBm(rCoP;=`<%l<S%9[C}i$h&j(k%1l(Sp(V!bOY%ZYZ&cZr%Zrs&}st%ZtuCruw%Zwx(rx!Q%Z!Q![Cr![!^%Z!^!_*g!_!c%Z!c!}Cr!}#O%Z#O#P&c#P#R%Z#R#SCr#S#T%Z#T#oCr#o#p*g#p$g%Z$g;'SCr;'S;=`El<%lOCr%9[EoP;=`<%lCr07[FRk$h&j(Sp(V!b$[#t(P,2j(a$I[OY%ZYZ&cZr%Zrs&}st%ZtuEruw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Er![!^%Z!^!_*g!_!c%Z!c!}Er!}#O%Z#O#P&c#P#R%Z#R#SEr#S#T%Z#T#oEr#o#p*g#p$g%Z$g;'SEr;'S;=`I|<%lOEr+dHRk$h&j(Sp(V!b$[#tOY%ZYZ&cZr%Zrs&}st%ZtuGvuw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Gv![!^%Z!^!_*g!_!c%Z!c!}Gv!}#O%Z#O#P&c#P#R%Z#R#SGv#S#T%Z#T#oGv#o#p*g#p$g%Z$g;'SGv;'S;=`Iv<%lOGv+dIyP;=`<%lGv07[JPP;=`<%lEr(KWJ_`$h&j(Sp(V!b#m(ChOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KWKl_$h&j$P(Ch(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z,#xLva(v+JY$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sv%ZvwM{wx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KWNW`$h&j#y(Ch(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'At! c_(U';W$h&j(SpOY!!bYZ!#hZr!!brs!#hsw!!bwx!$xx!^!!b!^!_!%z!_#O!!b#O#P!#h#P#o!!b#o#p!%z#p;'S!!b;'S;=`!'c<%lO!!b'l!!i_$h&j(SpOY!!bYZ!#hZr!!brs!#hsw!!bwx!$xx!^!!b!^!_!%z!_#O!!b#O#P!#h#P#o!!b#o#p!%z#p;'S!!b;'S;=`!'c<%lO!!b&z!#mX$h&jOw!#hwx6cx!^!#h!^!_!$Y!_#o!#h#o#p!$Y#p;'S!#h;'S;=`!$r<%lO!#h`!$]TOw!$Ywx7]x;'S!$Y;'S;=`!$l<%lO!$Y`!$oP;=`<%l!$Y&z!$uP;=`<%l!#h'l!%R]$c`$h&j(SpOY(rYZ&cZr(rrs&cs!^(r!^!_)r!_#O(r#O#P&c#P#o(r#o#p)r#p;'S(r;'S;=`*a<%lO(r!Q!&PZ(SpOY!%zYZ!$YZr!%zrs!$Ysw!%zwx!&rx#O!%z#O#P!$Y#P;'S!%z;'S;=`!']<%lO!%z!Q!&yU$c`(SpOY)rZr)rs#O)r#P;'S)r;'S;=`*Z<%lO)r!Q!'`P;=`<%l!%z'l!'fP;=`<%l!!b/5|!'t_!i/.^$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z#&U!)O_!h!Lf$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z-!n!*[b$h&j(Sp(V!b(Q%&f#n(ChOY%ZYZ&cZr%Zrs&}sw%Zwx(rxz%Zz{!+d{!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW!+o`$h&j(Sp(V!b#k(ChOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z+;x!,|`$h&j(Sp(V!bo+4YOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z,$U!.Z_!Y+Jf$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z07[!/ec$h&j(Sp(V!b}.2^OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!0p!P!Q%Z!Q![!3Y![!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z#%|!0ya$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!2O!P!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z#%|!2Z_!X!L^$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad!3eg$h&j(Sp(V!bp'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![!3Y![!^%Z!^!_*g!_!g%Z!g!h!4|!h#O%Z#O#P&c#P#R%Z#R#S!3Y#S#X%Z#X#Y!4|#Y#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad!5Vg$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx{%Z{|!6n|}%Z}!O!6n!O!Q%Z!Q![!8S![!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S!8S#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad!6wc$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![!8S![!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S!8S#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad!8_c$h&j(Sp(V!bp'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![!8S![!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S!8S#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z07[!9uf$h&j(Sp(V!b#l(ChOY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcxz!;Zz{#-}{!P!;Z!P!Q#/d!Q!^!;Z!^!_#(i!_!`#7S!`!a#8i!a!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z?O!;fb$h&j(Sp(V!b!U7`OY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z>^!<w`$h&j(V!b!U7`OY!<nYZ&cZw!<nwx!=yx!P!<n!P!Q!Eq!Q!^!<n!^!_!Gr!_!}!<n!}#O!KS#O#P!Dy#P#o!<n#o#p!Gr#p;'S!<n;'S;=`!L]<%lO!<n<z!>Q^$h&j!U7`OY!=yYZ&cZ!P!=y!P!Q!>|!Q!^!=y!^!_!@c!_!}!=y!}#O!CW#O#P!Dy#P#o!=y#o#p!@c#p;'S!=y;'S;=`!Ek<%lO!=y<z!?Td$h&j!U7`O!^&c!_#W&c#W#X!>|#X#Z&c#Z#[!>|#[#]&c#]#^!>|#^#a&c#a#b!>|#b#g&c#g#h!>|#h#i&c#i#j!>|#j#k!>|#k#m&c#m#n!>|#n#o&c#p;'S&c;'S;=`&w<%lO&c7`!@hX!U7`OY!@cZ!P!@c!P!Q!AT!Q!}!@c!}#O!Ar#O#P!Bq#P;'S!@c;'S;=`!CQ<%lO!@c7`!AYW!U7`#W#X!AT#Z#[!AT#]#^!AT#a#b!AT#g#h!AT#i#j!AT#j#k!AT#m#n!AT7`!AuVOY!ArZ#O!Ar#O#P!B[#P#Q!@c#Q;'S!Ar;'S;=`!Bk<%lO!Ar7`!B_SOY!ArZ;'S!Ar;'S;=`!Bk<%lO!Ar7`!BnP;=`<%l!Ar7`!BtSOY!@cZ;'S!@c;'S;=`!CQ<%lO!@c7`!CTP;=`<%l!@c<z!C][$h&jOY!CWYZ&cZ!^!CW!^!_!Ar!_#O!CW#O#P!DR#P#Q!=y#Q#o!CW#o#p!Ar#p;'S!CW;'S;=`!Ds<%lO!CW<z!DWX$h&jOY!CWYZ&cZ!^!CW!^!_!Ar!_#o!CW#o#p!Ar#p;'S!CW;'S;=`!Ds<%lO!CW<z!DvP;=`<%l!CW<z!EOX$h&jOY!=yYZ&cZ!^!=y!^!_!@c!_#o!=y#o#p!@c#p;'S!=y;'S;=`!Ek<%lO!=y<z!EnP;=`<%l!=y>^!Ezl$h&j(V!b!U7`OY&}YZ&cZw&}wx&cx!^&}!^!_'}!_#O&}#O#P&c#P#W&}#W#X!Eq#X#Z&}#Z#[!Eq#[#]&}#]#^!Eq#^#a&}#a#b!Eq#b#g&}#g#h!Eq#h#i&}#i#j!Eq#j#k!Eq#k#m&}#m#n!Eq#n#o&}#o#p'}#p;'S&};'S;=`(l<%lO&}8r!GyZ(V!b!U7`OY!GrZw!Grwx!@cx!P!Gr!P!Q!Hl!Q!}!Gr!}#O!JU#O#P!Bq#P;'S!Gr;'S;=`!J|<%lO!Gr8r!Hse(V!b!U7`OY'}Zw'}x#O'}#P#W'}#W#X!Hl#X#Z'}#Z#[!Hl#[#]'}#]#^!Hl#^#a'}#a#b!Hl#b#g'}#g#h!Hl#h#i'}#i#j!Hl#j#k!Hl#k#m'}#m#n!Hl#n;'S'};'S;=`(f<%lO'}8r!JZX(V!bOY!JUZw!JUwx!Arx#O!JU#O#P!B[#P#Q!Gr#Q;'S!JU;'S;=`!Jv<%lO!JU8r!JyP;=`<%l!JU8r!KPP;=`<%l!Gr>^!KZ^$h&j(V!bOY!KSYZ&cZw!KSwx!CWx!^!KS!^!_!JU!_#O!KS#O#P!DR#P#Q!<n#Q#o!KS#o#p!JU#p;'S!KS;'S;=`!LV<%lO!KS>^!LYP;=`<%l!KS>^!L`P;=`<%l!<n=l!Ll`$h&j(Sp!U7`OY!LcYZ&cZr!Lcrs!=ys!P!Lc!P!Q!Mn!Q!^!Lc!^!_# o!_!}!Lc!}#O#%P#O#P!Dy#P#o!Lc#o#p# o#p;'S!Lc;'S;=`#&Y<%lO!Lc=l!Mwl$h&j(Sp!U7`OY(rYZ&cZr(rrs&cs!^(r!^!_)r!_#O(r#O#P&c#P#W(r#W#X!Mn#X#Z(r#Z#[!Mn#[#](r#]#^!Mn#^#a(r#a#b!Mn#b#g(r#g#h!Mn#h#i(r#i#j!Mn#j#k!Mn#k#m(r#m#n!Mn#n#o(r#o#p)r#p;'S(r;'S;=`*a<%lO(r8Q# vZ(Sp!U7`OY# oZr# ors!@cs!P# o!P!Q#!i!Q!}# o!}#O#$R#O#P!Bq#P;'S# o;'S;=`#$y<%lO# o8Q#!pe(Sp!U7`OY)rZr)rs#O)r#P#W)r#W#X#!i#X#Z)r#Z#[#!i#[#])r#]#^#!i#^#a)r#a#b#!i#b#g)r#g#h#!i#h#i)r#i#j#!i#j#k#!i#k#m)r#m#n#!i#n;'S)r;'S;=`*Z<%lO)r8Q#$WX(SpOY#$RZr#$Rrs!Ars#O#$R#O#P!B[#P#Q# o#Q;'S#$R;'S;=`#$s<%lO#$R8Q#$vP;=`<%l#$R8Q#$|P;=`<%l# o=l#%W^$h&j(SpOY#%PYZ&cZr#%Prs!CWs!^#%P!^!_#$R!_#O#%P#O#P!DR#P#Q!Lc#Q#o#%P#o#p#$R#p;'S#%P;'S;=`#&S<%lO#%P=l#&VP;=`<%l#%P=l#&]P;=`<%l!Lc?O#&kn$h&j(Sp(V!b!U7`OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#W%Z#W#X#&`#X#Z%Z#Z#[#&`#[#]%Z#]#^#&`#^#a%Z#a#b#&`#b#g%Z#g#h#&`#h#i%Z#i#j#&`#j#k#&`#k#m%Z#m#n#&`#n#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z9d#(r](Sp(V!b!U7`OY#(iZr#(irs!Grsw#(iwx# ox!P#(i!P!Q#)k!Q!}#(i!}#O#+`#O#P!Bq#P;'S#(i;'S;=`#,`<%lO#(i9d#)th(Sp(V!b!U7`OY*gZr*grs'}sw*gwx)rx#O*g#P#W*g#W#X#)k#X#Z*g#Z#[#)k#[#]*g#]#^#)k#^#a*g#a#b#)k#b#g*g#g#h#)k#h#i*g#i#j#)k#j#k#)k#k#m*g#m#n#)k#n;'S*g;'S;=`+Z<%lO*g9d#+gZ(Sp(V!bOY#+`Zr#+`rs!JUsw#+`wx#$Rx#O#+`#O#P!B[#P#Q#(i#Q;'S#+`;'S;=`#,Y<%lO#+`9d#,]P;=`<%l#+`9d#,cP;=`<%l#(i?O#,o`$h&j(Sp(V!bOY#,fYZ&cZr#,frs!KSsw#,fwx#%Px!^#,f!^!_#+`!_#O#,f#O#P!DR#P#Q!;Z#Q#o#,f#o#p#+`#p;'S#,f;'S;=`#-q<%lO#,f?O#-tP;=`<%l#,f?O#-zP;=`<%l!;Z07[#.[b$h&j(Sp(V!b'z0/l!U7`OY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z07[#/o_$h&j(Sp(V!bT0/lOY#/dYZ&cZr#/drs#0nsw#/dwx#4Ox!^#/d!^!_#5}!_#O#/d#O#P#1p#P#o#/d#o#p#5}#p;'S#/d;'S;=`#6|<%lO#/d06j#0w]$h&j(V!bT0/lOY#0nYZ&cZw#0nwx#1px!^#0n!^!_#3R!_#O#0n#O#P#1p#P#o#0n#o#p#3R#p;'S#0n;'S;=`#3x<%lO#0n05W#1wX$h&jT0/lOY#1pYZ&cZ!^#1p!^!_#2d!_#o#1p#o#p#2d#p;'S#1p;'S;=`#2{<%lO#1p0/l#2iST0/lOY#2dZ;'S#2d;'S;=`#2u<%lO#2d0/l#2xP;=`<%l#2d05W#3OP;=`<%l#1p01O#3YW(V!bT0/lOY#3RZw#3Rwx#2dx#O#3R#O#P#2d#P;'S#3R;'S;=`#3r<%lO#3R01O#3uP;=`<%l#3R06j#3{P;=`<%l#0n05x#4X]$h&j(SpT0/lOY#4OYZ&cZr#4Ors#1ps!^#4O!^!_#5Q!_#O#4O#O#P#1p#P#o#4O#o#p#5Q#p;'S#4O;'S;=`#5w<%lO#4O00^#5XW(SpT0/lOY#5QZr#5Qrs#2ds#O#5Q#O#P#2d#P;'S#5Q;'S;=`#5q<%lO#5Q00^#5tP;=`<%l#5Q05x#5zP;=`<%l#4O01p#6WY(Sp(V!bT0/lOY#5}Zr#5}rs#3Rsw#5}wx#5Qx#O#5}#O#P#2d#P;'S#5};'S;=`#6v<%lO#5}01p#6yP;=`<%l#5}07[#7PP;=`<%l#/d)3h#7ab$h&j$P(Ch(Sp(V!b!U7`OY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;ZAt#8vb$Y#t$h&j(Sp(V!b!U7`OY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z'Ad#:Zp$h&j(Sp(V!bp'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!3Y!P!Q%Z!Q![#<_![!^%Z!^!_*g!_!g%Z!g!h!4|!h#O%Z#O#P&c#P#R%Z#R#S#<_#S#U%Z#U#V#?i#V#X%Z#X#Y!4|#Y#b%Z#b#c#>_#c#d#Bq#d#l%Z#l#m#Es#m#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#<jk$h&j(Sp(V!bp'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!3Y!P!Q%Z!Q![#<_![!^%Z!^!_*g!_!g%Z!g!h!4|!h#O%Z#O#P&c#P#R%Z#R#S#<_#S#X%Z#X#Y!4|#Y#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#>j_$h&j(Sp(V!bp'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#?rd$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!R#AQ!R!S#AQ!S!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#AQ#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#A]f$h&j(Sp(V!bp'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!R#AQ!R!S#AQ!S!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#AQ#S#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#Bzc$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!Y#DV!Y!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#DV#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#Dbe$h&j(Sp(V!bp'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!Y#DV!Y!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#DV#S#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#E|g$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![#Ge![!^%Z!^!_*g!_!c%Z!c!i#Ge!i#O%Z#O#P&c#P#R%Z#R#S#Ge#S#T%Z#T#Z#Ge#Z#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#Gpi$h&j(Sp(V!bp'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![#Ge![!^%Z!^!_*g!_!c%Z!c!i#Ge!i#O%Z#O#P&c#P#R%Z#R#S#Ge#S#T%Z#T#Z#Ge#Z#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z*)x#Il_!d$b$h&j#})Lv(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z)[#Jv_al$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z04f#LS^h#)`#O-<U(Sp(V!b(z7`OY*gZr*grs'}sw*gwx)rx!P*g!P!Q#MO!Q!^*g!^!_#Mt!_!`$ f!`#O*g#P;'S*g;'S;=`+Z<%lO*g(n#MXX$j&j(Sp(V!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g(El#M}Z#o(Ch(Sp(V!bOY*gZr*grs'}sw*gwx)rx!_*g!_!`#Np!`#O*g#P;'S*g;'S;=`+Z<%lO*g(El#NyX$P(Ch(Sp(V!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g(El$ oX#p(Ch(Sp(V!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g*)x$!ga#]*!Y$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`0z!`!a$#l!a#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(K[$#w_#h(Cl$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z*)x$%Vag!*r#p(Ch$e#|$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`$&[!`!a$'f!a#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$&g_#p(Ch$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$'qa#o(Ch$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`!a$(v!a#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$)R`#o(Ch$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(Kd$*`a(n(Ct$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!a%Z!a!b$+e!b#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$+p`$h&j#z(Ch(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#`$,}_!y$Ip$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z04f$.X_!P0,v$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(n$/]Z$h&jO!^$0O!^!_$0f!_#i$0O#i#j$0k#j#l$0O#l#m$2^#m#o$0O#o#p$0f#p;'S$0O;'S;=`$4i<%lO$0O(n$0VT_#S$h&jO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c#S$0kO_#S(n$0p[$h&jO!Q&c!Q![$1f![!^&c!_!c&c!c!i$1f!i#T&c#T#Z$1f#Z#o&c#o#p$3|#p;'S&c;'S;=`&w<%lO&c(n$1kZ$h&jO!Q&c!Q![$2^![!^&c!_!c&c!c!i$2^!i#T&c#T#Z$2^#Z#o&c#p;'S&c;'S;=`&w<%lO&c(n$2cZ$h&jO!Q&c!Q![$3U![!^&c!_!c&c!c!i$3U!i#T&c#T#Z$3U#Z#o&c#p;'S&c;'S;=`&w<%lO&c(n$3ZZ$h&jO!Q&c!Q![$0O![!^&c!_!c&c!c!i$0O!i#T&c#T#Z$0O#Z#o&c#p;'S&c;'S;=`&w<%lO&c#S$4PR!Q![$4Y!c!i$4Y#T#Z$4Y#S$4]S!Q![$4Y!c!i$4Y#T#Z$4Y#q#r$0f(n$4lP;=`<%l$0O#1[$4z_!V#)l$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$6U`#w(Ch$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z+;p$7c_$h&j(Sp(V!b(]+4QOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z07[$8qk$h&j(Sp(V!b(P,2j$^#t(a$I[OY%ZYZ&cZr%Zrs&}st%Ztu$8buw%Zwx(rx}%Z}!O$:f!O!Q%Z!Q![$8b![!^%Z!^!_*g!_!c%Z!c!}$8b!}#O%Z#O#P&c#P#R%Z#R#S$8b#S#T%Z#T#o$8b#o#p*g#p$g%Z$g;'S$8b;'S;=`$<l<%lO$8b+d$:qk$h&j(Sp(V!b$^#tOY%ZYZ&cZr%Zrs&}st%Ztu$:fuw%Zwx(rx}%Z}!O$:f!O!Q%Z!Q![$:f![!^%Z!^!_*g!_!c%Z!c!}$:f!}#O%Z#O#P&c#P#R%Z#R#S$:f#S#T%Z#T#o$:f#o#p*g#p$g%Z$g;'S$:f;'S;=`$<f<%lO$:f+d$<iP;=`<%l$:f07[$<oP;=`<%l$8b#Jf$<{X![#Hb(Sp(V!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g,#x$=sa(u+JY$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p#q$+e#q;'S%Z;'S;=`+a<%lO%Z)>v$?V_!Z(CdsBr$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z?O$@a_!n7`$h&j(Sp(V!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z07[$Aq|$h&j(Sp(V!b'x0/l$[#t(P,2j(a$I[OX%ZXY+gYZ&cZ[+g[p%Zpq+gqr%Zrs&}st%ZtuEruw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Er![!^%Z!^!_*g!_!c%Z!c!}Er!}#O%Z#O#P&c#P#R%Z#R#SEr#S#T%Z#T#oEr#o#p*g#p$f%Z$f$g+g$g#BYEr#BY#BZ$A`#BZ$ISEr$IS$I_$A`$I_$JTEr$JT$JU$A`$JU$KVEr$KV$KW$A`$KW&FUEr&FU&FV$A`&FV;'SEr;'S;=`I|<%l?HTEr?HT?HU$A`?HUOEr07[$D|k$h&j(Sp(V!b'y0/l$[#t(P,2j(a$I[OY%ZYZ&cZr%Zrs&}st%ZtuEruw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Er![!^%Z!^!_*g!_!c%Z!c!}Er!}#O%Z#O#P&c#P#R%Z#R#SEr#S#T%Z#T#oEr#o#p*g#p$g%Z$g;'SEr;'S;=`I|<%lOEr",tokenizers:[Cd,Md,qd,Yd,2,3,4,5,6,7,8,9,10,11,12,13,14,Ad,new Ih("$S~RRtu[#O#Pg#S#T#|~_P#o#pb~gOu~~jVO#i!P#i#j!U#j#l!P#l#m!q#m;'S!P;'S;=`#v<%lO!P~!UO!R~~!XS!Q![!e!c!i!e#T#Z!e#o#p#Z~!hR!Q![!q!c!i!q#T#Z!q~!tR!Q![!}!c!i!}#T#Z!}~#QR!Q![!P!c!i!P#T#Z!P~#^R!Q![#g!c!i#g#T#Z#g~#jS!Q![#g!c!i#g#T#Z#g#q#r!P~#yP;=`<%l!P~$RO(_~~",141,336),new Ih("j~RQYZXz{^~^O'|~~aP!P!Qd~iO'}~~",25,319)],topRules:{Script:[0,7],SingleExpression:[1,272],SingleClassItem:[2,273]},dialects:{jsx:0,ts:14980},dynamicPrecedences:{77:1,79:1,91:1,167:1,196:1},specialized:[{term:323,get:t=>_d[t]||-1},{term:339,get:t=>jd[t]||-1},{term:92,get:t=>Ed[t]||-1}],tokenPrec:15004}),Wd=[LO("function ${name}(${params}) {\n\t${}\n}",{label:"function",detail:"definition",type:"keyword"}),LO("for (let ${index} = 0; ${index} < ${bound}; ${index}++) {\n\t${}\n}",{label:"for",detail:"loop",type:"keyword"}),LO("for (let ${name} of ${collection}) {\n\t${}\n}",{label:"for",detail:"of loop",type:"keyword"}),LO("do {\n\t${}\n} while (${})",{label:"do",detail:"loop",type:"keyword"}),LO("while (${}) {\n\t${}\n}",{label:"while",detail:"loop",type:"keyword"}),LO("try {\n\t${}\n} catch (${error}) {\n\t${}\n}",{label:"try",detail:"/ catch block",type:"keyword"}),LO("if (${}) {\n\t${}\n}",{label:"if",detail:"block",type:"keyword"}),LO("if (${}) {\n\t${}\n} else {\n\t${}\n}",{label:"if",detail:"/ else block",type:"keyword"}),LO("class ${name} {\n\tconstructor(${params}) {\n\t\t${}\n\t}\n}",{label:"class",detail:"definition",type:"keyword"}),LO('import {${names}} from "${module}"\n${}',{label:"import",detail:"named",type:"keyword"}),LO('import ${name} from "${module}"\n${}',{label:"import",detail:"default",type:"keyword"})],Ld=Wd.concat([LO("interface ${name} {\n\t${}\n}",{label:"interface",detail:"definition",type:"keyword"}),LO("type ${name} = ${type}",{label:"type",detail:"definition",type:"keyword"}),LO("enum ${name} {\n\t${}\n}",{label:"enum",detail:"definition",type:"keyword"})]),Gd=new T,Bd=new Set(["Script","Block","FunctionExpression","FunctionDeclaration","ArrowFunction","MethodDeclaration","ForStatement"]);function Ud(t){return(e,i)=>{let n=e.node.getChild("VariableDefinition");return n&&i(n,t),!0}}const Id=["FunctionDeclaration"],Nd={FunctionDeclaration:Ud("function"),ClassDeclaration:Ud("class"),ClassExpression:()=>!0,EnumDeclaration:Ud("constant"),TypeAliasDeclaration:Ud("type"),NamespaceDeclaration:Ud("namespace"),VariableDefinition(t,e){t.matchContext(Id)||e(t,"variable")},TypeDefinition(t,e){e(t,"type")},__proto__:null};function Hd(t,e){let i=Gd.get(e);if(i)return i;let n=[],s=!0;function r(e,i){let s=t.sliceString(e.from,e.to);n.push({label:s,type:i})}return e.cursor(c.IncludeAnonymous).iterate((e=>{if(s)s=!1;else if(e.name){let t=Nd[e.name];if(t&&t(e,r)||Bd.has(e.name))return!1}else if(e.to-e.from>8192){for(let i of Hd(t,e.node))n.push(i);return!1}})),Gd.set(e,n),n}const Fd=/^[\w$\xa1-\uffff][\w$\d\xa1-\uffff]*$/,Kd=["TemplateString","String","RegExp","LineComment","BlockComment","VariableDefinition","TypeDefinition","Label","PropertyDefinition","PropertyName","PrivatePropertyDefinition","PrivatePropertyName",".","?."];function Jd(t){let e=Wa(t.state).resolveInner(t.pos,-1);if(Kd.indexOf(e.name)>-1)return null;let i="VariableName"==e.name||e.to-e.from<20&&Fd.test(t.state.sliceDoc(e.from,e.to));if(!i&&!t.explicit)return null;let n=[];for(let i=e;i;i=i.parent)Bd.has(i.name)&&(n=n.concat(Hd(t.state.doc,i)));return{options:n,from:i?e.from:t.pos,validFor:Fd}}function tp(t,e,i){var n;let s=[];for(;;){let r,o=e.firstChild;if("VariableName"==(null==o?void 0:o.name))return s.push(t(o)),{path:s.reverse(),name:i};if("MemberExpression"!=(null==o?void 0:o.name)||"PropertyName"!=(null===(n=r=o.lastChild)||void 0===n?void 0:n.name))return null;s.push(t(r)),e=o}}function ep(t){let e=e=>t.state.doc.sliceString(e.from,e.to),i=Wa(t.state).resolveInner(t.pos,-1);return"PropertyName"==i.name?tp(e,i.parent,e(i)):"."!=i.name&&"?."!=i.name||"MemberExpression"!=i.parent.name?Kd.indexOf(i.name)>-1?null:"VariableName"==i.name||i.to-i.from<20&&Fd.test(e(i))?{path:[],name:e(i)}:"MemberExpression"==i.name?tp(e,i,""):t.explicit?{path:[],name:""}:null:tp(e,i.parent,"")}const ip=Da.define({name:"javascript",parser:Dd.configure({props:[cl.add({IfStatement:Ql({except:/^\s*({|else\b)/}),TryStatement:Ql({except:/^\s*({|catch\b|finally\b)/}),LabeledStatement:bl,SwitchBody:t=>{let e=t.textAfter,i=/^\s*\}/.test(e),n=/^\s*(case|default)\b/.test(e);return t.baseIndent+(i?0:n?1:2)*t.unit},Block:ml({closing:"}"}),ArrowFunction:t=>t.baseIndent+t.unit,"TemplateString BlockComment":()=>null,"Statement Property":Ql({except:/^{/}),JSXElement(t){let e=/^\s*<\//.test(t.textAfter);return t.lineIndent(t.node.from)+(e?0:t.unit)},JSXEscape(t){let e=/\s*\}/.test(t.textAfter);return t.lineIndent(t.node.from)+(e?0:t.unit)},"JSXOpenTag JSXSelfClosingTag":t=>t.column(t.node.from)+t.unit}),wl.add({"Block ClassBody SwitchBody EnumBody ObjectExpression ArrayExpression ObjectType":yl,BlockComment:t=>({from:t.from+2,to:t.to-2})})]}),languageData:{closeBrackets:{brackets:["(","[","{","'",'"',"`"]},commentTokens:{line:"//",block:{open:"/*",close:"*/"}},indentOnInput:/^\s*(?:case |default:|\{|\}|<\/)$/,wordChars:"$"}}),np={test:t=>/^JSX/.test(t.name),facet:za({commentTokens:{block:{open:"{/*",close:"*/}"}}})},sp=ip.configure({dialect:"ts"},"typescript"),rp=ip.configure({dialect:"jsx",props:[_a.add((t=>t.isTop?[np]:void 0))]}),op=ip.configure({dialect:"jsx ts",props:[_a.add((t=>t.isTop?[np]:void 0))]},"typescript");let ap=t=>({label:t,type:"keyword"});const lp="break case const continue default delete export extends false finally in instanceof let new return static super switch this throw true typeof var yield".split(" ").map(ap),hp=lp.concat(["declare","implements","private","protected","public"].map(ap));function cp(t={}){let e=t.jsx?t.typescript?op:rp:t.typescript?sp:ip,i=t.typescript?Ld.concat(hp):Wd.concat(lp);return new il(e,[ip.data.of({autocomplete:Dc(Kd,Ec(i))}),ip.data.of({autocomplete:Jd}),t.jsx?fp:[]])}function Op(t,e,i=t.length){for(let n=null==e?void 0:e.firstChild;n;n=n.nextSibling)if("JSXIdentifier"==n.name||"JSXBuiltin"==n.name||"JSXNamespacedName"==n.name||"JSXMemberExpression"==n.name)return t.sliceString(n.from,Math.min(n.to,i));return""}const up="object"==typeof navigator&&/Android\b/.test(navigator.userAgent),fp=zr.inputHandler.of(((t,e,i,n,s)=>{if((up?t.composing:t.compositionStarted)||t.state.readOnly||e!=i||">"!=n&&"/"!=n||!ip.isActiveAt(t.state,e,-1))return!1;let r=s(),{state:o}=r,a=o.changeByRange((t=>{var e;let i,{head:s}=t,r=Wa(o).resolveInner(s-1,-1);if("JSXStartTag"==r.name&&(r=r.parent),o.doc.sliceString(s-1,s)!=n||"JSXAttributeValue"==r.name&&r.to>s);else{if(">"==n&&"JSXFragmentTag"==r.name)return{range:t,changes:{from:s,insert:"</>"}};if("/"==n&&"JSXStartCloseTag"==r.name){let t=r.parent,n=t.parent;if(n&&t.from==s-2&&((i=Op(o.doc,n.firstChild,s))||"JSXFragmentTag"==(null===(e=n.firstChild)||void 0===e?void 0:e.name))){let t=`${i}>`;return{range:Tt.cursor(s+t.length,-1),changes:{from:s,insert:t}}}}else if(">"==n){let e=function(t){for(;;){if("JSXOpenTag"==t.name||"JSXSelfClosingTag"==t.name||"JSXFragmentTag"==t.name)return t;if("JSXEscape"==t.name||!t.parent)return null;t=t.parent}}(r);if(e&&!/^\/?>|^<\//.test(o.doc.sliceString(s,s+2))&&(i=Op(o.doc,e,s)))return{range:t,changes:{from:s,insert:`</${i}>`}}}}return{range:t}}));return!a.changes.empty&&(t.dispatch([r,o.update(a,{userEvent:"input.complete",scrollIntoView:!0})]),!0)}));function dp(t,e,i,n){return i.line(t+n.line).from+e+(1==t?n.col-1:-1)}function pp(t,e,i){let n=dp(t.line,t.column,e,i),s={from:n,to:null!=t.endLine&&1!=t.endColumn?dp(t.endLine,t.endColumn,e,i):n,message:t.message,source:t.ruleId?"eslint:"+t.ruleId:"eslint",severity:1==t.severity?"warning":"error"};if(t.fix){let{range:e,text:r}=t.fix,o=e[0]+i.pos-n,a=e[1]+i.pos-n;s.actions=[{name:"fix",apply(t,e){t.dispatch({changes:{from:e+o,to:e+a,insert:r},scrollIntoView:!0})}}]}return s}var mp=Object.freeze({__proto__:null,autoCloseTags:fp,completionPath:ep,esLint:function(t,e){return e||(e={parserOptions:{ecmaVersion:2019,sourceType:"module"},env:{browser:!0,node:!0,es6:!0,es2015:!0,es2017:!0,es2020:!0},rules:{}},t.getRules().forEach(((t,i)=>{t.meta.docs.recommended&&(e.rules[i]=2)}))),i=>{let{state:n}=i,s=[];for(let{from:i,to:r}of ip.findRegions(n)){let o=n.doc.lineAt(i),a={line:o.number-1,col:i-o.from,pos:i};for(let o of t.verify(n.sliceDoc(i,r),e))s.push(pp(o,n.doc,a))}return s}},javascript:cp,javascriptLanguage:ip,jsxLanguage:rp,localCompletionSource:Jd,scopeCompletionSource:function(t){let e=new Map;return i=>{let n=ep(i);if(!n)return null;let s=t;for(let t of n.path)if(s=s[t],!s)return null;let r=e.get(s);return r||e.set(s,r=function(t,e){let i=[],n=new Set;for(let s=0;;s++){for(let r of(Object.getOwnPropertyNames||Object.keys)(t)){if(!/^[a-zA-Z_$\xaa-\uffdc][\w$\xaa-\uffdc]*$/.test(r)||n.has(r))continue;let o;n.add(r);try{o=t[r]}catch(t){continue}i.push({label:r,type:"function"==typeof o?/^[A-Z]/.test(r)?"class":e?"function":"method":e?"variable":"property",boost:-s})}let r=Object.getPrototypeOf(t);if(!r)return i;t=r}}(s,!n.path.length)),{from:i.pos-n.name.length,options:r,validFor:Fd}}},snippets:Wd,tsxLanguage:op,typescriptLanguage:sp,typescriptSnippets:Ld});const gp=["_blank","_self","_top","_parent"],bp=["ascii","utf-8","utf-16","latin1","latin1"],Qp=["get","post","put","delete"],Sp=["application/x-www-form-urlencoded","multipart/form-data","text/plain"],vp=["true","false"],wp={},yp={a:{attrs:{href:null,ping:null,type:null,media:null,target:gp,hreflang:null}},abbr:wp,address:wp,area:{attrs:{alt:null,coords:null,href:null,target:null,ping:null,media:null,hreflang:null,type:null,shape:["default","rect","circle","poly"]}},article:wp,aside:wp,audio:{attrs:{src:null,mediagroup:null,crossorigin:["anonymous","use-credentials"],preload:["none","metadata","auto"],autoplay:["autoplay"],loop:["loop"],controls:["controls"]}},b:wp,base:{attrs:{href:null,target:gp}},bdi:wp,bdo:wp,blockquote:{attrs:{cite:null}},body:wp,br:wp,button:{attrs:{form:null,formaction:null,name:null,value:null,autofocus:["autofocus"],disabled:["autofocus"],formenctype:Sp,formmethod:Qp,formnovalidate:["novalidate"],formtarget:gp,type:["submit","reset","button"]}},canvas:{attrs:{width:null,height:null}},caption:wp,center:wp,cite:wp,code:wp,col:{attrs:{span:null}},colgroup:{attrs:{span:null}},command:{attrs:{type:["command","checkbox","radio"],label:null,icon:null,radiogroup:null,command:null,title:null,disabled:["disabled"],checked:["checked"]}},data:{attrs:{value:null}},datagrid:{attrs:{disabled:["disabled"],multiple:["multiple"]}},datalist:{attrs:{data:null}},dd:wp,del:{attrs:{cite:null,datetime:null}},details:{attrs:{open:["open"]}},dfn:wp,div:wp,dl:wp,dt:wp,em:wp,embed:{attrs:{src:null,type:null,width:null,height:null}},eventsource:{attrs:{src:null}},fieldset:{attrs:{disabled:["disabled"],form:null,name:null}},figcaption:wp,figure:wp,footer:wp,form:{attrs:{action:null,name:null,"accept-charset":bp,autocomplete:["on","off"],enctype:Sp,method:Qp,novalidate:["novalidate"],target:gp}},h1:wp,h2:wp,h3:wp,h4:wp,h5:wp,h6:wp,head:{children:["title","base","link","style","meta","script","noscript","command"]},header:wp,hgroup:wp,hr:wp,html:{attrs:{manifest:null}},i:wp,iframe:{attrs:{src:null,srcdoc:null,name:null,width:null,height:null,sandbox:["allow-top-navigation","allow-same-origin","allow-forms","allow-scripts"],seamless:["seamless"]}},img:{attrs:{alt:null,src:null,ismap:null,usemap:null,width:null,height:null,crossorigin:["anonymous","use-credentials"]}},input:{attrs:{alt:null,dirname:null,form:null,formaction:null,height:null,list:null,max:null,maxlength:null,min:null,name:null,pattern:null,placeholder:null,size:null,src:null,step:null,value:null,width:null,accept:["audio/*","video/*","image/*"],autocomplete:["on","off"],autofocus:["autofocus"],checked:["checked"],disabled:["disabled"],formenctype:Sp,formmethod:Qp,formnovalidate:["novalidate"],formtarget:gp,multiple:["multiple"],readonly:["readonly"],required:["required"],type:["hidden","text","search","tel","url","email","password","datetime","date","month","week","time","datetime-local","number","range","color","checkbox","radio","file","submit","image","reset","button"]}},ins:{attrs:{cite:null,datetime:null}},kbd:wp,keygen:{attrs:{challenge:null,form:null,name:null,autofocus:["autofocus"],disabled:["disabled"],keytype:["RSA"]}},label:{attrs:{for:null,form:null}},legend:wp,li:{attrs:{value:null}},link:{attrs:{href:null,type:null,hreflang:null,media:null,sizes:["all","16x16","16x16 32x32","16x16 32x32 64x64"]}},map:{attrs:{name:null}},mark:wp,menu:{attrs:{label:null,type:["list","context","toolbar"]}},meta:{attrs:{content:null,charset:bp,name:["viewport","application-name","author","description","generator","keywords"],"http-equiv":["content-language","content-type","default-style","refresh"]}},meter:{attrs:{value:null,min:null,low:null,high:null,max:null,optimum:null}},nav:wp,noscript:wp,object:{attrs:{data:null,type:null,name:null,usemap:null,form:null,width:null,height:null,typemustmatch:["typemustmatch"]}},ol:{attrs:{reversed:["reversed"],start:null,type:["1","a","A","i","I"]},children:["li","script","template","ul","ol"]},optgroup:{attrs:{disabled:["disabled"],label:null}},option:{attrs:{disabled:["disabled"],label:null,selected:["selected"],value:null}},output:{attrs:{for:null,form:null,name:null}},p:wp,param:{attrs:{name:null,value:null}},pre:wp,progress:{attrs:{value:null,max:null}},q:{attrs:{cite:null}},rp:wp,rt:wp,ruby:wp,samp:wp,script:{attrs:{type:["text/javascript"],src:null,async:["async"],defer:["defer"],charset:bp}},section:wp,select:{attrs:{form:null,name:null,size:null,autofocus:["autofocus"],disabled:["disabled"],multiple:["multiple"]}},slot:{attrs:{name:null}},small:wp,source:{attrs:{src:null,type:null,media:null}},span:wp,strong:wp,style:{attrs:{type:["text/css"],media:null,scoped:null}},sub:wp,summary:wp,sup:wp,table:wp,tbody:wp,td:{attrs:{colspan:null,rowspan:null,headers:null}},template:wp,textarea:{attrs:{dirname:null,form:null,maxlength:null,name:null,placeholder:null,rows:null,cols:null,autofocus:["autofocus"],disabled:["disabled"],readonly:["readonly"],required:["required"],wrap:["soft","hard"]}},tfoot:wp,th:{attrs:{colspan:null,rowspan:null,headers:null,scope:["row","col","rowgroup","colgroup"]}},thead:wp,time:{attrs:{datetime:null}},title:wp,tr:wp,track:{attrs:{src:null,label:null,default:null,kind:["subtitles","captions","descriptions","chapters","metadata"],srclang:null}},ul:{children:["li","script","template","ul","ol"]},var:wp,video:{attrs:{src:null,poster:null,width:null,height:null,crossorigin:["anonymous","use-credentials"],preload:["auto","metadata","none"],autoplay:["autoplay"],mediagroup:["movie"],muted:["muted"],controls:["controls"]}},wbr:wp},xp={accesskey:null,class:null,contenteditable:vp,contextmenu:null,dir:["ltr","rtl","auto"],draggable:["true","false","auto"],dropzone:["copy","move","link","string:","file:"],hidden:["hidden"],id:null,inert:["inert"],itemid:null,itemprop:null,itemref:null,itemscope:["itemscope"],itemtype:null,lang:["ar","bn","de","en-GB","en-US","es","fr","hi","id","ja","pa","pt","ru","tr","zh"],spellcheck:vp,autocorrect:vp,autocapitalize:vp,style:null,tabindex:null,title:null,translate:["yes","no"],rel:["stylesheet","alternate","author","bookmark","help","license","next","nofollow","noreferrer","prefetch","prev","search","tag"],role:"alert application article banner button cell checkbox complementary contentinfo dialog document feed figure form grid gridcell heading img list listbox listitem main navigation region row rowgroup search switch tab table tabpanel textbox timer".split(" "),"aria-activedescendant":null,"aria-atomic":vp,"aria-autocomplete":["inline","list","both","none"],"aria-busy":vp,"aria-checked":["true","false","mixed","undefined"],"aria-controls":null,"aria-describedby":null,"aria-disabled":vp,"aria-dropeffect":null,"aria-expanded":["true","false","undefined"],"aria-flowto":null,"aria-grabbed":["true","false","undefined"],"aria-haspopup":vp,"aria-hidden":vp,"aria-invalid":["true","false","grammar","spelling"],"aria-label":null,"aria-labelledby":null,"aria-level":null,"aria-live":["off","polite","assertive"],"aria-multiline":vp,"aria-multiselectable":vp,"aria-owns":null,"aria-posinset":null,"aria-pressed":["true","false","mixed","undefined"],"aria-readonly":vp,"aria-relevant":null,"aria-required":vp,"aria-selected":["true","false","undefined"],"aria-setsize":null,"aria-sort":["ascending","descending","none","other"],"aria-valuemax":null,"aria-valuemin":null,"aria-valuenow":null,"aria-valuetext":null},kp="beforeunload copy cut dragstart dragover dragleave dragenter dragend drag paste focus blur change click load mousedown mouseenter mouseleave mouseup keydown keyup resize scroll unload".split(" ").map((t=>"on"+t));for(let t of kp)xp[t]=null;class Pp{constructor(t,e){this.tags=Object.assign(Object.assign({},yp),t),this.globalAttrs=Object.assign(Object.assign({},xp),e),this.allTags=Object.keys(this.tags),this.globalAttrNames=Object.keys(this.globalAttrs)}}function $p(t,e,i=t.length){if(!e)return"";let n=e.firstChild,s=n&&n.getChild("TagName");return s?t.sliceString(s.from,Math.min(s.to,i)):""}function Zp(t,e=!1){for(;t;t=t.parent)if("Element"==t.name){if(!e)return t;e=!1}return null}function Tp(t,e,i){let n=i.tags[$p(t,Zp(e))];return(null==n?void 0:n.children)||i.allTags}function Xp(t,e){let i=[];for(let n=Zp(e);n&&!n.type.isTop;n=Zp(n.parent)){let s=$p(t,n);if(s&&"CloseTag"==n.lastChild.name)break;s&&i.indexOf(s)<0&&("EndTag"==e.name||e.from>=n.firstChild.to)&&i.push(s)}return i}Pp.default=new Pp;const Rp=/^[:\-\.\w\u00b7-\uffff]*$/;function Ap(t,e,i,n,s){let r=/\s*>/.test(t.sliceDoc(s,s+5))?"":">",o=Zp(i,!0);return{from:n,to:s,options:Tp(t.doc,o,e).map((t=>({label:t,type:"type"}))).concat(Xp(t.doc,i).map(((t,e)=>({label:"/"+t,apply:"/"+t+r,type:"type",boost:99-e})))),validFor:/^\/?[:\-\.\w\u00b7-\uffff]*$/}}function Cp(t,e,i,n){let s=/\s*>/.test(t.sliceDoc(n,n+5))?"":">";return{from:i,to:n,options:Xp(t.doc,e).map(((t,e)=>({label:t,apply:t+s,type:"type",boost:99-e}))),validFor:Rp}}function Mp(t,e){let{state:i,pos:n}=e,s=Wa(i).resolveInner(n,-1),r=s.resolve(n);for(let t,e=n;r==s&&(t=s.childBefore(e));){let i=t.lastChild;if(!i||!i.type.isError||i.from<i.to)break;r=s=t,e=i.from}return"TagName"==s.name?s.parent&&/CloseTag$/.test(s.parent.name)?Cp(i,s,s.from,n):Ap(i,t,s,s.from,n):"StartTag"==s.name?Ap(i,t,s,n,n):"StartCloseTag"==s.name||"IncompleteCloseTag"==s.name?Cp(i,s,n,n):e.explicit&&("OpenTag"==s.name||"SelfClosingTag"==s.name)||"AttributeName"==s.name?function(t,e,i,n,s){let r=Zp(i),o=r?e.tags[$p(t.doc,r)]:null,a=o&&o.attrs?Object.keys(o.attrs):[];return{from:n,to:s,options:(o&&!1===o.globalAttrs?a:a.length?a.concat(e.globalAttrNames):e.globalAttrNames).map((t=>({label:t,type:"property"}))),validFor:Rp}}(i,t,s,"AttributeName"==s.name?s.from:n,n):"Is"==s.name||"AttributeValue"==s.name||"UnquotedAttributeValue"==s.name?function(t,e,i,n,s){var r;let o,a=null===(r=i.parent)||void 0===r?void 0:r.getChild("AttributeName"),l=[];if(a){let r=t.sliceDoc(a.from,a.to),h=e.globalAttrs[r];if(!h){let n=Zp(i),s=n?e.tags[$p(t.doc,n)]:null;h=(null==s?void 0:s.attrs)&&s.attrs[r]}if(h){let e=t.sliceDoc(n,s).toLowerCase(),i='"',r='"';/^['"]/.test(e)?(o='"'==e[0]?/^[^"]*$/:/^[^']*$/,i="",r=t.sliceDoc(s,s+1)==e[0]?"":e[0],e=e.slice(1),n++):o=/^[^\s<>='"]*$/;for(let t of h)l.push({label:t,apply:i+t+r,type:"constant"})}}return{from:n,to:s,options:l,validFor:o}}(i,t,s,"Is"==s.name?n:s.from,n):!e.explicit||"Element"!=r.name&&"Text"!=r.name&&"Document"!=r.name?null:function(t,e,i,n){let s=[],r=0;for(let n of Tp(t.doc,i,e))s.push({label:"<"+n,type:"type"});for(let e of Xp(t.doc,i))s.push({label:"</"+e+">",type:"type",boost:99-r++});return{from:n,to:n,options:s,validFor:/^<\/?[:\-\.\w\u00b7-\uffff]*$/}}(i,t,s,n)}function qp(t){return Mp(Pp.default,t)}function Vp(t){let{extraTags:e,extraGlobalAttributes:i}=t,n=i||e?new Pp(e,i):Pp.default;return t=>Mp(n,t)}const Yp=ip.parser.configure({top:"SingleExpression"}),zp=[{tag:"script",attrs:t=>"text/typescript"==t.type||"ts"==t.lang,parser:sp.parser},{tag:"script",attrs:t=>"text/babel"==t.type||"text/jsx"==t.type,parser:rp.parser},{tag:"script",attrs:t=>"text/typescript-jsx"==t.type,parser:op.parser},{tag:"script",attrs:t=>/^(importmap|speculationrules|application\/(.+\+)?json)$/i.test(t.type),parser:Yp},{tag:"script",attrs:t=>!t.type||/^(?:text|application)\/(?:x-)?(?:java|ecma)script$|^module$|^$/i.test(t.type),parser:ip.parser},{tag:"style",attrs:t=>(!t.lang||"css"==t.lang)&&(!t.type||/^(text\/)?(x-)?(stylesheet|css)$/i.test(t.type)),parser:Yc.parser}],_p=[{name:"style",parser:Yc.parser.configure({top:"Styles"})}].concat(kp.map((t=>({name:t,parser:ip.parser})))),jp=Da.define({name:"html",parser:kd.configure({props:[cl.add({Element(t){let e=/^(\s*)(<\/)?/.exec(t.textAfter);return t.node.to<=t.pos+e[0].length?t.continue():t.lineIndent(t.node.from)+(e[2]?0:t.unit)},"OpenTag CloseTag SelfClosingTag":t=>t.column(t.node.from)+t.unit,Document(t){if(t.pos+/\s*/.exec(t.textAfter)[0].length<t.node.to)return t.continue();let e,i=null;for(let e=t.node;;){let t=e.lastChild;if(!t||"Element"!=t.name||t.to!=e.to)break;i=e=t}return i&&(!(e=i.lastChild)||"CloseTag"!=e.name&&"SelfClosingTag"!=e.name)?t.lineIndent(i.from)+t.unit:null}}),wl.add({Element(t){let e=t.firstChild,i=t.lastChild;return e&&"OpenTag"==e.name?{from:e.to,to:"CloseTag"==i.name?i.from:t.to}:null}}),lh.add({"OpenTag CloseTag":t=>t.getChild("TagName")})]}),languageData:{commentTokens:{block:{open:"\x3c!--",close:"--\x3e"}},indentOnInput:/^\s*<\/\w+\W$/,wordChars:"-._"}}),Ep=jp.configure({wrap:Td(zp,_p)});function Dp(t={}){let e,i="";!1===t.matchClosingTags&&(i="noMatch"),!0===t.selfClosingTags&&(i=(i?i+" ":"")+"selfClosing"),(t.nestedLanguages&&t.nestedLanguages.length||t.nestedAttributes&&t.nestedAttributes.length)&&(e=Td((t.nestedLanguages||[]).concat(zp),(t.nestedAttributes||[]).concat(_p)));let n=e?jp.configure({wrap:e,dialect:i}):i?Ep.configure({dialect:i}):Ep;return new il(n,[Ep.data.of({autocomplete:Vp(t)}),!1!==t.autoCloseTags?Lp:[],cp().support,zc().support])}const Wp=new Set("area base br col command embed frame hr img input keygen link meta param source track wbr menuitem".split(" ")),Lp=zr.inputHandler.of(((t,e,i,n,s)=>{if(t.composing||t.state.readOnly||e!=i||">"!=n&&"/"!=n||!Ep.isActiveAt(t.state,e,-1))return!1;let r=s(),{state:o}=r,a=o.changeByRange((t=>{var e,i,s;let r,a=o.doc.sliceString(t.from-1,t.to)==n,{head:l}=t,h=Wa(o).resolveInner(l-1,-1);if("TagName"!=h.name&&"StartTag"!=h.name||(h=h.parent),a&&">"==n&&"OpenTag"==h.name){if("CloseTag"!=(null===(i=null===(e=h.parent)||void 0===e?void 0:e.lastChild)||void 0===i?void 0:i.name)&&(r=$p(o.doc,h.parent,l))&&!Wp.has(r)){return{range:t,changes:{from:l,to:l+(">"===o.doc.sliceString(l,l+1)?1:0),insert:`</${r}>`}}}}else if(a&&"/"==n&&"IncompleteCloseTag"==h.name){let t=h.parent;if(h.from==l-2&&"CloseTag"!=(null===(s=t.lastChild)||void 0===s?void 0:s.name)&&(r=$p(o.doc,t,l))&&!Wp.has(r)){let t=l+(">"===o.doc.sliceString(l,l+1)?1:0),e=`${r}>`;return{range:Tt.cursor(l+e.length,-1),changes:{from:l,to:t,insert:e}}}}return{range:t}}));return!a.changes.empty&&(t.dispatch([r,o.update(a,{userEvent:"input.complete",scrollIntoView:!0})]),!0)}));var Gp=Object.freeze({__proto__:null,autoCloseTags:Lp,html:Dp,htmlCompletionSource:qp,htmlCompletionSourceWith:Vp,htmlLanguage:Ep,htmlPlain:jp});const Bp="function"==typeof String.prototype.normalize?t=>t.normalize("NFKD"):t=>t;class Up{constructor(t,e,i=0,n=t.length,s,r){this.test=r,this.value={from:0,to:0},this.done=!1,this.matches=[],this.buffer="",this.bufferPos=0,this.iter=t.iterRange(i,n),this.bufferStart=i,this.normalize=s?t=>s(Bp(t)):Bp,this.query=this.normalize(e)}peek(){if(this.bufferPos==this.buffer.length){if(this.bufferStart+=this.buffer.length,this.iter.next(),this.iter.done)return-1;this.bufferPos=0,this.buffer=this.iter.value}return pt(this.buffer,this.bufferPos)}next(){for(;this.matches.length;)this.matches.pop();return this.nextOverlapping()}nextOverlapping(){for(;;){let t=this.peek();if(t<0)return this.done=!0,this;let e=mt(t),i=this.bufferStart+this.bufferPos;this.bufferPos+=gt(t);let n=this.normalize(e);for(let t=0,s=i;;t++){let r=n.charCodeAt(t),o=this.match(r,s);if(t==n.length-1){if(o)return this.value=o,this;break}s==i&&t<e.length&&e.charCodeAt(t)==r&&s++}}}match(t,e){let i=null;for(let n=0;n<this.matches.length;n+=2){let s=this.matches[n],r=!1;this.query.charCodeAt(s)==t&&(s==this.query.length-1?i={from:this.matches[n+1],to:e+1}:(this.matches[n]++,r=!0)),r||(this.matches.splice(n,2),n-=2)}return this.query.charCodeAt(0)==t&&(1==this.query.length?i={from:e,to:e+1}:this.matches.push(1,e)),i&&this.test&&!this.test(i.from,i.to,this.buffer,this.bufferStart)&&(i=null),i}}"undefined"!=typeof Symbol&&(Up.prototype[Symbol.iterator]=function(){return this});const Ip={highlightWordAroundCursor:!1,minSelectionLength:1,maxMatches:100,wholeWords:!1},Np=At.define({combine:t=>ve(t,Ip,{highlightWordAroundCursor:(t,e)=>t||e,minSelectionLength:Math.min,maxMatches:Math.min})});function Hp(t){let e=[em,tm];return t&&e.push(Np.of(t)),e}const Fp=Fi.mark({class:"cm-selectionMatch"}),Kp=Fi.mark({class:"cm-selectionMatch cm-selectionMatch-main"});function Jp(t,e,i,n){return!(0!=i&&t(e.sliceDoc(i-1,i))==me.Word||n!=e.doc.length&&t(e.sliceDoc(n,n+1))==me.Word)}const tm=En.fromClass(class{constructor(t){this.decorations=this.getDeco(t)}update(t){(t.selectionSet||t.docChanged||t.viewportChanged)&&(this.decorations=this.getDeco(t.view))}getDeco(t){let e=t.state.facet(Np),{state:i}=t,n=i.selection;if(n.ranges.length>1)return Fi.none;let s,r=n.main,o=null;if(r.empty){if(!e.highlightWordAroundCursor)return Fi.none;let t=i.wordAt(r.head);if(!t)return Fi.none;o=i.charCategorizer(r.head),s=i.sliceDoc(t.from,t.to)}else{let t=r.to-r.from;if(t<e.minSelectionLength||t>200)return Fi.none;if(e.wholeWords){if(s=i.sliceDoc(r.from,r.to),o=i.charCategorizer(r.head),!Jp(o,i,r.from,r.to)||!function(t,e,i,n){return t(e.sliceDoc(i,i+1))==me.Word&&t(e.sliceDoc(n-1,n))==me.Word}(o,i,r.from,r.to))return Fi.none}else if(s=i.sliceDoc(r.from,r.to).trim(),!s)return Fi.none}let a=[];for(let n of t.visibleRanges){let t=new Up(i.doc,s,n.from,n.to);for(;!t.next().done;){let{from:n,to:s}=t.value;if((!o||Jp(o,i,n,s))&&(r.empty&&n<=r.from&&s>=r.to?a.push(Kp.range(n,s)):(n>=r.to||s<=r.from)&&a.push(Fp.range(n,s)),a.length>e.maxMatches))return Fi.none}}return Fi.set(a)}},{decorations:t=>t.decorations}),em=zr.baseTheme({".cm-selectionMatch":{backgroundColor:"#99ff7780"},".cm-searchMatch .cm-selectionMatch":{backgroundColor:"transparent"}});const im=({state:t,dispatch:e})=>{let{ranges:i}=t.selection;if(i.some((t=>t.from===t.to)))return(({state:t,dispatch:e})=>{let{selection:i}=t,n=Tt.create(i.ranges.map((e=>t.wordAt(e.head)||Tt.cursor(e.head))),i.mainIndex);return!n.eq(i)&&(e(t.update({selection:n})),!0)})({state:t,dispatch:e});let n=t.sliceDoc(i[0].from,i[0].to);if(t.selection.ranges.some((e=>t.sliceDoc(e.from,e.to)!=n)))return!1;let s=function(t,e){let{main:i,ranges:n}=t.selection,s=t.wordAt(i.head),r=s&&s.from==i.from&&s.to==i.to;for(let i=!1,s=new Up(t.doc,e,n[n.length-1].to);;){if(s.next(),!s.done){if(i&&n.some((t=>t.from==s.value.from)))continue;if(r){let e=t.wordAt(s.value.from);if(!e||e.from!=s.value.from||e.to!=s.value.to)continue}return s.value}if(i)return null;s=new Up(t.doc,e,0,Math.max(0,n[n.length-1].from-1)),i=!0}}(t,n);return!!s&&(e(t.update({selection:t.selection.addRange(Tt.range(s.from,s.to),!1),effects:zr.scrollIntoView(s.to)})),!0)};function nm(t){const e=t.selection.main.head;return t.doc.lineAt(e)}function sm(t,e){let i=0;t:for(let n=0;n<t.length;n++)switch(t[n]){case" ":i+=1;continue t;case"\t":i+=e-i%e;continue t;case"\r":continue t;default:break t}return i}const rm=At.define({combine:t=>ve(t,{highlightActiveBlock:!0,hideFirstIndent:!1,markerType:"fullScope",thickness:1})});class om{constructor(t,e,i,n){this.lines=t,this.state=e,this.map=new Map,this.unitWidth=i,this.markerType=n;for(const t of this.lines)this.add(t);this.state.facet(rm).highlightActiveBlock&&this.findAndSetActiveLines()}has(t){return this.map.has("number"==typeof t?t:t.number)}get(t){const e=this.map.get("number"==typeof t?t:t.number);if(!e)throw new Error("Line not found in indentation map");return e}set(t,e,i){const n=!t.text.trim().length,s={line:t,col:e,level:i,empty:n};return this.map.set(s.line.number,s),s}add(t){if(this.has(t))return this.get(t);if(!t.length||!t.text.trim().length){if(1===t.number)return this.set(t,0,0);if(t.number===this.state.doc.lines){const e=this.closestNonEmpty(t,-1);return this.set(t,0,e.level)}const e=this.closestNonEmpty(t,-1),i=this.closestNonEmpty(t,1);return e.level>=i.level&&"codeOnly"!==this.markerType?this.set(t,0,e.level):e.empty&&0===e.level&&0!==i.level?this.set(t,0,0):i.level>e.level?this.set(t,0,e.level+1):this.set(t,0,i.level)}const e=sm(t.text,this.state.tabSize),i=Math.floor(e/this.unitWidth);return this.set(t,e,i)}closestNonEmpty(t,e){let i=t.number+e;for(;-1===e?i>=1:i<=this.state.doc.lines;){if(this.has(i)){const t=this.get(i);if(!t.empty)return t}const t=this.state.doc.line(i);if(t.text.trim().length){const e=sm(t.text,this.state.tabSize),i=Math.floor(e/this.unitWidth);return this.set(t,e,i)}i+=e}const n=this.state.doc.line(-1===e?1:this.state.doc.lines);return this.set(n,0,0)}findAndSetActiveLines(){const t=nm(this.state);if(!this.has(t))return;let e,i,n=this.get(t);if(this.has(n.line.number+1)){const t=this.get(n.line.number+1);t.level>n.level&&(n=t)}if(this.has(n.line.number-1)){const t=this.get(n.line.number-1);t.level>n.level&&(n=t)}if(0!==n.level){for(n.active=n.level,e=n.line.number;e>1;e--){if(!this.has(e-1))continue;const t=this.get(e-1);if(t.level<n.level)break;t.active=n.level}for(i=n.line.number;i<this.state.doc.lines;i++){if(!this.has(i+1))continue;const t=this.get(i+1);if(t.level<n.level)break;t.active=n.level}}}}function am(t){const e={light:"#F0F1F2",dark:"#2B3245",activeLight:"#E4E5E6",activeDark:"#3C445C"};let i=e;return t&&(i=Object.assign(Object.assign({},e),t)),zr.baseTheme({"&light":{"--indent-marker-bg-color":i.light,"--indent-marker-active-bg-color":i.activeLight},"&dark":{"--indent-marker-bg-color":i.dark,"--indent-marker-active-bg-color":i.activeDark},".cm-line":{position:"relative"},".cm-indent-markers::before":{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,background:"var(--indent-markers)",pointerEvents:"none",zIndex:"-1"}})}function lm(t,e,i,n,s){return`${`repeating-linear-gradient(to right, var(${t}) 0 ${e}px, transparent ${e}px ${i}ch)`} ${n*i}.5ch/calc(${i*s}ch - 1px) no-repeat`}function hm(t,e,i,n){const{level:s,active:r}=t;if(i&&0===s)return[];const o=i?1:0,a=[];if(void 0!==r){const t=r-o-1;t>0&&a.push(lm("--indent-marker-bg-color",n,e,o,t)),a.push(lm("--indent-marker-active-bg-color",n,e,r-1,1)),r!==s&&a.push(lm("--indent-marker-bg-color",n,e,r,s-r))}else a.push(lm("--indent-marker-bg-color",n,e,o,s-o));return a.join(",")}class cm{constructor(t){this.view=t,this.unitWidth=ol(t.state),this.currentLineNumber=nm(t.state).number,this.generate(t.state)}update(t){const e=ol(t.state),i=e!==this.unitWidth;i&&(this.unitWidth=e);const n=nm(t.state).number,s=n!==this.currentLineNumber;this.currentLineNumber=n;const r=t.state.facet(rm).highlightActiveBlock&&s;(t.docChanged||t.viewportChanged||i||r)&&this.generate(t.state)}generate(t){const e=new $e,i=function(t,e=t.state){const i=new Set;for(const{from:n,to:s}of t.visibleRanges){let t=n;for(;t<=s;){const n=e.doc.lineAt(t);i.has(n)||i.add(n),t=n.to+1}}return i}(this.view,t),{hideFirstIndent:n,markerType:s,thickness:r}=t.facet(rm),o=new om(i,t,this.unitWidth,s);for(const t of i){const i=o.get(t.number);if(!(null==i?void 0:i.level))continue;const s=hm(i,this.unitWidth,n,r);e.add(t.from,t.from,Fi.line({class:"cm-indent-markers",attributes:{style:`--indent-markers: ${s}`}}))}this.decorations=e.finish()}}function Om(t={}){return[rm.of(t),am(t.colors),En.fromClass(cm,{decorations:t=>t.decorations})]}function um(){return import("./angular.js")}async function fm(){return gh.define((await import("./legacy.js").then((function(t){return t.c}))).clojure)}async function dm(){return gh.define((await import("./legacy.js").then((function(t){return t.a}))).coffeeScript)}function pm(){return import("./cpp.js")}const mm=Da.define({name:Yc.name,parser:Yc.parser,languageData:{commentTokens:{block:{open:"/*",close:"*/"}},indentOnInput:/^\s*\}$/,wordChars:""}}),gm={cssCompletionSource:Vc,cssLanguage:mm,css:()=>new il(mm,mm.data.of({autocomplete:Vc}))};async function bm(){return gh.define((await import("./legacy.js").then((function(t){return t.b}))).dart)}async function Qm(){return gh.define((await import("./legacy.js").then((function(t){return t.d}))).gss)}async function Sm(){return gh.define((await import("./legacy.js").then((function(t){return t.g}))).go)}function vm(){return import("./java.js")}async function wm(){return gh.define((await import("./legacy.js").then((function(t){return t.b}))).kotlin)}function ym(){return import("./less.js")}function xm(){return import("./markdown.js")}function km(){return import("./php.js")}function Pm(){return import("./python.js")}function $m(){return import("./sass.js")}async function Zm(){return gh.define((await import("./legacy.js").then((function(t){return t.b}))).scala)}async function Tm(){return gh.define((await import("./legacy.js").then((function(t){return t.s}))).shell)}async function Xm(){return import("./svelte.js")}async function Rm(){return(await import("./legacy.js").then((function(t){return t.d}))).css}function Am(){return import("./vue.js")}function Cm(){return import("./wast.js")}function Mm(){return import("./xml.js")}export{dm as $,za as A,Ya as B,_c as C,vl as D,Nh as E,nl as F,Na as G,lc as H,Dc as I,Ec as J,c as K,Da as L,T as M,o as N,LO as O,R as P,sp as Q,Yc as R,cp as S,Oa as T,zc as U,zr as V,Dd as W,Ih as X,lh as Y,um as Z,fm as _,il as a,dh as a$,pm as a0,gm as a1,bm as a2,Qm as a3,Sm as a4,vm as a5,wm as a6,ym as a7,xm as a8,km as a9,qu as aA,Ku as aB,id as aC,ed as aD,Kf as aE,zu as aF,ju as aG,kf as aH,Rf as aI,Af as aJ,Cf as aK,Mf as aL,nd as aM,yu as aN,Yu as aO,_u as aP,Gp as aQ,mp as aR,ah as aS,zl as aT,qh as aU,La as aV,Ll as aW,ql as aX,Bl as aY,Sl as aZ,gh as a_,Pm as aa,$m as ab,Zm as ac,Tm as ad,Xm as ae,Rm as af,Am as ag,Cm as ah,Mm as ai,vO as aj,pu as ak,eu as al,au as am,yO as an,HO as ao,bu as ap,Su as aq,SO as ar,vu as as,wu as at,wO as au,xf as av,hf as aw,cf as ax,ff as ay,df as az,hc as b,Hl as b0,Ba as b1,Ga as b2,Hp as b3,im as b4,re as b5,oe as b6,St as b7,vt as b8,Ut as b9,To as bA,En as bB,ts as bC,Ni as bD,x as bE,ba as bF,De as bG,Om as bH,At as ba,ut as bb,Qt as bc,ye as bd,Pe as be,$e as bf,Zt as bg,le as bh,ae as bi,_t as bj,st as bk,he as bl,Fi as bm,so as bn,Io as bo,Lo as bp,Ho as bq,Qo as br,na as bs,la as bt,uo as bu,Po as bv,zo as bw,xo as bx,Wo as by,Vo as bz,Ql as c,ml as d,wl as e,bl as f,yl as g,Dp as h,cl as i,ip as j,qc as k,n as l,a as m,O as n,Wa as o,C as p,Tt as q,ze as r,da as s,qa as t,Gt as u,Br as v,qp as w,Se as x,ja as y,rl as z};
//# sourceMappingURL=codemirror.js.map
