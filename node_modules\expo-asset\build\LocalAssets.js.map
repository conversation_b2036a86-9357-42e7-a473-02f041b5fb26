{"version": 3, "file": "LocalAssets.js", "sourceRoot": "", "sources": ["../src/LocalAssets.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,iBAAiB,CAAC;AAEjD,sDAAsD;AACtD,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;AAErC;;GAEG;AACH,MAAM,UAAU,gBAAgB,CAAC,IAAY,EAAE,IAAmB;IAChE,MAAM,cAAc,GAAG,IAAI,CAAC;IAC5B,MAAM,oBAAoB,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE,EAAE,CAAC;IAErD,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,cAAc,IAAI,WAAW,CAAC,CAAC,CAAC;YACnC,OAAO,WAAW,CAAC,cAAc,CAAC,CAAC;QACrC,CAAC;QACD,KAAK,oBAAoB,IAAI,WAAW,CAAC,CAAC,CAAC;YACzC,gDAAgD;YAChD,OAAO,WAAW,CAAC,oBAAoB,CAAC,CAAC;QAC3C,CAAC;QACD;YACE,OAAO,IAAI,CAAC;IAChB,CAAC;AACH,CAAC", "sourcesContent": ["import { getLocalAssets } from './PlatformUtils';\n\n// localAssets are provided by the expo-updates module\nconst localAssets = getLocalAssets();\n\n/**\n * Returns the URI of a local asset from its hash, or null if the asset is not available locally\n */\nexport function getLocalAssetUri(hash: string, type: string | null): string | null {\n  const localAssetsKey = hash;\n  const legacyLocalAssetsKey = `${hash}.${type ?? ''}`;\n\n  switch (true) {\n    case localAssetsKey in localAssets: {\n      return localAssets[localAssetsKey];\n    }\n    case legacyLocalAssetsKey in localAssets: {\n      // legacy updates store assets with an extension\n      return localAssets[legacyLocalAssetsKey];\n    }\n    default:\n      return null;\n  }\n}\n"]}