import"../icon_button/icon_button.js";import*as o from"../../lit/lit.js";import*as t from"../../visual_logging/visual_logging.js";var r=`*{margin:0;padding:0;box-sizing:border-box}*:focus,\n*:focus-visible,\n:host(:focus),\n:host(:focus-visible){outline:none}:host{display:inline-flex;flex-direction:row;align-items:center}button{--hover-layer-color:var(--sys-color-state-hover-on-subtle);--active-layer-color:var(--sys-color-state-ripple-neutral-on-subtle);--button-border-size:1px;--button-height:var(--sys-size-11);--button-width:fit-content;align-items:center;background:transparent;border-radius:var(--sys-shape-corner-full);cursor:inherit;display:inline-flex;position:relative;font-family:var(--default-font-family);font-size:var(--sys-typescale-body4-size);font-weight:var(--ref-typeface-weight-medium);line-height:var(--sys-typescale-body4-line-height);height:var(--button-height);justify-content:center;padding:0 var(--sys-size-6);white-space:nowrap;width:var(--button-width);&.primary-toggle{--toggle-color:var(--sys-color-primary-bright)}&.red-toggle{--toggle-color:var(--sys-color-error-bright)}devtools-icon{width:var(--icon-size);height:var(--icon-size)}&.toolbar,\n  &.icon,\n  &.only-icon{--button-height:26px;--button-width:26px;--icon-size:var(--sys-size-9);padding:0;border:none;overflow:hidden;&.small{--button-height:var(--sys-size-9);--button-width:var(--sys-size-9);--icon-size:var(--sys-size-8)}&.micro{--button-height:var(--sys-size-8);--button-width:var(--sys-size-8);--icon-size:var(--sys-size-8);border-radius:var(--sys-shape-corner-extra-small)}&.toggled devtools-icon{color:var(--toggle-color);&.long-click{color:var(--icon-default)}}&.checked devtools-icon{color:var(--toggle-color);&::after{content:"";width:var(--sys-size-3);height:var(--sys-size-3);border-radius:var(--sys-shape-corner-full);background-color:var(--sys-color-primary-bright);position:absolute;top:var(--dot-toggle-top);left:var(--dot-toggle-left)}}devtools-icon.long-click{position:absolute;top:2px;left:3px}}&.primary{--hover-layer-color:var(--sys-color-state-hover-on-prominent);--active-layer-color:var(--sys-color-state-ripple-primary);border:var(--button-border-size) solid var(--sys-color-primary);background:var(--sys-color-primary);color:var(--sys-color-on-primary);devtools-icon{color:var(--sys-color-on-primary)}}&.tonal{border:none;background:var(--sys-color-tonal-container);color:var(--sys-color-on-tonal-container);devtools-icon{color:var(--sys-color-on-tonal-container)}}&.primary-toolbar{devtools-icon{color:var(--icon-primary)}}&.text{border:none;color:var(--sys-color-primary);devtools-icon{color:var(--icon-primary)}}&.text-with-icon{padding-left:var(--sys-size-4);devtools-icon{width:var(--sys-size-9);height:var(--sys-size-9);margin-right:var(--sys-size-2)}}&.outlined{border:var(--button-border-size) solid var(--sys-color-tonal-outline);background:transparent;color:var(--sys-color-primary);devtools-icon{color:var(--icon-primary)}}&:disabled{pointer-events:none;color:var(--sys-color-state-disabled);&.primary{border:var(--button-border-size) solid var(--sys-color-state-disabled-container);background:var(--sys-color-state-disabled-container)}&.tonal{border:var(--button-border-size) solid var(--sys-color-state-disabled-container);background:var(--sys-color-state-disabled-container)}&.outlined{border:var(--button-border-size) solid var(--sys-color-state-disabled-container)}&.toolbar,\n    &.icon{background:transparent}devtools-icon{color:var(--icon-disabled)}}&:not(.icon, .toolbar).only-icon{width:100%;padding:0;&.small{width:var(--button-height)}}&:focus-visible{outline:2px solid var(--sys-color-state-focus-ring);outline-offset:2px;z-index:1;&.toolbar,\n    &.icon,\n    &.reduced-focus-ring{outline-offset:-2px}}&:has(.spinner){padding-left:var(--sys-size-4)}&:hover::after{content:"";height:100%;width:100%;border-radius:inherit;position:absolute;top:0;left:0;background-color:var(--hover-layer-color);&.primary{border:var(--button-border-size) solid color-mix(in srgb,var(--sys-color-primary),var(--sys-color-state-hover-on-prominent) 6%)}&.tonal{background:color-mix(in srgb,var(--sys-color-tonal-container),var(--sys-color-state-hover-on-subtle))}&.toobar{devtools-icon{color:var(--icon-default-hover)}}}&:active::before,\n  &.active::before{content:"";height:100%;width:100%;border-radius:inherit;position:absolute;top:0;left:0;background-color:var(--active-layer-color);&.primary{border:var(--button-border-size) solid color-mix(in srgb,var(--sys-color-primary),var(--sys-color-state-ripple-primary) 32%)}&.tonal{background:color-mix(in srgb,var(--sys-color-tonal-container),var(--sys-color-state-ripple-primary) 50%)}&.toolbar{devtools-icon{color:var(--icon-toggled)}}}}.spinner{display:block;width:12px;height:12px;border-radius:6px;border:2px solid var(--sys-color-cdt-base-container);animation:spinner-animation 1s linear infinite;border-right-color:transparent;margin-right:4px;&.outlined{border:2px solid var(--sys-color-primary);border-right-color:transparent}&.disabled{border:2px solid var(--sys-color-state-disabled);border-right-color:transparent}}@keyframes spinner-animation{from{transform:rotate(0)}to{transform:rotate(360deg)}}\n/*# sourceURL=${import.meta.resolve("./button.css")} */\n`;const{html:e,Directives:{ifDefined:i,ref:s,classMap:n}}=o;class a extends HTMLElement{static formAssociated=!0;#o=this.attachShadow({mode:"open",delegatesFocus:!0});#t=this.#r.bind(this);#e={size:"REGULAR",variant:"primary",toggleOnClick:!0,disabled:!1,active:!1,spinner:!1,type:"button",longClickable:!1};#i=this.attachInternals();#s=o.Directives.createRef();constructor(){super(),this.setAttribute("role","presentation"),this.addEventListener("click",this.#t,!0)}cloneNode(o){const t=super.cloneNode(o);return Object.assign(t.#e,this.#e),t.#n(),t}set data(o){this.#e.variant=o.variant,this.#e.iconName=o.iconName,this.#e.toggledIconName=o.toggledIconName,this.#e.toggleOnClick=void 0===o.toggleOnClick||o.toggleOnClick,this.#e.size="REGULAR","size"in o&&o.size&&(this.#e.size=o.size),this.#e.active=Boolean(o.active),this.#e.spinner=Boolean("spinner"in o&&o.spinner),this.#e.type="button","type"in o&&o.type&&(this.#e.type=o.type),this.#e.toggled=o.toggled,this.#e.toggleType=o.toggleType,this.#e.checked=o.checked,this.#e.disabled=Boolean(o.disabled),this.#e.title=o.title,this.#e.jslogContext=o.jslogContext,this.#e.longClickable=o.longClickable,this.#n()}set iconName(o){this.#e.iconName=o,this.#n()}set toggledIconName(o){this.#e.toggledIconName=o,this.#n()}set toggleType(o){this.#e.toggleType=o,this.#n()}set variant(o){this.#e.variant=o,this.#n()}set size(o){this.#e.size=o,this.#n()}set reducedFocusRing(o){this.#e.reducedFocusRing=o,this.#n()}set type(o){this.#e.type=o,this.#n()}set title(o){this.#e.title=o,this.#n()}get disabled(){return this.#e.disabled}set disabled(o){this.#a(o),this.#n()}set toggleOnClick(o){this.#e.toggleOnClick=o,this.#n()}set toggled(o){this.#e.toggled=o,this.#n()}get toggled(){return Boolean(this.#e.toggled)}set checked(o){this.#e.checked=o,this.#n()}set active(o){this.#e.active=o,this.#n()}get active(){return this.#e.active}set spinner(o){this.#e.spinner=o,this.#n()}get jslogContext(){return this.#e.jslogContext}set jslogContext(o){this.#e.jslogContext=o,this.#n()}set longClickable(o){this.#e.longClickable=o,this.#n()}#a(o){this.#e.disabled=o,this.#n()}connectedCallback(){this.#n()}#r(o){if(this.#e.disabled)return o.stopPropagation(),void o.preventDefault();this.form&&"submit"===this.#e.type&&(o.preventDefault(),this.form.dispatchEvent(new SubmitEvent("submit",{submitter:this}))),this.form&&"reset"===this.#e.type&&(o.preventDefault(),this.form.reset()),this.#e.toggleOnClick&&"icon_toggle"===this.#e.variant&&this.#e.iconName&&(this.toggled=!this.#e.toggled)}#l(o){"Enter"!==o.key&&" "!==o.key||o.stopPropagation()}#c(){return"toolbar"===this.#e.variant||"primary_toolbar"===this.#e.variant}#n(){const a=this.#s.value?.assignedNodes(),l=!Boolean(a?.length);if(!this.#e.variant)throw new Error("Button requires a variant to be defined");if(this.#c()){if(!this.#e.iconName)throw new Error("Toolbar button requires an icon");if(!l)throw new Error("Toolbar button does not accept children")}if("icon"===this.#e.variant){if(!this.#e.iconName)throw new Error("Icon button requires an icon");if(!l)throw new Error("Icon button does not accept children")}const c=Boolean(this.#e.iconName),p={primary:"primary"===this.#e.variant,tonal:"tonal"===this.#e.variant,outlined:"outlined"===this.#e.variant,text:"text"===this.#e.variant,toolbar:this.#c(),"primary-toolbar":"primary_toolbar"===this.#e.variant,icon:"icon"===this.#e.variant||"icon_toggle"===this.#e.variant||"adorner_icon"===this.#e.variant,"primary-toggle":"primary-toggle"===this.#e.toggleType,"red-toggle":"red-toggle"===this.#e.toggleType,toggled:Boolean(this.#e.toggled),checked:Boolean(this.#e.checked),"text-with-icon":c&&!l,"only-icon":c&&l,micro:"MICRO"===this.#e.size,small:"SMALL"===this.#e.size,"reduced-focus-ring":Boolean(this.#e.reducedFocusRing),active:this.#e.active},d={primary:"primary"===this.#e.variant,outlined:"outlined"===this.#e.variant,disabled:this.#e.disabled,spinner:!0},h=this.#e.jslogContext&&t.action().track({click:!0}).context(this.#e.jslogContext);o.render(e`
        <style>${r}</style>
        <button title=${i(this.#e.title)}
                .disabled=${this.#e.disabled}
                class=${n(p)}
                aria-pressed=${i(this.#e.toggled)}
                jslog=${i(h)}
                @keydown=${this.#l}
        >${c?e`
                <devtools-icon name=${i(this.#e.toggled?this.#e.toggledIconName:this.#e.iconName)}>
                </devtools-icon>`:""}
          ${this.#e.longClickable?e`<devtools-icon name=${"triangle-bottom-right"} class="long-click"
            ></devtools-icon>`:""}
          ${this.#e.spinner?e`<span class=${n(d)}></span>`:""}
          <slot @slotchange=${this.#n} ${s(this.#s)}></slot>
        </button>
      `,this.#o,{host:this})}get value(){return this.#e.value||""}set value(o){this.#e.value=o}get form(){return this.#i.form}get name(){return this.getAttribute("name")}get type(){return this.#e.type}get validity(){return this.#i.validity}get validationMessage(){return this.#i.validationMessage}get willValidate(){return this.#i.willValidate}checkValidity(){return this.#i.checkValidity()}reportValidity(){return this.#i.reportValidity()}}customElements.define("devtools-button",a);var l=Object.freeze({__proto__:null,Button:a}),c={cssText:`.text-button{margin:2px;height:24px;font-size:12px;font-family:var(--default-font-family);border:1px solid var(--sys-color-tonal-outline);border-radius:12px;padding:0 12px;font-weight:500;color:var(--sys-color-primary);background-color:var(--sys-color-cdt-base-container);flex:none;white-space:nowrap}.text-button:disabled{opacity:38%}.text-button:not(:disabled):focus,\n.text-button:not(:disabled):hover,\n.text-button:not(:disabled):active{background-color:var(--sys-color-state-hover-on-subtle)}.text-button:not(:disabled, .primary-button):focus-visible{outline:2px solid var(--sys-color-state-focus-ring);color:var(--sys-color-on-primary);background-color:var(--sys-color-cdt-base-container)}.text-button:not(:disabled, .running):focus,\n.text-button:not(:disabled, .running):hover,\n.text-button:not(:disabled, .running):active{color:var(--sys-color-primary)}.text-button.link-style,\n.text-button.link-style:hover,\n.text-button.link-style:active{background:none;border:none;outline:none;border-radius:2px;margin:0;padding:0!important;font:inherit;cursor:pointer;height:unset}.text-button.primary-button,\n.text-button.primary-button:not(:disabled):focus{background-color:var(--sys-color-primary);border:none;color:var(--sys-color-on-primary)}.text-button.primary-button:not(:disabled):active{background-color:color-mix(in srgb,var(--sys-color-primary),var(--sys-color-state-ripple-primary) 32%);color:var(--sys-color-on-primary)}.text-button.primary-button:not(:disabled):hover{background-color:color-mix(in srgb,var(--sys-color-primary),var(--sys-color-state-hover-on-prominent) 6%);color:var(--sys-color-on-primary)}.text-button.primary-button:not(:disabled):focus-visible{background-color:var(--sys-color-primary);outline-offset:2px;outline:2px solid var(--sys-color-state-focus-ring);color:var(--sys-color-on-primary)}@media (forced-colors: active){.text-button{background-color:ButtonFace;color:ButtonText;border-color:ButtonText}.text-button:disabled{forced-color-adjust:none;opacity:100%;background:ButtonFace;border-color:GrayText;color:GrayText}.text-button:not(:disabled):focus-visible{forced-color-adjust:none;background-color:ButtonFace;color:Highlight!important;border-color:Highlight;outline:2px solid ButtonText;box-shadow:var(--legacy-focus-ring-active-shadow)}.text-button:not(:disabled):hover,\n  .text-button:not(:disabled):active{forced-color-adjust:none;background-color:Highlight;color:HighlightText!important;box-shadow:var(--sys-color-primary)}.text-button.primary-button{forced-color-adjust:none;background-color:Highlight;color:HighlightText;border:1px solid Highlight}.text-button.primary-button:not(:disabled):focus-visible{background-color:Highlight;color:HighlightText!important;border-color:ButtonText}.text-button.primary-button:not(:disabled):hover,\n  .text-button.primary-button:not(:disabled):active{background-color:HighlightText;color:Highlight!important;border-color:Highlight}}\n/*# sourceURL=${import.meta.resolve("./textButton.css")} */\n`};export{l as Button,c as textButtonStyles};
