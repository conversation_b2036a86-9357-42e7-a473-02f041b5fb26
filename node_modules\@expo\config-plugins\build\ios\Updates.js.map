{"version": 3, "file": "Updates.js", "names": ["_BuildProperties", "data", "require", "_iosPlugins", "_withPlugins", "_Updates", "_warnings", "Config", "exports", "withUpdates", "config", "with<PERSON><PERSON><PERSON>", "withUpdatesPlist", "withUpdatesNativeDebugPodfileProps", "createBuildPodfilePropsConfigPlugin", "propName", "propValueGetter", "updates", "useNativeDebug", "undefined", "withExpoPlist", "projectRoot", "modRequest", "expoUpdatesPackageVersion", "getExpoUpdatesPackageVersion", "modResults", "setUpdatesConfigAsync", "expoPlist", "checkOnLaunch", "getUpdatesCheckOnLaunch", "timeout", "getUpdatesTimeout", "useEmbeddedUpdate", "getUpdatesUseEmbeddedUpdate", "addWarningIOS", "newExpoPlist", "ENABLED", "getUpdatesEnabled", "CHECK_ON_LAUNCH", "LAUNCH_WAIT_MS", "UPDATES_HAS_EMBEDDED_UPDATE", "updateUrl", "getUpdateUrl", "UPDATE_URL", "codeSigningCertificate", "getUpdatesCodeSigningCertificate", "CODE_SIGNING_CERTIFICATE", "codeSigningMetadata", "getUpdatesCodeSigningMetadata", "CODE_SIGNING_METADATA", "requestHeaders", "getUpdatesRequestHeaders", "UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY", "disableAntiBrickingMeasures", "getDisableAntiBrickingMeasures", "DISABLE_ANTI_BRICKING_MEASURES", "setVersionsConfigAsync", "runtimeVersion", "getRuntimeVersionNullableAsync", "RUNTIME_VERSION", "Error"], "sources": ["../../src/ios/Updates.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { createBuildPodfilePropsConfigPlugin } from './BuildProperties';\nimport { ExpoPlist } from './IosConfig.types';\nimport { ConfigPlugin } from '../Plugin.types';\nimport { withExpoPlist } from '../plugins/ios-plugins';\nimport { withPlugins } from '../plugins/withPlugins';\nimport {\n  ExpoConfigUpdates,\n  getDisableAntiBrickingMeasures,\n  getExpoUpdatesPackageVersion,\n  getRuntimeVersionNullableAsync,\n  getUpdatesCheckOnLaunch,\n  getUpdatesCodeSigningCertificate,\n  getUpdatesCodeSigningMetadata,\n  getUpdatesRequestHeaders,\n  getUpdatesEnabled,\n  getUpdatesTimeout,\n  getUpdatesUseEmbeddedUpdate,\n  getUpdateUrl,\n} from '../utils/Updates';\nimport { addWarningIOS } from '../utils/warnings';\n\nexport enum Config {\n  ENABLED = 'EXUpdatesEnabled',\n  CHECK_ON_LAUNCH = 'EXUpdatesCheckOnLaunch',\n  LAUNCH_WAIT_MS = 'EXUpdatesLaunchWaitMs',\n  RUNTIME_VERSION = 'EXUpdatesRuntimeVersion',\n  UPDATE_URL = 'EXUpdatesURL',\n  UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY = 'EXUpdatesRequestHeaders',\n  UPDATES_HAS_EMBEDDED_UPDATE = 'EXUpdatesHasEmbeddedUpdate',\n  CODE_SIGNING_CERTIFICATE = 'EXUpdatesCodeSigningCertificate',\n  CODE_SIGNING_METADATA = 'EXUpdatesCodeSigningMetadata',\n  DISABLE_ANTI_BRICKING_MEASURES = 'EXUpdatesDisableAntiBrickingMeasures',\n}\n\n// when making changes to this config plugin, ensure the same changes are also made in eas-cli and build-tools\n// Also ensure the docs are up-to-date: https://docs.expo.dev/bare/installing-updates/\n\nexport const withUpdates: ConfigPlugin = (config) => {\n  return withPlugins(config, [withUpdatesPlist, withUpdatesNativeDebugPodfileProps]);\n};\n\n/**\n * A config-plugin to update `ios/Podfile.properties.json` from the `updates.useNativeDebug` in expo config\n */\nexport const withUpdatesNativeDebugPodfileProps = createBuildPodfilePropsConfigPlugin<ExpoConfig>(\n  [\n    {\n      propName: 'updatesNativeDebug',\n      propValueGetter: (config) => (config?.updates?.useNativeDebug === true ? 'true' : undefined),\n    },\n  ],\n  'withUpdatesNativeDebugPodfileProps'\n);\n\nconst withUpdatesPlist: ConfigPlugin = (config) => {\n  return withExpoPlist(config, async (config) => {\n    const projectRoot = config.modRequest.projectRoot;\n    const expoUpdatesPackageVersion = getExpoUpdatesPackageVersion(projectRoot);\n    config.modResults = await setUpdatesConfigAsync(\n      projectRoot,\n      config,\n      config.modResults,\n      expoUpdatesPackageVersion\n    );\n    return config;\n  });\n};\n\nexport async function setUpdatesConfigAsync(\n  projectRoot: string,\n  config: ExpoConfigUpdates,\n  expoPlist: ExpoPlist,\n  expoUpdatesPackageVersion?: string | null\n): Promise<ExpoPlist> {\n  const checkOnLaunch = getUpdatesCheckOnLaunch(config, expoUpdatesPackageVersion);\n  const timeout = getUpdatesTimeout(config);\n  const useEmbeddedUpdate = getUpdatesUseEmbeddedUpdate(config);\n\n  // TODO: is there a better place for this validation?\n  if (!useEmbeddedUpdate && timeout === 0 && checkOnLaunch !== 'ALWAYS') {\n    addWarningIOS(\n      'updates.useEmbeddedUpdate',\n      `updates.checkOnLaunch should be set to \"ON_LOAD\" and updates.fallbackToCacheTimeout should be set to a non-zero value when updates.useEmbeddedUpdate is set to false. This is because an update must be fetched on the initial launch, when no embedded update is available.`\n    );\n  }\n\n  const newExpoPlist = {\n    ...expoPlist,\n    [Config.ENABLED]: getUpdatesEnabled(config),\n    [Config.CHECK_ON_LAUNCH]: checkOnLaunch,\n    [Config.LAUNCH_WAIT_MS]: timeout,\n  };\n\n  // The native config name is \"has embedded update\", but we want to expose\n  // this to the user as \"use embedded update\", since this is more accurate.\n  // The field does not disable actually building and embedding the update,\n  // only whether it is actually used.\n  if (useEmbeddedUpdate) {\n    delete newExpoPlist[Config.UPDATES_HAS_EMBEDDED_UPDATE];\n  } else {\n    newExpoPlist[Config.UPDATES_HAS_EMBEDDED_UPDATE] = false;\n  }\n\n  const updateUrl = getUpdateUrl(config);\n  if (updateUrl) {\n    newExpoPlist[Config.UPDATE_URL] = updateUrl;\n  } else {\n    delete newExpoPlist[Config.UPDATE_URL];\n  }\n\n  const codeSigningCertificate = getUpdatesCodeSigningCertificate(projectRoot, config);\n  if (codeSigningCertificate) {\n    newExpoPlist[Config.CODE_SIGNING_CERTIFICATE] = codeSigningCertificate;\n  } else {\n    delete newExpoPlist[Config.CODE_SIGNING_CERTIFICATE];\n  }\n\n  const codeSigningMetadata = getUpdatesCodeSigningMetadata(config);\n  if (codeSigningMetadata) {\n    newExpoPlist[Config.CODE_SIGNING_METADATA] = codeSigningMetadata;\n  } else {\n    delete newExpoPlist[Config.CODE_SIGNING_METADATA];\n  }\n\n  const requestHeaders = getUpdatesRequestHeaders(config);\n  if (requestHeaders) {\n    newExpoPlist[Config.UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY] = requestHeaders;\n  } else {\n    delete newExpoPlist[Config.UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY];\n  }\n\n  const disableAntiBrickingMeasures = getDisableAntiBrickingMeasures(config);\n  if (disableAntiBrickingMeasures) {\n    newExpoPlist[Config.DISABLE_ANTI_BRICKING_MEASURES] = disableAntiBrickingMeasures;\n  } else {\n    delete newExpoPlist[Config.DISABLE_ANTI_BRICKING_MEASURES];\n  }\n\n  return await setVersionsConfigAsync(projectRoot, config, newExpoPlist);\n}\n\nexport async function setVersionsConfigAsync(\n  projectRoot: string,\n  config: ExpoConfigUpdates,\n  expoPlist: ExpoPlist\n): Promise<ExpoPlist> {\n  const newExpoPlist = { ...expoPlist };\n\n  const runtimeVersion = await getRuntimeVersionNullableAsync(projectRoot, config, 'ios');\n  if (!runtimeVersion && expoPlist[Config.RUNTIME_VERSION]) {\n    throw new Error(\n      'A runtime version is set in your Expo.plist, but is missing from your Expo app config (app.json/app.config.js). Set runtimeVersion in your Expo app config or remove EXUpdatesRuntimeVersion from your Expo.plist.'\n    );\n  }\n\n  if (runtimeVersion) {\n    delete newExpoPlist['EXUpdatesSDKVersion'];\n    newExpoPlist[Config.RUNTIME_VERSION] = runtimeVersion;\n  } else {\n    delete newExpoPlist['EXUpdatesSDKVersion'];\n    delete newExpoPlist[Config.RUNTIME_VERSION];\n  }\n\n  return newExpoPlist;\n}\n"], "mappings": ";;;;;;;;;AAEA,SAAAA,iBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,gBAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAE,YAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,WAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,aAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,YAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,SAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,QAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAcA,SAAAK,UAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,SAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAkD,IAEtCM,MAAM,GAAAC,OAAA,CAAAD,MAAA,0BAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAAA,OAANA,MAAM;AAAA,OAalB;AACA;AAEO,MAAME,WAAyB,GAAIC,MAAM,IAAK;EACnD,OAAO,IAAAC,0BAAW,EAACD,MAAM,EAAE,CAACE,gBAAgB,EAAEC,kCAAkC,CAAC,CAAC;AACpF,CAAC;;AAED;AACA;AACA;AAFAL,OAAA,CAAAC,WAAA,GAAAA,WAAA;AAGO,MAAMI,kCAAkC,GAAAL,OAAA,CAAAK,kCAAA,GAAG,IAAAC,sDAAmC,EACnF,CACE;EACEC,QAAQ,EAAE,oBAAoB;EAC9BC,eAAe,EAAGN,MAAM,IAAMA,MAAM,EAAEO,OAAO,EAAEC,cAAc,KAAK,IAAI,GAAG,MAAM,GAAGC;AACpF,CAAC,CACF,EACD,oCACF,CAAC;AAED,MAAMP,gBAA8B,GAAIF,MAAM,IAAK;EACjD,OAAO,IAAAU,2BAAa,EAACV,MAAM,EAAE,MAAOA,MAAM,IAAK;IAC7C,MAAMW,WAAW,GAAGX,MAAM,CAACY,UAAU,CAACD,WAAW;IACjD,MAAME,yBAAyB,GAAG,IAAAC,uCAA4B,EAACH,WAAW,CAAC;IAC3EX,MAAM,CAACe,UAAU,GAAG,MAAMC,qBAAqB,CAC7CL,WAAW,EACXX,MAAM,EACNA,MAAM,CAACe,UAAU,EACjBF,yBACF,CAAC;IACD,OAAOb,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAEM,eAAegB,qBAAqBA,CACzCL,WAAmB,EACnBX,MAAyB,EACzBiB,SAAoB,EACpBJ,yBAAyC,EACrB;EACpB,MAAMK,aAAa,GAAG,IAAAC,kCAAuB,EAACnB,MAAM,EAAEa,yBAAyB,CAAC;EAChF,MAAMO,OAAO,GAAG,IAAAC,4BAAiB,EAACrB,MAAM,CAAC;EACzC,MAAMsB,iBAAiB,GAAG,IAAAC,sCAA2B,EAACvB,MAAM,CAAC;;EAE7D;EACA,IAAI,CAACsB,iBAAiB,IAAIF,OAAO,KAAK,CAAC,IAAIF,aAAa,KAAK,QAAQ,EAAE;IACrE,IAAAM,yBAAa,EACX,2BAA2B,EAC3B,8QACF,CAAC;EACH;EAEA,MAAMC,YAAY,GAAG;IACnB,GAAGR,SAAS;IACZ,CAACpB,MAAM,CAAC6B,OAAO,GAAG,IAAAC,4BAAiB,EAAC3B,MAAM,CAAC;IAC3C,CAACH,MAAM,CAAC+B,eAAe,GAAGV,aAAa;IACvC,CAACrB,MAAM,CAACgC,cAAc,GAAGT;EAC3B,CAAC;;EAED;EACA;EACA;EACA;EACA,IAAIE,iBAAiB,EAAE;IACrB,OAAOG,YAAY,CAAC5B,MAAM,CAACiC,2BAA2B,CAAC;EACzD,CAAC,MAAM;IACLL,YAAY,CAAC5B,MAAM,CAACiC,2BAA2B,CAAC,GAAG,KAAK;EAC1D;EAEA,MAAMC,SAAS,GAAG,IAAAC,uBAAY,EAAChC,MAAM,CAAC;EACtC,IAAI+B,SAAS,EAAE;IACbN,YAAY,CAAC5B,MAAM,CAACoC,UAAU,CAAC,GAAGF,SAAS;EAC7C,CAAC,MAAM;IACL,OAAON,YAAY,CAAC5B,MAAM,CAACoC,UAAU,CAAC;EACxC;EAEA,MAAMC,sBAAsB,GAAG,IAAAC,2CAAgC,EAACxB,WAAW,EAAEX,MAAM,CAAC;EACpF,IAAIkC,sBAAsB,EAAE;IAC1BT,YAAY,CAAC5B,MAAM,CAACuC,wBAAwB,CAAC,GAAGF,sBAAsB;EACxE,CAAC,MAAM;IACL,OAAOT,YAAY,CAAC5B,MAAM,CAACuC,wBAAwB,CAAC;EACtD;EAEA,MAAMC,mBAAmB,GAAG,IAAAC,wCAA6B,EAACtC,MAAM,CAAC;EACjE,IAAIqC,mBAAmB,EAAE;IACvBZ,YAAY,CAAC5B,MAAM,CAAC0C,qBAAqB,CAAC,GAAGF,mBAAmB;EAClE,CAAC,MAAM;IACL,OAAOZ,YAAY,CAAC5B,MAAM,CAAC0C,qBAAqB,CAAC;EACnD;EAEA,MAAMC,cAAc,GAAG,IAAAC,mCAAwB,EAACzC,MAAM,CAAC;EACvD,IAAIwC,cAAc,EAAE;IAClBf,YAAY,CAAC5B,MAAM,CAAC6C,yCAAyC,CAAC,GAAGF,cAAc;EACjF,CAAC,MAAM;IACL,OAAOf,YAAY,CAAC5B,MAAM,CAAC6C,yCAAyC,CAAC;EACvE;EAEA,MAAMC,2BAA2B,GAAG,IAAAC,yCAA8B,EAAC5C,MAAM,CAAC;EAC1E,IAAI2C,2BAA2B,EAAE;IAC/BlB,YAAY,CAAC5B,MAAM,CAACgD,8BAA8B,CAAC,GAAGF,2BAA2B;EACnF,CAAC,MAAM;IACL,OAAOlB,YAAY,CAAC5B,MAAM,CAACgD,8BAA8B,CAAC;EAC5D;EAEA,OAAO,MAAMC,sBAAsB,CAACnC,WAAW,EAAEX,MAAM,EAAEyB,YAAY,CAAC;AACxE;AAEO,eAAeqB,sBAAsBA,CAC1CnC,WAAmB,EACnBX,MAAyB,EACzBiB,SAAoB,EACA;EACpB,MAAMQ,YAAY,GAAG;IAAE,GAAGR;EAAU,CAAC;EAErC,MAAM8B,cAAc,GAAG,MAAM,IAAAC,yCAA8B,EAACrC,WAAW,EAAEX,MAAM,EAAE,KAAK,CAAC;EACvF,IAAI,CAAC+C,cAAc,IAAI9B,SAAS,CAACpB,MAAM,CAACoD,eAAe,CAAC,EAAE;IACxD,MAAM,IAAIC,KAAK,CACb,oNACF,CAAC;EACH;EAEA,IAAIH,cAAc,EAAE;IAClB,OAAOtB,YAAY,CAAC,qBAAqB,CAAC;IAC1CA,YAAY,CAAC5B,MAAM,CAACoD,eAAe,CAAC,GAAGF,cAAc;EACvD,CAAC,MAAM;IACL,OAAOtB,YAAY,CAAC,qBAAqB,CAAC;IAC1C,OAAOA,YAAY,CAAC5B,MAAM,CAACoD,eAAe,CAAC;EAC7C;EAEA,OAAOxB,YAAY;AACrB", "ignoreList": []}