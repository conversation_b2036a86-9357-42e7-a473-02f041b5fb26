/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 */

import type { InspectorProxyQueries } from "../inspector-proxy/InspectorProxy";
import type { <PERSON><PERSON><PERSON>Launcher } from "../types/BrowserLauncher";
import type { EventReporter } from "../types/EventReporter";
import type { Experiments } from "../types/Experiments";
import type { Logger } from "../types/Logger";
import type { NextHandleFunction } from "connect";
type Options = Readonly<{
  serverBaseUrl: string;
  logger?: Logger;
  browserLauncher: BrowserLauncher;
  eventReporter?: EventReporter;
  experiments: Experiments;
  inspectorProxy: InspectorProxyQueries;
}>;
/**
 * Open the debugger frontend for a given CDP target.
 *
 * Currently supports React Native DevTools (rn_fusebox.html) and legacy Hermes
 * (rn_inspector.html) targets.
 *
 * @see https://chromedevtools.github.io/devtools-protocol/
 */
declare function openDebuggerMiddleware(
  $$PARAM_0$$: Options
): NextHandleFunction;
export default openDebuggerMiddleware;
