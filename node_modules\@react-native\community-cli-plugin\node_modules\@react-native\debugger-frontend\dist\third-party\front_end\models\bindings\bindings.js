import*as e from"../../core/common/common.js";import*as t from"../../core/platform/platform.js";import{assertNotNullOrUndefined as o}from"../../core/platform/platform.js";import*as r from"../../core/sdk/sdk.js";import*as s from"../text_utils/text_utils.js";import*as i from"../workspace/workspace.js";import*as n from"../../core/i18n/i18n.js";const a={unknownErrorLoadingFile:"Unknown error loading file"},c=n.i18n.registerUIStrings("models/bindings/ContentProviderBasedProject.ts",a),u=n.i18n.getLocalizedString.bind(void 0,c);class d extends i.Workspace.ProjectStore{#e;#t;constructor(e,t,o,r,s){super(e,t,o,r),this.#e=s,this.#t=new WeakMap,e.addProject(this)}async requestFileContent(e){const{contentProvider:t}=this.#t.get(e);try{return await t.requestContentData()}catch(e){return{error:e?String(e):u(a.unknownErrorLoadingFile)}}}isServiceProject(){return this.#e}async requestMetadata(e){const{metadata:t}=this.#t.get(e);return t}canSetFileContent(){return!1}async setFileContent(e,t,o){}fullDisplayName(e){let t=e.parentURL().replace(/^(?:https?|file)\:\/\//,"");try{t=decodeURI(t)}catch{}return t+"/"+e.displayName(!0)}mimeType(e){const{mimeType:t}=this.#t.get(e);return t}canRename(){return!1}rename(e,t,o){const r=e.url();this.performRename(r,t,((t,r)=>{t&&r&&this.renameUISourceCode(e,r),o(t,r)}))}excludeFolder(e){}canExcludeFolder(e){return!1}async createFile(e,t,o,r){return null}canCreateFile(){return!1}deleteFile(e){}remove(){}performRename(e,t,o){o(!1)}searchInFileContent(e,t,o,r){const{contentProvider:s}=this.#t.get(e);return s.searchInContent(t,o,r)}async findFilesMatchingSearchRequest(e,o,r){const i=new Map;return r.setTotalWork(o.length),await Promise.all(o.map(async function(o){let n=!0,a=[];for(const r of e.queries().slice()){const i=await this.searchInFileContent(o,r,!e.ignoreCase(),e.isRegex());if(!i.length){n=!1;break}a=t.ArrayUtilities.mergeOrdered(a,i,s.ContentProvider.SearchMatch.comparator)}n&&i.set(o,a);r.incrementWorked(1)}.bind(this))),r.done(),i}indexContent(e){queueMicrotask(e.done.bind(e))}addUISourceCodeWithProvider(e,t,o,r){this.#t.set(e,{mimeType:r,metadata:o,contentProvider:t}),this.addUISourceCode(e)}addContentProvider(e,t,o){const r=this.createUISourceCode(e,t.contentType());return this.addUISourceCodeWithProvider(r,t,null,o),r}reset(){this.removeProject(),this.workspace().addProject(this)}dispose(){this.removeProject()}}var l=Object.freeze({__proto__:null,ContentProviderBasedProject:d});const g={removeFromIgnoreList:"Remove from ignore list",addScriptToIgnoreList:"Add script to ignore list",addDirectoryToIgnoreList:"Add directory to ignore list",addAllContentScriptsToIgnoreList:"Add all extension scripts to ignore list",addAllThirdPartyScriptsToIgnoreList:"Add all third-party scripts to ignore list",addAllAnonymousScriptsToIgnoreList:"Add all anonymous scripts to ignore list"},p=n.i18n.registerUIStrings("models/bindings/IgnoreListManager.ts",g),h=n.i18n.getLocalizedString.bind(void 0,p);let m;class S{#o;#r;#s;#i;constructor(t){this.#o=t,r.TargetManager.TargetManager.instance().addModelListener(r.DebuggerModel.DebuggerModel,r.DebuggerModel.Events.GlobalObjectCleared,this.clearCacheIfNeeded.bind(this),this),r.TargetManager.TargetManager.instance().addModelListener(r.RuntimeModel.RuntimeModel,r.RuntimeModel.Events.ExecutionContextCreated,this.onExecutionContextCreated,this,{scoped:!0}),r.TargetManager.TargetManager.instance().addModelListener(r.RuntimeModel.RuntimeModel,r.RuntimeModel.Events.ExecutionContextDestroyed,this.onExecutionContextDestroyed,this,{scoped:!0}),e.Settings.Settings.instance().moduleSetting("skip-stack-frames-pattern").addChangeListener(this.patternChanged.bind(this)),e.Settings.Settings.instance().moduleSetting("skip-content-scripts").addChangeListener(this.patternChanged.bind(this)),e.Settings.Settings.instance().moduleSetting("automatically-ignore-list-known-third-party-scripts").addChangeListener(this.patternChanged.bind(this)),e.Settings.Settings.instance().moduleSetting("enable-ignore-listing").addChangeListener(this.patternChanged.bind(this)),e.Settings.Settings.instance().moduleSetting("skip-anonymous-scripts").addChangeListener(this.patternChanged.bind(this)),this.#r=new Set,this.#s=new Map,this.#i=new Set,r.TargetManager.TargetManager.instance().observeModels(r.DebuggerModel.DebuggerModel,this)}static instance(e={forceNew:null,debuggerWorkspaceBinding:null}){const{forceNew:t,debuggerWorkspaceBinding:o}=e;if(!m||t){if(!o)throw new Error(`Unable to create settings: debuggerWorkspaceBinding must be provided: ${(new Error).stack}`);m=new S(o)}return m}static removeInstance(){m=void 0}addChangeListener(e){this.#r.add(e)}removeChangeListener(e){this.#r.delete(e)}modelAdded(e){this.setIgnoreListPatterns(e);const t=e.sourceMapManager();t.addEventListener(r.SourceMapManager.Events.SourceMapAttached,this.sourceMapAttached,this),t.addEventListener(r.SourceMapManager.Events.SourceMapDetached,this.sourceMapDetached,this)}modelRemoved(e){this.clearCacheIfNeeded();const t=e.sourceMapManager();t.removeEventListener(r.SourceMapManager.Events.SourceMapAttached,this.sourceMapAttached,this),t.removeEventListener(r.SourceMapManager.Events.SourceMapDetached,this.sourceMapDetached,this)}isContentScript(e){return!e.isDefault}onExecutionContextCreated(e){if(this.isContentScript(e.data)&&(this.#i.add(e.data.uniqueId),this.skipContentScripts))for(const e of r.TargetManager.TargetManager.instance().models(r.DebuggerModel.DebuggerModel))this.updateIgnoredExecutionContexts(e)}onExecutionContextDestroyed(e){if(this.isContentScript(e.data)&&(this.#i.delete(e.data.uniqueId),this.skipContentScripts))for(const e of r.TargetManager.TargetManager.instance().models(r.DebuggerModel.DebuggerModel))this.updateIgnoredExecutionContexts(e)}clearCacheIfNeeded(){this.#s.size>1024&&this.#s.clear()}getSkipStackFramesPatternSetting(){return e.Settings.Settings.instance().moduleSetting("skip-stack-frames-pattern")}setIgnoreListPatterns(e){const t=this.enableIgnoreListing?this.getSkipStackFramesPatternSetting().getAsArray():[],o=[];for(const e of t)!e.disabled&&e.pattern&&o.push(e.pattern);return e.setBlackboxPatterns(o,this.skipAnonymousScripts)}updateIgnoredExecutionContexts(e){return e.setBlackboxExecutionContexts(this.skipContentScripts?Array.from(this.#i):[])}getGeneralRulesForUISourceCode(e){return{isContentScript:e.project().type()===i.Workspace.projectTypes.ContentScripts,isKnownThirdParty:e.isKnownThirdParty()}}isUserOrSourceMapIgnoreListedUISourceCode(e){if(e.isUnconditionallyIgnoreListed())return!0;const t=this.uiSourceCodeURL(e);return this.isUserIgnoreListedURL(t,this.getGeneralRulesForUISourceCode(e))}isUserIgnoreListedURL(e,t){if(!this.enableIgnoreListing)return!1;if(t?.isContentScript&&this.skipContentScripts)return!0;if(t?.isKnownThirdParty&&this.automaticallyIgnoreListKnownThirdPartyScripts)return!0;if(!e)return this.skipAnonymousScripts;if(this.#s.has(e))return Boolean(this.#s.get(e));const o=null!==this.getFirstMatchedRegex(e);return this.#s.set(e,o),o}getFirstMatchedRegex(e){if(!e)return null;const t=this.getSkipStackFramesPatternSetting().getAsArray();if(!this.urlToRegExpString(e))return null;for(let o=0;o<t.length;++o){const r=t[o];if(r.disabled||r.disabledForUrl===e)continue;const s=new RegExp(r.pattern);if(s.test(e))return s}return null}sourceMapAttached(e){const t=e.data.client,o=e.data.sourceMap;this.updateScriptRanges(t,o)}sourceMapDetached(e){const t=e.data.client;this.updateScriptRanges(t,void 0)}async updateScriptRanges(e,t){let o=!1;if(S.instance().isUserIgnoreListedURL(e.sourceURL,{isContentScript:e.isContentScript()})||(o=t?.sourceURLs().some((e=>this.isUserIgnoreListedURL(e,{isKnownThirdParty:t.hasIgnoreListHint(e)})))??!1),!o)return L.get(e)&&await e.setBlackboxedRanges([])&&L.delete(e),void await this.#o.updateLocations(e);if(!t)return;const r=t.findRanges((e=>this.isUserIgnoreListedURL(e,{isKnownThirdParty:t.hasIgnoreListHint(e)})),{isStartMatching:!0}).flatMap((e=>[e.start,e.end]));!function(e,t){if(e.length!==t.length)return!1;for(let o=0;o<e.length;++o)if(e[o].lineNumber!==t[o].lineNumber||e[o].columnNumber!==t[o].columnNumber)return!1;return!0}(L.get(e)||[],r)&&await e.setBlackboxedRanges(r)&&L.set(e,r),this.#o.updateLocations(e)}uiSourceCodeURL(e){return e.project().type()===i.Workspace.projectTypes.Debugger?null:e.url()}canIgnoreListUISourceCode(e){const t=this.uiSourceCodeURL(e);return!!t&&Boolean(this.urlToRegExpString(t))}ignoreListUISourceCode(e){const t=this.uiSourceCodeURL(e);t&&this.ignoreListURL(t)}unIgnoreListUISourceCode(e){this.unIgnoreListURL(this.uiSourceCodeURL(e),this.getGeneralRulesForUISourceCode(e))}get enableIgnoreListing(){return e.Settings.Settings.instance().moduleSetting("enable-ignore-listing").get()}set enableIgnoreListing(t){e.Settings.Settings.instance().moduleSetting("enable-ignore-listing").set(t)}get skipContentScripts(){return this.enableIgnoreListing&&e.Settings.Settings.instance().moduleSetting("skip-content-scripts").get()}get skipAnonymousScripts(){return this.enableIgnoreListing&&e.Settings.Settings.instance().moduleSetting("skip-anonymous-scripts").get()}get automaticallyIgnoreListKnownThirdPartyScripts(){return this.enableIgnoreListing&&e.Settings.Settings.instance().moduleSetting("automatically-ignore-list-known-third-party-scripts").get()}ignoreListContentScripts(){this.enableIgnoreListing||(this.enableIgnoreListing=!0),e.Settings.Settings.instance().moduleSetting("skip-content-scripts").set(!0)}unIgnoreListContentScripts(){e.Settings.Settings.instance().moduleSetting("skip-content-scripts").set(!1)}ignoreListAnonymousScripts(){this.enableIgnoreListing||(this.enableIgnoreListing=!0),e.Settings.Settings.instance().moduleSetting("skip-anonymous-scripts").set(!0)}unIgnoreListAnonymousScripts(){e.Settings.Settings.instance().moduleSetting("skip-anonymous-scripts").set(!1)}ignoreListThirdParty(){this.enableIgnoreListing||(this.enableIgnoreListing=!0),e.Settings.Settings.instance().moduleSetting("automatically-ignore-list-known-third-party-scripts").set(!0)}unIgnoreListThirdParty(){e.Settings.Settings.instance().moduleSetting("automatically-ignore-list-known-third-party-scripts").set(!1)}ignoreListURL(e){const t=this.urlToRegExpString(e);t&&this.addRegexToIgnoreList(t,e)}addRegexToIgnoreList(e,t){const o=this.getSkipStackFramesPatternSetting().getAsArray();let r=!1;for(let s=0;s<o.length;++s){const i=o[s];(i.pattern===e||t&&i.disabledForUrl===t)&&(i.disabled=!1,i.disabledForUrl=void 0,r=!0)}r||o.push({pattern:e,disabled:!1}),this.enableIgnoreListing||(this.enableIgnoreListing=!0),this.getSkipStackFramesPatternSetting().setAsArray(o)}unIgnoreListURL(e,t){if(t?.isContentScript&&this.unIgnoreListContentScripts(),t?.isKnownThirdParty&&this.unIgnoreListThirdParty(),!e)return void this.unIgnoreListAnonymousScripts();let o=this.getSkipStackFramesPatternSetting().getAsArray();const r=S.instance().urlToRegExpString(e);if(r){o=o.filter((function(e){return e.pattern!==r}));for(let t=0;t<o.length;++t){const r=o[t];if(!r.disabled)try{new RegExp(r.pattern).test(e)&&(r.disabled=!0,r.disabledForUrl=e)}catch{}}this.getSkipStackFramesPatternSetting().setAsArray(o)}}removeIgnoreListPattern(e){let t=this.getSkipStackFramesPatternSetting().getAsArray();t=t.filter((function(t){return t.pattern!==e})),this.getSkipStackFramesPatternSetting().setAsArray(t)}ignoreListHasPattern(e,t){return this.getSkipStackFramesPatternSetting().getAsArray().some((o=>!(t&&o.disabled)&&o.pattern===e))}async patternChanged(){this.#s.clear();const e=[];for(const t of r.TargetManager.TargetManager.instance().models(r.DebuggerModel.DebuggerModel)){e.push(this.setIgnoreListPatterns(t));const o=t.sourceMapManager();for(const r of t.scripts())e.push(this.updateScriptRanges(r,o.sourceMapForClient(r)));e.push(this.updateIgnoredExecutionContexts(t))}await Promise.all(e);const t=Array.from(this.#r);for(const e of t)e();this.patternChangeFinishedForTests()}patternChangeFinishedForTests(){}urlToRegExpString(o){const r=new e.ParsedURL.ParsedURL(o);if(r.isAboutBlank()||r.isDataURL())return"";if(!r.isValid)return"^"+t.StringUtilities.escapeForRegExp(o)+"$";let s=r.lastPathComponent;if(s?s="/"+s:r.folderPathComponents&&(s=r.folderPathComponents+"/"),s||(s=r.host),!s)return"";const i=r.scheme;let n="";return i&&"http"!==i&&"https"!==i&&(n="^"+i+"://","chrome-extension"===i&&(n+=r.host+"\\b"),n+=".*"),n+t.StringUtilities.escapeForRegExp(s)+(o.endsWith(s)?"$":"\\b")}getIgnoreListURLContextMenuItems(e){if(e.project().type()===i.Workspace.projectTypes.FileSystem)return[];const t=[],o=this.canIgnoreListUISourceCode(e),r=this.isUserOrSourceMapIgnoreListedUISourceCode(e),s=!this.uiSourceCodeURL(e),{isContentScript:n,isKnownThirdParty:a}=this.getGeneralRulesForUISourceCode(e);return r?(o||n||a||s)&&t.push({text:h(g.removeFromIgnoreList),callback:this.unIgnoreListUISourceCode.bind(this,e),jslogContext:"remove-script-from-ignorelist"}):(o?t.push({text:h(g.addScriptToIgnoreList),callback:this.ignoreListUISourceCode.bind(this,e),jslogContext:"add-script-to-ignorelist"}):s&&t.push({text:h(g.addAllAnonymousScriptsToIgnoreList),callback:this.ignoreListAnonymousScripts.bind(this),jslogContext:"add-anonymous-scripts-to-ignorelist"}),t.push(...this.getIgnoreListGeneralContextMenuItems({isContentScript:n,isKnownThirdParty:a}))),t}getIgnoreListGeneralContextMenuItems(e){const t=[];return e?.isContentScript&&t.push({text:h(g.addAllContentScriptsToIgnoreList),callback:this.ignoreListContentScripts.bind(this),jslogContext:"add-content-scripts-to-ignorelist"}),e?.isKnownThirdParty&&t.push({text:h(g.addAllThirdPartyScriptsToIgnoreList),callback:this.ignoreListThirdParty.bind(this),jslogContext:"add-3p-scripts-to-ignorelist"}),t}getIgnoreListFolderContextMenuItems(e,o){const r=[],s="^"+t.StringUtilities.escapeForRegExp(e)+"/";return this.ignoreListHasPattern(s,!0)?r.push({text:h(g.removeFromIgnoreList),callback:this.removeIgnoreListPattern.bind(this,s),jslogContext:"remove-from-ignore-list"}):this.isUserIgnoreListedURL(e,o)?r.push({text:h(g.removeFromIgnoreList),callback:this.unIgnoreListURL.bind(this,e,o),jslogContext:"remove-from-ignore-list"}):o?.isCurrentlyIgnoreListed||(r.push({text:h(g.addDirectoryToIgnoreList),callback:this.addRegexToIgnoreList.bind(this,s),jslogContext:"add-directory-to-ignore-list"}),r.push(...this.getIgnoreListGeneralContextMenuItems(o))),r}}const L=new WeakMap;var M=Object.freeze({__proto__:null,IgnoreListManager:S});const f=new WeakMap,b=new WeakMap;let C;class w extends e.ObjectWrapper.ObjectWrapper{constructor(){super()}static instance({forceNew:e}={forceNew:!1}){return C&&!e||(C=new w),C}}class I{static resolveFrame(e,t){const o=I.targetForUISourceCode(e),s=o?.model(r.ResourceTreeModel.ResourceTreeModel);return s?s.frameForId(t):null}static setInitialFrameAttribution(e,t){if(!t)return;const o=I.resolveFrame(e,t);if(!o)return;const r=new Map;r.set(t,{frame:o,count:1}),f.set(e,r)}static cloneInitialFrameAttribution(e,t){const o=f.get(e);if(!o)return;const r=new Map;for(const e of o.keys()){const t=o.get(e);void 0!==t&&r.set(e,{frame:t.frame,count:t.count})}f.set(t,r)}static addFrameAttribution(e,t){const o=I.resolveFrame(e,t);if(!o)return;const r=f.get(e);if(!r)return;const s=r.get(t)||{frame:o,count:0};if(s.count+=1,r.set(t,s),1!==s.count)return;const i={uiSourceCode:e,frame:o};w.instance().dispatchEventToListeners("FrameAttributionAdded",i)}static removeFrameAttribution(e,t){const o=f.get(e);if(!o)return;const r=o.get(t);if(console.assert(Boolean(r),"Failed to remove frame attribution for url: "+e.url()),!r)return;if(r.count-=1,r.count>0)return;o.delete(t);const s={uiSourceCode:e,frame:r.frame};w.instance().dispatchEventToListeners("FrameAttributionRemoved",s)}static targetForUISourceCode(e){return b.get(e.project())||null}static setTargetForProject(e,t){b.set(e,t)}static getTargetForProject(e){return b.get(e)||null}static framesForUISourceCode(e){const t=I.targetForUISourceCode(e),o=t?.model(r.ResourceTreeModel.ResourceTreeModel),s=f.get(e);if(!o||!s)return[];return Array.from(s.keys()).map((e=>o.frameForId(e))).filter((e=>!!e))}}var v=Object.freeze({__proto__:null,NetworkProject:I,NetworkProjectManager:w});class y{#n;#o;#a=new Map;#c;#u;#d=new Map;#l=new Map;#g=new t.MapUtilities.Multimap;constructor(e,t,o){this.#n=e.sourceMapManager(),this.#o=o,this.#c=new d(t,"jsSourceMaps:stub:"+e.target().id(),i.Workspace.projectTypes.Service,"",!0),this.#u=[this.#n.addEventListener(r.SourceMapManager.Events.SourceMapWillAttach,this.sourceMapWillAttach,this),this.#n.addEventListener(r.SourceMapManager.Events.SourceMapFailedToAttach,this.sourceMapFailedToAttach,this),this.#n.addEventListener(r.SourceMapManager.Events.SourceMapAttached,this.sourceMapAttached,this),this.#n.addEventListener(r.SourceMapManager.Events.SourceMapDetached,this.sourceMapDetached,this)]}setFunctionRanges(e,t){for(const o of this.#g.get(e))o.augmentWithScopes(e.url(),t)}addStubUISourceCode(t){const o=this.#c.addContentProvider(e.ParsedURL.ParsedURL.concatenate(t.sourceURL,":sourcemap"),s.StaticContentProvider.StaticContentProvider.fromString(t.sourceURL,e.ResourceType.resourceTypes.Script,"\n\n\n\n\n// Please wait a bit.\n// Compiled script is not shown while source map is being loaded!"),"text/javascript");this.#a.set(t,o)}removeStubUISourceCode(e){const t=this.#a.get(e);this.#a.delete(e),t&&this.#c.removeUISourceCode(t.url())}getLocationRangesForSameSourceLocation(e){const t=e.debuggerModel,o=e.script();if(!o)return[];const r=this.#n.sourceMapForClient(o);if(!r)return[];const{lineNumber:s,columnNumber:i}=o.rawLocationToRelativeLocation(e),n=r.findEntry(s,i);if(!n||!n.sourceURL)return[];const a=this.#l.get(r);if(!a)return[];const c=a.uiSourceCodeForURL(n.sourceURL);if(!c)return[];if(!this.#g.hasValue(c,r))return[];return r.findReverseRanges(n.sourceURL,n.sourceLineNumber,n.sourceColumnNumber).map((({startLine:e,startColumn:r,endLine:s,endColumn:i})=>{const n=o.relativeLocationToRawLocation({lineNumber:e,columnNumber:r}),a=o.relativeLocationToRawLocation({lineNumber:s,columnNumber:i});return{start:t.createRawLocation(o,n.lineNumber,n.columnNumber),end:t.createRawLocation(o,a.lineNumber,a.columnNumber)}}))}uiSourceCodeForURL(e,t){const o=t?i.Workspace.projectTypes.ContentScripts:i.Workspace.projectTypes.Network;for(const t of this.#d.values()){if(t.type()!==o)continue;const r=t.uiSourceCodeForURL(e);if(r)return r}return null}rawLocationToUILocation(e){const t=e.script();if(!t)return null;const{lineNumber:o,columnNumber:r}=t.rawLocationToRelativeLocation(e),s=this.#a.get(t);if(s)return new i.UISourceCode.UILocation(s,o,r);const n=this.#n.sourceMapForClient(t);if(!n)return null;const a=this.#l.get(n);if(!a)return null;const c=n.findEntry(o,r,e.inlineFrameIndex);if(!c||!c.sourceURL)return null;const u=a.uiSourceCodeForURL(c.sourceURL);return u&&this.#g.hasValue(u,n)?u.uiLocation(c.sourceLineNumber,c.sourceColumnNumber):null}uiLocationToRawLocations(e,t,o){const r=[];for(const s of this.#g.get(e)){const i=s.sourceLineMapping(e.url(),t,o);if(!i)continue;const n=this.#n.clientForSourceMap(s);if(!n)continue;const a=n.relativeLocationToRawLocation(i);r.push(n.debuggerModel.createRawLocation(n,a.lineNumber,a.columnNumber))}return r}uiLocationRangeToRawLocationRanges(e,t){if(!this.#g.has(e))return null;const o=[];for(const r of this.#g.get(e)){const s=this.#n.clientForSourceMap(r);if(s)for(const i of r.reverseMapTextRanges(e.url(),t)){const e=s.relativeLocationToRawLocation(i.start),t=s.relativeLocationToRawLocation(i.end),r=s.debuggerModel.createRawLocation(s,e.lineNumber,e.columnNumber),n=s.debuggerModel.createRawLocation(s,t.lineNumber,t.columnNumber);o.push({start:r,end:n})}}return o}getMappedLines(e){if(!this.#g.has(e))return null;const t=new Set;for(const o of this.#g.get(e))for(const r of o.mappings())r.sourceURL===e.url()&&t.add(r.sourceLineNumber);return t}sourceMapWillAttach(e){const{client:t}=e.data;this.addStubUISourceCode(t),this.#o.updateLocations(t),S.instance().isUserIgnoreListedURL(t.sourceURL,{isContentScript:t.isContentScript()})&&this.#n.cancelAttachSourceMap(t)}sourceMapFailedToAttach(e){const{client:t}=e.data;this.removeStubUISourceCode(t),this.#o.updateLocations(t)}sourceMapAttached(t){const{client:o,sourceMap:n}=t.data,a=new Set([o]);this.removeStubUISourceCode(o);const c=o.target(),u=`jsSourceMaps:${o.isContentScript()?"extensions":""}:${c.id()}`;let l=this.#d.get(u);if(!l){const e=o.isContentScript()?i.Workspace.projectTypes.ContentScripts:i.Workspace.projectTypes.Network;l=new d(this.#c.workspace(),u,e,"",!1),I.setTargetForProject(l,c),this.#d.set(u,l)}this.#l.set(n,l);for(const t of n.sourceURLs()){const c=e.ResourceType.resourceTypes.SourceMapScript,u=l.createUISourceCode(t,c);n.hasIgnoreListHint(t)&&u.markKnownThirdParty();const d=n.embeddedContentByURL(t),g=null!==d?s.StaticContentProvider.StaticContentProvider.fromString(t,c,d):new r.CompilerSourceMappingContentProvider.CompilerSourceMappingContentProvider(t,c,o.createPageResourceLoadInitiator());let p=null;if(null!==d){const e=new TextEncoder;p=new i.UISourceCode.UISourceCodeMetadata(null,e.encode(d).length)}const h=e.ResourceType.ResourceType.mimeFromURL(t)??c.canonicalMimeType();this.#g.set(u,n),I.setInitialFrameAttribution(u,o.frameId);const m=l.uiSourceCodeForURL(t);if(null!==m){for(const e of this.#g.get(m)){this.#g.delete(m,e);const o=this.#n.clientForSourceMap(e);o&&(I.removeFrameAttribution(m,o.frameId),n.compatibleForURL(t,e)&&(this.#g.set(u,e),I.addFrameAttribution(u,o.frameId)),a.add(o))}l.removeUISourceCode(t)}l.addUISourceCodeWithProvider(u,g,p,h)}Promise.all([...a].map((e=>this.#o.updateLocations(e)))).then((()=>this.sourceMapAttachedForTest(n)))}sourceMapDetached(e){const{client:t,sourceMap:o}=e.data,r=this.#l.get(o);if(r){for(const e of r.uiSourceCodes())this.#g.delete(e,o)&&(I.removeFrameAttribution(e,t.frameId),this.#g.has(e)||r.removeUISourceCode(e.url()));this.#l.delete(o),this.#o.updateLocations(t)}}scriptsForUISourceCode(e){const t=[];for(const o of this.#g.get(e)){const e=this.#n.clientForSourceMap(o);e&&t.push(e)}return t}sourceMapAttachedForTest(e){}dispose(){e.EventTarget.removeEventListeners(this.#u);for(const e of this.#d.values())e.dispose();this.#c.dispose()}}var T=Object.freeze({__proto__:null,CompilerScriptMapping:y});class R{#p;#h;#m;constructor(e,t){this.#p=e,this.#h=t,this.#h.add(this),this.#m=null}async update(){this.#p&&(this.#m?await this.#m.then((()=>this.update())):(this.#m=this.#p(this),await this.#m,this.#m=null))}async uiLocation(){throw new Error("Not implemented")}dispose(){this.#h.delete(this),this.#p=null}isDisposed(){return!this.#h.has(this)}async isIgnoreListed(){throw new Error("Not implemented")}}class F{#S;constructor(){this.#S=new Set}add(e){this.#S.add(e)}delete(e){this.#S.delete(e)}has(e){return this.#S.has(e)}disposeAll(){for(const e of this.#S)e.dispose()}}var U=Object.freeze({__proto__:null,LiveLocationPool:F,LiveLocationWithPool:R});class P{#n;#L;#u;#M;constructor(e,t,o){this.#n=t,this.#L=new d(o,"cssSourceMaps:"+e.id(),i.Workspace.projectTypes.Network,"",!1),I.setTargetForProject(this.#L,e),this.#M=new Map,this.#u=[this.#n.addEventListener(r.SourceMapManager.Events.SourceMapAttached,this.sourceMapAttached,this),this.#n.addEventListener(r.SourceMapManager.Events.SourceMapDetached,this.sourceMapDetached,this)]}sourceMapAttachedForTest(e){}async sourceMapAttached(e){const t=e.data.client,o=e.data.sourceMap,r=this.#L,s=this.#M;for(const e of o.sourceURLs()){let i=s.get(e);i||(i=new j(r,e,t.createPageResourceLoadInitiator()),s.set(e,i)),i.addSourceMap(o,t.frameId)}await z.instance().updateLocations(t),this.sourceMapAttachedForTest(o)}async sourceMapDetached(e){const t=e.data.client,o=e.data.sourceMap,r=this.#M;for(const e of o.sourceURLs()){const s=r.get(e);s&&(s.removeSourceMap(o,t.frameId),s.getUiSourceCode()||r.delete(e))}await z.instance().updateLocations(t)}rawLocationToUILocation(e){const t=e.header();if(!t)return null;const o=this.#n.sourceMapForClient(t);if(!o)return null;let{lineNumber:r,columnNumber:s}=e;o.mapsOrigin()&&t.isInline&&(r-=t.startLine,0===r&&(s-=t.startColumn));const i=o.findEntry(r,s);if(!i||!i.sourceURL)return null;const n=this.#L.uiSourceCodeForURL(i.sourceURL);return n?n.uiLocation(i.sourceLineNumber,i.sourceColumnNumber):null}uiLocationToRawLocations(e){const{uiSourceCode:t,lineNumber:o,columnNumber:s=0}=e,i=k.get(t);if(!i)return[];const n=[];for(const e of i.getReferringSourceMaps()){const i=e.findReverseEntries(t.url(),o,s),a=this.#n.clientForSourceMap(e);a&&n.push(...i.map((e=>new r.CSSModel.CSSLocation(a,e.lineNumber,e.columnNumber))))}return n}static uiSourceOrigin(e){const t=k.get(e);return t?t.getReferringSourceMaps().map((e=>e.compiledURL())):[]}dispose(){e.EventTarget.removeEventListeners(this.#u),this.#L.dispose()}}const k=new WeakMap;let j=class{#L;#f;#b;referringSourceMaps;uiSourceCode;constructor(e,t,o){this.#L=e,this.#f=t,this.#b=o,this.referringSourceMaps=[],this.uiSourceCode=null}recreateUISourceCodeIfNeeded(t){const o=this.referringSourceMaps[this.referringSourceMaps.length-1],n=e.ResourceType.resourceTypes.SourceMapStyleSheet,a=o.embeddedContentByURL(this.#f),c=null!==a?s.StaticContentProvider.StaticContentProvider.fromString(this.#f,n,a):new r.CompilerSourceMappingContentProvider.CompilerSourceMappingContentProvider(this.#f,n,this.#b),u=this.#L.createUISourceCode(this.#f,n);k.set(u,this);const d=e.ResourceType.ResourceType.mimeFromURL(this.#f)||n.canonicalMimeType(),l="string"==typeof a?new i.UISourceCode.UISourceCodeMetadata(null,a.length):null;this.uiSourceCode?(I.cloneInitialFrameAttribution(this.uiSourceCode,u),this.#L.removeUISourceCode(this.uiSourceCode.url())):I.setInitialFrameAttribution(u,t),this.uiSourceCode=u,this.#L.addUISourceCodeWithProvider(this.uiSourceCode,c,l,d)}addSourceMap(e,t){this.uiSourceCode&&I.addFrameAttribution(this.uiSourceCode,t),this.referringSourceMaps.push(e),this.recreateUISourceCodeIfNeeded(t)}removeSourceMap(e,t){const o=this.uiSourceCode;I.removeFrameAttribution(o,t);const r=this.referringSourceMaps.lastIndexOf(e);-1!==r&&this.referringSourceMaps.splice(r,1),this.referringSourceMaps.length?this.recreateUISourceCodeIfNeeded(t):(this.#L.removeUISourceCode(o.url()),this.uiSourceCode=null)}getReferringSourceMaps(){return this.referringSourceMaps}getUiSourceCode(){return this.uiSourceCode}};var D=Object.freeze({__proto__:null,SASSSourceMapping:P});function E(e){return r.ResourceTreeModel.ResourceTreeModel.resourceForURL(e)}function x(e,t,o){const s=e.model(r.ResourceTreeModel.ResourceTreeModel);if(!s)return null;const i=s.frameForId(t);return i?N(i.resourceForURL(o)):null}function N(e){return!e||"number"!=typeof e.contentSize()&&!e.lastModified()?null:new i.UISourceCode.UISourceCodeMetadata(e.lastModified(),e.contentSize())}var A=Object.freeze({__proto__:null,displayNameForURL:function(o){if(!o)return"";const s=E(o);if(s)return s.displayName;const n=i.Workspace.WorkspaceImpl.instance().uiSourceCodeForURL(o);if(n)return n.displayName();const a=r.TargetManager.TargetManager.instance().inspectedURL();if(!a)return t.StringUtilities.trimURL(o,"");const c=e.ParsedURL.ParsedURL.fromString(a);if(!c)return o;const u=c.lastPathComponent,d=a.indexOf(u);if(-1!==d&&d+u.length===a.length){const e=a.substring(0,d);if(o.startsWith(e)&&o.length>d)return o.substring(d)}const l=t.StringUtilities.trimURL(o,c.host);return"/"===l?c.host+"/":l},metadataForURL:x,resourceForURL:E,resourceMetadata:N});const O=new WeakMap;class W{#C;#w;#I;#v;#u;constructor(e,t){this.#C=e;const o=this.#C.target();this.#w=new d(t,"css:"+o.id(),i.Workspace.projectTypes.Network,"",!1),I.setTargetForProject(this.#w,o),this.#I=new d(t,"inspector:"+o.id(),i.Workspace.projectTypes.Inspector,"",!0),this.#v=new Map,this.#u=[this.#C.addEventListener(r.CSSModel.Events.StyleSheetAdded,this.styleSheetAdded,this),this.#C.addEventListener(r.CSSModel.Events.StyleSheetRemoved,this.styleSheetRemoved,this),this.#C.addEventListener(r.CSSModel.Events.StyleSheetChanged,this.styleSheetChanged,this)]}addSourceMap(e,t){this.#v.get(e)?.addSourceMap(e,t)}rawLocationToUILocation(e){const t=e.header();if(!t||!this.acceptsHeader(t))return null;const o=this.#v.get(t.resourceURL());if(!o)return null;let r=e.lineNumber,s=e.columnNumber;if(t.isInline&&t.hasSourceURL){r-=t.lineNumberInSource(0);const e=t.columnNumberInSource(r,0);void 0===e?s=e:s-=e}return o.getUiSourceCode().uiLocation(r,s)}uiLocationToRawLocations(e){const t=O.get(e.uiSourceCode);if(!t)return[];const o=[];for(const s of t.getHeaders()){let t=e.lineNumber,i=e.columnNumber;s.isInline&&s.hasSourceURL&&(i=s.columnNumberInSource(t,e.columnNumber||0),t=s.lineNumberInSource(t)),o.push(new r.CSSModel.CSSLocation(s,t,i))}return o}acceptsHeader(e){return!e.isConstructedByNew()&&(!(e.isInline&&!e.hasSourceURL&&"inspector"!==e.origin)&&!!e.resourceURL())}styleSheetAdded(e){const t=e.data;if(!this.acceptsHeader(t))return;const o=t.resourceURL();let r=this.#v.get(o);if(r)r.addHeader(t);else{const e=t.isViaInspector()?this.#I:this.#w;r=new B(this.#C,e,t),this.#v.set(o,r)}}styleSheetRemoved(e){const t=e.data;if(!this.acceptsHeader(t))return;const o=t.resourceURL(),r=this.#v.get(o);r&&(1===r.getHeaders().size?(r.dispose(),this.#v.delete(o)):r.removeHeader(t))}styleSheetChanged(e){const t=this.#C.styleSheetHeaderForId(e.data.styleSheetId);if(!t||!this.acceptsHeader(t))return;const o=this.#v.get(t.resourceURL());o&&o.styleSheetChanged(t)}dispose(){for(const e of this.#v.values())e.dispose();this.#v.clear(),e.EventTarget.removeEventListeners(this.#u),this.#I.removeProject(),this.#w.removeProject()}}class B{#C;#L;headers;uiSourceCode;#u;#y;#T;#R;#F;constructor(t,o,r){this.#C=t,this.#L=o,this.headers=new Set([r]);const s=t.target(),n=r.resourceURL(),a=x(s,r.frameId,n);this.uiSourceCode=this.#L.createUISourceCode(n,r.contentType()),O.set(this.uiSourceCode,this),I.setInitialFrameAttribution(this.uiSourceCode,r.frameId),this.#L.addUISourceCodeWithProvider(this.uiSourceCode,this,a,"text/css"),this.#u=[this.uiSourceCode.addEventListener(i.UISourceCode.Events.WorkingCopyChanged,this.workingCopyChanged,this),this.uiSourceCode.addEventListener(i.UISourceCode.Events.WorkingCopyCommitted,this.workingCopyCommitted,this)],this.#y=new e.Throttler.Throttler(B.updateTimeout),this.#T=!1}addHeader(e){this.headers.add(e),I.addFrameAttribution(this.uiSourceCode,e.frameId)}removeHeader(e){this.headers.delete(e),I.removeFrameAttribution(this.uiSourceCode,e.frameId)}styleSheetChanged(e){if(console.assert(this.headers.has(e)),this.#F||!this.headers.has(e))return;const t=this.mirrorContent.bind(this,e,!0);this.#y.schedule(t,"Default")}workingCopyCommitted(){if(this.#R)return;const e=this.mirrorContent.bind(this,this.uiSourceCode,!0);this.#y.schedule(e,"AsSoonAsPossible")}workingCopyChanged(){if(this.#R)return;const e=this.mirrorContent.bind(this,this.uiSourceCode,!1);this.#y.schedule(e,"Default")}async mirrorContent(e,t){if(this.#T)return void this.styleFileSyncedForTest();let o=null;if(o=e===this.uiSourceCode?this.uiSourceCode.workingCopy():s.ContentData.ContentData.textOr(await e.requestContentData(),null),null===o||this.#T)return void this.styleFileSyncedForTest();e!==this.uiSourceCode&&(this.#R=!0,this.uiSourceCode.setWorkingCopy(o),this.#R=!1),this.#F=!0;const r=[];for(const s of this.headers)s!==e&&r.push(this.#C.setStyleSheetText(s.id,o,t));await Promise.all(r),this.#F=!1,this.styleFileSyncedForTest()}styleFileSyncedForTest(){}dispose(){this.#T||(this.#T=!0,this.#L.removeUISourceCode(this.uiSourceCode.url()),e.EventTarget.removeEventListeners(this.#u))}contentURL(){return console.assert(this.headers.size>0),this.#U().originalContentProvider().contentURL()}contentType(){return console.assert(this.headers.size>0),this.#U().originalContentProvider().contentType()}requestContent(){return console.assert(this.headers.size>0),this.#U().originalContentProvider().requestContent()}requestContentData(){return console.assert(this.headers.size>0),this.#U().originalContentProvider().requestContentData()}searchInContent(e,t,o){return console.assert(this.headers.size>0),this.#U().originalContentProvider().searchInContent(e,t,o)}#U(){return console.assert(this.headers.size>0),this.headers.values().next().value}static updateTimeout=200;getHeaders(){return this.headers}getUiSourceCode(){return this.uiSourceCode}addSourceMap(e,t){const o=this.#C.sourceMapManager();this.headers.forEach((r=>{o.detachSourceMap(r),o.attachSourceMap(r,e,t)}))}}var H=Object.freeze({__proto__:null,StyleFile:B,StylesSourceMapping:W});let _;class z{#P;#k;#j;constructor(e,t){this.#P=e,this.#k=new Map,t.observeModels(r.CSSModel.CSSModel,this),this.#j=new Set}static instance(e={forceNew:null,resourceMapping:null,targetManager:null}){const{forceNew:t,resourceMapping:o,targetManager:r}=e;if(!_||t){if(!o||!r)throw new Error(`Unable to create CSSWorkspaceBinding: resourceMapping and targetManager must be provided: ${(new Error).stack}`);_=new z(o,r)}return _}static removeInstance(){_=void 0}get modelToInfo(){return this.#k}getCSSModelInfo(e){return this.#k.get(e)}modelAdded(e){this.#k.set(e,new V(e,this.#P))}modelRemoved(e){this.getCSSModelInfo(e).dispose(),this.#k.delete(e)}async pendingLiveLocationChangesPromise(){await Promise.all(this.#j)}recordLiveLocationChange(e){e.then((()=>{this.#j.delete(e)})),this.#j.add(e)}async updateLocations(e){const t=this.getCSSModelInfo(e.cssModel()).updateLocations(e);this.recordLiveLocationChange(t),await t}createLiveLocation(e,t,o){const r=this.getCSSModelInfo(e.cssModel()).createLiveLocation(e,t,o);return this.recordLiveLocationChange(r),r}propertyRawLocation(e,t){const o=e.ownerStyle;if(!o||o.type!==r.CSSStyleDeclaration.Type.Regular||!o.styleSheetId)return null;const s=o.cssModel().styleSheetHeaderForId(o.styleSheetId);if(!s)return null;const i=t?e.nameRange():e.valueRange();if(!i)return null;const n=i.startLine,a=i.startColumn;return new r.CSSModel.CSSLocation(s,s.lineNumberInSource(n),s.columnNumberInSource(n,a))}propertyUILocation(e,t){const o=this.propertyRawLocation(e,t);return o?this.rawLocationToUILocation(o):null}rawLocationToUILocation(e){return this.getCSSModelInfo(e.cssModel()).rawLocationToUILocation(e)}uiLocationToRawLocations(e){const t=[];for(const o of this.#k.values())t.push(...o.uiLocationToRawLocations(e));return t}}let V=class{#u;#P;#D;#E;#S;#x;constructor(e,o){this.#u=[e.addEventListener(r.CSSModel.Events.StyleSheetAdded,(e=>{this.styleSheetAdded(e)}),this),e.addEventListener(r.CSSModel.Events.StyleSheetRemoved,(e=>{this.styleSheetRemoved(e)}),this)],this.#P=o,this.#D=new W(e,o.workspace);const s=e.sourceMapManager();this.#E=new P(e.target(),s,o.workspace),this.#S=new t.MapUtilities.Multimap,this.#x=new t.MapUtilities.Multimap}get locations(){return this.#S}async createLiveLocation(e,t,o){const r=new q(e,this,t,o),s=e.header();return s?(r.setHeader(s),this.#S.set(s,r),await r.update()):this.#x.set(e.url,r),r}disposeLocation(e){const t=e.header();t?this.#S.delete(t,e):this.#x.delete(e.url,e)}updateLocations(e){const t=[];for(const o of this.#S.get(e))t.push(o.update());return Promise.all(t)}async styleSheetAdded(e){const t=e.data;if(!t.sourceURL)return;const o=[];for(const e of this.#x.get(t.sourceURL))e.setHeader(t),this.#S.set(t,e),o.push(e.update());await Promise.all(o),this.#x.deleteAll(t.sourceURL)}async styleSheetRemoved(e){const t=e.data,o=[];for(const e of this.#S.get(t))e.setHeader(t),this.#x.set(e.url,e),o.push(e.update());await Promise.all(o),this.#S.deleteAll(t)}addSourceMap(e,t){this.#D.addSourceMap(e,t)}rawLocationToUILocation(e){let t=null;return t=t||this.#E.rawLocationToUILocation(e),t=t||this.#D.rawLocationToUILocation(e),t=t||this.#P.cssLocationToUILocation(e),t}uiLocationToRawLocations(e){let t=this.#E.uiLocationToRawLocations(e);return t.length?t:(t=this.#D.uiLocationToRawLocations(e),t.length?t:this.#P.uiLocationToCSSLocations(e))}dispose(){e.EventTarget.removeEventListeners(this.#u),this.#D.dispose(),this.#E.dispose()}};class q extends R{url;#N;#A;#O;headerInternal;constructor(e,t,o,r){super(o,r),this.url=e.url,this.#N=e.lineNumber,this.#A=e.columnNumber,this.#O=t,this.headerInternal=null}header(){return this.headerInternal}setHeader(e){this.headerInternal=e}async uiLocation(){if(!this.headerInternal)return null;const e=new r.CSSModel.CSSLocation(this.headerInternal,this.#N,this.#A);return z.instance().rawLocationToUILocation(e)}dispose(){super.dispose(),this.#O.disposeLocation(this)}async isIgnoreListed(){return!1}}var G=Object.freeze({__proto__:null,CSSWorkspaceBinding:z,LiveLocation:q,ModelInfo:V});const K={errorInDebuggerLanguagePlugin:"Error in debugger language plugin: {PH1}",loadingDebugSymbolsForVia:"[{PH1}] Loading debug symbols for {PH2} (via {PH3})...",loadingDebugSymbolsFor:"[{PH1}] Loading debug symbols for {PH2}...",loadedDebugSymbolsForButDidnt:"[{PH1}] Loaded debug symbols for {PH2}, but didn't find any source files",loadedDebugSymbolsForFound:"[{PH1}] Loaded debug symbols for {PH2}, found {PH3} source file(s)",failedToLoadDebugSymbolsFor:"[{PH1}] Failed to load debug symbols for {PH2} ({PH3})",failedToLoadDebugSymbolsForFunction:'No debug information for function "{PH1}"',debugSymbolsIncomplete:"The debug information for function {PH1} is incomplete"},$=n.i18n.registerUIStrings("models/bindings/DebuggerLanguagePlugins.ts",K),J=n.i18n.getLocalizedString.bind(void 0,$);function X(e){return`${e.sourceURL}@${e.hash}`}function Q(e){const{script:t}=e;return{rawModuleId:X(t),codeOffset:e.location().columnNumber-(t.codeOffset()||0),inlineFrameIndex:e.inlineFrameIndex}}class Y extends Error{exception;exceptionDetails;constructor(e,t){const{description:o}=t.exception||{};super(o||t.text),this.exception=e,this.exceptionDetails=t}static makeLocal(e,t){const o={type:"object",subtype:"error",description:t},r={text:"Uncaught",exceptionId:-1,columnNumber:0,lineNumber:0,exception:o},s=e.debuggerModel.runtimeModel().createRemoteObject(o);return new Y(s,r)}}class Z extends r.RemoteObject.LocalJSONObject{constructor(e){super(e)}get description(){return this.type}get type(){return"namespace"}}async function ee(e,t,o){if("reftype"===t.type){const o=await async function(e,t){if(!/^(local|global|operand)$/.test(t.valueClass))return{type:"undefined"};const o=Number(t.index),r=`${t.valueClass}s[${o}]`,s=await e.debuggerModel.agent.invoke_evaluateOnCallFrame({callFrameId:e.id,expression:r,silent:!0,generatePreview:!0,throwOnSideEffect:!0});return s.getError()||s.exceptionDetails?{type:"undefined"}:s.result}(e,t);return e.debuggerModel.runtimeModel().createRemoteObject(o)}return new re(e,t,o)}class te extends r.RemoteObject.RemoteObjectImpl{variables;#W;#B;stopId;constructor(e,t,o){super(e.debuggerModel.runtimeModel(),void 0,"object",void 0,null),this.variables=[],this.#W=e,this.#B=o,this.stopId=t}async doGetProperties(e,t,o){if(t)return{properties:[],internalProperties:[]};const s=[],i={};function n(e,t){return new r.RemoteObject.RemoteObjectProperty(e,t,!1,!1,!0,!1)}for(const e of this.variables){let t;try{const o=await this.#B.evaluate(e.name,Q(this.#W),this.stopId);t=o?await ee(this.#W,o,this.#B):new r.RemoteObject.LocalJSONObject(void 0)}catch(e){console.warn(e),t=new r.RemoteObject.LocalJSONObject(void 0)}if(e.nestedName&&e.nestedName.length>1){let o=i;for(let t=0;t<e.nestedName.length-1;t++){const r=e.nestedName[t];let s=o[r];s||(s=new Z({}),o[r]=s),o=s.value}o[e.nestedName[e.nestedName.length-1]]=t}else s.push(n(e.name,t))}for(const e in i)s.push(n(e,i[e]));return{properties:s,internalProperties:[]}}}class oe{#H;#_;#z;#V;#q;constructor(e,t,o,r,s,i){if(s&&"data:"!==new URL(s).protocol)throw new Error("The icon must be a data:-URL");this.#H=e,this.#_=o,this.#z=r,this.#V=s,this.#q=new te(e,t,i)}async getVariableValue(e){for(let t=0;t<this.#q.variables.length;++t){if(this.#q.variables[t].name!==e)continue;const o=await this.#q.getAllProperties(!1,!1);if(!o.properties)continue;const{value:r}=o.properties[t];if(r)return r}return null}callFrame(){return this.#H}type(){return this.#_}typeName(){return this.#z}name(){}range(){return null}object(){return this.#q}description(){return""}icon(){return this.#V}extraProperties(){return[]}}class re extends r.RemoteObject.RemoteObject{extensionObject;plugin;callFrame;constructor(e,t,o){super(),this.extensionObject=t,this.plugin=o,this.callFrame=e}get linearMemoryAddress(){return this.extensionObject.linearMemoryAddress}get linearMemorySize(){return this.extensionObject.linearMemorySize}get objectId(){return this.extensionObject.objectId}get type(){return"array"===this.extensionObject.type||"null"===this.extensionObject.type?"object":this.extensionObject.type}get subtype(){if("array"===this.extensionObject.type||"null"===this.extensionObject.type)return this.extensionObject.type}get value(){return this.extensionObject.value}unserializableValue(){}get description(){return this.extensionObject.description}set description(e){}get hasChildren(){return this.extensionObject.hasChildren}get preview(){}get className(){return this.extensionObject.className??null}arrayLength(){return 0}arrayBufferByteLength(){return 0}getOwnProperties(e,t){return this.getAllProperties(!1,e,t)}async getAllProperties(e,t,s){const{objectId:i}=this.extensionObject;if(i){o(this.plugin.getProperties);const e=await this.plugin.getProperties(i);return{properties:await Promise.all(e.map((async e=>new r.RemoteObject.RemoteObjectProperty(e.name,await ee(this.callFrame,e.value,this.plugin))))),internalProperties:null}}return{properties:null,internalProperties:null}}release(){const{objectId:e}=this.extensionObject;e&&(o(this.plugin.releaseObject),this.plugin.releaseObject(e))}debuggerModel(){return this.callFrame.debuggerModel}runtimeModel(){return this.callFrame.debuggerModel.runtimeModel()}isLinearMemoryInspectable(){return void 0!==this.extensionObject.linearMemoryAddress}}class se{#G;#o;#K;#$;#J;callFrameByStopId=new Map;stopIdByCallFrame=new Map;nextStopId=0n;constructor(e,t,o){this.#G=t,this.#o=o,this.#K=[],this.#$=new Map,e.observeModels(r.DebuggerModel.DebuggerModel,this),this.#J=new Map}async evaluateOnCallFrame(e,t){const{script:o}=e,{expression:s,returnByValue:i,throwOnSideEffect:n}=t,{plugin:a}=await this.rawModuleIdAndPluginForScript(o);if(!a)return null;const c=Q(e);if(0===(await a.rawLocationToSourceLocation(c)).length)return null;if(i)return{error:"Cannot return by value"};if(n)return{error:"Cannot guarantee side-effect freedom"};try{const t=await a.evaluate(s,c,this.stopIdForCallFrame(e));return t?{object:await ee(e,t,a),exceptionDetails:void 0}:{object:new r.RemoteObject.LocalJSONObject(void 0),exceptionDetails:void 0}}catch(t){if(t instanceof Y){const{exception:e,exceptionDetails:o}=t;return{object:e,exceptionDetails:o}}const{exception:o,exceptionDetails:r}=Y.makeLocal(e,t.message);return{object:o,exceptionDetails:r}}}stopIdForCallFrame(e){let t=this.stopIdByCallFrame.get(e);return void 0!==t||(t=this.nextStopId++,this.stopIdByCallFrame.set(e,t),this.callFrameByStopId.set(t,e)),t}callFrameForStopId(e){return this.callFrameByStopId.get(e)}expandCallFrames(e){return Promise.all(e.map((async e=>{const t=await this.getFunctionInfo(e.script,e.location());if(t){if("frames"in t&&t.frames.length)return t.frames.map((({name:t},o)=>e.createVirtualCallFrame(o,t)));if("missingSymbolFiles"in t&&t.missingSymbolFiles.length){const o=t.missingSymbolFiles,r=J(K.debugSymbolsIncomplete,{PH1:e.functionName});e.missingDebugInfoDetails={details:r,resources:o}}else e.missingDebugInfoDetails={details:J(K.failedToLoadDebugSymbolsForFunction,{PH1:e.functionName}),resources:[]}}return e}))).then((e=>e.flat()))}modelAdded(e){this.#$.set(e,new ie(e,this.#G)),e.addEventListener(r.DebuggerModel.Events.GlobalObjectCleared,this.globalObjectCleared,this),e.addEventListener(r.DebuggerModel.Events.ParsedScriptSource,this.parsedScriptSource,this),e.addEventListener(r.DebuggerModel.Events.DebuggerResumed,this.debuggerResumed,this),e.setEvaluateOnCallFrameCallback(this.evaluateOnCallFrame.bind(this)),e.setExpandCallFramesCallback(this.expandCallFrames.bind(this))}modelRemoved(t){t.removeEventListener(r.DebuggerModel.Events.GlobalObjectCleared,this.globalObjectCleared,this),t.removeEventListener(r.DebuggerModel.Events.ParsedScriptSource,this.parsedScriptSource,this),t.removeEventListener(r.DebuggerModel.Events.DebuggerResumed,this.debuggerResumed,this),t.setEvaluateOnCallFrameCallback(null),t.setExpandCallFramesCallback(null);const o=this.#$.get(t);o&&(o.dispose(),this.#$.delete(t)),this.#J.forEach(((o,r)=>{const s=o.scripts.filter((e=>e.debuggerModel!==t));0===s.length?(o.plugin.removeRawModule(r).catch((t=>{e.Console.Console.instance().error(J(K.errorInDebuggerLanguagePlugin,{PH1:t.message}),!1)})),this.#J.delete(r)):o.scripts=s}))}globalObjectCleared(e){const t=e.data;this.modelRemoved(t),this.modelAdded(t)}addPlugin(e){this.#K.push(e);for(const e of this.#$.keys())for(const t of e.scripts())this.hasPluginForScript(t)||this.parsedScriptSource({data:t})}removePlugin(e){this.#K=this.#K.filter((t=>t!==e));const t=new Set;this.#J.forEach(((o,r)=>{o.plugin===e&&(o.scripts.forEach((e=>t.add(e))),this.#J.delete(r))}));for(const e of t){this.#$.get(e.debuggerModel).removeScript(e),this.parsedScriptSource({data:e})}}hasPluginForScript(e){const t=X(e),o=this.#J.get(t);return o?.scripts.includes(e)??!1}async rawModuleIdAndPluginForScript(e){const t=X(e),o=this.#J.get(t);return o&&(await o.addRawModulePromise,o===this.#J.get(t))?{rawModuleId:t,plugin:o.plugin}:{rawModuleId:t,plugin:null}}uiSourceCodeForURL(e,t){const o=this.#$.get(e);return o?o.getProject().uiSourceCodeForURL(t):null}async rawLocationToUILocation(t){const o=t.script();if(!o)return null;const{rawModuleId:r,plugin:s}=await this.rawModuleIdAndPluginForScript(o);if(!s)return null;const i={rawModuleId:r,codeOffset:t.columnNumber-(o.codeOffset()||0),inlineFrameIndex:t.inlineFrameIndex};try{const e=await s.rawLocationToSourceLocation(i);for(const t of e){const e=this.uiSourceCodeForURL(o.debuggerModel,t.sourceFileURL);if(e)return e.uiLocation(t.lineNumber,t.columnNumber>=0?t.columnNumber:void 0)}}catch(t){e.Console.Console.instance().error(J(K.errorInDebuggerLanguagePlugin,{PH1:t.message}),!1)}return null}uiLocationToRawLocationRanges(t,o,s=-1){const i=[];return this.scriptsForUISourceCode(t).forEach((e=>{const n=X(e),a=this.#J.get(n);if(!a)return;const{plugin:c}=a;i.push(async function(e,i,n){const a={rawModuleId:e,sourceFileURL:t.url(),lineNumber:o,columnNumber:s},c=await i.sourceLocationToRawLocation(a);if(!c)return[];return c.map((e=>({start:new r.DebuggerModel.Location(n.debuggerModel,n.scriptId,0,Number(e.startOffset)+(n.codeOffset()||0)),end:new r.DebuggerModel.Location(n.debuggerModel,n.scriptId,0,Number(e.endOffset)+(n.codeOffset()||0))})))}(n,c,e))})),0===i.length?Promise.resolve(null):Promise.all(i).then((e=>e.flat())).catch((t=>(e.Console.Console.instance().error(J(K.errorInDebuggerLanguagePlugin,{PH1:t.message}),!1),null)))}async uiLocationToRawLocations(e,t,o){const r=await this.uiLocationToRawLocationRanges(e,t,o);return r?r.map((({start:e})=>e)):null}async uiLocationRangeToRawLocationRanges(e,t){const o=[];for(let r=t.startLine;r<=t.endLine;++r)o.push(this.uiLocationToRawLocationRanges(e,r));const r=[];for(const e of await Promise.all(o)){if(null===e)return null;for(const o of e){const[e,i]=await Promise.all([this.rawLocationToUILocation(o.start),this.rawLocationToUILocation(o.end)]);if(null===e||null===i)continue;t.intersection(new s.TextRange.TextRange(e.lineNumber,e.columnNumber??0,i.lineNumber,i.columnNumber??1/0)).isEmpty()||r.push(o)}}return r}scriptsForUISourceCode(e){for(const t of this.#$.values()){const o=t.uiSourceCodeToScripts.get(e);if(o)return o}return[]}setDebugInfoURL(e,t){this.hasPluginForScript(e)||(e.debugSymbols={type:"ExternalDWARF",externalURL:t},this.parsedScriptSource({data:e}),e.debuggerModel.setDebugInfoURL(e,t))}parsedScriptSource(t){const o=t.data;if(o.sourceURL)for(const t of this.#K){if(!t.handleScript(o))continue;const r=X(o);let s=this.#J.get(r);if(s)s.scripts.push(o);else{const i=(async()=>{const i=e.Console.Console.instance(),n=o.sourceURL,a=o.debugSymbols?.externalURL||"";a?i.log(J(K.loadingDebugSymbolsForVia,{PH1:t.name,PH2:n,PH3:a})):i.log(J(K.loadingDebugSymbolsFor,{PH1:t.name,PH2:n}));try{const c=!a&&e.ParsedURL.schemeIs(n,"wasm:")?await o.getWasmBytecode():void 0,u=await t.addRawModule(r,a,{url:n,code:c});if(s!==this.#J.get(r))return[];if("missingSymbolFiles"in u){const e=t.createPageResourceLoadInitiator();return{missingSymbolFiles:u.missingSymbolFiles.map((t=>({resourceUrl:t,initiator:e})))}}const d=u;return 0===d.length?i.warn(J(K.loadedDebugSymbolsForButDidnt,{PH1:t.name,PH2:n})):i.log(J(K.loadedDebugSymbolsForFound,{PH1:t.name,PH2:n,PH3:d.length})),d}catch(e){return i.error(J(K.failedToLoadDebugSymbolsFor,{PH1:t.name,PH2:n,PH3:e.message}),!1),this.#J.delete(r),[]}})();s={rawModuleId:r,plugin:t,scripts:[o],addRawModulePromise:i},this.#J.set(r,s)}return void s.addRawModulePromise.then((e=>{if(!("missingSymbolFiles"in e)&&o.debuggerModel.scriptForId(o.scriptId)===o){const t=this.#$.get(o.debuggerModel);t&&(t.addSourceFiles(o,e),this.#o.updateLocations(o))}}))}}debuggerResumed(e){const t=Array.from(this.callFrameByStopId.values()).filter((t=>t.debuggerModel===e.data));for(const e of t){const t=this.stopIdByCallFrame.get(e);o(t),this.stopIdByCallFrame.delete(e),this.callFrameByStopId.delete(t)}}getSourcesForScript(e){const t=X(e),o=this.#J.get(t);return o?o.addRawModulePromise:Promise.resolve(void 0)}async resolveScopeChain(t){const o=t.script,{rawModuleId:r,plugin:s}=await this.rawModuleIdAndPluginForScript(o);if(!s)return null;const i={rawModuleId:r,codeOffset:t.location().columnNumber-(o.codeOffset()||0),inlineFrameIndex:t.inlineFrameIndex},n=this.stopIdForCallFrame(t);try{if(0===(await s.rawLocationToSourceLocation(i)).length)return null;const e=new Map,o=await s.listVariablesInScope(i);for(const r of o||[]){let o=e.get(r.scope);if(!o){const{type:i,typeName:a,icon:c}=await s.getScopeInfo(r.scope);o=new oe(t,n,i,a,c,s),e.set(r.scope,o)}o.object().variables.push(r)}return Array.from(e.values())}catch(t){return e.Console.Console.instance().error(J(K.errorInDebuggerLanguagePlugin,{PH1:t.message}),!1),null}}async getFunctionInfo(t,o){const{rawModuleId:r,plugin:s}=await this.rawModuleIdAndPluginForScript(t);if(!s)return null;const i={rawModuleId:r,codeOffset:o.columnNumber-(t.codeOffset()||0),inlineFrameIndex:0};try{const e=await s.getFunctionInfo(i);if("missingSymbolFiles"in e){const t=s.createPageResourceLoadInitiator();return{missingSymbolFiles:e.missingSymbolFiles.map((e=>({resourceUrl:e,initiator:t}))),..."frames"in e&&{frames:e.frames}}}return e}catch(t){return e.Console.Console.instance().warn(J(K.errorInDebuggerLanguagePlugin,{PH1:t.message})),{frames:[]}}}async getInlinedFunctionRanges(t){const o=t.script();if(!o)return[];const{rawModuleId:s,plugin:i}=await this.rawModuleIdAndPluginForScript(o);if(!i)return[];const n={rawModuleId:s,codeOffset:t.columnNumber-(o.codeOffset()||0)};try{return(await i.getInlinedFunctionRanges(n)).map((e=>({start:new r.DebuggerModel.Location(o.debuggerModel,o.scriptId,0,Number(e.startOffset)+(o.codeOffset()||0)),end:new r.DebuggerModel.Location(o.debuggerModel,o.scriptId,0,Number(e.endOffset)+(o.codeOffset()||0))})))}catch(t){return e.Console.Console.instance().warn(J(K.errorInDebuggerLanguagePlugin,{PH1:t.message})),[]}}async getInlinedCalleesRanges(t){const o=t.script();if(!o)return[];const{rawModuleId:s,plugin:i}=await this.rawModuleIdAndPluginForScript(o);if(!i)return[];const n={rawModuleId:s,codeOffset:t.columnNumber-(o.codeOffset()||0)};try{return(await i.getInlinedCalleesRanges(n)).map((e=>({start:new r.DebuggerModel.Location(o.debuggerModel,o.scriptId,0,Number(e.startOffset)+(o.codeOffset()||0)),end:new r.DebuggerModel.Location(o.debuggerModel,o.scriptId,0,Number(e.endOffset)+(o.codeOffset()||0))})))}catch(t){return e.Console.Console.instance().warn(J(K.errorInDebuggerLanguagePlugin,{PH1:t.message})),[]}}async getMappedLines(e){const t=await Promise.all(this.scriptsForUISourceCode(e).map((e=>this.rawModuleIdAndPluginForScript(e))));let o=null;for(const{rawModuleId:r,plugin:s}of t){if(!s)continue;const t=await s.getMappedLines(r,e.url());void 0!==t&&(null===o?o=new Set(t):t.forEach((e=>o.add(e))))}return o}}let ie=class{project;uiSourceCodeToScripts;constructor(e,t){this.project=new d(t,"language_plugins::"+e.target().id(),i.Workspace.projectTypes.Network,"",!1),I.setTargetForProject(this.project,e.target()),this.uiSourceCodeToScripts=new Map}addSourceFiles(t,o){const s=t.createPageResourceLoadInitiator();for(const i of o){let o=this.project.uiSourceCodeForURL(i);if(o){const e=this.uiSourceCodeToScripts.get(o);e.includes(t)||e.push(t)}else{o=this.project.createUISourceCode(i,e.ResourceType.resourceTypes.SourceMapScript),I.setInitialFrameAttribution(o,t.frameId),this.uiSourceCodeToScripts.set(o,[t]);const n=new r.CompilerSourceMappingContentProvider.CompilerSourceMappingContentProvider(i,e.ResourceType.resourceTypes.SourceMapScript,s),a=e.ResourceType.ResourceType.mimeFromURL(i)||"text/javascript";this.project.addUISourceCodeWithProvider(o,n,null,a)}}}removeScript(e){this.uiSourceCodeToScripts.forEach(((t,o)=>{0===(t=t.filter((t=>t!==e))).length?(this.uiSourceCodeToScripts.delete(o),this.project.removeUISourceCode(o.url())):this.uiSourceCodeToScripts.set(o,t)}))}dispose(){this.project.dispose()}getProject(){return this.project}};var ne=Object.freeze({__proto__:null,DebuggerLanguagePluginManager:se,ExtensionRemoteObject:re,SourceScope:oe});class ae{#o;#L;#u;#X;#Q;constructor(e,t,o){ce.add(this),this.#o=o,this.#L=new d(t,"debugger:"+e.target().id(),i.Workspace.projectTypes.Debugger,"",!0),this.#u=[e.addEventListener(r.DebuggerModel.Events.GlobalObjectCleared,this.globalObjectCleared,this),e.addEventListener(r.DebuggerModel.Events.ParsedScriptSource,this.parsedScriptSource,this),e.addEventListener(r.DebuggerModel.Events.DiscardedAnonymousScriptSource,this.discardedScriptSource,this)],this.#X=new Map,this.#Q=new Map}static createV8ScriptURL(t){const o=e.ParsedURL.ParsedURL.extractName(t.sourceURL);return"debugger:///VM"+t.scriptId+(o?" "+o:"")}static scriptForUISourceCode(e){for(const t of ce){const o=t.#X.get(e);if(void 0!==o)return o}return null}uiSourceCodeForScript(e){return this.#Q.get(e)??null}rawLocationToUILocation(e){const t=e.script();if(!t)return null;const o=this.#Q.get(t);if(!o)return null;const{lineNumber:r,columnNumber:s}=t.rawLocationToRelativeLocation(e);return o.uiLocation(r,s)}uiLocationToRawLocations(e,t,o){const r=this.#X.get(e);return r?(({lineNumber:t,columnNumber:o}=r.relativeLocationToRawLocation({lineNumber:t,columnNumber:o})),[r.debuggerModel.createRawLocation(r,t,o??0)]):[]}uiLocationRangeToRawLocationRanges(e,{startLine:t,startColumn:o,endLine:r,endColumn:s}){const i=this.#X.get(e);if(!i)return[];({lineNumber:t,columnNumber:o}=i.relativeLocationToRawLocation({lineNumber:t,columnNumber:o})),({lineNumber:r,columnNumber:s}=i.relativeLocationToRawLocation({lineNumber:r,columnNumber:s}));return[{start:i.debuggerModel.createRawLocation(i,t,o),end:i.debuggerModel.createRawLocation(i,r,s)}]}parsedScriptSource(t){const o=t.data,r=ae.createV8ScriptURL(o),s=this.#L.createUISourceCode(r,e.ResourceType.resourceTypes.Script);o.isBreakpointCondition&&s.markAsUnconditionallyIgnoreListed(),this.#X.set(s,o),this.#Q.set(o,s),this.#L.addUISourceCodeWithProvider(s,o,null,"text/javascript"),this.#o.updateLocations(o)}discardedScriptSource(e){const t=e.data,o=this.#Q.get(t);void 0!==o&&(this.#Q.delete(t),this.#X.delete(o),this.#L.removeUISourceCode(o.url()))}globalObjectCleared(){this.#Q.clear(),this.#X.clear(),this.#L.reset()}dispose(){ce.delete(this),e.EventTarget.removeEventListeners(this.#u),this.globalObjectCleared(),this.#L.dispose()}}const ce=new Set;var ue=Object.freeze({__proto__:null,DefaultScriptMapping:ae});const de={liveEditFailed:"`LiveEdit` failed: {PH1}",liveEditCompileFailed:"`LiveEdit` compile failed: {PH1}"},le=n.i18n.registerUIStrings("models/bindings/ResourceScriptMapping.ts",de),ge=n.i18n.getLocalizedString.bind(void 0,le);class pe{debuggerModel;#G;debuggerWorkspaceBinding;#Y;#d;#Q;#u;constructor(e,t,o){this.debuggerModel=e,this.#G=t,this.debuggerWorkspaceBinding=o,this.#Y=new Map,this.#d=new Map,this.#Q=new Map;const s=e.runtimeModel();this.#u=[this.debuggerModel.addEventListener(r.DebuggerModel.Events.ParsedScriptSource,(e=>this.addScript(e.data)),this),this.debuggerModel.addEventListener(r.DebuggerModel.Events.GlobalObjectCleared,this.globalObjectCleared,this),s.addEventListener(r.RuntimeModel.Events.ExecutionContextDestroyed,this.executionContextDestroyed,this),s.target().targetManager().addEventListener("InspectedURLChanged",this.inspectedURLChanged,this)]}project(e){const t=(e.isContentScript()?"js:extensions:":"js::")+this.debuggerModel.target().id()+":"+e.frameId;let o=this.#d.get(t);if(!o){const r=e.isContentScript()?i.Workspace.projectTypes.ContentScripts:i.Workspace.projectTypes.Network;o=new d(this.#G,t,r,"",!1),I.setTargetForProject(o,this.debuggerModel.target()),this.#d.set(t,o)}return o}uiSourceCodeForScript(e){return this.#Q.get(e)??null}rawLocationToUILocation(e){const t=e.script();if(!t)return null;const o=this.#Q.get(t);if(!o)return null;const r=this.#Y.get(o);if(!r)return null;if(r.hasDivergedFromVM()&&!r.isMergingToVM()||r.isDivergingFromVM())return null;if(r.script!==t)return null;const{lineNumber:s,columnNumber:i=0}=e;return o.uiLocation(s,i)}uiLocationToRawLocations(e,t,o){const r=this.#Y.get(e);if(!r)return[];const{script:s}=r;return s?[this.debuggerModel.createRawLocation(s,t,o)]:[]}uiLocationRangeToRawLocationRanges(e,{startLine:t,startColumn:o,endLine:r,endColumn:s}){const i=this.#Y.get(e);if(!i)return null;const{script:n}=i;if(!n)return null;return[{start:this.debuggerModel.createRawLocation(n,t,o),end:this.debuggerModel.createRawLocation(n,r,s)}]}inspectedURLChanged(e){for(let t=this.debuggerModel.target();t!==e.data;t=t.parentTarget())if(null===t)return;for(const e of Array.from(this.#Q.keys()))this.removeScripts([e]),this.addScript(e)}addScript(t){if(t.isLiveEdit()||t.isBreakpointCondition)return;let o=t.sourceURL;if(!o)return;if(t.hasSourceURL)o=r.SourceMapManager.SourceMapManager.resolveRelativeSourceURL(t.debuggerModel.target(),o);else{if(t.isInlineScript())return;if(t.isContentScript()){if(!new e.ParsedURL.ParsedURL(o).isValid)return}}const s=this.project(t),i=s.uiSourceCodeForURL(o);if(i){const e=this.#Y.get(i);e?.script&&this.removeScripts([e.script])}const n=t.originalContentProvider(),a=s.createUISourceCode(o,n.contentType());I.setInitialFrameAttribution(a,t.frameId);const c=x(this.debuggerModel.target(),t.frameId,o),u=new he(this,a,t);this.#Y.set(a,u),this.#Q.set(t,a);const d=t.isWasm()?"application/wasm":"text/javascript";s.addUISourceCodeWithProvider(a,n,c,d),this.debuggerWorkspaceBinding.updateLocations(t)}scriptFile(e){return this.#Y.get(e)||null}removeScripts(e){const o=new t.MapUtilities.Multimap;for(const t of e){const e=this.#Q.get(t);if(!e)continue;const r=this.#Y.get(e);r&&r.dispose(),this.#Y.delete(e),this.#Q.delete(t),o.set(e.project(),e),this.debuggerWorkspaceBinding.updateLocations(t)}for(const e of o.keysArray()){const t=o.get(e);let r=!0;for(const o of e.uiSourceCodes())if(!t.has(o)){r=!1;break}r?(this.#d.delete(e.id()),e.removeProject()):t.forEach((t=>e.removeUISourceCode(t.url())))}}executionContextDestroyed(e){const t=e.data;this.removeScripts(this.debuggerModel.scriptsForExecutionContext(t))}globalObjectCleared(){const e=Array.from(this.#Q.keys());this.removeScripts(e)}resetForTest(){this.globalObjectCleared()}dispose(){e.EventTarget.removeEventListeners(this.#u),this.globalObjectCleared()}}class he extends e.ObjectWrapper.ObjectWrapper{#Z;uiSourceCode;script;#ee;#te;#oe;#re;#se=new e.Mutex.Mutex;constructor(e,t,o){super(),this.#Z=e,this.uiSourceCode=t,this.script=this.uiSourceCode.contentType().isScript()?o:null,this.uiSourceCode.addEventListener(i.UISourceCode.Events.WorkingCopyChanged,this.workingCopyChanged,this),this.uiSourceCode.addEventListener(i.UISourceCode.Events.WorkingCopyCommitted,this.workingCopyCommitted,this)}isDiverged(){if(this.uiSourceCode.isDirty())return!0;if(!this.script)return!1;if(void 0===this.#ee||null===this.#ee)return!1;const e=this.uiSourceCode.workingCopy();if(!e)return!1;if(!e.startsWith(this.#ee.trimEnd()))return!0;const t=this.uiSourceCode.workingCopy().substr(this.#ee.length);return Boolean(t.length)&&!t.match(r.Script.sourceURLRegex)}workingCopyChanged(){this.update()}workingCopyCommitted(){if(this.uiSourceCode.project().canSetFileContent())return;if(!this.script)return;const e=this.uiSourceCode.workingCopy();this.script.editSource(e).then((({status:t,exceptionDetails:o})=>{this.scriptSourceWasSet(e,t,o)}))}async scriptSourceWasSet(t,o,r){if("Ok"===o&&(this.#ee=t),await this.update(),"Ok"===o)return;if(!r)return void e.Console.Console.instance().addMessage(ge(de.liveEditFailed,{PH1:function(e){switch(e){case"BlockedByActiveFunction":return"Functions that are on the stack (currently being executed) can not be edited";case"BlockedByActiveGenerator":return"Async functions/generators that are active can not be edited";case"BlockedByTopLevelEsModuleChange":return"The top-level of ES modules can not be edited";case"CompileError":case"Ok":throw new Error("Compile errors and Ok status must not be reported on the console")}}(o)}),"warning");const s=ge(de.liveEditCompileFailed,{PH1:r.text});this.uiSourceCode.addLineMessage("Error",s,r.lineNumber,r.columnNumber)}async update(){const e=await this.#se.acquire(),t=this.isDiverged();t&&!this.#oe?await this.divergeFromVM():!t&&this.#oe&&await this.mergeToVM(),e()}async divergeFromVM(){this.script&&(this.#te=!0,await this.#Z.debuggerWorkspaceBinding.updateLocations(this.script),this.#te=void 0,this.#oe=!0,this.dispatchEventToListeners("DidDivergeFromVM"))}async mergeToVM(){this.script&&(this.#oe=void 0,this.#re=!0,await this.#Z.debuggerWorkspaceBinding.updateLocations(this.script),this.#re=void 0,this.dispatchEventToListeners("DidMergeToVM"))}hasDivergedFromVM(){return Boolean(this.#oe)}isDivergingFromVM(){return Boolean(this.#te)}isMergingToVM(){return Boolean(this.#re)}checkMapping(){this.script&&void 0===this.#ee?this.script.requestContentData().then((e=>{this.#ee=s.ContentData.ContentData.textOr(e,null),this.update().then((()=>this.mappingCheckedForTest()))})):this.mappingCheckedForTest()}mappingCheckedForTest(){}dispose(){this.uiSourceCode.removeEventListener(i.UISourceCode.Events.WorkingCopyChanged,this.workingCopyChanged,this),this.uiSourceCode.removeEventListener(i.UISourceCode.Events.WorkingCopyCommitted,this.workingCopyCommitted,this)}addSourceMapURL(e){this.script&&this.script.debuggerModel.setSourceMapURL(this.script,e)}addDebugInfoURL(e){if(!this.script)return;const{pluginManager:t}=Le.instance();t.setDebugInfoURL(this.script,e)}hasSourceMapURL(){return Boolean(this.script?.sourceMapURL)}async missingSymbolFiles(){if(!this.script)return null;const{pluginManager:e}=this.#Z.debuggerWorkspaceBinding,t=await e.getSourcesForScript(this.script);return t&&"missingSymbolFiles"in t?t.missingSymbolFiles:null}}var me=Object.freeze({__proto__:null,ResourceScriptFile:he,ResourceScriptMapping:pe});let Se;class Le{resourceMapping;#ie;#$;#j;pluginManager;constructor(e,t){this.resourceMapping=e,this.#ie=[],this.#$=new Map,t.addModelListener(r.DebuggerModel.DebuggerModel,r.DebuggerModel.Events.GlobalObjectCleared,this.globalObjectCleared,this),t.addModelListener(r.DebuggerModel.DebuggerModel,r.DebuggerModel.Events.DebuggerResumed,this.debuggerResumed,this),t.observeModels(r.DebuggerModel.DebuggerModel,this),this.#j=new Set,this.pluginManager=new se(t,e.workspace,this)}setFunctionRanges(e,t){for(const o of this.#$.values())o.compilerMapping.setFunctionRanges(e,t)}static instance(e={forceNew:null,resourceMapping:null,targetManager:null}){const{forceNew:t,resourceMapping:o,targetManager:r}=e;if(!Se||t){if(!o||!r)throw new Error(`Unable to create DebuggerWorkspaceBinding: resourceMapping and targetManager must be provided: ${(new Error).stack}`);Se=new Le(o,r)}return Se}static removeInstance(){Se=void 0}addSourceMapping(e){this.#ie.push(e)}removeSourceMapping(e){const t=this.#ie.indexOf(e);-1!==t&&this.#ie.splice(t,1)}async computeAutoStepRanges(e,t){function o(e,t){const{start:o,end:r}=t;return o.scriptId===e.scriptId&&(!(e.lineNumber<o.lineNumber||e.lineNumber>r.lineNumber)&&(!(e.lineNumber===o.lineNumber&&e.columnNumber<o.columnNumber)&&!(e.lineNumber===r.lineNumber&&e.columnNumber>=r.columnNumber)))}const r=t.location();if(!r)return[];const s=this.pluginManager;let i=[];if("StepOut"===e)return await s.getInlinedFunctionRanges(r);const n=await s.rawLocationToUILocation(r);if(n)return i=await s.uiLocationToRawLocationRanges(n.uiSourceCode,n.lineNumber,n.columnNumber)||[],i=i.filter((e=>o(r,e))),"StepOver"===e&&(i=i.concat(await s.getInlinedCalleesRanges(r))),i;const a=this.#$.get(r.debuggerModel)?.compilerMapping;return a?(i=a.getLocationRangesForSameSourceLocation(r),i=i.filter((e=>o(r,e))),i):[]}modelAdded(e){e.setBeforePausedCallback(this.shouldPause.bind(this)),this.#$.set(e,new Me(e,this)),e.setComputeAutoStepRangesCallback(this.computeAutoStepRanges.bind(this))}modelRemoved(e){e.setComputeAutoStepRangesCallback(null);const t=this.#$.get(e);t&&(t.dispose(),this.#$.delete(e))}async pendingLiveLocationChangesPromise(){await Promise.all(this.#j)}recordLiveLocationChange(e){e.then((()=>{this.#j.delete(e)})),this.#j.add(e)}async updateLocations(e){const t=this.#$.get(e.debuggerModel);if(t){const o=t.updateLocations(e);this.recordLiveLocationChange(o),await o}}async createLiveLocation(e,t,o){const r=this.#$.get(e.debuggerModel);if(!r)return null;const s=r.createLiveLocation(e,t,o);return this.recordLiveLocationChange(s),await s}async createStackTraceTopFrameLiveLocation(e,t,o){console.assert(e.length>0);const r=be.createStackTraceTopFrameLocation(e,this,t,o);return this.recordLiveLocationChange(r),await r}async createCallFrameLiveLocation(e,t,o){if(!e.script())return null;const r=e.debuggerModel,s=this.createLiveLocation(e,t,o);this.recordLiveLocationChange(s);const i=await s;return i?(this.registerCallFrameLiveLocation(r,i),i):null}async rawLocationToUILocation(e){for(const t of this.#ie){const o=t.rawLocationToUILocation(e);if(o)return o}const t=await this.pluginManager.rawLocationToUILocation(e);if(t)return t;const o=this.#$.get(e.debuggerModel);return o?o.rawLocationToUILocation(e):null}uiSourceCodeForSourceMapSourceURL(e,t,o){const r=this.#$.get(e);return r?r.compilerMapping.uiSourceCodeForURL(t,o):null}async uiSourceCodeForSourceMapSourceURLPromise(e,t,o){const r=this.uiSourceCodeForSourceMapSourceURL(e,t,o);return await(r||this.waitForUISourceCodeAdded(t,e.target()))}async uiSourceCodeForDebuggerLanguagePluginSourceURLPromise(e,t){const o=this.pluginManager.uiSourceCodeForURL(e,t);return await(o||this.waitForUISourceCodeAdded(t,e.target()))}uiSourceCodeForScript(e){const t=this.#$.get(e.debuggerModel);return t?t.uiSourceCodeForScript(e):null}waitForUISourceCodeAdded(e,t){return new Promise((o=>{const r=i.Workspace.WorkspaceImpl.instance(),s=r.addEventListener(i.Workspace.Events.UISourceCodeAdded,(n=>{const a=n.data;a.url()===e&&I.targetForUISourceCode(a)===t&&(r.removeEventListener(i.Workspace.Events.UISourceCodeAdded,s.listener),o(a))}))}))}async uiLocationToRawLocations(e,t,o){for(const r of this.#ie){const s=r.uiLocationToRawLocations(e,t,o);if(s.length)return s}const r=await this.pluginManager.uiLocationToRawLocations(e,t,o);if(r)return r;for(const r of this.#$.values()){const s=r.uiLocationToRawLocations(e,t,o);if(s.length)return s}return[]}async uiLocationRangeToRawLocationRanges(e,t){for(const o of this.#ie){const r=o.uiLocationRangeToRawLocationRanges(e,t);if(r)return r}const o=await this.pluginManager.uiLocationRangeToRawLocationRanges(e,t);if(o)return o;for(const o of this.#$.values()){const r=o.uiLocationRangeToRawLocationRanges(e,t);if(r)return r}return[]}async normalizeUILocation(e){const t=await this.uiLocationToRawLocations(e.uiSourceCode,e.lineNumber,e.columnNumber);for(const e of t){const t=await this.rawLocationToUILocation(e);if(t)return t}return e}async getMappedLines(e){for(const t of this.#$.values()){const o=t.getMappedLines(e);if(null!==o)return o}return await this.pluginManager.getMappedLines(e)}scriptFile(e,t){const o=this.#$.get(t);return o?o.getResourceScriptMapping().scriptFile(e):null}scriptsForUISourceCode(e){const t=new Set;this.pluginManager.scriptsForUISourceCode(e).forEach((e=>t.add(e)));for(const o of this.#$.values()){const r=o.getResourceScriptMapping().scriptFile(e);r?.script&&t.add(r.script),o.compilerMapping.scriptsForUISourceCode(e).forEach((e=>t.add(e)))}return[...t]}supportsConditionalBreakpoints(e){return this.pluginManager.scriptsForUISourceCode(e).every((e=>e.isJavaScript()))}globalObjectCleared(e){this.reset(e.data)}reset(e){const t=this.#$.get(e);if(t){for(const e of t.callFrameLocations.values())this.removeLiveLocation(e);t.callFrameLocations.clear()}}resetForTest(e){const t=e.model(r.DebuggerModel.DebuggerModel),o=this.#$.get(t);o&&o.getResourceScriptMapping().resetForTest()}registerCallFrameLiveLocation(e,t){const o=this.#$.get(e);if(o){o.callFrameLocations.add(t)}}removeLiveLocation(e){const t=this.#$.get(e.rawLocation.debuggerModel);t&&t.disposeLocation(e)}debuggerResumed(e){this.reset(e.data)}async shouldPause(t,o){const{callFrames:[r]}=t;if(!r)return!1;const s=r.functionLocation();if(!(o&&"step"===t.reason&&s&&r.script.isWasm()&&e.Settings.moduleSetting("wasm-auto-stepping").get()&&this.pluginManager.hasPluginForScript(r.script)))return!0;return!!await this.pluginManager.rawLocationToUILocation(r.location())||(o.script()!==s.script()||o.columnNumber!==s.columnNumber||o.lineNumber!==s.lineNumber)}}class Me{#ne;#o;callFrameLocations;#ae;#P;#Z;compilerMapping;#S;constructor(e,o){this.#ne=e,this.#o=o,this.callFrameLocations=new Set;const{workspace:r}=o.resourceMapping;this.#ae=new ae(e,r,o),this.#P=o.resourceMapping,this.#Z=new pe(e,r,o),this.compilerMapping=new y(e,r,o),this.#S=new t.MapUtilities.Multimap}async createLiveLocation(e,t,o){console.assert(""!==e.scriptId);const r=e.scriptId,s=new fe(r,e,this.#o,t,o);return this.#S.set(r,s),await s.update(),s}disposeLocation(e){this.#S.delete(e.scriptId,e)}async updateLocations(e){const t=[];for(const o of this.#S.get(e.scriptId))t.push(o.update());await Promise.all(t)}rawLocationToUILocation(e){let t=this.compilerMapping.rawLocationToUILocation(e);return t=t||this.#Z.rawLocationToUILocation(e),t=t||this.#P.jsLocationToUILocation(e),t=t||this.#ae.rawLocationToUILocation(e),t}uiSourceCodeForScript(e){let t=null;return t=t||this.#Z.uiSourceCodeForScript(e),t=t||this.#P.uiSourceCodeForScript(e),t=t||this.#ae.uiSourceCodeForScript(e),t}uiLocationToRawLocations(e,t,o=0){let r=this.compilerMapping.uiLocationToRawLocations(e,t,o);return r=r.length?r:this.#Z.uiLocationToRawLocations(e,t,o),r=r.length?r:this.#P.uiLocationToJSLocations(e,t,o),r=r.length?r:this.#ae.uiLocationToRawLocations(e,t,o),r}uiLocationRangeToRawLocationRanges(e,t){let o=this.compilerMapping.uiLocationRangeToRawLocationRanges(e,t);return o??=this.#Z.uiLocationRangeToRawLocationRanges(e,t),o??=this.#P.uiLocationRangeToJSLocationRanges(e,t),o??=this.#ae.uiLocationRangeToRawLocationRanges(e,t),o}getMappedLines(e){return this.compilerMapping.getMappedLines(e)}dispose(){this.#ne.setBeforePausedCallback(null),this.compilerMapping.dispose(),this.#Z.dispose(),this.#ae.dispose()}getResourceScriptMapping(){return this.#Z}}class fe extends R{scriptId;rawLocation;#ce;constructor(e,t,o,r,s){super(r,s),this.scriptId=e,this.rawLocation=t,this.#ce=o}async uiLocation(){const e=this.rawLocation;return await this.#ce.rawLocationToUILocation(e)}dispose(){super.dispose(),this.#ce.removeLiveLocation(this)}async isIgnoreListed(){const e=await this.uiLocation();return!!e&&S.instance().isUserOrSourceMapIgnoreListedUISourceCode(e.uiSourceCode)}}class be extends R{#ue;#de;#S;constructor(e,t){super(e,t),this.#ue=!0,this.#de=null,this.#S=null}static async createStackTraceTopFrameLocation(e,t,o,r){const s=new be(o,r),i=e.map((e=>t.createLiveLocation(e,s.scheduleUpdate.bind(s),r)));return s.#S=(await Promise.all(i)).filter((e=>!!e)),await s.updateLocation(),s}async uiLocation(){return this.#de?await this.#de.uiLocation():null}async isIgnoreListed(){return!!this.#de&&await this.#de.isIgnoreListed()}dispose(){if(super.dispose(),this.#S)for(const e of this.#S)e.dispose();this.#S=null,this.#de=null}async scheduleUpdate(){this.#ue||(this.#ue=!0,queueMicrotask((()=>{this.updateLocation()})))}async updateLocation(){if(this.#ue=!1,this.#S&&0!==this.#S.length){this.#de=this.#S[0];for(const e of this.#S)if(!await e.isIgnoreListed()){this.#de=e;break}this.update()}}}var Ce=Object.freeze({__proto__:null,DebuggerWorkspaceBinding:Le,Location:fe});class we{#le;#ge;#pe;#he;#me;#Se;#Le;#Me;#fe;#be;#Ce;#we;constructor(e,t,o){this.#le=e,this.#ge=e.size,this.#pe=0,this.#me=t||Number.MAX_VALUE,this.#Se=o,this.#Le=new TextDecoder,this.#Me=!1,this.#fe=null,this.#he=null}async read(e){if(this.#Se&&this.#Se(this),this.#le?.type.endsWith("gzip")){const e=this.#le.stream(),t=this.decompressStream(e);this.#he=t.getReader()}else this.#we=new FileReader,this.#we.onload=this.onChunkLoaded.bind(this),this.#we.onerror=this.onError.bind(this);return this.#Ce=e,this.loadChunk(),await new Promise((e=>{this.#be=e}))}cancel(){this.#Me=!0}loadedSize(){return this.#pe}fileSize(){return this.#ge}fileName(){return this.#le?this.#le.name:""}error(){return this.#fe}decompressStream(e){const t=new DecompressionStream("gzip");return e.pipeThrough(t)}onChunkLoaded(e){if(this.#Me)return;if(e.target.readyState!==FileReader.DONE)return;if(!this.#we)return;const t=this.#we.result;this.#pe+=t.byteLength;const o=this.#pe===this.#ge;this.decodeChunkBuffer(t,o)}async decodeChunkBuffer(e,t){if(!this.#Ce)return;const o=this.#Le.decode(e,{stream:!t});await this.#Ce.write(o,t),this.#Me||(this.#Se&&this.#Se(this),t?this.finishRead():this.loadChunk())}async finishRead(){this.#Ce&&(this.#le=null,this.#we=null,await this.#Ce.close(),this.#be(!this.#fe))}async loadChunk(){if(this.#Ce&&this.#le){if(this.#he){const{value:e,done:t}=await this.#he.read();if(t||!e)return await this.#Ce.write("",!0),await this.finishRead();this.decodeChunkBuffer(e.buffer,!1)}if(this.#we){const e=this.#pe,t=Math.min(this.#ge,e+this.#me),o=this.#le.slice(e,t);this.#we.readAsArrayBuffer(o)}}}onError(e){const t=e.target;this.#fe=t.error,this.#be(!1)}}var Ie=Object.freeze({__proto__:null,ChunkedFileReader:we,FileOutputStream:class{#Ie;#ve;#ye;constructor(){this.#Ie=[]}async open(e){this.#ye=!1,this.#Ie=[],this.#ve=e;const t=await i.FileManager.FileManager.instance().save(this.#ve,"",!0,!1);return t&&i.FileManager.FileManager.instance().addEventListener("AppendedToURL",this.onAppendDone,this),Boolean(t)}write(e){return new Promise((t=>{this.#Ie.push(t),i.FileManager.FileManager.instance().append(this.#ve,e)}))}async close(){this.#ye=!0,this.#Ie.length||(i.FileManager.FileManager.instance().removeEventListener("AppendedToURL",this.onAppendDone,this),i.FileManager.FileManager.instance().close(this.#ve))}onAppendDone(e){if(e.data!==this.#ve)return;const t=this.#Ie.shift();t&&t(),this.#Ie.length||this.#ye&&(i.FileManager.FileManager.instance().removeEventListener("AppendedToURL",this.onAppendDone,this),i.FileManager.FileManager.instance().close(this.#ve))}}});class ve{#Te=new WeakMap;constructor(){r.TargetManager.TargetManager.instance().observeModels(r.DebuggerModel.DebuggerModel,this),r.TargetManager.TargetManager.instance().observeModels(r.CSSModel.CSSModel,this)}modelAdded(e){const t=e.target(),o=this.#Te.get(t)??new ye;e instanceof r.DebuggerModel.DebuggerModel?o.setDebuggerModel(e):o.setCSSModel(e),this.#Te.set(t,o)}modelRemoved(e){const t=e.target(),o=this.#Te.get(t);o?.clear()}addMessage(e,t,o){const r=this.#Te.get(o);r?.addMessage(e,t)}clear(){for(const e of r.TargetManager.TargetManager.instance().targets()){const t=this.#Te.get(e);t?.clear()}}}class ye{#ne;#C;#Re=new Map;#h;constructor(){this.#h=new F,i.Workspace.WorkspaceImpl.instance().addEventListener(i.Workspace.Events.UISourceCodeAdded,this.#Fe.bind(this))}setDebuggerModel(e){if(this.#ne)throw new Error("Cannot set DebuggerModel twice");this.#ne=e,e.addEventListener(r.DebuggerModel.Events.ParsedScriptSource,(e=>{queueMicrotask((()=>{this.#Ue(e)}))})),e.addEventListener(r.DebuggerModel.Events.GlobalObjectCleared,this.#Pe,this)}setCSSModel(e){if(this.#C)throw new Error("Cannot set CSSModel twice");this.#C=e,e.addEventListener(r.CSSModel.Events.StyleSheetAdded,(e=>queueMicrotask((()=>this.#ke(e)))))}async addMessage(e,t){const o=new Re(e,this.#h),r=this.#je(t)??this.#De(t)??this.#Ee(t);if(r&&await o.updateLocationSource(r),t.url){let e=this.#Re.get(t.url);e||(e=[],this.#Re.set(t.url,e)),e.push({source:t,presentation:o})}}#Ee(e){if(!e.url)return null;const t=i.Workspace.WorkspaceImpl.instance().uiSourceCodeForURL(e.url);return t?new i.UISourceCode.UILocation(t,e.line,e.column):null}#De(e){if(!this.#C||!e.url)return null;return this.#C.createRawLocationsByURL(e.url,e.line,e.column)[0]??null}#je(e){if(!this.#ne)return null;if(e.scriptId)return this.#ne.createRawLocationByScriptId(e.scriptId,e.line,e.column);const t=e.stackTrace?.callFrames?e.stackTrace.callFrames[0]:null;return t?this.#ne.createRawLocationByScriptId(t.scriptId,t.lineNumber,t.columnNumber):e.url?this.#ne.createRawLocationByURL(e.url,e.line,e.column):null}#Ue(e){const t=e.data,o=this.#Re.get(t.sourceURL),r=[];for(const{presentation:e,source:s}of o??[]){const o=this.#je(s);o&&t.scriptId===o.scriptId&&r.push(e.updateLocationSource(o))}Promise.all(r).then(this.parsedScriptSourceForTest.bind(this))}parsedScriptSourceForTest(){}#Fe(e){const t=e.data,o=this.#Re.get(t.url()),r=[];for(const{presentation:e,source:s}of o??[])r.push(e.updateLocationSource(new i.UISourceCode.UILocation(t,s.line,s.column)));Promise.all(r).then(this.uiSourceCodeAddedForTest.bind(this))}uiSourceCodeAddedForTest(){}#ke(e){const t=e.data,o=this.#Re.get(t.sourceURL),s=[];for(const{source:e,presentation:i}of o??[])t.containsLocation(e.line,e.column)&&s.push(i.updateLocationSource(new r.CSSModel.CSSLocation(t,e.line,e.column)));Promise.all(s).then(this.styleSheetAddedForTest.bind(this))}styleSheetAddedForTest(){}clear(){this.#Pe()}#Pe(){const e=Array.from(this.#Re.values()).flat();for(const{presentation:t}of e)t.dispose();this.#Re.clear(),this.#h.disposeAll()}}class Te extends R{#Ee;constructor(e,t,o){super(t,o),this.#Ee=e}async isIgnoreListed(){return!1}async uiLocation(){return this.#Ee}}class Re{#xe;#Ne;#h;#Ae;constructor(e,t){this.#Ae=e,this.#h=t}async updateLocationSource(e){e instanceof r.DebuggerModel.Location?await Le.instance().createLiveLocation(e,this.#Oe.bind(this),this.#h):e instanceof r.CSSModel.CSSLocation?await z.instance().createLiveLocation(e,this.#Oe.bind(this),this.#h):e instanceof i.UISourceCode.UILocation&&(this.#Ne||(this.#Ne=new Te(e,this.#Oe.bind(this),this.#h),await this.#Ne.update()))}async#Oe(e){this.#xe&&this.#xe.removeMessage(this.#Ae),e!==this.#Ne&&(this.#xe?.removeMessage(this.#Ae),this.#Ne?.dispose(),this.#Ne=e);const t=await e.uiLocation();t&&(this.#Ae.range=s.TextRange.TextRange.createFromLocation(t.lineNumber,t.columnNumber||0),this.#xe=t.uiSourceCode,this.#xe.addMessage(this.#Ae))}dispose(){this.#xe?.removeMessage(this.#Ae),this.#Ne?.dispose()}}var Fe=Object.freeze({__proto__:null,PresentationConsoleMessageManager:class{#We=new ve;constructor(){r.TargetManager.TargetManager.instance().addModelListener(r.ConsoleModel.ConsoleModel,r.ConsoleModel.Events.MessageAdded,(e=>this.consoleMessageAdded(e.data))),r.ConsoleModel.ConsoleModel.allMessagesUnordered().forEach(this.consoleMessageAdded,this),r.TargetManager.TargetManager.instance().addModelListener(r.ConsoleModel.ConsoleModel,r.ConsoleModel.Events.ConsoleCleared,(()=>this.#We.clear()))}consoleMessageAdded(e){const t=e.runtimeModel();if(!e.isErrorOrWarning()||!e.runtimeModel()||"violation"===e.source||!t)return;const o="error"===e.level?"Error":"Warning";this.#We.addMessage(new i.UISourceCode.Message(o,e.messageText),e,t.target())}},PresentationSourceFrameMessage:Re,PresentationSourceFrameMessageHelper:ye,PresentationSourceFrameMessageManager:ve});const Ue=new WeakMap,Pe=new WeakMap,ke=new WeakSet;function je(e){return new s.TextRange.TextRange(e.lineOffset,e.columnOffset,e.endLine,e.endColumn)}function De(e){return new s.TextRange.TextRange(e.startLine,e.startColumn,e.endLine,e.endColumn)}class Ee{project;#M;#C;#u;constructor(e,t){const o=t.target();this.project=new d(e,"resources:"+o.id(),i.Workspace.projectTypes.Network,"",!1),I.setTargetForProject(this.project,o),this.#M=new Map;const s=o.model(r.CSSModel.CSSModel);console.assert(Boolean(s)),this.#C=s;for(const e of t.frames())for(const t of e.getResourcesMap().values())this.addResource(t);this.#u=[t.addEventListener(r.ResourceTreeModel.Events.ResourceAdded,this.resourceAdded,this),t.addEventListener(r.ResourceTreeModel.Events.FrameWillNavigate,this.frameWillNavigate,this),t.addEventListener(r.ResourceTreeModel.Events.FrameDetached,this.frameDetached,this),this.#C.addEventListener(r.CSSModel.Events.StyleSheetChanged,(e=>{this.styleSheetChanged(e)}),this)]}async styleSheetChanged(e){const t=this.#C.styleSheetHeaderForId(e.data.styleSheetId);if(!t||!t.isInline||t.isInline&&t.isMutable)return;const o=this.#M.get(t.resourceURL());o&&await o.styleSheetChanged(t,e.data.edit||null)}acceptsResource(t){const o=t.resourceType();return(o===e.ResourceType.resourceTypes.Image||o===e.ResourceType.resourceTypes.Font||o===e.ResourceType.resourceTypes.Document||o===e.ResourceType.resourceTypes.Manifest||o===e.ResourceType.resourceTypes.Fetch||o===e.ResourceType.resourceTypes.XHR)&&(!(o===e.ResourceType.resourceTypes.Image&&t.mimeType&&!t.mimeType.startsWith("image"))&&(!(o===e.ResourceType.resourceTypes.Font&&t.mimeType&&!t.mimeType.includes("font"))&&(o!==e.ResourceType.resourceTypes.Image&&o!==e.ResourceType.resourceTypes.Font||!e.ParsedURL.schemeIs(t.contentURL(),"data:"))))}resourceAdded(e){this.addResource(e.data)}addResource(e){if(!this.acceptsResource(e))return;let t=this.#M.get(e.url);t?t.addResource(e):(t=new xe(this.project,e),this.#M.set(e.url,t))}removeFrameResources(e){for(const t of e.resources()){if(!this.acceptsResource(t))continue;const e=this.#M.get(t.url);e&&(1===e.resources.size?(e.dispose(),this.#M.delete(t.url)):e.removeResource(t))}}frameWillNavigate(e){this.removeFrameResources(e.data)}frameDetached(e){this.removeFrameResources(e.data.frame)}resetForTest(){for(const e of this.#M.values())e.dispose();this.#M.clear()}dispose(){e.EventTarget.removeEventListeners(this.#u);for(const e of this.#M.values())e.dispose();this.#M.clear(),this.project.removeProject()}getProject(){return this.project}}class xe{resources;#L;#xe;#Be;constructor(e,t){this.resources=new Set([t]),this.#L=e,this.#xe=this.#L.createUISourceCode(t.url,t.contentType()),ke.add(this.#xe),t.frameId&&I.setInitialFrameAttribution(this.#xe,t.frameId),this.#L.addUISourceCodeWithProvider(this.#xe,this,N(t),t.mimeType),this.#Be=[],Promise.all([...this.inlineScripts().map((e=>Le.instance().updateLocations(e))),...this.inlineStyles().map((e=>z.instance().updateLocations(e)))])}inlineStyles(){const e=I.targetForUISourceCode(this.#xe),t=[];if(!e)return t;const o=e.model(r.CSSModel.CSSModel);if(o)for(const e of o.getStyleSheetIdsForURL(this.#xe.url())){const r=o.styleSheetHeaderForId(e);r&&t.push(r)}return t}inlineScripts(){const e=I.targetForUISourceCode(this.#xe);if(!e)return[];const t=e.model(r.DebuggerModel.DebuggerModel);return t?t.scripts().filter((e=>e.embedderName()===this.#xe.url())):[]}async styleSheetChanged(e,t){if(this.#Be.push({stylesheet:e,edit:t}),this.#Be.length>1)return;const o=await this.#xe.requestContentData();s.ContentData.ContentData.isError(o)||await this.innerStyleSheetChanged(o.text),this.#Be=[]}async innerStyleSheetChanged(e){const t=this.inlineScripts(),o=this.inlineStyles();let r=new s.Text.Text(e);for(const e of this.#Be){const i=e.edit;if(!i)continue;const n=e.stylesheet,a=Ue.get(n)??De(n),c=i.oldRange.relativeFrom(a.startLine,a.startColumn),u=i.newRange.relativeFrom(a.startLine,a.startColumn);r=new s.Text.Text(r.replaceRange(c,i.newText));const d=[];for(const e of t){const t=Pe.get(e)??je(e);t.follows(c)&&(Pe.set(e,t.rebaseAfterTextEdit(c,u)),d.push(Le.instance().updateLocations(e)))}for(const e of o){const t=Ue.get(e)??De(e);t.follows(c)&&(Ue.set(e,t.rebaseAfterTextEdit(c,u)),d.push(z.instance().updateLocations(e)))}await Promise.all(d)}this.#xe.addRevision(r.value())}addResource(e){this.resources.add(e),e.frameId&&I.addFrameAttribution(this.#xe,e.frameId)}removeResource(e){this.resources.delete(e),e.frameId&&I.removeFrameAttribution(this.#xe,e.frameId)}dispose(){this.#L.removeUISourceCode(this.#xe.url()),Promise.all([...this.inlineScripts().map((e=>Le.instance().updateLocations(e))),...this.inlineStyles().map((e=>z.instance().updateLocations(e)))])}firstResource(){return console.assert(this.resources.size>0),this.resources.values().next().value}contentURL(){return this.firstResource().contentURL()}contentType(){return this.firstResource().contentType()}requestContent(){return this.firstResource().requestContent()}requestContentData(){return this.firstResource().requestContentData()}searchInContent(e,t,o){return this.firstResource().searchInContent(e,t,o)}}var Ne=Object.freeze({__proto__:null,ResourceMapping:class{workspace;#k;constructor(e,t){this.workspace=t,this.#k=new Map,e.observeModels(r.ResourceTreeModel.ResourceTreeModel,this)}modelAdded(e){const t=new Ee(this.workspace,e);this.#k.set(e,t)}modelRemoved(e){const t=this.#k.get(e);t&&(t.dispose(),this.#k.delete(e))}infoForTarget(e){const t=e.model(r.ResourceTreeModel.ResourceTreeModel);return t&&this.#k.get(t)||null}uiSourceCodeForScript(e){const t=this.infoForTarget(e.debuggerModel.target());if(!t)return null;return t.getProject().uiSourceCodeForURL(e.sourceURL)}cssLocationToUILocation(e){const t=e.header();if(!t)return null;const o=this.infoForTarget(e.cssModel().target());if(!o)return null;const r=o.getProject().uiSourceCodeForURL(e.url);if(!r)return null;const s=Ue.get(t)??De(t),i=e.lineNumber+s.startLine-t.startLine;let n=e.columnNumber;return e.lineNumber===t.startLine&&(n+=s.startColumn-t.startColumn),r.uiLocation(i,n)}jsLocationToUILocation(e){const t=e.script();if(!t)return null;const o=this.infoForTarget(e.debuggerModel.target());if(!o)return null;const r=t.embedderName();if(!r)return null;const s=o.getProject().uiSourceCodeForURL(r);if(!s)return null;const{startLine:i,startColumn:n}=Pe.get(t)??je(t);let{lineNumber:a,columnNumber:c}=e;return a===t.lineOffset&&(c+=n-t.columnOffset),a+=i-t.lineOffset,t.hasSourceURL&&(0===a&&(c+=t.columnOffset),a+=t.lineOffset),s.uiLocation(a,c)}uiLocationToJSLocations(e,t,o){if(!ke.has(e))return[];const s=I.targetForUISourceCode(e);if(!s)return[];const i=s.model(r.DebuggerModel.DebuggerModel);if(!i)return[];const n=[];for(const r of i.scripts()){if(r.embedderName()!==e.url())continue;const s=Pe.get(r)??je(r);if(!s.containsLocation(t,o))continue;let a=t,c=o;r.hasSourceURL&&(a-=s.startLine,0===a&&(c-=s.startColumn)),n.push(i.createRawLocation(r,a,c))}return n}uiLocationRangeToJSLocationRanges(e,t){if(!ke.has(e))return null;const o=I.targetForUISourceCode(e);if(!o)return null;const s=o.model(r.DebuggerModel.DebuggerModel);if(!s)return null;const i=[];for(const o of s.scripts()){if(o.embedderName()!==e.url())continue;const r=(Pe.get(o)??je(o)).intersection(t);if(r.isEmpty())continue;let{startLine:n,startColumn:a,endLine:c,endColumn:u}=r;o.hasSourceURL&&(n-=r.startLine,0===n&&(a-=r.startColumn),c-=r.startLine,0===c&&(u-=r.startColumn));const d=s.createRawLocation(o,n,a),l=s.createRawLocation(o,c,u);i.push({start:d,end:l})}return i}getMappedLines(e){if(!ke.has(e))return null;const t=I.targetForUISourceCode(e);if(!t)return null;const o=t.model(r.DebuggerModel.DebuggerModel);if(!o)return null;const s=new Set;for(const t of o.scripts()){if(t.embedderName()!==e.url())continue;const{startLine:o,endLine:r}=Pe.get(t)??je(t);for(let e=o;e<=r;++e)s.add(e)}return s}uiLocationToCSSLocations(e){if(!ke.has(e.uiSourceCode))return[];const t=I.targetForUISourceCode(e.uiSourceCode);if(!t)return[];const o=t.model(r.CSSModel.CSSModel);return o?o.createRawLocationsByURL(e.uiSourceCode.url(),e.lineNumber,e.columnNumber):[]}resetForTest(e){const t=e.model(r.ResourceTreeModel.ResourceTreeModel),o=t?this.#k.get(t):null;o&&o.resetForTest()}}});var Ae=Object.freeze({__proto__:null,TempFile:class{#He;constructor(){this.#He=null}write(e){this.#He&&e.unshift(this.#He),this.#He=new Blob(e,{type:"text/plain"})}read(){return this.readRange()}size(){return this.#He?this.#He.size:0}async readRange(t,o){if(!this.#He)return e.Console.Console.instance().error("Attempt to read a temp file that was never written"),"";const r="number"==typeof t||"number"==typeof o?this.#He.slice(t,o):this.#He,s=new FileReader;try{await new Promise(((e,t)=>{s.onloadend=e,s.onerror=t,s.readAsText(r)}))}catch(t){e.Console.Console.instance().error("Failed to read from temp file: "+t.message)}return s.result}async copyToOutputStream(e,t){if(!this.#He)return e.close(),null;const o=new we(this.#He,1e7,t);return await o.read(e).then((e=>e?null:o.error()))}remove(){this.#He=null}}});export{G as CSSWorkspaceBinding,T as CompilerScriptMapping,l as ContentProviderBasedProject,ne as DebuggerLanguagePlugins,Ce as DebuggerWorkspaceBinding,ue as DefaultScriptMapping,Ie as FileUtils,M as IgnoreListManager,U as LiveLocation,v as NetworkProject,Fe as PresentationConsoleMessageHelper,Ne as ResourceMapping,me as ResourceScriptMapping,A as ResourceUtils,D as SASSSourceMapping,H as StylesSourceMapping,Ae as TempFile};
