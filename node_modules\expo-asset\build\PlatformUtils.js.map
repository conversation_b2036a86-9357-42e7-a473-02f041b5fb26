{"version": 3, "file": "PlatformUtils.js", "sourceRoot": "", "sources": ["../src/PlatformUtils.ts"], "names": [], "mappings": "AAAA,OAAO,SAAS,MAAM,gBAAgB,CAAC;AACvC,OAAO,EAAE,mBAAmB,EAAE,2BAA2B,EAAE,MAAM,mBAAmB,CAAC;AAIrF,OAAO,EAAE,kBAAkB,EAAE,MAAM,aAAa,CAAC;AAEjD,MAAM,WAAW,GAAG,2BAA2B,CAAoB,aAAa,CAAC,CAAC;AAElF,MAAM,kBAAkB,GAAG,CAAC,GAAG,EAAE;IAC/B,IAAI,CAAC;QACH,OAAO,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IACvC,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC,CAAC,EAAE,CAAC;AAEL,SAAS,iBAAiB;IACxB,OAAO,kBAAkB,IAAI,IAAI,CAAC;AACpC,CAAC;AAED,2FAA2F;AAC3F,gFAAgF;AAChF,MAAM,gCAAgC,GAAG,CAAC,CAAC,WAAW,EAAE,SAAS,CAAC;AAClE,MAAM,gCAAgC,GAAG,WAAW,EAAE,qBAAqB,CAAC;AAE5E,kGAAkG;AAClG,uCAAuC;AACvC,MAAM,+BAA+B,GACnC,gCAAgC,IAAI,CAAC,gCAAgC,CAAC;AAExE,sFAAsF;AACtF,qCAAqC;AACrC,MAAM,CAAC,MAAM,wBAAwB,GAAG,iBAAiB,EAAE,IAAI,+BAA+B,CAAC;AAE/F,gFAAgF;AAChF,iEAAiE;AACjE,MAAM,UAAU,cAAc;IAC5B,OAAO,WAAW,EAAE,WAAW,IAAI,EAAE,CAAC;AACxC,CAAC;AAED,MAAM,UAAU,YAAY;IAC1B,OAAO,SAAS,CAAC,uBAAuB,CAAC;AAC3C,CAAC;AAED,yCAAyC;AACzC,MAAM,CAAC,MAAM,eAAe,GAAG,SAAS,CAAC,aAAa;IACpD,CAAC,CAAC,kBAAkB,CAAC,SAAS,CAAC,aAAa,CAAC;IAC7C,CAAC,CAAC,IAAI,CAAC", "sourcesContent": ["import Constants from 'expo-constants';\nimport { requireNativeModule, requireOptionalNativeModule } from 'expo-modules-core';\n// @ts-ignore -- optional interface, will gracefully degrade to `any` if not installed\nimport type { ExpoUpdatesModule } from 'expo-updates';\n\nimport { getManifestBaseUrl } from './AssetUris';\n\nconst ExpoUpdates = requireOptionalNativeModule<ExpoUpdatesModule>('ExpoUpdates');\n\nconst NativeExpoGoModule = (() => {\n  try {\n    return requireNativeModule('ExpoGo');\n  } catch {\n    return null;\n  }\n})();\n\nfunction isRunningInExpoGo(): boolean {\n  return NativeExpoGoModule != null;\n}\n\n// expo-updates (and Expo Go expo-updates override) manages assets from updates and exposes\n// the ExpoUpdates.localAssets constant containing information about the assets.\nconst expoUpdatesIsInstalledAndEnabled = !!ExpoUpdates?.isEnabled;\nconst expoUpdatesIsUsingEmbeddedAssets = ExpoUpdates?.isUsingEmbeddedAssets;\n\n// if expo-updates is installed but we're running directly from the embedded bundle, we don't want\n// to override the AssetSourceResolver.\nconst shouldUseUpdatesAssetResolution =\n  expoUpdatesIsInstalledAndEnabled && !expoUpdatesIsUsingEmbeddedAssets;\n\n// Expo Go always uses the updates module for asset resolution (local assets) since it\n// overrides the expo-updates module.\nexport const IS_ENV_WITH_LOCAL_ASSETS = isRunningInExpoGo() || shouldUseUpdatesAssetResolution;\n\n// Get the localAssets property from the ExpoUpdates native module so that we do\n// not need to include expo-updates as a dependency of expo-asset\nexport function getLocalAssets(): Record<string, string> {\n  return ExpoUpdates?.localAssets ?? {};\n}\n\nexport function getManifest2(): typeof Constants.__unsafeNoWarnManifest2 {\n  return Constants.__unsafeNoWarnManifest2;\n}\n\n// Compute manifest base URL if available\nexport const manifestBaseUrl = Constants.experienceUrl\n  ? getManifestBaseUrl(Constants.experienceUrl)\n  : null;\n"]}