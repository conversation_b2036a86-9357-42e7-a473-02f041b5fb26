import*as e from"../../core/common/common.js";import*as t from"../../core/host/host.js";import*as i from"../../core/i18n/i18n.js";import*as s from"../../core/platform/platform.js";import{assertNotNullOrUndefined as r}from"../../core/platform/platform.js";import*as o from"../../core/sdk/sdk.js";import*as a from"../../models/issues_manager/issues_manager.js";import*as n from"../../ui/components/icon_button/icon_button.js";import*as d from"../../ui/components/legacy_wrapper/legacy_wrapper.js";import*as l from"../../ui/legacy/components/source_frame/source_frame.js";import*as c from"../../ui/legacy/legacy.js";import*as h from"../../ui/components/buttons/buttons.js";import*as g from"../../ui/legacy/components/inline_editor/inline_editor.js";import*as u from"../../ui/legacy/components/utils/utils.js";import*as p from"../../ui/visual_logging/visual_logging.js";import*as m from"./components/components.js";import{StorageMetadataView as v}from"./components/components.js";import*as w from"../../models/bindings/bindings.js";import*as S from"../../ui/legacy/components/data_grid/data_grid.js";import"../../ui/components/report_view/report_view.js";import*as b from"../../ui/legacy/components/object_ui/object_ui.js";import*as y from"../../ui/lit/lit.js";import{render as k,html as f,Directives as I,nothing as C}from"../../ui/lit/lit.js";import*as T from"./preloading/components/components.js";import*as x from"../../models/text_utils/text_utils.js";import*as E from"../network/components/components.js";import*as M from"../network/network.js";import*as R from"../../models/logs/logs.js";import*as L from"../network/forward/forward.js";import*as A from"../mobile_throttling/mobile_throttling.js";import*as B from"../../ui/legacy/components/perf_ui/perf_ui.js";import*as P from"../../ui/legacy/components/cookie_table/cookie_table.js";import*as D from"../../third_party/json5/json5.js";class F extends c.TreeOutline.TreeElement{resourcesPanel;constructor(e,t,i,s){super(t,i,s),this.resourcesPanel=e,c.ARIAUtils.setLabel(this.listItemElement,t),this.listItemElement.tabIndex=-1}deselect(){super.deselect(),this.listItemElement.tabIndex=-1}get itemURL(){throw new Error("Unimplemented Method")}onselect(e){if(!e)return!1;const t=[];for(let e=this;e;e=e.parent){const i=e instanceof F&&e.itemURL;if(!i)break;t.push(i)}return this.resourcesPanel.setLastSelectedItemPath(t),!1}showView(e){this.resourcesPanel.showView(e)}}class O extends F{expandedSetting;categoryName;categoryLink;emptyCategoryHeadline;categoryDescription;constructor(t,i,s,r,o,a=!1){super(t,i,!1,o),this.expandedSetting=e.Settings.Settings.instance().createSetting("resources-"+o+"-expanded",a),this.categoryName=i,this.categoryLink=null,this.emptyCategoryHeadline=s,this.categoryDescription=r}get itemURL(){return"category://"+this.categoryName}setLink(e){this.categoryLink=e}onselect(e){return super.onselect(e),this.updateCategoryView(),!1}updateCategoryView(){const e=0===this.childCount()?this.emptyCategoryHeadline:this.categoryName;this.resourcesPanel.showCategoryView(this.categoryName,e,this.categoryDescription,this.categoryLink)}appendChild(e,t){super.appendChild(e,t),this.selected&&1===this.childCount()&&this.updateCategoryView()}removeChild(e){super.removeChild(e),this.selected&&0===this.childCount()&&this.updateCategoryView()}onattach(){super.onattach(),this.expandedSetting.get()&&this.expand()}onexpand(){this.expandedSetting.set(!0)}oncollapse(){this.expandedSetting.set(!1)}}var V={cssText:`.report-field-name{flex-basis:152px}.manifest-view-header{min-width:600px;flex-shrink:0;flex-grow:0}.manifest-container{overflow:auto}.inline-icon{width:16px;height:16px;margin-inline:var(--sys-size-3);&[name="check-circle"]{color:var(--icon-checkmark-green)}}.multiline-value{padding-top:var(--sys-size-5);white-space:normal}select{margin:4px}.inline-button{vertical-align:sub}\n/*# sourceURL=${import.meta.resolve("./appManifestView.css")} */\n`};const U={errorsAndWarnings:"Errors and warnings",installability:"Installability",identity:"Identity",presentation:"Presentation",protocolHandlers:"Protocol Handlers",icons:"Icons",windowControlsOverlay:"Window Controls Overlay",name:"Name",shortName:"Short name",url:"URL",computedAppId:"Computed App ID",appIdExplainer:"This is used by the browser to know whether the manifest should be updating an existing application, or whether it refers to a new web app that can be installed.",learnMore:"Learn more",appIdNote:"Note: `id` is not specified in the manifest, `start_url` is used instead. To specify an App ID that matches the current identity, set the `id` field to {PH1} {PH2}.",copyToClipboard:"Copy suggested ID to clipboard",copiedToClipboard:"Copied suggested ID {PH1} to clipboard",description:"Description",startUrl:"Start URL",themeColor:"Theme color",backgroundColor:"Background color",orientation:"Orientation",display:"Display",newNoteUrl:"New note URL",descriptionMayBeTruncated:"Description may be truncated.",shortcutsMayBeNotAvailable:"The maximum number of shortcuts is platform dependent. Some shortcuts may be not available.",showOnlyTheMinimumSafeAreaFor:"Show only the minimum safe area for maskable icons",documentationOnMaskableIcons:"documentation on maskable icons",needHelpReadOurS:"Need help? Read the {PH1}.",shortcutS:"Shortcut #{PH1}",shortcutSShouldIncludeAXPixel:"Shortcut #{PH1} should include a 96×96 pixel icon",screenshotS:"Screenshot #{PH1}",pageIsNotLoadedInTheMainFrame:"Page is not loaded in the main frame",pageIsNotServedFromASecureOrigin:"Page is not served from a secure origin",pageHasNoManifestLinkUrl:"Page has no manifest <link> `URL`",manifestCouldNotBeFetchedIsEmpty:"Manifest could not be fetched, is empty, or could not be parsed",manifestStartUrlIsNotValid:"Manifest '`start_url`' is not valid",manifestDoesNotContainANameOr:"Manifest does not contain a '`name`' or '`short_name`' field",manifestDisplayPropertyMustBeOne:"Manifest '`display`' property must be one of '`standalone`', '`fullscreen`', or '`minimal-ui`'",manifestDoesNotContainASuitable:"Manifest does not contain a suitable icon—PNG, SVG, or WebP format of at least {PH1}px is required, the '`sizes`' attribute must be set, and the '`purpose`' attribute, if set, must include '`any`'.",avoidPurposeAnyAndMaskable:"Declaring an icon with '`purpose`' of '`any maskable`' is discouraged. It is likely to look incorrect on some platforms due to too much or too little padding.",noSuppliedIconIsAtLeastSpxSquare:"No supplied icon is at least {PH1} pixels square in `PNG`, `SVG`, or `WebP` format, with the purpose attribute unset or set to '`any`'.",couldNotDownloadARequiredIcon:"Could not download a required icon from the manifest",downloadedIconWasEmptyOr:"Downloaded icon was empty or corrupted",theSpecifiedApplicationPlatform:"The specified application platform is not supported on Android",noPlayStoreIdProvided:"No Play store ID provided",thePlayStoreAppUrlAndPlayStoreId:"The Play Store app URL and Play Store ID do not match",theAppIsAlreadyInstalled:"The app is already installed",aUrlInTheManifestContainsA:"A URL in the manifest contains a username, password, or port",pageIsLoadedInAnIncognitoWindow:"Page is loaded in an incognito window",pageDoesNotWorkOffline:"Page does not work offline",couldNotCheckServiceWorker:"Could not check `service worker` without a '`start_url`' field in the manifest",manifestSpecifies:"Manifest specifies '`prefer_related_applications`: true'",preferrelatedapplicationsIsOnly:"'`prefer_related_applications`' is only supported on `Chrome` Beta and Stable channels on `Android`.",manifestContainsDisplayoverride:"Manifest contains '`display_override`' field, and the first supported display mode must be one of '`standalone`', '`fullscreen`', or '`minimal-ui`'",pageDoesNotWorkOfflineThePage:"Page does not work offline. Starting in Chrome 93, the installability criteria are changing, and this site will not be installable. See {PH1} for more information.",imageFromS:"Image from {PH1}",screenshot:"Screenshot",formFactor:"Form factor",label:"Label",platform:"Platform",icon:"Icon",sSrcIsNotSet:"{PH1} '`src`' is not set",sUrlSFailedToParse:"{PH1} URL ''{PH2}'' failed to parse",sSFailedToLoad:"{PH1} {PH2} failed to load",sSDoesNotSpecifyItsSizeInThe:"{PH1} {PH2} does not specify its size in the manifest",sSShouldSpecifyItsSizeAs:"{PH1} {PH2} should specify its size as `[width]x[height]`",sSShouldHaveSquareIcon:"Most operating systems require square icons. Please include at least one square icon in the array.",actualSizeSspxOfSSDoesNotMatch:"Actual size ({PH1}×{PH2})px of {PH3} {PH4} does not match specified size ({PH5}×{PH6}px)",actualWidthSpxOfSSDoesNotMatch:"Actual width ({PH1}px) of {PH2} {PH3} does not match specified width ({PH4}px)",actualHeightSpxOfSSDoesNotMatch:"Actual height ({PH1}px) of {PH2} {PH3} does not match specified height ({PH4}px)",sSSizeShouldBeAtLeast320:"{PH1} {PH2} size should be at least 320×320",sSSizeShouldBeAtMost3840:"{PH1} {PH2} size should be at most 3840×3840",sSWidthDoesNotComplyWithRatioRequirement:"{PH1} {PH2} width can't be more than 2.3 times as long as the height",sSHeightDoesNotComplyWithRatioRequirement:"{PH1} {PH2} height can't be more than 2.3 times as long as the width",screenshotPixelSize:"Screenshot {url} should specify a pixel size `[width]x[height]` instead of `any` as first size.",noScreenshotsForRicherPWAInstallOnDesktop:"Richer PWA Install UI won’t be available on desktop. Please add at least one screenshot with the `form_factor` set to `wide`.",noScreenshotsForRicherPWAInstallOnMobile:"Richer PWA Install UI won’t be available on mobile. Please add at least one screenshot for which `form_factor` is not set or set to a value other than `wide`.",tooManyScreenshotsForDesktop:"No more than 8 screenshots will be displayed on desktop. The rest will be ignored.",tooManyScreenshotsForMobile:"No more than 5 screenshots will be displayed on mobile. The rest will be ignored.",screenshotsMustHaveSameAspectRatio:"All screenshots with the same `form_factor` must have the same aspect ratio as the first screenshot with that `form_factor`. Some screenshots will be ignored.",wcoFound:"Chrome has successfully found the {PH1} value for the {PH2} field in the {PH3}.",wcoNotFound:"Define {PH1} in the manifest to use the Window Controls Overlay API and customize your app's title bar.",customizePwaTitleBar:"Customize the window controls overlay of your PWA's title bar",wcoNeedHelpReadMore:"Need help? Read {PH1}.",selectWindowControlsOverlayEmulationOs:"Emulate the Window Controls Overlay on"},W=i.i18n.registerUIStrings("panels/application/AppManifestView.ts",U),N=i.i18n.getLocalizedString.bind(void 0,W);class j extends(e.ObjectWrapper.eventMixin(c.Widget.VBox)){emptyView;reportView;errorsSection;installabilitySection;identitySection;presentationSection;iconsSection;windowControlsSection;protocolHandlersSection;shortcutSections;screenshotsSections;nameField;shortNameField;descriptionField;startURLField;themeColorSwatch;backgroundColorSwatch;orientationField;displayField;newNoteUrlField;throttler;registeredListeners;target;resourceTreeModel;serviceWorkerManager;overlayModel;protocolHandlersView;constructor(e,t,i){super(!0),this.registerRequiredCSS(V),this.contentElement.classList.add("manifest-container"),this.contentElement.setAttribute("jslog",`${p.pane("manifest")}`),this.emptyView=e,this.emptyView.appendLink("https://web.dev/add-manifest/"),this.emptyView.show(this.contentElement),this.emptyView.hideWidget(),this.reportView=t,this.reportView.registerRequiredCSS(V),this.reportView.element.classList.add("manifest-view-header"),this.reportView.show(this.contentElement),this.reportView.hideWidget(),this.errorsSection=this.reportView.appendSection(N(U.errorsAndWarnings),void 0,"errors-and-warnings"),this.installabilitySection=this.reportView.appendSection(N(U.installability),void 0,"installability"),this.identitySection=this.reportView.appendSection(N(U.identity),"undefined,identity"),this.presentationSection=this.reportView.appendSection(N(U.presentation),"undefined,presentation"),this.protocolHandlersSection=this.reportView.appendSection(N(U.protocolHandlers),"undefined,protocol-handlers"),this.protocolHandlersView=new m.ProtocolHandlersView.ProtocolHandlersView,this.protocolHandlersSection.appendFieldWithCustomView(this.protocolHandlersView),this.iconsSection=this.reportView.appendSection(N(U.icons),"report-section-icons","icons"),this.windowControlsSection=this.reportView.appendSection(U.windowControlsOverlay,void 0,"window-controls-overlay"),this.shortcutSections=[],this.screenshotsSections=[],this.nameField=this.identitySection.appendField(N(U.name)),this.shortNameField=this.identitySection.appendField(N(U.shortName)),this.descriptionField=this.identitySection.appendFlexedField(N(U.description)),this.startURLField=this.presentationSection.appendField(N(U.startUrl)),c.ARIAUtils.setLabel(this.startURLField,N(U.startUrl));const s=this.presentationSection.appendField(N(U.themeColor));this.themeColorSwatch=new g.ColorSwatch.ColorSwatch,s.appendChild(this.themeColorSwatch);const r=this.presentationSection.appendField(N(U.backgroundColor));this.backgroundColorSwatch=new g.ColorSwatch.ColorSwatch,r.appendChild(this.backgroundColorSwatch),this.orientationField=this.presentationSection.appendField(N(U.orientation)),this.displayField=this.presentationSection.appendField(N(U.display)),this.newNoteUrlField=this.presentationSection.appendField(N(U.newNoteUrl)),this.throttler=i,o.TargetManager.TargetManager.instance().observeTargets(this),this.registeredListeners=[]}getStaticSections(){return[this.identitySection,this.presentationSection,this.protocolHandlersSection,this.iconsSection,this.windowControlsSection]}getManifestElement(){return this.reportView.getHeaderElement()}targetAdded(e){e===o.TargetManager.TargetManager.instance().primaryPageTarget()&&(this.target=e,this.resourceTreeModel=e.model(o.ResourceTreeModel.ResourceTreeModel),this.serviceWorkerManager=e.model(o.ServiceWorkerManager.ServiceWorkerManager),this.overlayModel=e.model(o.OverlayModel.OverlayModel),this.resourceTreeModel&&this.serviceWorkerManager&&this.overlayModel&&(this.updateManifest(!0),this.registeredListeners=[this.resourceTreeModel.addEventListener(o.ResourceTreeModel.Events.DOMContentLoaded,(()=>{this.updateManifest(!0)})),this.serviceWorkerManager.addEventListener("RegistrationUpdated",(()=>{this.updateManifest(!1)}))]))}targetRemoved(t){this.target===t&&this.resourceTreeModel&&this.serviceWorkerManager&&this.overlayModel&&(delete this.resourceTreeModel,delete this.serviceWorkerManager,delete this.overlayModel,e.EventTarget.removeEventListeners(this.registeredListeners))}async updateManifest(e){if(!this.resourceTreeModel)return;const[{url:t,data:i,errors:s},r,o]=await Promise.all([this.resourceTreeModel.fetchAppManifest(),this.resourceTreeModel.getInstallabilityErrors(),this.resourceTreeModel.getAppId()]);this.throttler.schedule((()=>this.renderManifest(t,i,s,r,o)),e?"AsSoonAsPossible":"Default")}async renderManifest(s,r,o,a,d){const l=d?.appId||null,g=d?.recommendedId||null;if(!(r&&"{}"!==r||o.length))return this.emptyView.showWidget(),this.reportView.hideWidget(),void this.dispatchEventToListeners("ManifestDetected",!1);this.emptyView.hideWidget(),this.reportView.showWidget(),this.dispatchEventToListeners("ManifestDetected",!0);const m=u.Linkifier.Linkifier.linkifyURL(s);m.tabIndex=0,this.reportView.setURL(m),this.errorsSection.clearContent(),this.errorsSection.element.classList.toggle("hidden",!o.length);for(const e of o){const t=c.UIUtils.createIconLabel({title:e.message,iconName:e.critical?"cross-circle-filled":"warning-filled",color:e.critical?"var(--icon-error)":"var(--icon-warning)"});this.errorsSection.appendRow().appendChild(t)}if(!r)return;65279===r.charCodeAt(0)&&(r=r.slice(1));const v=JSON.parse(r);this.nameField.textContent=_("name"),this.shortNameField.textContent=_("short_name");const w=[],S=_("description");this.descriptionField.textContent=S,S.length>300&&w.push(N(U.descriptionMayBeTruncated));const b=_("start_url");if(l&&g){const e=this.identitySection.appendField(N(U.computedAppId));c.ARIAUtils.setLabel(e,"App Id"),e.textContent=l;const s=n.Icon.create("help","inline-icon");s.title=N(U.appIdExplainer),s.setAttribute("jslog",`${p.action("help").track({hover:!0})}`),e.appendChild(s);const r=c.XLink.XLink.create("https://developer.chrome.com/blog/pwa-manifest-id/",N(U.learnMore),void 0,void 0,"learn-more");if(e.appendChild(r),!_("id")){const s=e.createChild("div","multiline-value"),r=document.createElement("code");r.textContent=g;const o=new h.Button.Button;o.data={variant:"icon",iconName:"copy",size:"SMALL",jslogContext:"manifest.copy-id",title:N(U.copyToClipboard)},o.className="inline-button",o.addEventListener("click",(()=>{c.ARIAUtils.alert(N(U.copiedToClipboard,{PH1:g})),t.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(g)})),s.appendChild(i.i18n.getFormatLocalizedString(W,U.appIdNote,{PH1:r,PH2:o}))}}else this.identitySection.removeField(N(U.computedAppId));if(this.startURLField.removeChildren(),b){const t=e.ParsedURL.ParsedURL.completeURL(s,b);if(t){const e=u.Linkifier.Linkifier.linkifyURL(t,{text:b});e.tabIndex=0,e.setAttribute("jslog",`${p.link("start-url").track({click:!0})}`),this.startURLField.appendChild(e)}}this.themeColorSwatch.classList.toggle("hidden",!_("theme_color"));const y=e.Color.parse(_("theme_color")||"white")||e.Color.parse("white");y&&this.themeColorSwatch.renderColor(y),this.backgroundColorSwatch.classList.toggle("hidden",!_("background_color"));const k=e.Color.parse(_("background_color")||"white")||e.Color.parse("white");k&&this.backgroundColorSwatch.renderColor(k),this.orientationField.textContent=_("orientation");const f=_("display");this.displayField.textContent=f;const I=(v.note_taking||{}).new_note_url,C="string"==typeof I;if(this.newNoteUrlField.parentElement?.classList.toggle("hidden",!C),this.newNoteUrlField.removeChildren(),C){const t=e.ParsedURL.ParsedURL.completeURL(s,I),i=u.Linkifier.Linkifier.linkifyURL(t,{text:I});i.tabIndex=0,this.newNoteUrlField.appendChild(i)}const T=v.protocol_handlers||[];this.protocolHandlersView.data={protocolHandlers:T,manifestLink:s};const x=v.icons||[];this.iconsSection.clearContent();const E=v.shortcuts||[];for(const e of this.shortcutSections)e.detach(!0);const M=v.screenshots||[];for(const e of this.screenshotsSections)e.detach(!0);const R=[],L=c.UIUtils.CheckboxLabel.create(N(U.showOnlyTheMinimumSafeAreaFor));L.classList.add("mask-checkbox"),L.setAttribute("jslog",`${p.toggle("show-minimal-safe-area-for-maskable-icons").track({change:!0})}`),L.addEventListener("click",(()=>{this.iconsSection.setIconMasked(L.checkboxElement.checked)})),this.iconsSection.appendRow().appendChild(L);const A=c.XLink.XLink.create("https://web.dev/maskable-icon/",N(U.documentationOnMaskableIcons),void 0,void 0,"learn-more");this.iconsSection.appendRow().appendChild(i.i18n.getFormatLocalizedString(W,U.needHelpReadOurS,{PH1:A}));let B=!1;for(const e of x){const t=await this.appendImageResourceToSection(s,e,this.iconsSection,!1);R.push(...t.imageResourceErrors),B=t.squareSizedIconAvailable||B}B||R.push(N(U.sSShouldHaveSquareIcon)),E.length>4&&w.push(N(U.shortcutsMayBeNotAvailable));let P=1;for(const t of E){const i=this.reportView.appendSection(N(U.shortcutS,{PH1:P}));i.element.setAttribute("jslog",`${p.section("shortcuts")}`),this.shortcutSections.push(i),i.appendFlexedField(N(U.name),t.name),t.short_name&&i.appendFlexedField(N(U.shortName),t.short_name),t.description&&i.appendFlexedField(N(U.description),t.description);const r=i.appendFlexedField(N(U.url)),o=e.ParsedURL.ParsedURL.completeURL(s,t.url),a=u.Linkifier.Linkifier.linkifyURL(o,{text:t.url});a.setAttribute("jslog",`${p.link("shortcut").track({click:!0})}`),a.tabIndex=0,r.appendChild(a);const n=t.icons||[];let d=!1;for(const e of n){const{imageResourceErrors:t}=await this.appendImageResourceToSection(s,e,i,!1);if(R.push(...t),!d&&e.sizes){const t=e.sizes.match(/^(\d+)x(\d+)$/);t&&t[1]>=96&&t[2]>=96&&(d=!0)}}d||R.push(N(U.shortcutSShouldIncludeAXPixel,{PH1:P})),P++}let D=1;const F=new Map;let O=!1;for(const e of M){const t=this.reportView.appendSection(N(U.screenshotS,{PH1:D}));this.screenshotsSections.push(t),e.form_factor&&t.appendFlexedField(N(U.formFactor),e.form_factor),e.label&&t.appendFlexedField(N(U.label),e.label),e.platform&&t.appendFlexedField(N(U.platform),e.platform);const{imageResourceErrors:i,naturalWidth:r,naturalHeight:o}=await this.appendImageResourceToSection(s,e,t,!0);if(R.push(...i),e.form_factor&&r&&o){F.has(e.form_factor)||F.set(e.form_factor,{width:r,height:o});const t=F.get(e.form_factor);t&&(O=O||r*t.height!=o*t.width)}D++}O&&w.push(N(U.screenshotsMustHaveSameAspectRatio));const V=M.filter((e=>"wide"===e.form_factor)),j=M.filter((e=>"wide"!==e.form_factor));V.length<1&&w.push(N(U.noScreenshotsForRicherPWAInstallOnDesktop)),j.length<1&&w.push(N(U.noScreenshotsForRicherPWAInstallOnMobile)),V.length>8&&w.push(N(U.tooManyScreenshotsForDesktop)),j.length>5&&w.push(N(U.tooManyScreenshotsForMobile)),this.installabilitySection.clearContent(),this.installabilitySection.element.classList.toggle("hidden",!a.length);const H=this.getInstallabilityErrorMessages(a);for(const e of H){const t=document.createTextNode(e);this.installabilitySection.appendRow().appendChild(t)}this.errorsSection.element.classList.toggle("hidden",!o.length&&!R.length&&!w.length);for(const e of w){const t=document.createTextNode(e);this.errorsSection.appendRow().appendChild(t)}for(const e of R){const t=document.createTextNode(e);this.errorsSection.appendRow().appendChild(t)}function _(e){const t=v[e];return"string"!=typeof t?"":t}this.windowControlsSection.clearContent();const K=(v.display_override||[]).includes("window-controls-overlay"),q=c.XLink.XLink.create("https://developer.mozilla.org/en-US/docs/Web/Manifest/display_override","display-override",void 0,void 0,"display-override"),z=document.createElement("code");z.appendChild(q);const G=this.windowControlsSection.appendRow();if(K){const e=n.Icon.create("check-circle","inline-icon");G.appendChild(e);const t=document.createElement("code");t.classList.add("wco"),t.textContent="window-controls-overlay",G.appendChild(i.i18n.getFormatLocalizedString(W,U.wcoFound,{PH1:t,PH2:z,PH3:m})),this.overlayModel&&await this.appendWindowControlsToSection(this.overlayModel,s,_("theme_color"))}else{const e=n.Icon.create("info","inline-icon");G.appendChild(e),G.appendChild(i.i18n.getFormatLocalizedString(W,U.wcoNotFound,{PH1:z}))}const $=c.XLink.XLink.create("https://learn.microsoft.com/en-us/microsoft-edge/progressive-web-apps-chromium/how-to/window-controls-overlay",N(U.customizePwaTitleBar),void 0,void 0,"customize-pwa-tittle-bar");this.windowControlsSection.appendRow().appendChild(i.i18n.getFormatLocalizedString(W,U.wcoNeedHelpReadMore,{PH1:$})),this.dispatchEventToListeners("ManifestRendered")}getInstallabilityErrorMessages(e){const t=[];for(const i of e){let e;switch(i.errorId){case"not-in-main-frame":e=N(U.pageIsNotLoadedInTheMainFrame);break;case"not-from-secure-origin":e=N(U.pageIsNotServedFromASecureOrigin);break;case"no-manifest":e=N(U.pageHasNoManifestLinkUrl);break;case"manifest-empty":e=N(U.manifestCouldNotBeFetchedIsEmpty);break;case"start-url-not-valid":e=N(U.manifestStartUrlIsNotValid);break;case"manifest-missing-name-or-short-name":e=N(U.manifestDoesNotContainANameOr);break;case"manifest-display-not-supported":e=N(U.manifestDisplayPropertyMustBeOne);break;case"manifest-missing-suitable-icon":if(1!==i.errorArguments.length||"minimum-icon-size-in-pixels"!==i.errorArguments[0].name){console.error("Installability error does not have the correct errorArguments");break}e=N(U.manifestDoesNotContainASuitable,{PH1:i.errorArguments[0].value});break;case"no-acceptable-icon":if(1!==i.errorArguments.length||"minimum-icon-size-in-pixels"!==i.errorArguments[0].name){console.error("Installability error does not have the correct errorArguments");break}e=N(U.noSuppliedIconIsAtLeastSpxSquare,{PH1:i.errorArguments[0].value});break;case"cannot-download-icon":e=N(U.couldNotDownloadARequiredIcon);break;case"no-icon-available":e=N(U.downloadedIconWasEmptyOr);break;case"platform-not-supported-on-android":e=N(U.theSpecifiedApplicationPlatform);break;case"no-id-specified":e=N(U.noPlayStoreIdProvided);break;case"ids-do-not-match":e=N(U.thePlayStoreAppUrlAndPlayStoreId);break;case"already-installed":e=N(U.theAppIsAlreadyInstalled);break;case"url-not-supported-for-webapk":e=N(U.aUrlInTheManifestContainsA);break;case"in-incognito":e=N(U.pageIsLoadedInAnIncognitoWindow);break;case"not-offline-capable":e=N(U.pageDoesNotWorkOffline);break;case"no-url-for-service-worker":e=N(U.couldNotCheckServiceWorker);break;case"prefer-related-applications":e=N(U.manifestSpecifies);break;case"prefer-related-applications-only-beta-stable":e=N(U.preferrelatedapplicationsIsOnly);break;case"manifest-display-override-not-supported":e=N(U.manifestContainsDisplayoverride);break;case"warn-not-offline-capable":e=N(U.pageDoesNotWorkOfflineThePage,{PH1:"https://developer.chrome.com/blog/improved-pwa-offline-detection/"});break;default:console.error(`Installability error id '${i.errorId}' is not recognized`)}e&&t.push(e)}return t}async loadImage(e){const t=document.createElement("div");t.classList.add("image-wrapper");const i=document.createElement("img"),s=new Promise(((e,t)=>{i.onload=e,i.onerror=t}));i.src=e,i.alt=N(U.imageFromS,{PH1:e}),t.appendChild(i);try{return await s,{wrapper:t,image:i}}catch{}return null}parseSizes(e,t,i,s){const r=e?e.split(/\s+/):[],o=[];for(const e of r){if("any"===e){o.find((e=>"any"in e))||o.push({any:"any",formatted:"any"});continue}const r=e.match(/^(?<width>\d+)[xX](?<height>\d+)$/);if(r){const e=parseInt(r.groups?.width||"",10),t=parseInt(r.groups?.height||"",10),i=`${e}×${t}px`;o.push({width:e,height:t,formatted:i})}else s.push(N(U.sSShouldSpecifyItsSizeAs,{PH1:t,PH2:i}))}return o}checkSizeProblem(e,t,i,s,r){if("any"in e)return{hasSquareSize:i.naturalWidth===i.naturalHeight};const o=e.width===e.height;return i.naturalWidth!==e.width&&i.naturalHeight!==e.height?{error:N(U.actualSizeSspxOfSSDoesNotMatch,{PH1:i.naturalWidth,PH2:i.naturalHeight,PH3:s,PH4:r,PH5:e.width,PH6:e.height}),hasSquareSize:o}:i.naturalWidth!==e.width?{error:N(U.actualWidthSpxOfSSDoesNotMatch,{PH1:i.naturalWidth,PH2:s,PH3:r,PH4:e.width}),hasSquareSize:o}:i.naturalHeight!==e.height?{error:N(U.actualHeightSpxOfSSDoesNotMatch,{PH1:i.naturalHeight,PH2:s,PH3:r,PH4:e.height}),hasSquareSize:o}:{hasSquareSize:o}}async appendImageResourceToSection(t,i,s,r){const o=[],a=N(r?U.screenshot:U.icon);if(!i.src)return o.push(N(U.sSrcIsNotSet,{PH1:a})),{imageResourceErrors:o};const n=e.ParsedURL.ParsedURL.completeURL(t,i.src);if(!n)return o.push(N(U.sUrlSFailedToParse,{PH1:a,PH2:i.src})),{imageResourceErrors:o};const d=await this.loadImage(n);if(!d)return o.push(N(U.sSFailedToLoad,{PH1:a,PH2:n})),{imageResourceErrors:o};const{wrapper:l,image:c}=d,{naturalWidth:h,naturalHeight:g}=c,u=this.parseSizes(i.sizes,a,n,o),p=u.map((e=>e.formatted)).join(" ")+"\n"+(i.type||""),m=s.appendFlexedField(p);let v=!1;if(i.sizes){r&&u.length>0&&"any"in u[0]&&o.push(N(U.screenshotPixelSize,{url:n}));for(const e of u){const{error:t,hasSquareSize:s}=this.checkSizeProblem(e,i.type,c,a,n);if(v=v||s,t)o.push(t);else if(r){const t="any"in e?c.naturalWidth:e.width,i="any"in e?c.naturalHeight:e.height;t<320||i<320?o.push(N(U.sSSizeShouldBeAtLeast320,{PH1:a,PH2:n})):t>3840||i>3840?o.push(N(U.sSSizeShouldBeAtMost3840,{PH1:a,PH2:n})):t>2.3*i?o.push(N(U.sSWidthDoesNotComplyWithRatioRequirement,{PH1:a,PH2:n})):i>2.3*t&&o.push(N(U.sSHeightDoesNotComplyWithRatioRequirement,{PH1:a,PH2:n}))}}}else o.push(N(U.sSDoesNotSpecifyItsSizeInThe,{PH1:a,PH2:n}));c.width=c.naturalWidth;const w="string"==typeof i.purpose?i.purpose.toLowerCase():"";return w.includes("any")&&w.includes("maskable")&&o.push(N(U.avoidPurposeAnyAndMaskable)),m.appendChild(l),{imageResourceErrors:o,squareSizedIconAvailable:v,naturalWidth:h,naturalHeight:g}}async appendWindowControlsToSection(e,t,i){if(!await e.hasStyleSheetText(t))return;await e.toggleWindowControlsToolbar(!1);const s=c.UIUtils.CheckboxLabel.create(N(U.selectWindowControlsOverlayEmulationOs),!1);s.checkboxElement.addEventListener("click",(async()=>{await(this.overlayModel?.toggleWindowControlsToolbar(s.checkboxElement.checked))}));const r=s.createChild("select");r.appendChild(c.UIUtils.createOption("Windows","Windows","windows")),r.appendChild(c.UIUtils.createOption("macOS","Mac","macos")),r.appendChild(c.UIUtils.createOption("Linux","Linux","linux")),r.selectedIndex=0,this.overlayModel&&(r.value=this.overlayModel?.getWindowControlsConfig().selectedPlatform),r.addEventListener("change",(async()=>{const e=r.options[r.selectedIndex].value;this.overlayModel&&(this.overlayModel.setWindowControlsPlatform(e),await this.overlayModel.toggleWindowControlsToolbar(s.checkboxElement.checked))})),this.windowControlsSection.appendRow().appendChild(s),e.setWindowControlsThemeColor(i)}}var H=Object.freeze({__proto__:null,AppManifestView:j});const _={backForwardCache:"Back/forward cache"},K=i.i18n.registerUIStrings("panels/application/BackForwardCacheTreeElement.ts",_),q=i.i18n.getLocalizedString.bind(void 0,K);class z extends F{view;constructor(e){super(e,q(_.backForwardCache),!1,"bfcache");const t=n.Icon.create("database");this.setLeadingIcons([t])}get itemURL(){return"bfcache://"}onselect(e){return super.onselect(e),this.view||(this.view=d.LegacyWrapper.legacyWrapper(c.Widget.Widget,new m.BackForwardCacheView.BackForwardCacheView)),this.showView(this.view),t.userMetrics.panelShown("back-forward-cache"),!1}}class G extends o.SDKModel.SDKModel{backgroundServiceAgent;events;constructor(e){super(e),this.backgroundServiceAgent=e.backgroundServiceAgent(),e.registerBackgroundServiceDispatcher(this),this.events=new Map}enable(e){this.events.set(e,[]),this.backgroundServiceAgent.invoke_startObserving({service:e})}setRecording(e,t){this.backgroundServiceAgent.invoke_setRecording({shouldRecord:e,service:t})}clearEvents(e){this.events.set(e,[]),this.backgroundServiceAgent.invoke_clearEvents({service:e})}getEvents(e){return this.events.get(e)||[]}recordingStateChanged({isRecording:e,service:t}){this.dispatchEventToListeners($.RecordingStateChanged,{isRecording:e,serviceName:t})}backgroundServiceEventReceived({backgroundServiceEvent:e}){this.events.get(e.service).push(e),this.dispatchEventToListeners($.BackgroundServiceEventReceived,e)}}var $;o.SDKModel.SDKModel.register(G,{capabilities:1,autostart:!1}),function(e){e.RecordingStateChanged="RecordingStateChanged",e.BackgroundServiceEventReceived="BackgroundServiceEventReceived"}($||($={}));var X=Object.freeze({__proto__:null,BackgroundServiceModel:G,get Events(){return $}}),J={cssText:`.empty-view-scroller{overflow:auto}\n/*# sourceURL=${import.meta.resolve("./emptyWidget.css")} */\n`},Q={cssText:`.background-service-toolbar{background-color:var(--sys-color-cdt-base-container);border-bottom:var(--sys-size-1) solid var(--sys-color-divider)}.data-grid{flex:auto;border:none}[slot="insertion-point-main"]{overflow:auto}.background-service-preview{position:absolute;background-color:var(--sys-color-cdt-base-container);justify-content:center;align-items:center;overflow:auto;font-size:13px;color:var(--sys-color-on-surface-subtle)}.background-service-preview > div{max-width:450px;margin:10px;text-align:center}.background-service-preview > div > p{flex:none;white-space:pre-line}.background-service-shortcut{color:var(--sys-color-on-surface-subtle)}.background-service-metadata{padding-left:5px;padding-top:10px}.background-service-metadata-entry{padding-left:10px;padding-bottom:5px}.background-service-metadata-name{color:var(--sys-color-on-surface-subtle);display:inline-block;margin-right:0.25em;font-weight:bold}.background-service-metadata-value{display:inline;margin-right:1em;white-space:pre-wrap;word-break:break-all;user-select:text}.background-service-empty-value{color:var(--sys-color-state-disabled);font-style:italic}.background-service-record-inline-button{margin-bottom:6px}\n/*# sourceURL=${import.meta.resolve("./backgroundServiceView.css")} */\n`};const Y={backgroundFetch:"Background fetch",backgroundSync:"Background sync",pushMessaging:"Push messaging",notifications:"Notifications",paymentHandler:"Payment handler",periodicBackgroundSync:"Periodic background sync",clear:"Clear",saveEvents:"Save events",showEventsFromOtherDomains:"Show events from other domains",showEventsForOtherStorageKeys:"Show events from other storage partitions",stopRecordingEvents:"Stop recording events",startRecordingEvents:"Start recording events",timestamp:"Timestamp",event:"Event",origin:"Origin",storageKey:"Storage Key",swScope:"Service Worker Scope",instanceId:"Instance ID",backgroundServices:"Background services",noEventSelected:"No event selected",selectAnEventToViewMetadata:"Select an event to view its metadata",recordingSActivity:"Recording {PH1} activity...",noRecording:"No recording yet",devtoolsWillRecordAllSActivity:"DevTools will record all {PH1} activity for up to 3 days, even when closed.",startRecordingToDebug:'Start to debug background services by using the "{PH1}" button or by hitting {PH2}.',empty:"empty",noMetadataForThisEvent:"No metadata for this event"},Z=i.i18n.registerUIStrings("panels/application/BackgroundServiceView.ts",Y),ee=i.i18n.getLocalizedString.bind(void 0,Z);class te extends c.Widget.VBox{serviceName;model;serviceWorkerManager;securityOriginManager;storageKeyManager;recordAction;recordButton;originCheckbox;storageKeyCheckbox;saveButton;toolbar;splitWidget;dataGrid;previewPanel;selectedEventNode;preview;static getUIString(e){switch(e){case"backgroundFetch":return ee(Y.backgroundFetch);case"backgroundSync":return ee(Y.backgroundSync);case"pushMessaging":return ee(Y.pushMessaging);case"notifications":return ee(Y.notifications);case"paymentHandler":return ee(Y.paymentHandler);case"periodicBackgroundSync":return ee(Y.periodicBackgroundSync);default:return""}}constructor(e,t){super(!0),this.registerRequiredCSS(J,Q),this.serviceName=e;const i=s.StringUtilities.toKebabCase(e);if(this.element.setAttribute("jslog",`${p.pane().context(i)}`),this.model=t,this.model.addEventListener($.RecordingStateChanged,this.onRecordingStateChanged,this),this.model.addEventListener($.BackgroundServiceEventReceived,this.onEventReceived,this),this.model.enable(this.serviceName),this.serviceWorkerManager=this.model.target().model(o.ServiceWorkerManager.ServiceWorkerManager),this.securityOriginManager=this.model.target().model(o.SecurityOriginManager.SecurityOriginManager),!this.securityOriginManager)throw new Error("SecurityOriginManager instance is missing");if(this.securityOriginManager.addEventListener(o.SecurityOriginManager.Events.MainSecurityOriginChanged,(()=>this.onOriginChanged())),this.storageKeyManager=this.model.target().model(o.StorageKeyManager.StorageKeyManager),!this.storageKeyManager)throw new Error("StorageKeyManager instance is missing");this.storageKeyManager.addEventListener("MainStorageKeyChanged",(()=>this.onStorageKeyChanged())),this.recordAction=c.ActionRegistry.ActionRegistry.instance().getAction("background-service.toggle-recording"),this.toolbar=this.contentElement.createChild("devtools-toolbar","background-service-toolbar"),this.toolbar.setAttribute("jslog",`${p.toolbar()}`),this.setupToolbar(),this.splitWidget=new c.SplitWidget.SplitWidget(!1,!0),this.splitWidget.show(this.contentElement),this.dataGrid=this.createDataGrid(),this.previewPanel=new c.Widget.VBox,this.previewPanel.element.setAttribute("jslog",`${p.pane("preview").track({resize:!0})}`),this.selectedEventNode=null,this.preview=null,this.splitWidget.setMainWidget(this.dataGrid.asWidget()),this.splitWidget.setSidebarWidget(this.previewPanel),this.splitWidget.hideMain(),this.showPreview(null)}getDataGrid(){return this.dataGrid}async setupToolbar(){this.toolbar.wrappable=!0,this.recordButton=c.Toolbar.Toolbar.createActionButton(this.recordAction),this.recordButton.toggleOnClick(!1),this.toolbar.appendToolbarItem(this.recordButton);const e=new c.Toolbar.ToolbarButton(ee(Y.clear),"clear",void 0,"background-service.clear");e.addEventListener("Click",(()=>this.clearEvents())),this.toolbar.appendToolbarItem(e),this.toolbar.appendSeparator(),this.saveButton=new c.Toolbar.ToolbarButton(ee(Y.saveEvents),"download",void 0,"background-service.save-events"),this.saveButton.addEventListener("Click",(e=>{this.saveToFile()})),this.saveButton.setEnabled(!1),this.toolbar.appendToolbarItem(this.saveButton),this.toolbar.appendSeparator(),this.originCheckbox=new c.Toolbar.ToolbarCheckbox(ee(Y.showEventsFromOtherDomains),ee(Y.showEventsFromOtherDomains),(()=>this.refreshView()),"show-events-from-other-domains"),this.toolbar.appendToolbarItem(this.originCheckbox),this.storageKeyCheckbox=new c.Toolbar.ToolbarCheckbox(ee(Y.showEventsForOtherStorageKeys),ee(Y.showEventsForOtherStorageKeys),(()=>this.refreshView()),"show-events-from-other-partitions"),this.toolbar.appendToolbarItem(this.storageKeyCheckbox)}refreshView(){this.clearView();const e=this.model.getEvents(this.serviceName).filter((e=>this.acceptEvent(e)));for(const t of e)this.addEvent(t)}clearView(){this.selectedEventNode=null,this.dataGrid.rootNode().removeChildren(),this.splitWidget.hideMain(),this.saveButton.setEnabled(!1),this.showPreview(null)}toggleRecording(){this.model.setRecording(!this.recordButton.isToggled(),this.serviceName)}clearEvents(){this.model.clearEvents(this.serviceName),this.clearView()}onRecordingStateChanged({data:e}){e.serviceName===this.serviceName&&e.isRecording!==this.recordButton.isToggled()&&(this.recordButton.setToggled(e.isRecording),this.updateRecordButtonTooltip(),this.showPreview(this.selectedEventNode))}updateRecordButtonTooltip(){const e=this.recordButton.isToggled()?ee(Y.stopRecordingEvents):ee(Y.startRecordingEvents);this.recordButton.setTitle(e,"background-service.toggle-recording")}onEventReceived({data:e}){this.acceptEvent(e)&&this.addEvent(e)}onOriginChanged(){this.originCheckbox.checked()||this.refreshView()}onStorageKeyChanged(){this.storageKeyCheckbox.checked()||this.refreshView()}addEvent(e){const t=this.createEventData(e),i=new ie(t,e.eventMetadata);this.dataGrid.rootNode().appendChild(i),"Both"!==this.splitWidget.showMode()&&this.splitWidget.showBoth(),1===this.dataGrid.rootNode().children.length&&(this.saveButton.setEnabled(!0),this.showPreview(this.selectedEventNode))}createDataGrid(){const e=[{id:"id",title:"#",weight:1},{id:"timestamp",title:ee(Y.timestamp),weight:7},{id:"event-name",title:ee(Y.event),weight:8},{id:"origin",title:ee(Y.origin),weight:8},{id:"storage-key",title:ee(Y.storageKey),weight:8},{id:"sw-scope",title:ee(Y.swScope),weight:4},{id:"instance-id",title:ee(Y.instanceId),weight:8}],t=new S.DataGrid.DataGridImpl({displayName:ee(Y.backgroundServices),columns:e,refreshCallback:void 0,deleteCallback:void 0});return t.setStriped(!0),t.addEventListener("SelectedNode",(e=>this.showPreview(e.data))),t}createEventData(e){let t="";const i=this.serviceWorkerManager?this.serviceWorkerManager.registrations().get(e.serviceWorkerRegistrationId):void 0;return i&&(t=i.scopeURL.substr(i.securityOrigin.length)),{id:this.dataGrid.rootNode().children.length+1,timestamp:c.UIUtils.formatTimestamp(1e3*e.timestamp,!0),origin:e.origin,"storage-key":e.storageKey,"sw-scope":t,"event-name":e.eventName,"instance-id":e.instanceId}}acceptEvent(e){if(e.service!==this.serviceName)return!1;if(this.originCheckbox.checked()||this.storageKeyCheckbox.checked())return!0;const t=e.origin.substr(0,e.origin.length-1),i=e.storageKey;return this.securityOriginManager.securityOrigins().includes(t)||this.storageKeyManager.storageKeys().includes(i)}createLearnMoreLink(){let e="https://developer.chrome.com/docs/devtools/javascript/background-services/?utm_source=devtools";switch(this.serviceName){case"backgroundFetch":e+="#fetch";break;case"backgroundSync":e+="#sync";break;case"pushMessaging":e+="#push";break;case"notifications":e+="#notifications"}return e}showPreview(e){if(this.selectedEventNode&&this.selectedEventNode===e)return;if(this.selectedEventNode=e,this.preview&&this.preview.detach(),this.selectedEventNode)return this.preview=this.selectedEventNode.createPreview(),void this.preview.show(this.previewPanel.contentElement);const t=new c.EmptyWidget.EmptyWidget("","");if(this.dataGrid.rootNode().children.length)t.header=ee(Y.noEventSelected),t.text=ee(Y.selectAnEventToViewMetadata);else if(this.recordButton.isToggled()){const e=te.getUIString(this.serviceName).toLowerCase();t.header=ee(Y.recordingSActivity,{PH1:e}),t.text=ee(Y.devtoolsWillRecordAllSActivity,{PH1:e})}else{const e=c.ShortcutRegistry.ShortcutRegistry.instance().shortcutsForAction("background-service.toggle-recording")[0];t.header=ee(Y.noRecording),t.text=ee(Y.startRecordingToDebug,{PH1:ee(Y.startRecordingEvents),PH2:e.title()}),t.appendLink(this.createLearnMoreLink());const i=c.UIUtils.createTextButton(ee(Y.startRecordingEvents),(()=>this.toggleRecording()),{jslogContext:"start-recording",variant:"tonal"});t.contentElement.appendChild(i)}this.preview=t,this.preview.show(this.previewPanel.contentElement)}async saveToFile(){const e=`${this.serviceName}-${s.DateUtilities.toISO8601Compact(new Date)}.json`,t=new w.FileUtils.FileOutputStream;if(!await t.open(e))return;const i=this.model.getEvents(this.serviceName).filter((e=>this.acceptEvent(e)));await t.write(JSON.stringify(i,void 0,2)),t.close()}}class ie extends S.DataGrid.DataGridNode{eventMetadata;constructor(e,t){super(e),this.eventMetadata=t.sort(((e,t)=>s.StringUtilities.compare(e.key,t.key)))}createPreview(){const e=new c.Widget.VBox;e.element.classList.add("background-service-metadata"),e.element.setAttribute("jslog",`${p.section("metadata")}`);for(const t of this.eventMetadata){const i=document.createElement("div");i.classList.add("background-service-metadata-entry"),i.createChild("div","background-service-metadata-name").textContent=t.key+": ",t.value?i.createChild("div","background-service-metadata-value source-code").textContent=t.value:i.createChild("div","background-service-metadata-value background-service-empty-value").textContent=ee(Y.empty),e.element.appendChild(i)}if(!e.element.children.length){const t=document.createElement("div");t.classList.add("background-service-metadata-entry"),t.createChild("div","background-service-metadata-name background-service-empty-value").textContent=ee(Y.noMetadataForThisEvent),e.element.appendChild(t)}return e}}var se=Object.freeze({__proto__:null,ActionDelegate:class{handleAction(e,t){const i=e.flavor(te);if("background-service.toggle-recording"===t){if(!i)throw new Error("BackgroundServiceView instance is missing");return i.toggleRecording(),!0}return!1}},BackgroundServiceView:te,EventDataNode:ie});const re={bounceTrackingMitigations:"Bounce tracking mitigations"},oe=i.i18n.registerUIStrings("panels/application/BounceTrackingMitigationsTreeElement.ts",re),ae=i.i18n.getLocalizedString.bind(void 0,oe);class ne extends F{view;constructor(e){super(e,ae(re.bounceTrackingMitigations),!1,"bounce-tracking-mitigations");const t=n.Icon.create("database");this.setLeadingIcons([t])}get itemURL(){return"bounce-tracking-mitigations://"}onselect(e){return super.onselect(e),this.view||(this.view=d.LegacyWrapper.legacyWrapper(c.Widget.Widget,new m.BounceTrackingMitigationsView.BounceTrackingMitigationsView)),this.showView(this.view),t.userMetrics.panelShown("bounce-tracking-mitigations"),!1}}var de=Object.freeze({__proto__:null,BounceTrackingMitigationsTreeElement:ne,i18nString:ae});class le extends e.ObjectWrapper.ObjectWrapper{model;storageKeyInternal;isLocalStorageInternal;constructor(e,t,i){super(),this.model=e,this.storageKeyInternal=t,this.isLocalStorageInternal=i}static storageId(e,t){return{storageKey:e,isLocalStorage:t}}get id(){return le.storageId(this.storageKeyInternal,this.isLocalStorageInternal)}get storageKey(){return this.storageKeyInternal}get isLocalStorage(){return this.isLocalStorageInternal}getItems(){return this.model.agent.invoke_getDOMStorageItems({storageId:this.id}).then((({entries:e})=>e))}setItem(e,t){this.model.agent.invoke_setDOMStorageItem({storageId:this.id,key:e,value:t})}removeItem(e){this.model.agent.invoke_removeDOMStorageItem({storageId:this.id,key:e})}clear(){this.model.agent.invoke_clear({storageId:this.id})}}class ce extends o.SDKModel.SDKModel{storageKeyManagerInternal;storagesInternal;agent;enabled;constructor(e){super(e),this.storageKeyManagerInternal=e.model(o.StorageKeyManager.StorageKeyManager),this.storagesInternal={},this.agent=e.domstorageAgent()}enable(){if(!this.enabled){if(this.target().registerDOMStorageDispatcher(new he(this)),this.storageKeyManagerInternal){this.storageKeyManagerInternal.addEventListener("StorageKeyAdded",this.storageKeyAdded,this),this.storageKeyManagerInternal.addEventListener("StorageKeyRemoved",this.storageKeyRemoved,this);for(const e of this.storageKeyManagerInternal.storageKeys())this.addStorageKey(e)}this.agent.invoke_enable(),this.enabled=!0}}clearForStorageKey(e){if(this.enabled){for(const t of[!0,!1]){const i=this.storageKey(e,t),s=this.storagesInternal[i];if(!s)return;s.clear()}this.removeStorageKey(e),this.addStorageKey(e)}}storageKeyAdded(e){this.addStorageKey(e.data)}addStorageKey(e){for(const t of[!0,!1]){const i=this.storageKey(e,t);console.assert(!this.storagesInternal[i]);const s=new le(this,e,t);this.storagesInternal[i]=s,this.dispatchEventToListeners("DOMStorageAdded",s)}}storageKeyRemoved(e){this.removeStorageKey(e.data)}removeStorageKey(e){for(const t of[!0,!1]){const i=this.storageKey(e,t),s=this.storagesInternal[i];s&&(delete this.storagesInternal[i],this.dispatchEventToListeners("DOMStorageRemoved",s))}}storageKey(e,t){return JSON.stringify(le.storageId(e,t))}domStorageItemsCleared(e){const t=this.storageForId(e);t&&t.dispatchEventToListeners("DOMStorageItemsCleared")}domStorageItemRemoved(e,t){const i=this.storageForId(e);if(!i)return;const s={key:t};i.dispatchEventToListeners("DOMStorageItemRemoved",s)}domStorageItemAdded(e,t,i){const s=this.storageForId(e);if(!s)return;const r={key:t,value:i};s.dispatchEventToListeners("DOMStorageItemAdded",r)}domStorageItemUpdated(e,t,i,s){const r=this.storageForId(e);if(!r)return;const o={key:t,oldValue:i,value:s};r.dispatchEventToListeners("DOMStorageItemUpdated",o)}storageForId(e){return console.assert(Boolean(e.storageKey)),this.storagesInternal[this.storageKey(e.storageKey||"",e.isLocalStorage)]}storages(){const e=[];for(const t in this.storagesInternal)e.push(this.storagesInternal[t]);return e}}o.SDKModel.SDKModel.register(ce,{capabilities:2,autostart:!1});class he{model;constructor(e){this.model=e}domStorageItemsCleared({storageId:e}){this.model.domStorageItemsCleared(e)}domStorageItemRemoved({storageId:e,key:t}){this.model.domStorageItemRemoved(e,t)}domStorageItemAdded({storageId:e,key:t,newValue:i}){this.model.domStorageItemAdded(e,t,i)}domStorageItemUpdated({storageId:e,key:t,oldValue:i,newValue:s}){this.model.domStorageItemUpdated(e,t,i,s)}}var ge=Object.freeze({__proto__:null,DOMStorage:le,DOMStorageDispatcher:he,DOMStorageModel:ce});class ue extends e.ObjectWrapper.ObjectWrapper{#e;#t;#i;#s;constructor(e,t,i,s){super(),this.#e=e,this.#t=t,this.#i=i,this.#s=s}get model(){return this.#e}get extensionId(){return this.#t}get name(){return this.#i}get key(){return`${this.extensionId}-${this.storageArea}`}get storageArea(){return this.#s}async getItems(e){const t={id:this.#t,storageArea:this.#s};e&&(t.keys=e);const i=await this.#e.agent.invoke_getStorageItems(t);if(i.getError())throw new Error(i.getError());return i.data}async setItem(e,t){const i=await this.#e.agent.invoke_setStorageItems({id:this.#t,storageArea:this.#s,values:{[e]:t}});if(i.getError())throw new Error(i.getError())}async removeItem(e){const t=await this.#e.agent.invoke_removeStorageItems({id:this.#t,storageArea:this.#s,keys:[e]});if(t.getError())throw new Error(t.getError())}async clear(){const e=await this.#e.agent.invoke_clearStorageItems({id:this.#t,storageArea:this.#s});if(e.getError())throw new Error(e.getError())}matchesTarget(t){if(!t)return!1;const i=t.targetInfo()?.url,s=i?e.ParsedURL.ParsedURL.fromString(i):null;return"chrome-extension"===s?.scheme&&s?.host===this.extensionId}}class pe extends o.SDKModel.SDKModel{#r;#o;agent;#a;constructor(e){super(e),this.#r=e.model(o.RuntimeModel.RuntimeModel),this.#o=new Map,this.agent=e.extensionsAgent()}enable(){this.#a||(this.#r&&(this.#r.addEventListener(o.RuntimeModel.Events.ExecutionContextCreated,this.#n,this),this.#r.addEventListener(o.RuntimeModel.Events.ExecutionContextDestroyed,this.#d,this),this.#r.executionContexts().forEach(this.#l,this)),this.#a=!0)}#c(e){const t=this.#o.get(e);if(t)return t;const i=new Map;return this.#o.set(e,i),i}#h(e,t){for(const i of["session","local","sync","managed"]){const s=this.#c(e),r=new ue(this,e,t,i);console.assert(!s.get(i)),r.getItems([]).then((()=>{this.#o.get(e)===s&&(s.get(i)||(s.set(i,r),this.dispatchEventToListeners("ExtensionStorageAdded",r)))})).catch((()=>{}))}}#g(e){const t=this.#o.get(e);if(t){for(const[e,i]of t)t.delete(e),this.dispatchEventToListeners("ExtensionStorageRemoved",i);this.#o.delete(e)}}#l(e){const t=this.#u(e);t&&this.#h(t,e.name)}#n(e){this.#l(e.data)}#u(t){const i=e.ParsedURL.ParsedURL.fromString(t.origin);return i&&"chrome-extension"===i.scheme?i.host:void 0}#p(e){const t=this.#u(e);if(t){if(this.#r?.executionContexts().some((e=>this.#u(e)===t)))return;this.#g(t)}}#d(e){this.#p(e.data)}storageForIdAndArea(e,t){return this.#o.get(e)?.get(t)}storages(){const e=[];for(const t of this.#o.values())e.push(...t.values());return e}}o.SDKModel.SDKModel.register(pe,{capabilities:4,autostart:!1});var me=Object.freeze({__proto__:null,ExtensionStorage:ue,ExtensionStorageModel:pe});const ve="";class we extends o.SDKModel.SDKModel{storageBucketModel;indexedDBAgent;storageAgent;databasesInternal;databaseNamesByStorageKeyAndBucket;updatedStorageBuckets;throttler;enabled;constructor(t){super(t),t.registerStorageDispatcher(this),this.storageBucketModel=t.model(o.StorageBucketsModel.StorageBucketsModel),this.indexedDBAgent=t.indexedDBAgent(),this.storageAgent=t.storageAgent(),this.databasesInternal=new Map,this.databaseNamesByStorageKeyAndBucket=new Map,this.updatedStorageBuckets=new Set,this.throttler=new e.Throttler.Throttler(1e3)}static keyFromIDBKey(e){if(null==e)return;let t;switch(typeof e){case"number":t={type:"number",number:e};break;case"string":t={type:"string",string:e};break;case"object":if(e instanceof Date)t={type:"date",date:e.getTime()};else{if(!Array.isArray(e))return;{const i=[];for(let t=0;t<e.length;++t){const s=we.keyFromIDBKey(e[t]);s&&i.push(s)}t={type:"array",array:i}}}break;default:return}return t}static keyRangeFromIDBKeyRange(e){return{lower:we.keyFromIDBKey(e.lower),upper:we.keyFromIDBKey(e.upper),lowerOpen:Boolean(e.lowerOpen),upperOpen:Boolean(e.upperOpen)}}static idbKeyPathFromKeyPath(e){let t;switch(e.type){case"null":t=null;break;case"string":t=e.string;break;case"array":t=e.array}return t}static keyPathStringFromIDBKeyPath(e){return"string"==typeof e?'"'+e+'"':e instanceof Array?'["'+e.join('", "')+'"]':null}enable(){if(!this.enabled){if(this.indexedDBAgent.invoke_enable(),this.storageBucketModel){this.storageBucketModel.addEventListener("BucketAdded",this.storageBucketAdded,this),this.storageBucketModel.addEventListener("BucketRemoved",this.storageBucketRemoved,this);for(const{bucket:e}of this.storageBucketModel.getBuckets())this.addStorageBucket(e)}this.enabled=!0}}clearForStorageKey(e){if(!this.enabled||!this.databaseNamesByStorageKeyAndBucket.has(e))return;for(const[t]of this.databaseNamesByStorageKeyAndBucket.get(e)||[]){const i=this.storageBucketModel?.getBucketByName(e,t??void 0)?.bucket;i&&this.removeStorageBucket(i)}this.databaseNamesByStorageKeyAndBucket.delete(e);const t=this.storageBucketModel?.getBucketsForStorageKey(e)||[];for(const{bucket:e}of t)this.addStorageBucket(e)}async deleteDatabase(e){this.enabled&&(await this.indexedDBAgent.invoke_deleteDatabase({storageBucket:e.storageBucket,databaseName:e.name}),this.loadDatabaseNamesByStorageBucket(e.storageBucket))}async refreshDatabaseNames(){for(const[e]of this.databaseNamesByStorageKeyAndBucket){const t=this.databaseNamesByStorageKeyAndBucket.get(e)?.keys()||[];for(const i of t){const t=this.storageBucketModel?.getBucketByName(e,i??void 0)?.bucket;t&&await this.loadDatabaseNamesByStorageBucket(t)}}this.dispatchEventToListeners(Se.DatabaseNamesRefreshed)}refreshDatabase(e){this.loadDatabase(e,!0)}async clearObjectStore(e,t){await this.indexedDBAgent.invoke_clearObjectStore({storageBucket:e.storageBucket,databaseName:e.name,objectStoreName:t})}async deleteEntries(e,t,i){const s=we.keyRangeFromIDBKeyRange(i);await this.indexedDBAgent.invoke_deleteObjectStoreEntries({storageBucket:e.storageBucket,databaseName:e.name,objectStoreName:t,keyRange:s})}storageBucketAdded({data:{bucketInfo:{bucket:e}}}){this.addStorageBucket(e)}storageBucketRemoved({data:{bucketInfo:{bucket:e}}}){this.removeStorageBucket(e)}addStorageBucket(e){const{storageKey:t}=e;this.databaseNamesByStorageKeyAndBucket.has(t)||(this.databaseNamesByStorageKeyAndBucket.set(t,new Map),this.storageAgent.invoke_trackIndexedDBForStorageKey({storageKey:t}));const i=this.databaseNamesByStorageKeyAndBucket.get(t)||new Map;console.assert(!i.has(e.name??ve)),i.set(e.name??ve,new Set),this.loadDatabaseNamesByStorageBucket(e)}removeStorageBucket(e){const{storageKey:t}=e;console.assert(this.databaseNamesByStorageKeyAndBucket.has(t));const i=this.databaseNamesByStorageKeyAndBucket.get(t)||new Map;console.assert(i.has(e.name??ve));const s=i.get(e.name??ve)||new Map;for(const e of s)this.databaseRemovedForStorageBucket(e);i.delete(e.name??ve),0===i.size&&(this.databaseNamesByStorageKeyAndBucket.delete(t),this.storageAgent.invoke_untrackIndexedDBForStorageKey({storageKey:t}))}updateStorageKeyDatabaseNames(e,t){const i=this.databaseNamesByStorageKeyAndBucket.get(e.storageKey);if(void 0===i)return;const s=new Set(t.map((t=>new ye(e,t)))),r=new Set(i.get(e.name??ve));i.set(e.name??ve,s);for(const e of r)e.inSet(s)||this.databaseRemovedForStorageBucket(e);for(const e of s)e.inSet(r)||this.databaseAddedForStorageBucket(e)}databases(){const e=[];for(const[,t]of this.databaseNamesByStorageKeyAndBucket)for(const[,i]of t)for(const t of i)e.push(t);return e}databaseAddedForStorageBucket(e){this.dispatchEventToListeners(Se.DatabaseAdded,{model:this,databaseId:e})}databaseRemovedForStorageBucket(e){this.dispatchEventToListeners(Se.DatabaseRemoved,{model:this,databaseId:e})}async loadDatabaseNamesByStorageBucket(e){const{storageKey:t}=e,{databaseNames:i}=await this.indexedDBAgent.invoke_requestDatabaseNames({storageBucket:e});if(!i)return[];if(!this.databaseNamesByStorageKeyAndBucket.has(t))return[];return(this.databaseNamesByStorageKeyAndBucket.get(t)||new Map).has(e.name??ve)?(this.updateStorageKeyDatabaseNames(e,i),i):[]}async loadDatabase(e,t){const i=(await this.indexedDBAgent.invoke_requestDatabase({storageBucket:e.storageBucket,databaseName:e.name})).databaseWithObjectStores;if(!this.databaseNamesByStorageKeyAndBucket.get(e.storageBucket.storageKey)?.has(e.storageBucket.name??ve))return;if(!i)return;const s=new ke(e,i.version);this.databasesInternal.set(e,s);for(const e of i.objectStores){const t=we.idbKeyPathFromKeyPath(e.keyPath),i=new fe(e.name,t,e.autoIncrement);for(let t=0;t<e.indexes.length;++t){const s=e.indexes[t],r=we.idbKeyPathFromKeyPath(s.keyPath),o=new Ie(s.name,r,s.unique,s.multiEntry);i.indexes.set(o.name,o)}s.objectStores.set(i.name,i)}this.dispatchEventToListeners(Se.DatabaseLoaded,{model:this,database:s,entriesUpdated:t})}loadObjectStoreData(e,t,i,s,r,o){this.requestData(e,e.name,t,"",i,s,r,o)}loadIndexData(e,t,i,s,r,o,a){this.requestData(e,e.name,t,i,s,r,o,a)}async requestData(e,t,i,s,r,a,n,d){const l=r?we.keyRangeFromIDBKeyRange(r):void 0,c=this.target().model(o.RuntimeModel.RuntimeModel),h=await this.indexedDBAgent.invoke_requestData({storageBucket:e.storageBucket,databaseName:t,objectStoreName:i,indexName:s,skipCount:a,pageSize:n,keyRange:l});if(!c||!this.databaseNamesByStorageKeyAndBucket.get(e.storageBucket.storageKey)?.has(e.storageBucket.name??ve))return;if(h.getError())return void console.error("IndexedDBAgent error: "+h.getError());const g=h.objectStoreDataEntries,u=[];for(const e of g){const t=c?.createRemoteObject(e.key),i=c?.createRemoteObject(e.primaryKey),s=c?.createRemoteObject(e.value);if(!t||!i||!s)return;u.push(new be(t,i,s))}d(u,h.hasMore)}async getMetadata(e,t){const i=e.name,s=t.name,r=await this.indexedDBAgent.invoke_getMetadata({storageBucket:e.storageBucket,databaseName:i,objectStoreName:s});return r.getError()?(console.error("IndexedDBAgent error: "+r.getError()),null):{entriesCount:r.entriesCount,keyGeneratorValue:r.keyGeneratorValue}}async refreshDatabaseListForStorageBucket(e){const t=await this.loadDatabaseNamesByStorageBucket(e);for(const i of t)this.loadDatabase(new ye(e,i),!1)}indexedDBListUpdated({storageKey:e,bucketId:t}){const i=this.storageBucketModel?.getBucketById(t)?.bucket;e&&i&&(this.updatedStorageBuckets.add(i),this.throttler.schedule((()=>{const e=Array.from(this.updatedStorageBuckets,(e=>{this.refreshDatabaseListForStorageBucket(e)}));return this.updatedStorageBuckets.clear(),Promise.all(e)})))}indexedDBContentUpdated({bucketId:e,databaseName:t,objectStoreName:i}){const s=this.storageBucketModel?.getBucketById(e)?.bucket;if(s){const e=new ye(s,t);this.dispatchEventToListeners(Se.IndexedDBContentUpdated,{databaseId:e,objectStoreName:i,model:this})}}attributionReportingTriggerRegistered(e){}cacheStorageListUpdated(e){}cacheStorageContentUpdated(e){}interestGroupAccessed(e){}interestGroupAuctionEventOccurred(e){}interestGroupAuctionNetworkRequestCreated(e){}sharedStorageAccessed(e){}storageBucketCreatedOrUpdated(e){}storageBucketDeleted(e){}attributionReportingSourceRegistered(e){}}var Se;o.SDKModel.SDKModel.register(we,{capabilities:8192,autostart:!1}),function(e){e.DatabaseAdded="DatabaseAdded",e.DatabaseRemoved="DatabaseRemoved",e.DatabaseLoaded="DatabaseLoaded",e.DatabaseNamesRefreshed="DatabaseNamesRefreshed",e.IndexedDBContentUpdated="IndexedDBContentUpdated"}(Se||(Se={}));class be{key;primaryKey;value;constructor(e,t,i){this.key=e,this.primaryKey=t,this.value=i}}class ye{storageBucket;name;constructor(e,t){this.storageBucket=e,this.name=t}inBucket(e){return this.storageBucket.name===e.name}equals(e){return this.name===e.name&&this.storageBucket.name===e.storageBucket.name&&this.storageBucket.storageKey===e.storageBucket.storageKey}inSet(e){for(const t of e)if(this.equals(t))return!0;return!1}}class ke{databaseId;version;objectStores;constructor(e,t){this.databaseId=e,this.version=t,this.objectStores=new Map}}class fe{name;keyPath;autoIncrement;indexes;constructor(e,t,i){this.name=e,this.keyPath=t,this.autoIncrement=i,this.indexes=new Map}get keyPathString(){return we.keyPathStringFromIDBKeyPath(this.keyPath)}}class Ie{name;keyPath;unique;multiEntry;constructor(e,t,i,s){this.name=e,this.keyPath=t,this.unique=i,this.multiEntry=s}get keyPathString(){return we.keyPathStringFromIDBKeyPath(this.keyPath)}}var Ce=Object.freeze({__proto__:null,Database:ke,DatabaseId:ye,Entry:be,get Events(){return Se},Index:Ie,IndexedDBModel:we,ObjectStore:fe}),Te={cssText:`.indexed-db-data-view .data-view-toolbar{position:relative;background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider)}.indexed-db-data-view .data-grid{flex:auto}.indexed-db-data-view .data-grid .data-container tr:nth-last-child(1){background-color:var(--sys-color-cdt-base-container)}.indexed-db-data-view .data-grid .data-container tr:nth-last-child(1) td{border:0}.indexed-db-data-view .data-grid .data-container tr:nth-last-child(2) td{border-bottom:1px solid var(--sys-color-divider)}.indexed-db-data-view .data-grid:focus .data-container tr.selected{background-color:var(--sys-color-tonal-container);color:inherit}.indexed-db-data-view .section,\n.indexed-db-data-view .section > .header,\n.indexed-db-data-view .section > .header .title{margin:0;min-height:inherit;line-height:inherit}.indexed-db-data-view .data-grid .data-container td .section .header .title{white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.indexed-db-key-path{color:var(--sys-color-error);white-space:pre-wrap;unicode-bidi:-webkit-isolate}.indexed-db-container{overflow:auto}.indexed-db-header{min-width:400px;flex-shrink:0;flex-grow:0}.source-code.indexed-db-key-path{font-size:unset!important}.resources-toolbar{padding-right:10px}.object-store-summary-bar{flex:0 0 27px;line-height:27px;padding-left:5px;background-color:var(--sys-color-cdt-base-container);border-top:1px solid var(--sys-color-divider);white-space:nowrap;text-overflow:ellipsis;overflow:hidden}\n/*# sourceURL=${import.meta.resolve("./indexedDBViews.css")} */\n`};const{html:xe}=y,Ee={version:"Version",objectStores:"Object stores",deleteDatabase:"Delete database",refreshDatabase:"Refresh database",confirmDeleteDatabase:'Delete "{PH1}" database?',databaseWillBeRemoved:"The selected database and contained data will be removed.",idb:"IDB",refresh:"Refresh",deleteSelected:"Delete selected",clearObjectStore:"Clear object store",dataMayBeStale:"Data may be stale",someEntriesMayHaveBeenModified:"Some entries may have been modified",keyString:"Key",primaryKey:"Primary key",valueString:"Value",indexedDb:"Indexed DB",keyPath:"Key path: ",showPreviousPage:"Show previous page",showNextPage:"Show next page",filterByKey:"Filter by key (show keys greater or equal to)",expandRecursively:"Expand Recursively",collapse:"Collapse",totalEntriesS:"Total entries: {PH1}",keyGeneratorValueS:"Key generator value: {PH1}"},Me=i.i18n.registerUIStrings("panels/application/IndexedDBViews.ts",Ee),Re=i.i18n.getLocalizedString.bind(void 0,Me);class Le extends m.StorageMetadataView.StorageMetadataView{model;database;constructor(e,t){super(),this.model=e,t&&this.update(t)}getTitle(){return this.database?.databaseId.name}async renderReportContent(){return this.database?xe`
      ${await super.renderReportContent()}
      ${this.key(Re(Ee.version))}
      ${this.value(this.database.version.toString())}
      ${this.key(Re(Ee.objectStores))}
      ${this.value(this.database.objectStores.size.toString())}
      <devtools-report-divider></devtools-report-divider>
      <devtools-report-section>
      <devtools-button
          aria-label=${Re(Ee.deleteDatabase)}
          .variant=${"outlined"}
          @click=${this.deleteDatabase}
          jslog=${p.action("delete-database").track({click:!0})}>
        ${Re(Ee.deleteDatabase)}
      </devtools-button>&nbsp;
      <devtools-button
          aria-label=${Re(Ee.refreshDatabase)}
          .variant=${"outlined"}
          @click=${this.refreshDatabaseButtonClicked}
          jslog=${p.action("refresh-database").track({click:!0})}>
        ${Re(Ee.refreshDatabase)}
      </devtools-button>
      </devtools-report-section>
      `:y.nothing}refreshDatabaseButtonClicked(){this.model.refreshDatabase(this.database.databaseId)}update(e){this.database=e;const t=this.model.target().model(o.StorageBucketsModel.StorageBucketsModel)?.getBucketByName(e.databaseId.storageBucket.storageKey,e.databaseId.storageBucket.name);t?this.setStorageBucket(t):this.setStorageKey(e.databaseId.storageBucket.storageKey),this.render().then((()=>this.updatedForTests()))}updatedForTests(){}async deleteDatabase(){await c.UIUtils.ConfirmDialog.show(Re(Ee.databaseWillBeRemoved),Re(Ee.confirmDeleteDatabase,{PH1:this.database.databaseId.name}),this,{jslogContext:"delete-database-confirmation"})&&this.model.deleteDatabase(this.database.databaseId)}wasShown(){super.wasShown()}}customElements.define("devtools-idb-database-view",Le);class Ae extends c.View.SimpleView{model;databaseId;isIndex;refreshObjectStoreCallback;refreshButton;deleteSelectedButton;clearButton;needsRefresh;clearingObjectStore;pageSize;skipCount;entries;objectStore;index;keyInput;dataGrid;previouslySelectedNode;lastPageSize;lastSkipCount;pageBackButton;pageForwardButton;lastKey;summaryBarElement;constructor(e,t,i,s,r){super(Re(Ee.idb)),this.registerRequiredCSS(Te),this.model=e,this.databaseId=t,this.isIndex=Boolean(s),this.refreshObjectStoreCallback=r,this.element.classList.add("indexed-db-data-view","storage-view"),this.element.setAttribute("jslog",`${p.pane("indexed-db-data-view")}`),this.refreshButton=new c.Toolbar.ToolbarButton(Re(Ee.refresh),"refresh"),this.refreshButton.addEventListener("Click",this.refreshButtonClicked,this),this.refreshButton.element.setAttribute("jslog",`${p.action("refresh").track({click:!0})}`),this.deleteSelectedButton=new c.Toolbar.ToolbarButton(Re(Ee.deleteSelected),"bin"),this.deleteSelectedButton.addEventListener("Click",(e=>{this.deleteButtonClicked(null)})),this.deleteSelectedButton.element.setAttribute("jslog",`${p.action("delete-selected").track({click:!0})}`),this.clearButton=new c.Toolbar.ToolbarButton(Re(Ee.clearObjectStore),"clear"),this.clearButton.addEventListener("Click",(()=>{this.clearButtonClicked()}),this),this.clearButton.element.setAttribute("jslog",`${p.action("clear-all").track({click:!0})}`);const o=c.UIUtils.createIconLabel({title:Re(Ee.dataMayBeStale),iconName:"warning",color:"var(--icon-warning)",width:"20px",height:"20px"});this.needsRefresh=new c.Toolbar.ToolbarItem(o),this.needsRefresh.setVisible(!1),this.needsRefresh.setTitle(Re(Ee.someEntriesMayHaveBeenModified)),this.clearingObjectStore=!1,this.createEditorToolbar(),this.pageSize=50,this.skipCount=0,this.update(i,s),this.entries=[]}createDataGrid(){const e=this.isIndex&&this.index?this.index.keyPath:this.objectStore.keyPath,t=[],i={title:void 0,titleDOMFragment:void 0,sortable:!1,sort:void 0,align:void 0,width:void 0,fixedWidth:void 0,editable:void 0,nonSelectable:void 0,longText:void 0,disclosure:void 0,weight:void 0,allowInSortByEvenWhenHidden:void 0,dataType:void 0,defaultWeight:void 0};t.push({...i,id:"number",title:"#",sortable:!1,width:"50px"}),t.push({...i,id:"key",titleDOMFragment:this.keyColumnHeaderFragment(Re(Ee.keyString),e),sortable:!1}),this.isIndex&&t.push({...i,id:"primary-key",titleDOMFragment:this.keyColumnHeaderFragment(Re(Ee.primaryKey),this.objectStore.keyPath),sortable:!1});const s=Re(Ee.valueString);t.push({...i,id:"value",title:s,sortable:!1});const r=new S.DataGrid.DataGridImpl({displayName:Re(Ee.indexedDb),columns:t,deleteCallback:this.deleteButtonClicked.bind(this),refreshCallback:this.updateData.bind(this,!0)});return r.setStriped(!0),r.addEventListener("SelectedNode",(()=>{this.updateToolbarEnablement(),this.updateSelectionColor()}),this),r}keyColumnHeaderFragment(e,t){const i=document.createDocumentFragment();if(c.UIUtils.createTextChild(i,e),null===t)return i;if(c.UIUtils.createTextChild(i," ("+Re(Ee.keyPath)),Array.isArray(t)){c.UIUtils.createTextChild(i,"[");for(let e=0;e<t.length;++e)0!==e&&c.UIUtils.createTextChild(i,", "),i.appendChild(this.keyPathStringFragment(t[e]));c.UIUtils.createTextChild(i,"]")}else{const e=t;i.appendChild(this.keyPathStringFragment(e))}return c.UIUtils.createTextChild(i,")"),i}keyPathStringFragment(e){const t=document.createDocumentFragment();c.UIUtils.createTextChild(t,'"');return t.createChild("span","source-code indexed-db-key-path").textContent=e,c.UIUtils.createTextChild(t,'"'),t}createEditorToolbar(){const e=this.element.createChild("devtools-toolbar","data-view-toolbar");e.setAttribute("jslog",`${p.toolbar()}`),e.appendToolbarItem(this.refreshButton),e.appendToolbarItem(this.clearButton),e.appendToolbarItem(this.deleteSelectedButton),e.appendToolbarItem(new c.Toolbar.ToolbarSeparator),this.pageBackButton=new c.Toolbar.ToolbarButton(Re(Ee.showPreviousPage),"triangle-left",void 0,"prev-page"),this.pageBackButton.addEventListener("Click",this.pageBackButtonClicked,this),e.appendToolbarItem(this.pageBackButton),this.pageForwardButton=new c.Toolbar.ToolbarButton(Re(Ee.showNextPage),"triangle-right",void 0,"next-page"),this.pageForwardButton.setEnabled(!1),this.pageForwardButton.addEventListener("Click",this.pageForwardButtonClicked,this),e.appendToolbarItem(this.pageForwardButton),this.keyInput=new c.Toolbar.ToolbarFilter(Re(Ee.filterByKey),.5),this.keyInput.addEventListener("TextChanged",this.updateData.bind(this,!1)),e.appendToolbarItem(this.keyInput),e.appendToolbarItem(new c.Toolbar.ToolbarSeparator),e.appendToolbarItem(this.needsRefresh)}pageBackButtonClicked(){this.skipCount=Math.max(0,this.skipCount-this.pageSize),this.updateData(!1)}pageForwardButtonClicked(){this.skipCount=this.skipCount+this.pageSize,this.updateData(!1)}populateContextMenu(e,t){const i=t;i.valueObjectPresentation&&(e.revealSection().appendItem(Re(Ee.expandRecursively),(()=>{i.valueObjectPresentation&&i.valueObjectPresentation.objectTreeElement().expandRecursively()}),{jslogContext:"expand-recursively"}),e.revealSection().appendItem(Re(Ee.collapse),(()=>{i.valueObjectPresentation&&i.valueObjectPresentation.objectTreeElement().collapse()}),{jslogContext:"collapse"}))}refreshData(){this.updateData(!0)}update(e=null,t=null){e&&(this.objectStore=e,this.index=t,this.dataGrid&&this.dataGrid.asWidget().detach(),this.dataGrid=this.createDataGrid(),this.dataGrid.setRowContextMenuCallback(this.populateContextMenu.bind(this)),this.dataGrid.asWidget().show(this.element),this.skipCount=0,this.updateData(!0))}parseKey(e){let t;try{t=JSON.parse(e)}catch{t=e}return t}updateData(e){const t=this.parseKey(this.keyInput.value()),i=this.pageSize;let s=this.skipCount,r=this.dataGrid.selectedNode?this.dataGrid.selectedNode.data.number:0;if(r=Math.max(r,this.skipCount),this.clearButton.setEnabled(!this.isIndex),!e&&this.lastKey===t&&this.lastPageSize===i&&this.lastSkipCount===s)return;function o(e,t){this.clear(),this.entries=e;let i=null;for(let t=0;t<e.length;++t){const o={};o.number=t+s,o.key=e[t].key,o["primary-key"]=e[t].primaryKey,o.value=e[t].value;const a=new Be(o);this.dataGrid.rootNode().appendChild(a),o.number<=r&&(i=a)}i&&i.select(),this.pageBackButton.setEnabled(Boolean(s)),this.pageForwardButton.setEnabled(t),this.needsRefresh.setVisible(!1),this.updateToolbarEnablement(),this.updateSelectionColor(),this.updatedDataForTests()}this.lastKey===t&&this.lastPageSize===i||(s=0,this.skipCount=0),this.lastKey=t,this.lastPageSize=i,this.lastSkipCount=s;const a=t?window.IDBKeyRange.lowerBound(t):null;this.isIndex&&this.index?this.model.loadIndexData(this.databaseId,this.objectStore.name,this.index.name,a,s,i,o.bind(this)):this.model.loadObjectStoreData(this.databaseId,this.objectStore.name,a,s,i,o.bind(this)),this.model.getMetadata(this.databaseId,this.objectStore).then(this.updateSummaryBar.bind(this))}updateSummaryBar(e){if(this.summaryBarElement||(this.summaryBarElement=this.element.createChild("div","object-store-summary-bar")),this.summaryBarElement.removeChildren(),!e)return;const t=this.summaryBarElement.createChild("span");t.textContent=Re(Ee.totalEntriesS,{PH1:String(e.entriesCount)}),this.objectStore.autoIncrement&&(t.textContent+=" ❘ ",t.textContent+=Re(Ee.keyGeneratorValueS,{PH1:String(e.keyGeneratorValue)}))}updatedDataForTests(){}refreshButtonClicked(){this.updateData(!0)}async clearButtonClicked(){this.clearButton.setEnabled(!1),this.clearingObjectStore=!0,await this.model.clearObjectStore(this.databaseId,this.objectStore.name),this.clearingObjectStore=!1,this.clearButton.setEnabled(!0),this.updateData(!0)}markNeedsRefresh(){this.clearingObjectStore||this.needsRefresh.setVisible(!0)}async deleteButtonClicked(e){if(!e&&!(e=this.dataGrid.selectedNode))return;const t=(this.isIndex?e.data["primary-key"]:e.data.key).value;await this.model.deleteEntries(this.databaseId,this.objectStore.name,window.IDBKeyRange.only(t)),this.refreshObjectStoreCallback()}clear(){this.dataGrid.rootNode().removeChildren(),this.entries=[]}updateToolbarEnablement(){const e=!this.dataGrid||0===this.dataGrid.rootNode().children.length;this.deleteSelectedButton.setEnabled(!e&&null!==this.dataGrid.selectedNode)}updateSelectionColor(){this.previouslySelectedNode&&this.previouslySelectedNode.element().querySelectorAll(".source-code").forEach((e=>{const t=e.shadowRoot;t?.adoptedStyleSheets.pop()})),this.previouslySelectedNode=this.dataGrid.selectedNode??void 0,this.dataGrid.selectedNode?.element().querySelectorAll(".source-code").forEach((e=>{const t=e.shadowRoot,i=new CSSStyleSheet;i.replaceSync("::selection {background-color: var(--sys-color-state-focus-select); color: currentColor;}"),t?.adoptedStyleSheets.push(i)}))}}class Be extends S.DataGrid.DataGridNode{selectable;valueObjectPresentation;constructor(e){super(e,!1),this.selectable=!0,this.valueObjectPresentation=null}createCell(e){const t=super.createCell(e),i=this.data[e];switch(e){case"value":{t.removeChildren();const e=b.ObjectPropertiesSection.ObjectPropertiesSection.defaultObjectPropertiesSection(i,void 0,!0,!0);t.appendChild(e.element),this.valueObjectPresentation=e;break}case"key":case"primary-key":{t.removeChildren();const e=b.ObjectPropertiesSection.ObjectPropertiesSection.defaultObjectPresentation(i,void 0,!0,!0);t.appendChild(e);break}}return t}}var Pe=Object.freeze({__proto__:null,IDBDataGridNode:Be,IDBDataView:Ae,IDBDatabaseView:Le});class De extends o.SDKModel.SDKModel{storageAgent;enabled;constructor(e){super(e),e.registerStorageDispatcher(this),this.storageAgent=e.storageAgent(),this.enabled=!1}enable(){this.enabled||this.storageAgent.invoke_setInterestGroupTracking({enable:!0})}disable(){this.enabled&&this.storageAgent.invoke_setInterestGroupTracking({enable:!1})}interestGroupAccessed(e){this.dispatchEventToListeners("InterestGroupAccess",e)}attributionReportingTriggerRegistered(e){}indexedDBListUpdated(e){}indexedDBContentUpdated(e){}interestGroupAuctionEventOccurred(e){}interestGroupAuctionNetworkRequestCreated(e){}cacheStorageListUpdated(e){}cacheStorageContentUpdated(e){}sharedStorageAccessed(e){}storageBucketCreatedOrUpdated(e){}storageBucketDeleted(e){}attributionReportingSourceRegistered(e){}}o.SDKModel.SDKModel.register(De,{capabilities:8192,autostart:!1});var Fe=Object.freeze({__proto__:null,InterestGroupStorageModel:De}),Oe={cssText:`devtools-interest-group-access-grid{overflow:auto}\n/*# sourceURL=${import.meta.resolve("./interestGroupStorageView.css")} */\n`};const Ve={noValueSelected:"No interest group selected",clickToDisplayBody:"Select any interest group event to display the group's current state",noDataAvailable:"No details available",noDataDescription:"The browser may have left the group."},Ue=i.i18n.registerUIStrings("panels/application/InterestGroupStorageView.ts",Ve),We=i.i18n.getLocalizedString.bind(void 0,Ue);class Ne extends c.SplitWidget.SplitWidget{interestGroupGrid=new m.InterestGroupAccessGrid.InterestGroupAccessGrid;events=[];detailsGetter;noDataView;noDisplayView;constructor(e){super(!1,!0),this.element.setAttribute("jslog",`${p.pane("interest-groups")}`),this.detailsGetter=e;const t=new c.Widget.VBox;this.noDisplayView=new c.EmptyWidget.EmptyWidget(We(Ve.noValueSelected),We(Ve.clickToDisplayBody)),this.noDataView=new c.EmptyWidget.EmptyWidget(We(Ve.noDataAvailable),We(Ve.noDataDescription)),t.setMinimumSize(0,120),this.setMainWidget(t),this.noDisplayView.setMinimumSize(0,80),this.setSidebarWidget(this.noDisplayView),this.noDataView.setMinimumSize(0,80),this.noDisplayView.contentElement.setAttribute("jslog",`${p.pane("details").track({resize:!0})}`),this.noDataView.contentElement.setAttribute("jslog",`${p.pane("details").track({resize:!0})}`),this.hideSidebar(),t.contentElement.appendChild(this.interestGroupGrid),this.interestGroupGrid.addEventListener("select",this.onFocus.bind(this))}wasShown(){super.wasShown();const e=this.mainWidget();e&&e.registerRequiredCSS(Oe)}addEvent(e){"Both"!==this.showMode()&&this.showBoth();this.events.find((t=>{return s=e,(i=t).accessTime===s.accessTime&&i.type===s.type&&i.ownerOrigin===s.ownerOrigin&&i.name===s.name;var i,s}))||(this.events.push(e),this.interestGroupGrid.data=this.events)}clearEvents(){this.events=[],this.interestGroupGrid.data=this.events,this.setSidebarWidget(this.noDisplayView),this.sidebarUpdatedForTesting()}async onFocus(e){const t=e,{ownerOrigin:i,name:s,type:r}=t.detail;let o=null;if("additionalBid"!==r&&"additionalBidWin"!==r&&"topLevelAdditionalBid"!==r&&(o=await this.detailsGetter.getInterestGroupDetails(i,s)),o){const e=await l.JSONView.JSONView.createView(JSON.stringify(o));e?.setMinimumSize(0,40),e&&(e.contentElement.setAttribute("jslog",`${p.pane("details").track({resize:!0})}`),this.setSidebarWidget(e))}else this.setSidebarWidget(this.noDataView);this.sidebarUpdatedForTesting()}getEventsForTesting(){return this.events}getInterestGroupGridForTesting(){return this.interestGroupGrid}sidebarUpdatedForTesting(){}}var je=Object.freeze({__proto__:null,InterestGroupStorageView:Ne});const He={interestGroups:"Interest groups"},_e=i.i18n.registerUIStrings("panels/application/InterestGroupTreeElement.ts",He),Ke=i.i18n.getLocalizedString.bind(void 0,_e);class qe extends F{view;constructor(e){super(e,Ke(He.interestGroups),!1,"interest-groups");const t=n.Icon.create("database");this.setLeadingIcons([t]),this.view=new Ne(this)}get itemURL(){return"interest-groups://"}async getInterestGroupDetails(e,t){const i=o.TargetManager.TargetManager.instance().primaryPageTarget();if(!i)return null;return(await i.storageAgent().invoke_getInterestGroupDetails({ownerOrigin:e,name:t})).details}onselect(e){return super.onselect(e),this.showView(this.view),t.userMetrics.panelShown("interest-groups"),!1}addEvent(e){this.view.addEvent(e)}clearEvents(){this.view.clearEvents()}}var ze=Object.freeze({__proto__:null,InterestGroupTreeElement:qe,i18nString:Ke}),Ge={cssText:`.report-content-box{overflow:initial}.report-field-name{flex:0 0 200px}.report-field-value{user-select:text;display:flex}.report-field .inline-name{color:var(--sys-color-state-disabled);padding-left:2ex;user-select:none;white-space:pre-line}.report-field .inline-name::after{content:":\\A0"}.report-field .inline-comment{color:var(--sys-color-token-subtle);padding-left:1ex;white-space:pre-line}.report-field .inline-comment::before{content:"("}.report-field .inline-comment::after{content:")"}.report-field .inline-span{color:var(--sys-color-token-subtle);padding-left:1ex;white-space:pre-line}.report-field-value-link{display:inline-block}.icon-link.devtools-link{background-color:var(--sys-color-primary);vertical-align:sub}.frame-details-container{overflow:auto}.frame-details-report-container{min-width:550px}.text-ellipsis{overflow:hidden;text-overflow:ellipsis}\n/*# sourceURL=${import.meta.resolve("./openedWindowDetailsView.css")} */\n`};const $e={yes:"Yes",no:"No",clickToOpenInElementsPanel:"Click to open in Elements panel",document:"Document",url:"URL",security:"Security",openerFrame:"Opener Frame",accessToOpener:"Access to opener",showsWhetherTheOpenedWindowIs:"Shows whether the opened window is able to access its opener and vice versa",windowWithoutTitle:"Window without title",closed:"closed",worker:"worker",type:"Type",securityIsolation:"Security & Isolation",crossoriginEmbedderPolicy:"Cross-Origin Embedder Policy",webWorker:"Web Worker",unknown:"Unknown",reportingTo:"reporting to"},Xe=i.i18n.registerUIStrings("panels/application/OpenedWindowDetailsView.ts",$e),Je=i.i18n.getLocalizedString.bind(void 0,Xe);async function Qe(t){let i=null;if(t instanceof o.ResourceTreeModel.ResourceTreeFrame?i=t:t&&(i=o.FrameManager.FrameManager.instance().getFrame(t)),!i)return null;const s=await i.getOwnerDOMNodeOrDocument();if(!s)return null;const r=function(e,t,i){const s=n.Icon.create(e,"icon-link devtools-link"),r=document.createElement("button");return c.Tooltip.Tooltip.install(r,t),r.classList.add("devtools-link","link-style","text-button"),r.appendChild(s),r.addEventListener("click",(e=>{e.consume(!0),i()})),r}("code-circle",Je($e.clickToOpenInElementsPanel),(()=>e.Revealer.reveal(s))),a=document.createElement("span");return a.textContent=`<${s.nodeName().toLocaleLowerCase()}>`,r.insertBefore(a,r.firstChild),r.addEventListener("mouseenter",(()=>{i&&i.highlight()})),r.addEventListener("mouseleave",(()=>{o.OverlayModel.OverlayModel.hideDOMNodeHighlight()})),r}class Ye extends c.ThrottledWidget.ThrottledWidget{targetInfo;isWindowClosed;reportView;documentSection;#m;securitySection;openerElementField;hasDOMAccessValue;constructor(e,t){super(),this.registerRequiredCSS(Ge),this.targetInfo=e,this.isWindowClosed=t,this.contentElement.classList.add("frame-details-container"),this.reportView=new c.ReportView.ReportView(this.buildTitle()),this.reportView.show(this.contentElement),this.reportView.registerRequiredCSS(Ge),this.reportView.element.classList.add("frame-details-report-container"),this.documentSection=this.reportView.appendSection(Je($e.document)),this.#m=this.documentSection.appendField(Je($e.url)).createChild("div","text-ellipsis"),this.securitySection=this.reportView.appendSection(Je($e.security)),this.openerElementField=this.securitySection.appendField(Je($e.openerFrame)),this.securitySection.setFieldVisible(Je($e.openerFrame),!1),this.hasDOMAccessValue=this.securitySection.appendField(Je($e.accessToOpener)),c.Tooltip.Tooltip.install(this.hasDOMAccessValue,Je($e.showsWhetherTheOpenedWindowIs)),this.update()}async doUpdate(){var e;this.reportView.setTitle(this.buildTitle()),this.#m.textContent=this.targetInfo.url,this.#m.title=this.targetInfo.url,this.hasDOMAccessValue.textContent=(e=this.targetInfo.canAccessOpener,Je(e?$e.yes:$e.no)),this.maybeDisplayOpenerFrame()}async maybeDisplayOpenerFrame(){this.openerElementField.removeChildren();const e=await Qe(this.targetInfo.openerFrameId);if(e)return this.openerElementField.append(e),void this.securitySection.setFieldVisible(Je($e.openerFrame),!0);this.securitySection.setFieldVisible(Je($e.openerFrame),!1)}buildTitle(){let e=this.targetInfo.title||Je($e.windowWithoutTitle);return this.isWindowClosed&&(e+=` (${Je($e.closed)})`),e}setIsWindowClosed(e){this.isWindowClosed=e}setTargetInfo(e){this.targetInfo=e}}class Ze extends c.ThrottledWidget.ThrottledWidget{targetInfo;reportView;documentSection;isolationSection;coepPolicy;constructor(e){super(),this.registerRequiredCSS(Ge),this.targetInfo=e,this.contentElement.classList.add("frame-details-container"),this.reportView=new c.ReportView.ReportView(this.targetInfo.title||this.targetInfo.url||Je($e.worker)),this.reportView.show(this.contentElement),this.reportView.registerRequiredCSS(Ge),this.reportView.element.classList.add("frame-details-report-container"),this.documentSection=this.reportView.appendSection(Je($e.document));const t=this.documentSection.appendField(Je($e.url)).createChild("div","text-ellipsis");t.textContent=this.targetInfo.url,t.title=this.targetInfo.url;this.documentSection.appendField(Je($e.type)).textContent=this.workerTypeToString(this.targetInfo.type),this.isolationSection=this.reportView.appendSection(Je($e.securityIsolation)),this.coepPolicy=this.isolationSection.appendField(Je($e.crossoriginEmbedderPolicy)),this.update()}workerTypeToString(e){return"worker"===e?Je($e.webWorker):"service_worker"===e?i.i18n.lockedString("Service Worker"):Je($e.unknown)}async updateCoopCoepStatus(){const e=o.TargetManager.TargetManager.instance().targetById(this.targetInfo.targetId);if(!e)return;const t=e.model(o.NetworkManager.NetworkManager),i=t&&await t.getSecurityIsolationStatus(null);if(!i)return;this.fillCrossOriginPolicy(this.coepPolicy,(e=>"None"!==e),i.coep)}fillCrossOriginPolicy(e,t,i){if(!i)return void(e.textContent="");const s=t(i.value);if(e.textContent=s?i.value:i.reportOnlyValue,!s&&t(i.reportOnlyValue)){const t=document.createElement("span");t.classList.add("inline-comment"),t.textContent="report-only",e.appendChild(t)}const r=s?i.reportingEndpoint:i.reportOnlyReportingEndpoint;if(r){e.createChild("span","inline-name").textContent=Je($e.reportingTo);e.createChild("span").textContent=r}}async doUpdate(){await this.updateCoopCoepStatus()}}var et=Object.freeze({__proto__:null,OpenedWindowDetailsView:Ye,WorkerDetailsView:Ze}),tt={cssText:`.empty-state{display:none}.empty{.empty-state{display:flex}devtools-split-view, .pretty-print-button, devtools-toolbar{display:none}}.preloading-toolbar{background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider);button.toolbar-has-dropdown{margin:var(--sys-size-2) 0}}\n/*# sourceURL=${import.meta.resolve("./preloading/preloadingView.css")} */\n`},it={cssText:`:host{padding:2px 1px 2px 2px}.title{padding-left:8px}.subtitle{padding-left:8px}\n/*# sourceURL=${import.meta.resolve("./preloading/preloadingViewDropDown.css")} */\n`};const st={filterFilterByRuleSet:"Filter by rule set",filterAllPreloads:"All speculative loads",noRuleSets:"no rule sets",validityValid:"Valid",validityInvalid:"Invalid",validitySomeRulesInvalid:"Some rules invalid",statusNotTriggered:"Not triggered",statusPending:"Pending",statusRunning:"Running",statusReady:"Ready",statusSuccess:"Success",statusFailure:"Failure",prettyPrint:"Pretty print",noRulesDetected:"No rules detected",rulesDescription:"On this page you will see the speculation rules used to prefetch and prerender page navigations.",noPrefetchAttempts:"No speculation detected",prefetchDescription:"On this page you will see details on speculative loads.",learnMore:"Learn more"},rt=i.i18n.registerUIStrings("panels/application/preloading/PreloadingView.ts",st),ot=i.i18n.getLocalizedString.bind(void 0,rt),at="https://developer.chrome.com/docs/devtools/application/debugging-speculation-rules",nt=Symbol("AllRuleSetRootId");class dt{static status(e){switch(e){case"NotTriggered":return ot(st.statusNotTriggered);case"Pending":return ot(st.statusPending);case"Running":return ot(st.statusRunning);case"Ready":return ot(st.statusReady);case"Success":return ot(st.statusSuccess);case"Failure":return ot(st.statusFailure);case"NotSupported":return i.i18n.lockedString("Internal error")}}static preloadsStatusSummary(e){return["NotTriggered","Pending","Running","Ready","Success","Failure"].filter((t=>(e?.get(t)||0)>0)).map((t=>(e?.get(t)||0)+" "+this.status(t))).join(", ").toLocaleLowerCase()}static validity({errorType:e}){switch(e){case void 0:return ot(st.validityValid);case"SourceIsNotJsonObject":return ot(st.validityInvalid);case"InvalidRulesSkipped":return ot(st.validitySomeRulesInvalid)}}static location(e){if(void 0!==e.backendNodeId)return i.i18n.lockedString("<script>");if(void 0!==e.url)return e.url;throw new Error("unreachable")}static processLocalId(e){const t=e.indexOf(".");return-1===t?e:e.slice(t+1)}static ruleSetLocationShort(e,t){const i=void 0===e.url?t:e.url;return w.ResourceUtils.displayNameForURL(i)}}function lt(){return o.TargetManager.TargetManager.instance().scopeTarget()?.inspectedURL()||""}class ct extends c.Widget.VBox{model;focusedRuleSetId=null;focusedPreloadingAttemptId=null;warningsContainer;warningsView=new pt;hsplit;ruleSetGrid=new T.RuleSetGrid.RuleSetGrid;ruleSetDetails=new T.RuleSetDetailsView.RuleSetDetailsView;shouldPrettyPrint=e.Settings.Settings.instance().moduleSetting("auto-pretty-print-minified").get();constructor(e){super(!0,!1),this.registerRequiredCSS(J,tt),this.model=e,o.TargetManager.TargetManager.instance().addScopeChangeListener(this.onScopeChange.bind(this)),o.TargetManager.TargetManager.instance().addModelListener(o.PreloadingModel.PreloadingModel,"ModelUpdated",this.render,this,{scoped:!0}),o.TargetManager.TargetManager.instance().addModelListener(o.PreloadingModel.PreloadingModel,"WarningsUpdated",this.warningsView.onWarningsUpdated,this.warningsView,{scoped:!0}),this.warningsContainer=document.createElement("div"),this.warningsContainer.classList.add("flex-none"),this.contentElement.insertBefore(this.warningsContainer,this.contentElement.firstChild),this.warningsView.show(this.warningsContainer),this.ruleSetGrid.addEventListener("select",this.onRuleSetsGridCellFocused.bind(this));k(f`
        <div class="empty-state">
          <span class="empty-state-header">${ot(st.noRulesDetected)}</span>
          <div class="empty-state-description">
            <span>${ot(st.rulesDescription)}</span>
            ${c.XLink.XLink.create(at,ot(st.learnMore),"x-link",void 0,"learn-more")}
          </div>
        </div>
        <devtools-split-view sidebar-position="second">
          <div slot="main">
            ${this.ruleSetGrid}
          </div>
          <div slot="sidebar" jslog=${p.section("rule-set-details")}>
            ${this.ruleSetDetails}
          </div>
        </devtools-split-view>
        <div class="pretty-print-button" style="border-top: 1px solid var(--sys-color-divider)">
        <devtools-button
          .iconName=${"brackets"}
          .toggledIconName=${"brackets"}
          .toggled=${this.shouldPrettyPrint}
          .toggleType=${"primary-toggle"}
          .title=${ot(st.prettyPrint)}
          .variant=${"icon_toggle"}
          .size=${"REGULAR"}
          @click=${()=>{this.shouldPrettyPrint=!this.shouldPrettyPrint,this.updateRuleSetDetails()}}
          jslog=${p.action().track({click:!0}).context("preloading-status-panel-pretty-print")}></devtools-button>
        </div>`,this.contentElement,{host:this}),this.hsplit=this.contentElement.querySelector("devtools-split-view")}wasShown(){super.wasShown(),this.warningsView.wasShown(),this.render()}onScopeChange(){const e=o.TargetManager.TargetManager.instance().scopeTarget()?.model(o.PreloadingModel.PreloadingModel);r(e),this.model=e,this.render()}revealRuleSet(e){this.focusedRuleSetId=e.ruleSetId,this.render()}updateRuleSetDetails(){const e=this.focusedRuleSetId,t=null===e?null:this.model.getRuleSetById(e);this.ruleSetDetails.shouldPrettyPrint=this.shouldPrettyPrint,this.ruleSetDetails.data=t,null===t?this.hsplit.setAttribute("sidebar-visibility","hidden"):this.hsplit.removeAttribute("sidebar-visibility")}render(){const e=this.model.getPreloadCountsByRuleSetId(),t=this.model.getAllRuleSets().map((({id:t,value:i})=>{const s=e.get(t)||new Map;return{ruleSet:i,preloadsStatusSummary:dt.preloadsStatusSummary(s)}}));this.ruleSetGrid.update({rows:t,pageURL:lt()}),this.contentElement.classList.toggle("empty",0===t.length),this.updateRuleSetDetails()}onRuleSetsGridCellFocused(e){const t=e;this.focusedRuleSetId=t.detail,this.render()}getInfobarContainerForTest(){return this.warningsView.contentElement}getRuleSetGridForTest(){return this.ruleSetGrid}getRuleSetDetailsForTest(){return this.ruleSetDetails}}class ht extends c.Widget.VBox{model;focusedPreloadingAttemptId=null;warningsContainer;warningsView=new pt;preloadingGrid=new T.PreloadingGrid.PreloadingGrid;preloadingDetails=new T.PreloadingDetailsReportView.PreloadingDetailsReportView;ruleSetSelector;constructor(e){super(!0,!1),this.registerRequiredCSS(J,tt),this.element.setAttribute("jslog",`${p.pane("preloading-speculations")}`),this.model=e,o.TargetManager.TargetManager.instance().addScopeChangeListener(this.onScopeChange.bind(this)),o.TargetManager.TargetManager.instance().addModelListener(o.PreloadingModel.PreloadingModel,"ModelUpdated",this.render,this,{scoped:!0}),o.TargetManager.TargetManager.instance().addModelListener(o.PreloadingModel.PreloadingModel,"WarningsUpdated",this.warningsView.onWarningsUpdated,this.warningsView,{scoped:!0}),this.warningsContainer=document.createElement("div"),this.warningsContainer.classList.add("flex-none"),this.contentElement.insertBefore(this.warningsContainer,this.contentElement.firstChild),this.warningsView.show(this.warningsContainer);const t=new c.Widget.VBox,i=t.contentElement.createChild("devtools-toolbar","preloading-toolbar");i.setAttribute("jslog",`${p.toolbar()}`),this.ruleSetSelector=new ut((()=>this.render())),i.appendToolbarItem(this.ruleSetSelector.item()),this.preloadingGrid.addEventListener("select",this.onPreloadingGridCellFocused.bind(this)),k(f`
        <div class="empty-state">
          <span class="empty-state-header">${ot(st.noPrefetchAttempts)}</span>
          <div class="empty-state-description">
            <span>${ot(st.prefetchDescription)}</span>
            ${c.XLink.XLink.create(at,ot(st.learnMore),"x-link",void 0,"learn-more")}
          </div>
        </div>
        <devtools-split-view sidebar-position="second">
          <div slot="main" class="overflow-auto" style="height: 100%">
            ${this.preloadingGrid}
          </div>
          <div slot="sidebar" class="overflow-auto" style="height: 100%">
            ${this.preloadingDetails}
          </div>
        </devtools-split-view>`,t.contentElement,{host:this}),t.show(this.contentElement)}wasShown(){super.wasShown(),this.warningsView.wasShown(),this.render()}onScopeChange(){const e=o.TargetManager.TargetManager.instance().scopeTarget()?.model(o.PreloadingModel.PreloadingModel);r(e),this.model=e,this.render()}setFilter(e){let t=e.ruleSetId;null!==t&&void 0===this.model.getRuleSetById(t)&&(t=null),this.ruleSetSelector.select(t)}updatePreloadingDetails(){const e=this.focusedPreloadingAttemptId,t=null===e?null:this.model.getPreloadingAttemptById(e);if(null===t)this.preloadingDetails.data=null;else{const e=this.model.getPipeline(t),i=t.ruleSetIds.map((e=>this.model.getRuleSetById(e))).filter((e=>null!==e));this.preloadingDetails.data={pipeline:e,ruleSets:i,pageURL:lt()}}}render(){const e=this.ruleSetSelector.getSelected(),t=this.model.getRepresentativePreloadingAttempts(e).map((({id:e,value:t})=>{const i=t,s=this.model.getPipeline(i),r=i.ruleSetIds.flatMap((e=>{const t=this.model.getRuleSetById(e);return null===t?[]:[t]}));return{id:e,pipeline:s,ruleSets:r}}));this.preloadingGrid.update({rows:t,pageURL:lt()}),this.contentElement.classList.toggle("empty",0===t.length),this.updatePreloadingDetails()}onPreloadingGridCellFocused(e){const t=e;this.focusedPreloadingAttemptId=t.detail,this.render()}getRuleSetSelectorToolbarItemForTest(){return this.ruleSetSelector.item()}getPreloadingGridForTest(){return this.preloadingGrid}getPreloadingDetailsForTest(){return this.preloadingDetails}selectRuleSetOnFilterForTest(e){this.ruleSetSelector.select(e)}}class gt extends c.Widget.VBox{model;warningsContainer;warningsView=new pt;usedPreloading=new T.UsedPreloadingView.UsedPreloadingView;constructor(e){super(!0,!1),this.registerRequiredCSS(J,tt),this.element.setAttribute("jslog",`${p.pane("speculative-loads")}`),this.model=e,o.TargetManager.TargetManager.instance().addScopeChangeListener(this.onScopeChange.bind(this)),o.TargetManager.TargetManager.instance().addModelListener(o.PreloadingModel.PreloadingModel,"ModelUpdated",this.render,this,{scoped:!0}),o.TargetManager.TargetManager.instance().addModelListener(o.PreloadingModel.PreloadingModel,"WarningsUpdated",this.warningsView.onWarningsUpdated,this.warningsView,{scoped:!0}),this.warningsContainer=document.createElement("div"),this.warningsContainer.classList.add("flex-none"),this.contentElement.insertBefore(this.warningsContainer,this.contentElement.firstChild),this.warningsView.show(this.warningsContainer);const t=new c.Widget.VBox;t.contentElement.appendChild(this.usedPreloading),t.show(this.contentElement)}wasShown(){super.wasShown(),this.warningsView.wasShown(),this.render()}onScopeChange(){const e=o.TargetManager.TargetManager.instance().scopeTarget()?.model(o.PreloadingModel.PreloadingModel);r(e),this.model=e,this.render()}render(){this.usedPreloading.data={pageURL:o.TargetManager.TargetManager.instance().scopeTarget()?.inspectedURL()||"",previousAttempts:this.model.getRepresentativePreloadingAttemptsOfPreviousPage().map((({value:e})=>e)),currentAttempts:this.model.getRepresentativePreloadingAttempts(null).map((({value:e})=>e))}}getUsedPreloadingForTest(){return this.usedPreloading}}class ut{model;onSelectionChanged=()=>{};toolbarItem;listModel;dropDown;constructor(e){const t=o.TargetManager.TargetManager.instance().scopeTarget()?.model(o.PreloadingModel.PreloadingModel);r(t),this.model=t,o.TargetManager.TargetManager.instance().addScopeChangeListener(this.onScopeChange.bind(this)),o.TargetManager.TargetManager.instance().addModelListener(o.PreloadingModel.PreloadingModel,"ModelUpdated",this.onModelUpdated,this,{scoped:!0}),this.listModel=new c.ListModel.ListModel,this.dropDown=new c.SoftDropDown.SoftDropDown(this.listModel,this),this.dropDown.setRowHeight(36),this.dropDown.setPlaceholderText(ot(st.filterAllPreloads)),this.toolbarItem=new c.Toolbar.ToolbarItem(this.dropDown.element),this.toolbarItem.setTitle(ot(st.filterFilterByRuleSet)),this.toolbarItem.element.classList.add("toolbar-has-dropdown"),this.toolbarItem.element.setAttribute("jslog",`${p.action("filter-by-rule-set").track({click:!0})}`),this.onModelUpdated(),this.onSelectionChanged=e}onScopeChange(){const e=o.TargetManager.TargetManager.instance().scopeTarget()?.model(o.PreloadingModel.PreloadingModel);r(e),this.model=e,this.onModelUpdated()}onModelUpdated(){const e=this.model.getAllRuleSets().map((({id:e})=>e)),t=[nt,...e],i=this.dropDown.getSelectedItem();this.listModel.replaceAll(t),null===i?this.dropDown.selectItem(nt):this.dropDown.selectItem(i),this.updateWidth(t)}updateWidth(e){const t=e.map((e=>this.titleFor(e).length)),i=Math.max(...t),s=Math.min(6*i+16,315);this.dropDown.setWidth(s)}translateItemIdToRuleSetId(e){return e===nt?null:e}getSelected(){const e=this.dropDown.getSelectedItem();return null===e?null:this.translateItemIdToRuleSetId(e)}select(e){this.dropDown.selectItem(e)}item(){return this.toolbarItem}titleFor(e){const t=this.translateItemIdToRuleSetId(e);if(null===t)return ot(st.filterAllPreloads);const s=this.model.getRuleSetById(t);return null===s?i.i18n.lockedString("Internal error"):dt.ruleSetLocationShort(s,lt())}subtitleFor(e){const t=this.translateItemIdToRuleSetId(e),i=this.model.getPreloadCountsByRuleSetId().get(t)||new Map;return dt.preloadsStatusSummary(i)||`(${ot(st.noRuleSets)})`}createElementForItem(e){const t=document.createElement("div"),i=c.UIUtils.createShadowRootWithCoreStyles(t,{cssFile:it}),r=i.createChild("div","title");c.UIUtils.createTextChild(r,s.StringUtilities.trimEndWithMaxLength(this.titleFor(e),100));const o=i.createChild("div","subtitle");return c.UIUtils.createTextChild(o,this.subtitleFor(e)),t}isItemSelectable(e){return!0}itemSelected(e){this.onSelectionChanged()}highlightedItemChanged(e,t,i,s){}}class pt extends c.Widget.VBox{infobar=new T.PreloadingDisabledInfobar.PreloadingDisabledInfobar;constructor(){super(!1,!1),this.registerRequiredCSS(J)}wasShown(){super.wasShown(),this.contentElement.append(this.infobar)}onWarningsUpdated(e){this.infobar.data=e.data}}var mt=Object.freeze({__proto__:null,PreloadingAttemptView:ht,PreloadingRuleSetView:ct,PreloadingSummaryView:gt,PreloadingWarningsView:pt});const vt={speculativeLoads:"Speculative loads",rules:"Rules",speculations:"Speculations"},wt=i.i18n.registerUIStrings("panels/application/PreloadingTreeElement.ts",vt),St=i.i18n.getLocalizedString.bind(void 0,wt);class bt extends F{#e;#v;view;#w;#S;constructor(e,t,i,s){super(e,s,!1,"speculative-loads"),this.#v=t,this.#w=i;const r=n.Icon.create("arrow-up-down");this.setLeadingIcons([r]),this.#S=!1}get itemURL(){return this.#w}initialize(e){this.#e=e,this.#S&&!this.view&&this.onselect(!1)}onselect(e){return super.onselect(e),this.#S=!0,!!this.#e&&(this.view||(this.view=new this.#v(this.#e)),this.showView(this.view),!1)}}class yt extends O{#e;#b;#S;#y=null;#k=null;constructor(e){super(e,St(vt.speculativeLoads),"","","preloading");const t=n.Icon.create("arrow-up-down");this.setLeadingIcons([t]),this.#S=!1}constructChildren(e){this.#y=new kt(e),this.#k=new ft(e),this.appendChild(this.#y),this.appendChild(this.#k)}initialize(e){if(null===this.#y||null===this.#k)throw new Error("unreachable");this.#e=e,this.#y.initialize(e),this.#k.initialize(e),this.#S&&!this.#b&&this.onselect(!1)}onselect(e){return super.onselect(e),this.#S=!0,!!this.#e&&(this.#b||(this.#b=new gt(this.#e)),this.showView(this.#b),!1)}expandAndRevealRuleSet(e){if(null===this.#y)throw new Error("unreachable");this.expand(),this.#y.revealRuleSet(e)}expandAndRevealAttempts(e){if(null===this.#k)throw new Error("unreachable");this.expand(),this.#k.revealAttempts(e)}}class kt extends bt{constructor(e){super(e,ct,"preloading://rule-set",St(vt.rules))}revealRuleSet(e){this.select(),void 0!==this.view&&this.view?.revealRuleSet(e)}}class ft extends bt{constructor(e){super(e,ht,"preloading://attempt",St(vt.speculations))}revealAttempts(e){this.select(),this.view?.setFilter(e)}}var It=Object.freeze({__proto__:null,PreloadingRuleSetTreeElement:kt,PreloadingSummaryTreeElement:yt});const Ct={noReportSelected:"No report selected",clickToDisplayBody:"Click on any report to display its body"},Tt=i.i18n.registerUIStrings("panels/application/ReportingApiReportsView.ts",Ct),xt=i.i18n.getLocalizedString.bind(void 0,Tt);class Et extends c.SplitWidget.SplitWidget{reportsGrid=new m.ReportsGrid.ReportsGrid;reports=[];constructor(e){super(!1,!0);const t=new c.Widget.VBox,i=new c.EmptyWidget.EmptyWidget(xt(Ct.noReportSelected),xt(Ct.clickToDisplayBody));t.setMinimumSize(0,80),this.setMainWidget(t),i.setMinimumSize(0,40),i.element.setAttribute("jslog",`${p.pane("preview").track({resize:!0})}`),this.setSidebarWidget(i),this.hideSidebar(),t.contentElement.appendChild(this.reportsGrid),this.reportsGrid.addEventListener("select",this.onFocus.bind(this)),e.addEventListener(o.NetworkManager.Events.ReportingApiReportAdded,(e=>this.onReportAdded(e.data)),this),e.addEventListener(o.NetworkManager.Events.ReportingApiReportUpdated,(e=>this.onReportUpdated(e.data)),this)}onReportAdded(e){"Both"!==this.showMode()&&this.showBoth(),this.reports.push(e),this.reportsGrid.data={reports:this.reports}}onReportUpdated(e){const t=this.reports.findIndex((t=>t.id===e.id));this.reports[t]=e,this.reportsGrid.data={reports:this.reports}}async onFocus(e){const t=e,i=this.reports.find((e=>e.id===t.detail));if(i){const e=await l.JSONView.JSONView.createView(JSON.stringify(i.body));e?.setMinimumSize(0,40),e&&this.setSidebarWidget(e)}}getReports(){return this.reports}getReportsGrid(){return this.reportsGrid}}var Mt=Object.freeze({__proto__:null,ReportingApiReportsView:Et,i18nString:xt});const Rt={noReportOrEndpoint:"No report or endpoint",reportingApiDescription:"On this page you will be able to inspect `Reporting API` reports and endpoints."},Lt=i.i18n.registerUIStrings("panels/application/ReportingApiView.ts",Rt),At=i.i18n.getLocalizedString.bind(void 0,Lt);class Bt extends c.SplitWidget.SplitWidget{endpointsGrid;endpoints;#f;#I;constructor(e){super(!1,!0),this.element.setAttribute("jslog",`${p.pane("reporting-api")}`),this.endpointsGrid=e,this.endpoints=new Map;const t=o.TargetManager.TargetManager.instance().primaryPageTarget(),i=t?.model(o.NetworkManager.NetworkManager);if(this.#f=new c.EmptyWidget.EmptyWidget(At(Rt.noReportOrEndpoint),At(Rt.reportingApiDescription)),this.#f.appendLink("https://developer.chrome.com/docs/capabilities/web-apis/reporting-api"),this.setMainWidget(this.#f),i){i.addEventListener(o.NetworkManager.Events.ReportingApiEndpointsChangedForOrigin,(e=>this.onEndpointsChangedForOrigin(e.data)),this),i.addEventListener(o.NetworkManager.Events.ReportingApiReportAdded,this.#C,this),this.#I=new Et(i);const e=new c.Widget.VBox;e.setMinimumSize(0,40),e.contentElement.appendChild(this.endpointsGrid),this.setSidebarWidget(e),i.enableReportingApi(),this.hideSidebar()}}#C(){void 0!==this.#I&&this.mainWidget()===this.#f&&(this.#f?.detach(),this.#f=void 0,this.setMainWidget(this.#I),this.showBoth())}onEndpointsChangedForOrigin(e){this.#C(),this.endpoints.set(e.origin,e.endpoints),this.endpointsGrid.data={endpoints:this.endpoints}}}var Pt=Object.freeze({__proto__:null,ReportingApiView:Bt,i18nString:At});const Dt={reportingApi:"Reporting API"},Ft=i.i18n.registerUIStrings("panels/application/ReportingApiTreeElement.ts",Dt),Ot=i.i18n.getLocalizedString.bind(void 0,Ft);class Vt extends F{view;constructor(e){super(e,Ot(Dt.reportingApi),!1,"reporting-api");const t=n.Icon.create("document");this.setLeadingIcons([t])}get itemURL(){return"reportingApi://"}onselect(e){return super.onselect(e),this.view||(this.view=new Bt(new m.EndpointsGrid.EndpointsGrid)),this.showView(this.view),t.userMetrics.panelShown("reporting-api"),!1}}var Ut={cssText:`.tree-outline{li.storage-group-list-item,\n  li.storage-group-list-item:not(:has(dt-checkbox)){padding:0 var(--sys-size-8) 0 var(--sys-size-3);&::before{display:none}&:hover .selection,\n    &:active .selection::before{background-color:transparent}& + ol{padding-left:0}}li.storage-group-list-item:not(:first-child){margin-top:var(--sys-size-6)}}.icons-container devtools-icon.red-icon{color:var(--icon-error)}.icons-container devtools-icon.warn-icon{color:var(--icon-warning)}devtools-icon.navigator-file-tree-item{color:var(--icon-file-default)}devtools-icon.navigator-folder-tree-item{color:var(--icon-folder-primary)}devtools-icon.navigator-script-tree-item{color:var(--icon-file-script)}devtools-icon.navigator-stylesheet-tree-item{color:var(--icon-file-styles)}devtools-icon.navigator-image-tree-item,\ndevtools-icon.navigator-font-tree-item{color:var(--icon-file-image)}.window-closed .tree-element-title{text-decoration:line-through}\n/*# sourceURL=${import.meta.resolve("./resourcesSidebar.css")} */\n`},Wt={cssText:`.service-worker-cache-data-view .data-view-toolbar{position:relative;background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider);padding-right:10px}.service-worker-cache-data-view .data-grid{flex:auto}.service-worker-cache-data-view .data-grid .data-container tr:nth-last-child(1) td{border:0}.service-worker-cache-data-view .data-grid .data-container tr:nth-last-child(2) td{border-bottom:1px solid var(--sys-color-divider)}.service-worker-cache-data-view .data-grid .data-container tr.selected{background-color:var(--sys-color-neutral-container);color:inherit}.service-worker-cache-data-view .data-grid:focus .data-container tr.selected{background-color:var(--sys-color-tonal-container);color:var(--sys-color-on-tonal-container)}.service-worker-cache-data-view .section,\n.service-worker-cache-data-view .section > .header,\n.service-worker-cache-data-view .section > .header .title{margin:0;min-height:inherit;line-height:inherit}.service-worker-cache-data-view .data-grid .data-container td .section .header .title{white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.cache-preview-panel-resizer{background-color:var(--sys-color-surface1);height:4px;border-bottom:1px solid var(--sys-color-divider)}.cache-storage-summary-bar{flex:0 0 27px;line-height:27px;padding-left:5px;background-color:var(--sys-color-cdt-base-container);border-top:1px solid var(--sys-color-divider);white-space:nowrap;text-overflow:ellipsis;overflow:hidden}\n/*# sourceURL=${import.meta.resolve("./serviceWorkerCacheViews.css")} */\n`};const Nt={cache:"Cache",refresh:"Refresh",deleteSelected:"Delete Selected",filterByPath:"Filter by path",noCacheEntrySelected:"No cache entry selected",selectACacheEntryAboveToPreview:"Select a cache entry above to preview",name:"Name",timeCached:"Time Cached",varyHeaderWarning:"⚠️ Set ignoreVary to true when matching this entry",serviceWorkerCache:"`Service Worker` Cache",matchingEntriesS:"Matching entries: {PH1}",totalEntriesS:"Total entries: {PH1}",headers:"Headers",preview:"Preview"},jt=i.i18n.registerUIStrings("panels/application/ServiceWorkerCacheViews.ts",Nt),Ht=i.i18n.getLocalizedString.bind(void 0,jt);class _t extends c.View.SimpleView{model;entriesForTest;splitWidget;previewPanel;preview;cache;dataGrid;refreshThrottler;refreshButton;deleteSelectedButton;entryPathFilter;returnCount;summaryBarElement;loadingPromise;metadataView=new m.StorageMetadataView.StorageMetadataView;constructor(t,i){super(Ht(Nt.cache)),this.registerRequiredCSS(Wt),this.model=t,this.entriesForTest=null,this.element.classList.add("service-worker-cache-data-view"),this.element.classList.add("storage-view"),this.element.setAttribute("jslog",`${p.pane("cache-storage-data")}`);const s=this.element.createChild("devtools-toolbar","data-view-toolbar");s.setAttribute("jslog",`${p.toolbar()}`),this.element.appendChild(this.metadataView),this.splitWidget=new c.SplitWidget.SplitWidget(!1,!1),this.splitWidget.show(this.element),this.previewPanel=new c.Widget.VBox;const r=this.previewPanel.element.createChild("div","cache-preview-panel-resizer");this.splitWidget.setMainWidget(this.previewPanel),this.splitWidget.installResizer(r),this.preview=null,this.cache=i;const a=this.model.target().model(o.StorageBucketsModel.StorageBucketsModel)?.getBucketByName(i.storageBucket.storageKey,i.storageBucket.name);a?this.metadataView.setStorageBucket(a):i.storageKey&&this.metadataView.setStorageKey(i.storageKey),this.dataGrid=null,this.refreshThrottler=new e.Throttler.Throttler(300),this.refreshButton=new c.Toolbar.ToolbarButton(Ht(Nt.refresh),"refresh",void 0,"cache-storage.refresh"),this.refreshButton.addEventListener("Click",this.refreshButtonClicked,this),s.appendToolbarItem(this.refreshButton),this.deleteSelectedButton=new c.Toolbar.ToolbarButton(Ht(Nt.deleteSelected),"cross",void 0,"cache-storage.delete-selected"),this.deleteSelectedButton.addEventListener("Click",(e=>{this.deleteButtonClicked(null)})),s.appendToolbarItem(this.deleteSelectedButton);const n=new c.Toolbar.ToolbarFilter(Ht(Nt.filterByPath),1);s.appendToolbarItem(n);const d=new e.Throttler.Throttler(300);this.entryPathFilter="",n.addEventListener("TextChanged",(()=>{d.schedule((()=>(this.entryPathFilter=n.value(),this.updateData(!0))))})),this.returnCount=null,this.summaryBarElement=null,this.loadingPromise=null,this.update(i)}resetDataGrid(){this.dataGrid&&this.dataGrid.asWidget().detach(),this.dataGrid=this.createDataGrid();const e=this.dataGrid.asWidget();this.splitWidget.setSidebarWidget(e),e.setMinimumSize(0,250)}wasShown(){this.model.addEventListener("CacheStorageContentUpdated",this.cacheContentUpdated,this),this.updateData(!0)}willHide(){this.model.removeEventListener("CacheStorageContentUpdated",this.cacheContentUpdated,this)}showPreview(e){e&&this.preview===e||(this.preview&&this.preview.detach(),e||(e=new c.EmptyWidget.EmptyWidget(Ht(Nt.noCacheEntrySelected),Ht(Nt.selectACacheEntryAboveToPreview))),this.preview=e,this.preview.show(this.previewPanel.element))}createDataGrid(){const e=[{id:"number",title:"#",sortable:!1,width:"3px"},{id:"name",title:Ht(Nt.name),weight:4,sortable:!0},{id:"response-type",title:i.i18n.lockedString("Response-Type"),weight:1,align:"right",sortable:!0},{id:"content-type",title:i.i18n.lockedString("Content-Type"),weight:1,sortable:!0},{id:"content-length",title:i.i18n.lockedString("Content-Length"),weight:1,align:"right",sortable:!0},{id:"response-time",title:Ht(Nt.timeCached),width:"12em",weight:1,align:"right",sortable:!0},{id:"vary-header",title:i.i18n.lockedString("Vary Header"),weight:1,sortable:!0}],t=new S.DataGrid.DataGridImpl({displayName:Ht(Nt.serviceWorkerCache),columns:e,deleteCallback:this.deleteButtonClicked.bind(this),refreshCallback:this.updateData.bind(this,!0)});return t.addEventListener("SortingChanged",this.sortingChanged,this),t.addEventListener("SelectedNode",(e=>{this.previewCachedResponse(e.data.data)}),this),t.setStriped(!0),t}sortingChanged(){if(!this.dataGrid)return;const e=this.dataGrid,t=e.isSortOrderAscending(),i=e.sortColumnId();let s;"name"===i?s=(e,t)=>e.name.localeCompare(t.name):"content-type"===i?s=(e,t)=>e.data.mimeType.localeCompare(t.data.mimeType):"content-length"===i?s=(e,t)=>e.data.resourceSize-t.data.resourceSize:"response-time"===i?s=(e,t)=>e.data.endTime-t.data.endTime:"response-type"===i?s=(e,t)=>e.responseType.localeCompare(t.responseType):"vary-header"===i&&(s=(e,t)=>e.varyHeader.localeCompare(t.varyHeader));const r=e.rootNode().children.slice();e.rootNode().removeChildren(),r.sort(((e,i)=>{const r=s(e,i);return t?r:-r})),r.forEach((t=>e.rootNode().appendChild(t)))}async deleteButtonClicked(e){(e||(e=this.dataGrid?.selectedNode??null))&&(await this.model.deleteCacheEntry(this.cache,e.data.url()),e.remove())}update(e=null){e&&(this.cache=e,this.resetDataGrid(),this.updateData(!0))}updateSummaryBar(){this.summaryBarElement||(this.summaryBarElement=this.element.createChild("div","cache-storage-summary-bar")),this.summaryBarElement.removeChildren();const e=this.summaryBarElement.createChild("span");this.entryPathFilter?e.textContent=Ht(Nt.matchingEntriesS,{PH1:String(this.returnCount)}):e.textContent=Ht(Nt.totalEntriesS,{PH1:String(this.returnCount)})}updateDataCallback(e,t,i){if(!this.dataGrid)return;const s=this.dataGrid.selectedNode?.data.url();this.refreshButton.setEnabled(!0),this.entriesForTest=t,this.returnCount=i,this.updateSummaryBar();const r=new Map,o=this.dataGrid.rootNode();for(const e of o.children)r.set(e.data.url,e);o.removeChildren();let a=null;for(let e=0;e<t.length;++e){const i=t[e];let n=r.get(i.requestURL);n&&n.data.responseTime===i.responseTime?n.data.number=e:(n=new qt(e,this.createRequest(i),i.responseType),n.selectable=!0),o.appendChild(n),i.requestURL===s&&(a=n)}a?a.revealAndSelect():this.showPreview(null),this.updatedForTest()}async updateData(e){if(!e&&this.loadingPromise)return await this.loadingPromise;if(this.refreshButton.setEnabled(!1),this.loadingPromise)return await this.loadingPromise;this.loadingPromise=new Promise((e=>{this.model.loadAllCacheData(this.cache,this.entryPathFilter,((t,i)=>{e({entries:t,returnCount:i})}))}));const{entries:t,returnCount:i}=await this.loadingPromise;this.updateDataCallback(0,t,i),this.loadingPromise=null}refreshButtonClicked(){this.updateData(!0)}cacheContentUpdated(e){const{cacheName:t,storageBucket:i}=e.data;this.cache.inBucket(i)&&this.cache.cacheName===t&&this.refreshThrottler.schedule((()=>Promise.resolve(this.updateData(!0))),"AsSoonAsPossible")}async previewCachedResponse(e){let t=Kt.get(e);t||(t=new zt(e),Kt.set(e,t)),this.dataGrid?.selectedNode&&e===this.dataGrid.selectedNode.data&&this.showPreview(t)}createRequest(t){const i=o.NetworkRequest.NetworkRequest.createWithoutBackendRequest("cache-storage-"+t.requestURL,t.requestURL,s.DevToolsPath.EmptyUrlString,null);i.requestMethod=t.requestMethod,i.setRequestHeaders(t.requestHeaders),i.statusCode=t.responseStatus,i.statusText=t.responseStatusText,i.protocol=new e.ParsedURL.ParsedURL(t.requestURL).scheme,i.responseHeaders=t.responseHeaders,i.setRequestHeadersText(""),i.endTime=t.responseTime;let r=t.responseHeaders.find((e=>"content-type"===e.name.toLowerCase())),a="text/plain";if(r){const e=s.MimeType.parseContentType(r.value);e.mimeType&&(a=e.mimeType)}i.mimeType=a,r=t.responseHeaders.find((e=>"content-length"===e.name.toLowerCase())),i.resourceSize=r&&Number(r.value)||0;let n=e.ResourceType.ResourceType.fromMimeType(a);return n||(n=e.ResourceType.ResourceType.fromURL(t.requestURL)||e.ResourceType.resourceTypes.Other),i.setResourceType(n),i.setContentDataProvider(this.requestContent.bind(this,i)),i}async requestContent(e){const t=await this.cache.requestCachedResponse(e.url(),e.requestHeaders());return t?new x.ContentData.ContentData(t.body,!0,e.mimeType,e.charset()??void 0):{error:"No cached response found"}}updatedForTest(){}}const Kt=new WeakMap;class qt extends S.DataGrid.DataGridNode{number;name;request;responseType;varyHeader;constructor(t,i,r){super(i),this.number=t;const o=new e.ParsedURL.ParsedURL(i.url());o.isValid?this.name=s.StringUtilities.trimURL(i.url(),o.domain()):this.name=i.url(),this.request=i,this.responseType=r,this.varyHeader=i.responseHeaders.find((e=>"vary"===e.name.toLowerCase()))?.value||""}createCell(e){const t=this.createTD(e);let i,s=this.request.url();"number"===e?i=String(this.number):"name"===e?i=this.name:"response-type"===e?i="opaqueResponse"===this.responseType?"opaque":"opaqueRedirect"===this.responseType?"opaqueredirect":this.responseType:"content-type"===e?i=this.request.mimeType:"content-length"===e?i=(0|this.request.resourceSize).toLocaleString("en-US"):"response-time"===e?i=new Date(1e3*this.request.endTime).toLocaleString():"vary-header"===e&&(i=this.varyHeader,this.varyHeader&&(s=Ht(Nt.varyHeaderWarning)));const r=t.parentElement;let o;return r&&this.dataGrid&&(o=this.dataGrid.elementToDataGridNode.get(r)),S.DataGrid.DataGridImpl.setElementText(t,i||"",!0,o),c.Tooltip.Tooltip.install(t,s),t}}class zt extends c.Widget.VBox{tabbedPane;resourceViewTabSetting;constructor(t){super(),this.tabbedPane=new c.TabbedPane.TabbedPane,this.tabbedPane.element.setAttribute("jslog",`${p.section("network-item-preview")}`),this.tabbedPane.addEventListener(c.TabbedPane.Events.TabSelected,this.tabSelected,this),this.resourceViewTabSetting=e.Settings.Settings.instance().createSetting("cache-storage-view-tab","preview"),this.tabbedPane.appendTab("headers",Ht(Nt.headers),d.LegacyWrapper.legacyWrapper(c.Widget.VBox,new E.RequestHeadersView.RequestHeadersView(t))),this.tabbedPane.appendTab("preview",Ht(Nt.preview),new M.RequestPreviewView.RequestPreviewView(t)),this.tabbedPane.show(this.element)}wasShown(){super.wasShown(),this.selectTab()}selectTab(e){e||(e=this.resourceViewTabSetting.get()),e&&!this.tabbedPane.selectTab(e)&&this.tabbedPane.selectTab("headers")}tabSelected(e){e.data.isUserGesture&&this.resourceViewTabSetting.set(e.data.tabId)}}var Gt=Object.freeze({__proto__:null,DataGridNode:qt,RequestView:zt,ServiceWorkerCacheView:_t});const $t={cacheStorage:"Cache storage",noCacheStorage:"No cache storage detected",cacheStorageDescription:"On this page you can view and delete cache data.",refreshCaches:"Refresh Caches",delete:"Delete"},Xt=i.i18n.registerUIStrings("panels/application/ServiceWorkerCacheTreeElement.ts",$t),Jt=i.i18n.getLocalizedString.bind(void 0,Xt);class Qt extends O{swCacheModels;swCacheTreeElements;storageBucket;constructor(e,t){super(e,Jt($t.cacheStorage),Jt($t.noCacheStorage),Jt($t.cacheStorageDescription),"cache-storage");const i=n.Icon.create("database");this.setLink("https://developer.chrome.com/docs/devtools/storage/cache/?utm_source=devtools"),this.setLeadingIcons([i]),this.swCacheModels=new Set,this.swCacheTreeElements=new Set,this.storageBucket=t}initialize(){this.swCacheModels.clear(),this.swCacheTreeElements.clear(),o.TargetManager.TargetManager.instance().observeModels(o.ServiceWorkerCacheModel.ServiceWorkerCacheModel,{modelAdded:e=>this.serviceWorkerCacheModelAdded(e),modelRemoved:e=>this.serviceWorkerCacheModelRemoved(e)})}onattach(){super.onattach(),this.listItemElement.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this),!0)}handleContextMenuEvent(e){const t=new c.ContextMenu.ContextMenu(e);t.defaultSection().appendItem(Jt($t.refreshCaches),this.refreshCaches.bind(this),{jslogContext:"refresh-caches"}),t.show()}refreshCaches(){for(const e of this.swCacheModels)e.refreshCacheNames()}serviceWorkerCacheModelAdded(e){e.enable(),this.swCacheModels.add(e);for(const t of e.caches())this.addCache(e,t);e.addEventListener("CacheAdded",this.cacheAdded,this),e.addEventListener("CacheRemoved",this.cacheRemoved,this)}serviceWorkerCacheModelRemoved(e){for(const t of e.caches())this.removeCache(e,t);e.removeEventListener("CacheAdded",this.cacheAdded,this),e.removeEventListener("CacheRemoved",this.cacheRemoved,this),this.swCacheModels.delete(e)}cacheAdded(e){const{model:t,cache:i}=e.data;this.addCache(t,i)}cacheInTree(e){return!this.storageBucket||e.inBucket(this.storageBucket)}addCache(e,t){if(this.cacheInTree(t)){const i=new Yt(this.resourcesPanel,e,t,void 0===this.storageBucket);this.swCacheTreeElements.add(i),this.appendChild(i)}}cacheRemoved(e){const{model:t,cache:i}=e.data;this.cacheInTree(i)&&this.removeCache(t,i)}removeCache(e,t){const i=this.cacheTreeElement(e,t);i&&(this.removeChild(i),this.swCacheTreeElements.delete(i),this.setExpandable(this.childCount()>0))}cacheTreeElement(e,t){for(const i of this.swCacheTreeElements)if(i.hasModelAndCache(e,t))return i;return null}}class Yt extends F{model;cache;view;constructor(e,t,i,s){let r;r=s?i.cacheName+" - "+i.storageKey:i.cacheName,super(e,r,!1,"cache-storage-instance"),this.model=t,this.cache=i,this.view=null;const o=n.Icon.create("table");this.setLeadingIcons([o])}get itemURL(){return"cache://"+this.cache.cacheId}onattach(){super.onattach(),this.listItemElement.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this),!0)}handleContextMenuEvent(e){const t=new c.ContextMenu.ContextMenu(e);t.defaultSection().appendItem(Jt($t.delete),this.clearCache.bind(this),{jslogContext:"delete"}),t.show()}clearCache(){this.model.deleteCache(this.cache)}update(e){this.cache=e,this.view&&this.view.update(e)}onselect(e){return super.onselect(e),this.view||(this.view=new _t(this.model,this.cache)),this.showView(this.view),t.userMetrics.panelShown("service-worker-cache"),!1}hasModelAndCache(e,t){return this.cache.equals(t)&&this.model===e}}var Zt={cssText:`.service-worker-version{display:flex;align-items:center;flex-wrap:wrap;devtools-button{margin-left:var(--sys-size-3)}}.service-worker-version-stack{position:relative}.service-worker-version-stack-bar{position:absolute;top:10px;bottom:20px;left:4px;content:"";border-left:1px solid var(--sys-color-divider);z-index:0}.service-worker-version:not(:last-child){margin-bottom:7px}.service-worker-version-string{flex-shrink:0}.service-worker-active-circle,\n.service-worker-redundant-circle,\n.service-worker-waiting-circle,\n.service-worker-installing-circle{position:relative;display:inline-block;width:10px;height:10px;z-index:10;margin-right:5px;border-radius:50%;border:1px solid var(--sys-color-token-subtle);align-self:center;flex-shrink:0}.service-worker-active-circle{background-color:var(--sys-color-green-bright)}.service-worker-waiting-circle{background-color:var(--sys-color-yellow-bright)}.service-worker-installing-circle{background-color:var(--sys-color-cdt-base-container)}.service-worker-redundant-circle{background-color:var(--sys-color-neutral-bright)}.service-worker-subtitle{padding-left:14px;line-height:14px;color:var(--sys-color-state-disabled)}.link{margin-left:7px}.service-worker-editor-with-button{align-items:baseline;display:flex}.service-worker-notification-editor{border:1px solid var(--sys-color-divider);display:flex;flex:auto;margin-right:4px;max-width:400px;min-width:80px}.report-field-value{white-space:normal}.report-field-value-filename,\n.service-worker-client-string{max-width:400px;overflow:hidden;text-overflow:ellipsis}.report-field-value-filename{display:contents}.report-field-value-subtitle{overflow:hidden;text-overflow:ellipsis}.service-worker-client{display:flex}.service-worker-client-focus-link{flex:none;margin-left:2px;align-self:center}.service-worker-notification-editor.source-code{padding:4px}.service-worker-list{background-color:var(--sys-color-cdt-base-container);overflow:auto}.service-workers-this-origin{flex-shrink:0;flex-grow:0}.devtools-link{line-height:14px;align-self:center;padding:1px}button.link{padding:1px}button.link:focus-visible{background-color:inherit}\n/*# sourceURL=${import.meta.resolve("./serviceWorkersView.css")} */\n`},ei={cssText:`.resource-service-worker-update-view{display:block;margin:6px;color:var(--sys-color-on-surface-subtle);overflow:auto}.service-worker-update-timing-table{border:1px solid var(--sys-color-divider);border-spacing:0;padding-left:10px;padding-right:10px;line-height:initial;table-layout:auto;overflow:hidden}.service-worker-update-timing-row{position:relative;height:20px;overflow:hidden;min-width:80px}.service-worker-update-timing-bar{position:absolute;min-width:1px;top:0;bottom:0}.service-worker-update-timing-bar-clickable::before{user-select:none;mask-image:var(--image-file-triangle-right);float:left;width:14px;height:14px;margin-right:2px;content:"";position:relative;background-color:var(--icon-default);transition:transform 200ms}.service-worker-update-timing-bar-clickable{position:relative;left:-12px}.service-worker-update-timing-bar-clickable:focus-visible{background-color:var(--sys-color-state-focus-highlight)}.service-worker-update-timing-bar-clickable[aria-checked="true"]::before{transform:rotate(90deg)}.service-worker-update-timing-bar-details-collapsed{display:none}.service-worker-update-timing-bar-details-expanded{display:table-row}.service-worker-update-timing-bar-details:focus-visible{background-color:var(--sys-color-state-focus-highlight)}.service-worker-update-timing-bar.activate{top:5px;height:10px;background-color:var(--sys-color-yellow-bright)}.service-worker-update-timing-bar.wait{top:5px;height:10px;background-color:var(--sys-color-purple-bright)}.service-worker-update-timing-bar.install{top:5px;height:10px;background-color:var(--sys-color-cyan-bright)}.service-worker-update-timing-table > tr > td{padding:4px 0;padding-right:10px}table.service-worker-update-timing-table > tr.service-worker-update-timing-table-header > td{border-top:5px solid transparent;color:var(--sys-color-token-subtle)}table.service-worker-update-timing-table > tr.service-worker-update-timing-bar-details > td:first-child{padding-left:12px}table.service-worker-update-timing-table > tr.service-worker-update-timeline > td:first-child{padding-left:12px}\n/*# sourceURL=${import.meta.resolve("./serviceWorkerUpdateCycleView.css")} */\n`};const ti={version:"Version",updateActivity:"Update Activity",timeline:"Timeline",startTimeS:"Start time: {PH1}",endTimeS:"End time: {PH1}"},ii=i.i18n.registerUIStrings("panels/application/ServiceWorkerUpdateCycleView.ts",ti),si=i.i18n.getLocalizedString.bind(void 0,ii);class ri{registration;rows;selectedRowIndex;tableElement;constructor(e){this.registration=e,this.rows=[],this.selectedRowIndex=-1,this.tableElement=document.createElement("table"),this.createTimingTable()}calculateServiceWorkerUpdateRanges(){function e(e,t){t.start<Number.MAX_VALUE&&t.start<=t.end&&e.push(t)}function t(t){let i=t.currentState,s=0,r=0,o=0,a=0;const n=i.status;if("new"===n)return[];for(;i;)"activated"===i.status?s=i.lastUpdatedTimestamp:"activating"===i.status?(0===s&&(s=i.lastUpdatedTimestamp),r=i.lastUpdatedTimestamp):"installed"===i.status?o=i.lastUpdatedTimestamp:"installing"===i.status&&(0===o&&(o=i.lastUpdatedTimestamp),a=i.lastUpdatedTimestamp),i=i.previousState;const d=[];return function(t,i,s,r,o,a,n){e(t,{id:i,phase:"Install",start:s,end:r}),"activating"!==n&&"activated"!==n&&"redundant"!==n||(e(t,{id:i,phase:"Wait",start:r,end:o}),e(t,{id:i,phase:"Activate",start:o,end:a}))}(d,t.id,a,o,r,s,n),d}const i=this.registration.versionsByMode(),s=["active","waiting","installing","redundant"];for(const e of s){const s=i.get(e);if(s){return t(s)}}return[]}createTimingTable(){this.tableElement.classList.add("service-worker-update-timing-table"),this.tableElement.setAttribute("jslog",`${p.tree("update-timing-table")}`);const e=this.calculateServiceWorkerUpdateRanges();this.updateTimingTable(e)}createTimingTableHead(){const e=this.tableElement.createChild("tr","service-worker-update-timing-table-header");c.UIUtils.createTextChild(e.createChild("td"),si(ti.version)),c.UIUtils.createTextChild(e.createChild("td"),si(ti.updateActivity)),c.UIUtils.createTextChild(e.createChild("td"),si(ti.timeline))}removeRows(){const e=this.tableElement.getElementsByTagName("tr");for(;e[0];)e[0].parentNode&&e[0].parentNode.removeChild(e[0]);this.rows=[]}updateTimingTable(e){this.selectedRowIndex=-1,this.removeRows(),this.createTimingTableHead();const t=e;if(0===t.length)return;const i=t.map((e=>e.start)),s=t.map((e=>e.end)),r=i.reduce(((e,t)=>Math.min(e,t))),o=s.reduce(((e,t)=>Math.max(e,t))),a=100/(o-r);for(const e of t){const t=e.phase,i=a*(e.start-r),s=a*(o-e.end),n=this.tableElement.createChild("tr","service-worker-update-timeline");n.setAttribute("jslog",`${p.treeItem("update-timeline").track({click:!0,keydown:"ArrowLeft|ArrowRight|ArrowUp|ArrowDown|Enter|Space"})}`),this.rows.push(n);const d=n.createChild("td");c.UIUtils.createTextChild(d,"#"+e.id),d.classList.add("service-worker-update-timing-bar-clickable"),d.setAttribute("tabindex","0"),d.setAttribute("role","switch"),d.addEventListener("focus",(e=>{this.onFocus(e)})),d.setAttribute("jslog",`${p.expand("timing-info").track({click:!0})}`),c.ARIAUtils.setChecked(d,!1);const l=n.createChild("td");c.UIUtils.createTextChild(l,t);const h=n.createChild("td").createChild("div","service-worker-update-timing-row").createChild("span","service-worker-update-timing-bar "+t.toLowerCase());h.style.left=i+"%",h.style.right=s+"%",h.textContent="​",this.constructUpdateDetails(n,e)}}constructUpdateDetails(e,t){const i=this.tableElement.createChild("tr","service-worker-update-timing-bar-details");i.classList.add("service-worker-update-timing-bar-details-collapsed");const s=i.createChild("td");s.colSpan=3;const r=new Date(t.start).toISOString();c.UIUtils.createTextChild(s.createChild("span"),si(ti.startTimeS,{PH1:r})),i.tabIndex=0;const o=this.tableElement.createChild("tr","service-worker-update-timing-bar-details");o.classList.add("service-worker-update-timing-bar-details-collapsed");const a=o.createChild("td");a.colSpan=3;const n=new Date(t.end).toISOString();c.UIUtils.createTextChild(a.createChild("span"),si(ti.endTimeS,{PH1:n})),o.tabIndex=0,e.addEventListener("keydown",(e=>{this.onKeydown(e,i,o)})),e.addEventListener("click",(e=>{this.onClick(e,i,o)}))}toggle(e,t,i,s){i.classList.contains("service-worker-update-timing-bar-clickable")&&(e.classList.toggle("service-worker-update-timing-bar-details-collapsed"),e.classList.toggle("service-worker-update-timing-bar-details-expanded"),t.classList.toggle("service-worker-update-timing-bar-details-collapsed"),t.classList.toggle("service-worker-update-timing-bar-details-expanded"),c.ARIAUtils.setChecked(i,!s))}onFocus(e){const t=e.target;if(!t)return;const i=t.parentElement;i&&(this.selectedRowIndex=this.rows.indexOf(i))}onKeydown(e,t,i){if(!e.target)return;const s=e.target,r=e,o="true"===s.getAttribute("aria-checked");return"Enter"===r.key||" "===r.key||!o&&"ArrowRight"===r.key||o&&"ArrowLeft"===r.key?(this.toggle(t,i,s,o),void e.preventDefault()):("ArrowDown"===r.key&&(this.selectedRowIndex>=0?this.selectNextRow():this.selectFirstRow(),e.preventDefault()),void("ArrowUp"===r.key&&(this.selectedRowIndex>=0?this.selectPreviousRow():this.selectLastRow(),e.preventDefault())))}focusRow(e){e.cells[0].focus()}blurRow(e){e.cells[0].blur()}selectFirstRow(){0!==this.rows.length&&(this.selectedRowIndex=0,this.focusRow(this.rows[0]))}selectLastRow(){0!==this.rows.length&&(this.selectedRowIndex=this.rows.length-1,this.focusRow(this.rows[this.selectedRowIndex]))}selectNextRow(){if(0===this.rows.length)return;const e=this.selectedRowIndex;this.selectedRowIndex++,this.selectedRowIndex>=this.rows.length&&(this.selectedRowIndex=0),this.blurRow(this.rows[e]),this.focusRow(this.rows[this.selectedRowIndex])}selectPreviousRow(){if(0===this.rows.length)return;const e=this.selectedRowIndex;this.selectedRowIndex--,this.selectedRowIndex<0&&(this.selectedRowIndex=this.rows.length-1),this.blurRow(this.rows[e]),this.focusRow(this.rows[this.selectedRowIndex])}onClick(e,t,i){const s=e.target;if(!s)return;const r="true"===s.getAttribute("aria-checked");this.toggle(t,i,s,r),e.preventDefault()}refresh(){const e=this.calculateServiceWorkerUpdateRanges();this.updateTimingTable(e)}}var oi=Object.freeze({__proto__:null,ServiceWorkerUpdateCycleView:ri});const ai={serviceWorkersFromOtherOrigins:"Service workers from other origins",updateOnReload:"Update on reload",onPageReloadForceTheService:"On page reload, force the `service worker` to update, and activate it",bypassForNetwork:"Bypass for network",bypassTheServiceWorkerAndLoad:"Bypass the `service worker` and load resources from the network",serviceWorkerForS:"`Service worker` for {PH1}",testPushMessageFromDevtools:"Test push message from DevTools.",networkRequests:"Network requests",update:"Update",unregisterServiceWorker:"Unregister service worker",unregister:"Unregister",source:"Source",status:"Status",clients:"Clients",pushString:"Push",pushData:"Push data",syncString:"Sync",syncTag:"Sync tag",periodicSync:"Periodic sync",periodicSyncTag:"Periodic sync tag",sRegistrationErrors:"{PH1} registration errors",receivedS:"Received {PH1}",routers:"Routers",sDeleted:"{PH1} - deleted",sActivatedAndIsS:"#{PH1} activated and is {PH2}",stopString:"Stop",inspect:"Inspect",startString:"Start",sIsRedundant:"#{PH1} is redundant",sWaitingToActivate:"#{PH1} waiting to activate",sTryingToInstall:"#{PH1} trying to install",updateCycle:"Update Cycle",workerS:"Worker: {PH1}",focus:"focus",seeAllRegistrations:"See all registrations"},ni=i.i18n.registerUIStrings("panels/application/ServiceWorkersView.ts",ai),di=i.i18n.getLocalizedString.bind(void 0,ni);let li=!1;class ci extends c.Widget.VBox{currentWorkersView;toolbar;sections;manager;securityOriginManager;sectionToRegistration;eventListeners;constructor(){super(!0),this.registerRequiredCSS(Zt),this.currentWorkersView=new c.ReportView.ReportView(i.i18n.lockedString("Service workers")),this.currentWorkersView.setBodyScrollable(!1),this.contentElement.classList.add("service-worker-list"),this.contentElement.setAttribute("jslog",`${p.pane("service-workers")}`),this.currentWorkersView.show(this.contentElement),this.currentWorkersView.element.classList.add("service-workers-this-origin"),this.currentWorkersView.element.setAttribute("jslog",`${p.section("this-origin")}`),this.toolbar=this.currentWorkersView.createToolbar(),this.sections=new Map,this.manager=null,this.securityOriginManager=null,this.sectionToRegistration=new WeakMap;const s=this.contentElement.createChild("div","service-workers-other-origin");s.setAttribute("jslog",`${p.section("other-origin")}`);const r=new c.ReportView.ReportView;r.setHeaderVisible(!1),r.show(s);const a=r.appendSection(di(ai.serviceWorkersFromOtherOrigins)).appendRow(),n=c.Fragment.html`<a class="devtools-link" role="link" tabindex="0" href="chrome://serviceworker-internals" target="_blank" style="display: inline; cursor: pointer;">${di(ai.seeAllRegistrations)}</a>`;n.setAttribute("jslog",`${p.link("view-all").track({click:!0})}`),self.onInvokeElement(n,(e=>{const t=o.TargetManager.TargetManager.instance().rootTarget();t&&t.targetAgent().invoke_createTarget({url:"chrome://serviceworker-internals?devtools"}),e.consume(!0)})),a.appendChild(n),this.toolbar.appendToolbarItem(A.ThrottlingManager.throttlingManager().createOfflineToolbarCheckbox());const d=e.Settings.Settings.instance().createSetting("service-worker-update-on-reload",!1);d.setTitle(di(ai.updateOnReload));const l=new c.Toolbar.ToolbarSettingCheckbox(d,di(ai.onPageReloadForceTheService));this.toolbar.appendToolbarItem(l);const h=e.Settings.Settings.instance().createSetting("bypass-service-worker",!1);h.setTitle(di(ai.bypassForNetwork));const g=new c.Toolbar.ToolbarSettingCheckbox(h,di(ai.bypassTheServiceWorkerAndLoad));this.toolbar.appendToolbarItem(g),this.eventListeners=new Map,o.TargetManager.TargetManager.instance().observeModels(o.ServiceWorkerManager.ServiceWorkerManager,this),this.updateListVisibility();document.body.addEventListener("drawerchange",(i=>{const s=i.detail?.isDrawerOpen;if(this.manager&&!s){const{serviceWorkerNetworkRequestsPanelStatus:{isOpen:i,openedAt:s}}=this.manager;if(i){const i=c.ViewManager.ViewManager.instance().locationNameForViewId("network");c.ViewManager.ViewManager.instance().showViewInLocation("network",i,!1),e.Revealer.reveal(L.UIFilter.UIRequestFilter.filters([]));Date.now()-s<2e3&&t.userMetrics.actionTaken(t.UserMetrics.Action.ServiceWorkerNetworkRequestClosedQuickly),this.manager.serviceWorkerNetworkRequestsPanelStatus={isOpen:!1,openedAt:0}}}}))}modelAdded(e){if(e.target()===o.TargetManager.TargetManager.instance().primaryPageTarget()){this.manager=e,this.securityOriginManager=e.target().model(o.SecurityOriginManager.SecurityOriginManager);for(const e of this.manager.registrations().values())this.updateRegistration(e);this.eventListeners.set(e,[this.manager.addEventListener("RegistrationUpdated",this.registrationUpdated,this),this.manager.addEventListener("RegistrationDeleted",this.registrationDeleted,this),this.securityOriginManager.addEventListener(o.SecurityOriginManager.Events.SecurityOriginAdded,this.updateSectionVisibility,this),this.securityOriginManager.addEventListener(o.SecurityOriginManager.Events.SecurityOriginRemoved,this.updateSectionVisibility,this)])}}modelRemoved(t){this.manager&&this.manager===t&&(e.EventTarget.removeEventListeners(this.eventListeners.get(t)||[]),this.eventListeners.delete(t),this.manager=null,this.securityOriginManager=null)}getTimeStamp(e){const t=e.versionsByMode();let i=0;const s=t.get("active"),r=t.get("installing"),o=t.get("waiting"),a=t.get("redundant");return s?i=s.scriptResponseTime:o?i=o.scriptResponseTime:r?i=r.scriptResponseTime:a&&(i=a.scriptResponseTime),i||0}updateSectionVisibility(){let e=!1;const t=[];for(const i of this.sections.values()){const s=this.getReportViewForOrigin(i.registration.securityOrigin);e=e||s===this.currentWorkersView,i.section.parentWidget()!==s&&t.push(i)}for(const e of t){const t=e.registration;this.removeRegistrationFromList(t),this.updateRegistration(t,!0)}this.currentWorkersView.sortSections(((e,t)=>{const i=this.sectionToRegistration.get(e),s=this.sectionToRegistration.get(t),r=i?this.getTimeStamp(i):0;return(s?this.getTimeStamp(s):0)-r}));for(const e of this.sections.values())e.section.parentWidget()===this.currentWorkersView||this.isRegistrationVisible(e.registration)?e.section.showWidget():e.section.hideWidget();this.contentElement.classList.toggle("service-worker-has-current",Boolean(e)),this.updateListVisibility()}registrationUpdated(e){this.updateRegistration(e.data),this.gcRegistrations()}gcRegistrations(){if(!this.manager||!this.securityOriginManager)return;let e=!1;const t=new Set(this.securityOriginManager.securityOrigins());for(const i of this.manager.registrations().values())if((t.has(i.securityOrigin)||this.isRegistrationVisible(i))&&!i.canBeRemoved()){e=!0;break}if(e)for(const e of this.manager.registrations().values()){!(t.has(e.securityOrigin)||this.isRegistrationVisible(e))&&e.canBeRemoved()&&this.removeRegistrationFromList(e)}}getReportViewForOrigin(e){return this.securityOriginManager&&(this.securityOriginManager.securityOrigins().includes(e)||this.securityOriginManager.unreachableMainSecurityOrigin()===e)?this.currentWorkersView:null}updateRegistration(e,t){let i=this.sections.get(e);if(!i){const t=e.scopeURL,s=this.getReportViewForOrigin(e.securityOrigin);if(!s)return;const r=s.appendSection(t);r.setUiGroupTitle(di(ai.serviceWorkerForS,{PH1:t})),this.sectionToRegistration.set(r,e),i=new hi(this.manager,r,e),this.sections.set(e,i)}t||(this.updateSectionVisibility(),i.scheduleUpdate())}registrationDeleted(e){this.removeRegistrationFromList(e.data)}removeRegistrationFromList(e){const t=this.sections.get(e);t&&t.section.detach(),this.sections.delete(e),this.updateSectionVisibility()}isRegistrationVisible(e){return!e.scopeURL}updateListVisibility(){this.contentElement.classList.toggle("service-worker-list-empty",0===this.sections.size)}}class hi{manager;section;registration;fingerprint;pushNotificationDataSetting;syncTagNameSetting;periodicSyncTagNameSetting;updateCycleView;routerView;networkRequests;updateButton;deleteButton;sourceField;statusField;clientsField;linkifier;clientInfoCache;throttler;updateCycleField;routerField;constructor(t,i,s){this.manager=t,this.section=i,this.registration=s,this.fingerprint=null,this.pushNotificationDataSetting=e.Settings.Settings.instance().createLocalSetting("push-data",di(ai.testPushMessageFromDevtools)),this.syncTagNameSetting=e.Settings.Settings.instance().createLocalSetting("sync-tag-name","test-tag-from-devtools"),this.periodicSyncTagNameSetting=e.Settings.Settings.instance().createLocalSetting("periodic-sync-tag-name","test-tag-from-devtools"),this.updateCycleView=new ri(s),this.routerView=new m.ServiceWorkerRouterView.ServiceWorkerRouterView,this.networkRequests=new h.Button.Button,this.networkRequests.data={iconName:"bottom-panel-open",variant:"text",title:di(ai.networkRequests),jslogContext:"show-network-requests"},this.networkRequests.textContent=di(ai.networkRequests),this.networkRequests.addEventListener("click",this.networkRequestsClicked.bind(this)),this.section.appendButtonToHeader(this.networkRequests),this.updateButton=c.UIUtils.createTextButton(di(ai.update),this.updateButtonClicked.bind(this),{variant:"text",title:di(ai.update),jslogContext:"update"}),this.section.appendButtonToHeader(this.updateButton),this.deleteButton=c.UIUtils.createTextButton(di(ai.unregister),this.unregisterButtonClicked.bind(this),{variant:"text",title:di(ai.unregisterServiceWorker),jslogContext:"unregister"}),this.section.appendButtonToHeader(this.deleteButton),this.sourceField=this.wrapWidget(this.section.appendField(di(ai.source))),this.statusField=this.wrapWidget(this.section.appendField(di(ai.status))),this.clientsField=this.wrapWidget(this.section.appendField(di(ai.clients))),this.createSyncNotificationField(di(ai.pushString),this.pushNotificationDataSetting.get(),di(ai.pushData),this.push.bind(this),"push-message"),this.createSyncNotificationField(di(ai.syncString),this.syncTagNameSetting.get(),di(ai.syncTag),this.sync.bind(this),"sync-tag"),this.createSyncNotificationField(di(ai.periodicSync),this.periodicSyncTagNameSetting.get(),di(ai.periodicSyncTag),(e=>this.periodicSync(e)),"periodic-sync-tag"),this.createUpdateCycleField(),this.maybeCreateRouterField(),this.linkifier=new u.Linkifier.Linkifier,this.clientInfoCache=new Map,this.throttler=new e.Throttler.Throttler(500)}createSyncNotificationField(e,t,i,s,r){const o=this.wrapWidget(this.section.appendField(e)).createChild("form","service-worker-editor-with-button"),a=c.UIUtils.createInput("source-code service-worker-notification-editor");a.setAttribute("jslog",`${p.textField().track({change:!0}).context(r)}`),o.appendChild(a);const n=c.UIUtils.createTextButton(e,void 0,{jslogContext:r});n.type="submit",o.appendChild(n),a.value=t,a.placeholder=i,c.ARIAUtils.setLabel(a,e),o.addEventListener("submit",(e=>{s(a.value||""),e.consume(!0)}))}scheduleUpdate(){li?this.update():this.throttler.schedule(this.update.bind(this))}targetForVersionId(e){const t=this.manager.findVersion(e);return t?.targetId?o.TargetManager.TargetManager.instance().targetById(t.targetId):null}addVersion(e,t,i){const s=e.createChild("div","service-worker-version");s.createChild("div",t);const r=s.createChild("span","service-worker-version-string");return r.textContent=i,c.ARIAUtils.markAsAlert(r),s}updateClientsField(e){this.clientsField.removeChildren(),this.section.setFieldVisible(di(ai.clients),Boolean(e.controlledClients.length));for(const t of e.controlledClients){const e=this.clientsField.createChild("div","service-worker-client"),i=this.clientInfoCache.get(t);i&&this.updateClientInfo(e,i),this.manager.target().targetAgent().invoke_getTargetInfo({targetId:t}).then(this.onClientInfo.bind(this,e))}}updateSourceField(t){this.sourceField.removeChildren();const i=e.ParsedURL.ParsedURL.extractName(t.scriptURL),s=this.sourceField.createChild("div","report-field-value-filename"),r=u.Linkifier.Linkifier.linkifyURL(t.scriptURL,{text:i});if(r.tabIndex=0,r.setAttribute("jslog",`${p.link("source-location").track({click:!0})}`),s.appendChild(r),this.registration.errors.length){const t=c.UIUtils.createIconLabel({title:String(this.registration.errors.length),iconName:"cross-circle-filled",color:"var(--icon-error)"});t.classList.add("devtools-link","link"),t.tabIndex=0,c.ARIAUtils.setLabel(t,di(ai.sRegistrationErrors,{PH1:this.registration.errors.length})),self.onInvokeElement(t,(()=>e.Console.Console.instance().show())),s.appendChild(t)}void 0!==t.scriptResponseTime&&(this.sourceField.createChild("div","report-field-value-subtitle").textContent=di(ai.receivedS,{PH1:new Date(1e3*t.scriptResponseTime).toLocaleString()}))}update(){const e=this.registration.fingerprint();if(e===this.fingerprint)return Promise.resolve();this.fingerprint=e,this.section.setHeaderButtonsState(this.registration.isDeleted);const t=this.registration.versionsByMode(),s=this.registration.scopeURL,r=this.registration.isDeleted?di(ai.sDeleted,{PH1:s}):s;this.section.setTitle(r);const a=t.get("active"),n=t.get("waiting"),d=t.get("installing"),l=t.get("redundant");this.statusField.removeChildren();const h=this.statusField.createChild("div","service-worker-version-stack");if(h.createChild("div","service-worker-version-stack-bar"),a){this.updateSourceField(a);const e=o.ServiceWorkerManager.ServiceWorkerVersion.RunningStatus[a.currentState.runningStatus](),t=this.addVersion(h,"service-worker-active-circle",di(ai.sActivatedAndIsS,{PH1:a.id,PH2:e}));if(a.isRunning()||a.isStarting()){const e=c.UIUtils.createTextButton(di(ai.stopString),this.stopButtonClicked.bind(this,a.id),{jslogContext:"stop"});if(t.appendChild(e),!this.targetForVersionId(a.id)){const e=c.UIUtils.createTextButton(di(ai.inspect),this.inspectButtonClicked.bind(this,a.id),{jslogContext:"inspect"});t.appendChild(e)}}else if(a.isStartable()){const e=c.UIUtils.createTextButton(di(ai.startString),this.startButtonClicked.bind(this),{jslogContext:"start"});t.appendChild(e)}this.updateClientsField(a),this.maybeCreateRouterField()}else l&&(this.updateSourceField(l),this.addVersion(h,"service-worker-redundant-circle",di(ai.sIsRedundant,{PH1:l.id})),this.updateClientsField(l));if(n){const e=this.addVersion(h,"service-worker-waiting-circle",di(ai.sWaitingToActivate,{PH1:n.id})),t=c.UIUtils.createTextButton(i.i18n.lockedString("skipWaiting"),this.skipButtonClicked.bind(this),{title:i.i18n.lockedString("skipWaiting"),jslogContext:"skip-waiting"});if(e.appendChild(t),void 0!==n.scriptResponseTime&&(e.createChild("div","service-worker-subtitle").textContent=di(ai.receivedS,{PH1:new Date(1e3*n.scriptResponseTime).toLocaleString()})),!this.targetForVersionId(n.id)&&(n.isRunning()||n.isStarting())){const t=c.UIUtils.createTextButton(di(ai.inspect),this.inspectButtonClicked.bind(this,n.id),{title:di(ai.inspect),jslogContext:"waiting-entry-inspect"});e.appendChild(t)}}if(d){const e=this.addVersion(h,"service-worker-installing-circle",di(ai.sTryingToInstall,{PH1:d.id}));if(void 0!==d.scriptResponseTime&&(e.createChild("div","service-worker-subtitle").textContent=di(ai.receivedS,{PH1:new Date(1e3*d.scriptResponseTime).toLocaleString()})),!this.targetForVersionId(d.id)&&(d.isRunning()||d.isStarting())){const t=c.UIUtils.createTextButton(di(ai.inspect),this.inspectButtonClicked.bind(this,d.id),{title:di(ai.inspect),jslogContext:"installing-entry-inspect"});e.appendChild(t)}}return this.updateCycleView.refresh(),Promise.resolve()}unregisterButtonClicked(){this.manager.deleteRegistration(this.registration.id)}createUpdateCycleField(){this.updateCycleField=this.wrapWidget(this.section.appendField(di(ai.updateCycle))),this.updateCycleField.appendChild(this.updateCycleView.tableElement)}maybeCreateRouterField(){const e=this.registration.versionsByMode().get("active"),t=di(ai.routers);e?.routerRules&&e.routerRules.length>0?(this.routerField||(this.routerField=this.wrapWidget(this.section.appendField(t))),this.routerField.lastElementChild||this.routerField.appendChild(this.routerView),this.routerView.update(e.routerRules)):(this.section.removeField(t),this.routerField=void 0)}updateButtonClicked(){this.manager.updateRegistration(this.registration.id)}networkRequestsClicked(){const i="drawer-view"===c.ViewManager.ViewManager.instance().locationNameForViewId("resources")?"panel":"drawer-view";c.ViewManager.ViewManager.instance().showViewInLocation("network",i),e.Revealer.reveal(L.UIFilter.UIRequestFilter.filters([{filterType:L.UIFilter.FilterType.Is,filterValue:"service-worker-intercepted"}]));const s=R.NetworkLog.NetworkLog.instance().requests();let r=null;if(Array.isArray(s))for(const e of s)!r&&e.fetchedViaServiceWorker&&(r=e),e.fetchedViaServiceWorker&&r&&r.responseReceivedTime<e.responseReceivedTime&&(r=e);if(r){const t=L.UIRequestLocation.UIRequestLocation.tab(r,"timing",{clearFilter:!1});e.Revealer.reveal(t)}this.manager.serviceWorkerNetworkRequestsPanelStatus={isOpen:!0,openedAt:Date.now()},t.userMetrics.actionTaken(t.UserMetrics.Action.ServiceWorkerNetworkRequestClicked)}push(e){this.pushNotificationDataSetting.set(e),this.manager.deliverPushMessage(this.registration.id,e)}sync(e){this.syncTagNameSetting.set(e),this.manager.dispatchSyncEvent(this.registration.id,e,!0)}periodicSync(e){this.periodicSyncTagNameSetting.set(e),this.manager.dispatchPeriodicSyncEvent(this.registration.id,e)}onClientInfo(e,t){const i=t.targetInfo;i&&(this.clientInfoCache.set(i.targetId,i),this.updateClientInfo(e,i))}updateClientInfo(e,t){if("page"!==t.type&&"iframe"===t.type){const i=e.createChild("span","service-worker-client-string");return void c.UIUtils.createTextChild(i,di(ai.workerS,{PH1:t.url}))}e.removeChildren();const i=e.createChild("span","service-worker-client-string");c.UIUtils.createTextChild(i,t.url);const s=new h.Button.Button;s.data={iconName:"select-element",variant:"icon",size:"SMALL",title:di(ai.focus),jslogContext:"client-focus"},s.className="service-worker-client-focus-link",s.addEventListener("click",this.activateTarget.bind(this,t.targetId)),e.appendChild(s)}activateTarget(e){this.manager.target().targetAgent().invoke_activateTarget({targetId:e})}startButtonClicked(){this.manager.startWorker(this.registration.scopeURL)}skipButtonClicked(){this.manager.skipWaiting(this.registration.scopeURL)}stopButtonClicked(e){this.manager.stopWorker(e)}inspectButtonClicked(e){this.manager.inspectWorker(e)}wrapWidget(e){const t=c.UIUtils.createShadowRootWithCoreStyles(e,{cssFile:[Zt,ei]}),i=document.createElement("div");return t.appendChild(i),i}}var gi=Object.freeze({__proto__:null,Section:hi,ServiceWorkersView:ci,setThrottleDisabledForDebugging:e=>{li=e}}),ui={cssText:`devtools-shared-storage-access-grid{overflow:auto}\n/*# sourceURL=${import.meta.resolve("./sharedStorageEventsView.css")} */\n`};const pi={noEventSelected:"No shared storage event selected",clickToDisplayBody:"Click on any shared storage event to display the event parameters"},mi=i.i18n.registerUIStrings("panels/application/SharedStorageEventsView.ts",pi),vi=i.i18n.getLocalizedString.bind(void 0,mi);class wi extends c.SplitWidget.SplitWidget{#T=new m.SharedStorageAccessGrid.SharedStorageAccessGrid;#x=[];#E;#M="";constructor(){super(!1,!0),this.element.setAttribute("jslog",`${p.pane("shared-storage-events")}`);const e=new c.Widget.VBox;this.#E=new c.EmptyWidget.EmptyWidget(vi(pi.noEventSelected),vi(pi.clickToDisplayBody)),e.setMinimumSize(0,80),this.setMainWidget(e),this.#E.setMinimumSize(0,40),this.setSidebarWidget(this.#E),this.hideSidebar(),e.contentElement.appendChild(this.#T),this.#T.addEventListener("select",this.#R.bind(this)),this.#T.setAttribute("jslog",`${p.section("events-table")}`),this.#L()?.addEventListener(o.ResourceTreeModel.Events.PrimaryPageChanged,this.clearEvents,this)}#L(){const e=o.TargetManager.TargetManager.instance().primaryPageTarget();return e?.model(o.ResourceTreeModel.ResourceTreeModel)||null}#A(){return this.#L()?.mainFrame||null}get id(){return this.#A()?.id||this.#M}wasShown(){super.wasShown();const e=this.sidebarWidget();e&&e.registerRequiredCSS(ui)}addEvent(e){e.mainFrameId===this.id&&(this.#x.some((t=>{return i=t,s=e,JSON.stringify(i)===JSON.stringify(s);var i,s}))||("Both"!==this.showMode()&&this.showBoth(),this.#x.push(e),this.#T.data=this.#x))}clearEvents(){this.#x=[],this.#T.data=this.#x,this.setSidebarWidget(this.#E),this.hideSidebar()}async#R(e){const t=e.detail;if(!t)return;const i=l.JSONView.JSONView.createViewSync(t);i.setMinimumSize(0,40),this.setSidebarWidget(i)}setDefaultIdForTesting(e){this.#M=e}getEventsForTesting(){return this.#x}getSharedStorageAccessGridForTesting(){return this.#T}}var Si=Object.freeze({__proto__:null,SharedStorageEventsView:wi});const bi={sharedStorage:"Shared storage"},yi=i.i18n.registerUIStrings("panels/application/SharedStorageListTreeElement.ts",bi),ki=i.i18n.getLocalizedString.bind(void 0,yi);class fi extends F{#B;view;constructor(t,i=!1){super(t,ki(bi.sharedStorage),!1,"shared-storage"),this.#B=e.Settings.Settings.instance().createSetting("resources-shared-storage-expanded",i);const s=n.Icon.create("database");this.setLeadingIcons([s]),this.view=new wi}get itemURL(){return"shared-storage://"}onselect(e){return super.onselect(e),this.resourcesPanel.showView(this.view),!1}onattach(){super.onattach(),this.#B.get()&&this.expand()}onexpand(){this.#B.set(!0)}oncollapse(){this.#B.set(!1)}addEvent(e){this.view.addEvent(e)}}var Ii=Object.freeze({__proto__:null,SharedStorageListTreeElement:fi});class Ci extends e.ObjectWrapper.ObjectWrapper{#e;#P;constructor(e,t){super(),this.#e=e,this.#P=t}get securityOrigin(){return this.#P}async getMetadata(){return await this.#e.storageAgent.invoke_getSharedStorageMetadata({ownerOrigin:this.securityOrigin}).then((({metadata:e})=>e))}async getEntries(){return await this.#e.storageAgent.invoke_getSharedStorageEntries({ownerOrigin:this.securityOrigin}).then((({entries:e})=>e))}async setEntry(e,t,i){await this.#e.storageAgent.invoke_setSharedStorageEntry({ownerOrigin:this.securityOrigin,key:e,value:t,ignoreIfPresent:i})}async deleteEntry(e){await this.#e.storageAgent.invoke_deleteSharedStorageEntry({ownerOrigin:this.securityOrigin,key:e})}async clear(){await this.#e.storageAgent.invoke_clearSharedStorageEntries({ownerOrigin:this.securityOrigin})}async resetBudget(){await this.#e.storageAgent.invoke_resetSharedStorageBudget({ownerOrigin:this.securityOrigin})}}class Ti extends o.SDKModel.SDKModel{#D;#F;storageAgent;#a;constructor(e){super(e),e.registerStorageDispatcher(this),this.#D=e.model(o.SecurityOriginManager.SecurityOriginManager),this.#F=new Map,this.storageAgent=e.storageAgent(),this.#a=!1}async enable(){this.#a||(this.#D.addEventListener(o.SecurityOriginManager.Events.SecurityOriginAdded,this.#O,this),this.#D.addEventListener(o.SecurityOriginManager.Events.SecurityOriginRemoved,this.#V,this),await this.storageAgent.invoke_setSharedStorageTracking({enable:!0}),this.#U(),this.#a=!0)}disable(){this.#a&&(this.#D.removeEventListener(o.SecurityOriginManager.Events.SecurityOriginAdded,this.#O,this),this.#D.removeEventListener(o.SecurityOriginManager.Events.SecurityOriginRemoved,this.#V,this),this.storageAgent.invoke_setSharedStorageTracking({enable:!1}),this.#W(),this.#a=!1)}dispose(){this.disable()}#U(){for(const e of this.#D.securityOrigins())this.#N(e)}#W(){for(const e of this.#F.keys())this.#j(e)}#O(e){this.#N(e.data)}#N(t){const i=new e.ParsedURL.ParsedURL(t);if(!i.isValid||"data"===i.scheme||"about"===i.scheme||"javascript"===i.scheme)return;if(this.#F.has(t))return;const s=new Ci(this,t);this.#F.set(t,s),this.dispatchEventToListeners("SharedStorageAdded",s)}#V(e){this.#j(e.data)}#j(e){const t=this.storageForOrigin(e);t&&(this.#F.delete(e),this.dispatchEventToListeners("SharedStorageRemoved",t))}storages(){return this.#F.values()}storageForOrigin(e){return this.#F.get(e)||null}numStoragesForTesting(){return this.#F.size}isChangeEvent(e){return["set","append","delete","clear"].includes(e.method)}sharedStorageAccessed(e){if(this.isChangeEvent(e)){const t=this.storageForOrigin(e.ownerOrigin);if(t){const i={accessTime:e.accessTime,method:e.method,mainFrameId:e.mainFrameId,ownerSite:e.ownerSite,params:e.params,scope:e.scope};t.dispatchEventToListeners("SharedStorageChanged",i)}else this.#N(e.ownerOrigin)}this.dispatchEventToListeners("SharedStorageAccess",e)}attributionReportingTriggerRegistered(e){}indexedDBListUpdated(e){}indexedDBContentUpdated(e){}cacheStorageListUpdated(e){}cacheStorageContentUpdated(e){}interestGroupAccessed(e){}interestGroupAuctionEventOccurred(e){}interestGroupAuctionNetworkRequestCreated(e){}storageBucketCreatedOrUpdated(e){}storageBucketDeleted(e){}attributionReportingSourceRegistered(e){}}o.SDKModel.SDKModel.register(Ti,{capabilities:8192,autostart:!1});var xi=Object.freeze({__proto__:null,SharedStorageForOrigin:Ci,SharedStorageModel:Ti});const Ei={refresh:"Refresh",clearAll:"Clear All",deleteSelected:"Delete Selected",refreshedStatus:"Table refreshed"},Mi=i.i18n.registerUIStrings("panels/application/StorageItemsView.ts",Ei),Ri=i.i18n.getLocalizedString.bind(void 0,Mi);class Li extends c.Widget.VBox{filterRegex;refreshButton;mainToolbar;filterItem;deleteAllButton;deleteSelectedButton;metadataView;constructor(e,t,i){super(!1),this.filterRegex=null,this.refreshButton=this.addButton(Ri(Ei.refresh),"refresh",(()=>{this.refreshItems(),c.ARIAUtils.alert(Ri(Ei.refreshedStatus))})),this.refreshButton.element.setAttribute("jslog",`${p.action("storage-items-view.refresh").track({click:!0})}`),this.mainToolbar=this.element.createChild("devtools-toolbar","top-resources-toolbar"),this.mainToolbar.setAttribute("jslog",`${p.toolbar()}`),this.filterItem=new c.Toolbar.ToolbarFilter(void 0,.4),this.filterItem.addEventListener("TextChanged",this.filterChanged,this);const s=new c.Toolbar.ToolbarSeparator;this.deleteAllButton=this.addButton(Ri(Ei.clearAll),"clear",this.deleteAllItems),this.deleteSelectedButton=this.addButton(Ri(Ei.deleteSelected),"cross",this.deleteSelectedItem),this.deleteSelectedButton.element.setAttribute("jslog",`${p.action("storage-items-view.delete-selected").track({click:!0})}`),this.deleteAllButton.element.id="storage-items-delete-all",this.deleteAllButton.element.setAttribute("jslog",`${p.action("storage-items-view.clear-all").track({click:!0})}`);const r=[this.refreshButton,this.filterItem,s,this.deleteAllButton,this.deleteSelectedButton];for(const e of r)this.mainToolbar.appendToolbarItem(e);this.metadataView=i??new m.StorageMetadataView.StorageMetadataView,this.contentElement.appendChild(this.metadataView)}setDeleteAllTitle(e){this.deleteAllButton.setTitle(e)}setDeleteAllGlyph(e){this.deleteAllButton.setGlyph(e)}appendToolbarItem(e){this.mainToolbar.appendToolbarItem(e)}setStorageKey(e){this.metadataView.setStorageKey(e)}addButton(e,t,i){const s=new c.Toolbar.ToolbarButton(e,t);return s.addEventListener("Click",i,this),s}filterChanged({data:e}){this.filterRegex=e?new RegExp(s.StringUtilities.escapeForRegExp(e),"i"):null,this.refreshItems()}filter(e,t){if(this.filterRegex){const i=this.filterRegex;return e.filter((e=>i.test(t(e))))}return e}hasFilter(){return Boolean(this.filterRegex)}wasShown(){this.refreshItems()}setCanDeleteAll(e){this.deleteAllButton.setEnabled(e)}setCanDeleteSelected(e){this.deleteSelectedButton.setEnabled(e)}setCanFilter(e){this.filterItem.setEnabled(e)}deleteAllItems(){}deleteSelectedItem(){}refreshItems(){}}var Ai=Object.freeze({__proto__:null,StorageItemsView:Li});const{ARIAUtils:Bi}=c,{EmptyWidget:Pi}=c.EmptyWidget,{VBox:Di,widgetConfig:Fi}=c.Widget,{Size:Oi}=c.Geometry,{repeat:Vi}=I,Ui={noPreviewSelected:"No value selected",selectAValueToPreview:"Select a value to preview",numberEntries:"Number of entries shown in table: {PH1}",key:"Key",value:"Value"},Wi=i.i18n.registerUIStrings("panels/application/KeyValueStorageItemsView.ts",Ui),Ni=i.i18n.getLocalizedString.bind(void 0,Wi);class ji extends Li{#H;#_;#K=[];#q=null;#b;#z=!0;#G;constructor(e,t,i,s,r){s||(s=(e,i,s)=>{k(f`
            <devtools-split-view sidebar-position="second" name="${t}-split-view-state">
               <devtools-widget
                  slot="main"
                  .widgetConfig=${Fi(Di,{minimumSize:new Oi(0,50)})}>
                <devtools-data-grid
                  .name=${`${t}-datagrid-with-preview`}
                  striped
                  style="flex: auto"
                  @select=${e.onSelect}
                  @sort=${e.onSort}
                  @refresh=${e.onReferesh}
                  @create=${e.onCreate}
                  @edit=${e.onEdit}
                  @delete=${e.onDelete}
                >
                  <table>
                    <tr>
                      <th id="key" sortable ?editable=${e.editable}>
                        ${Ni(Ui.key)}
                      </th>
                      <th id="value" ?editable=${e.editable}>
                        ${Ni(Ui.value)}
                      </th>
                    </tr>
                    ${Vi(e.items,(e=>e.key),(t=>f`
                      <tr data-key=${t.key} data-value=${t.value}
                          selected=${e.selectedKey===t.key||C}>
                        <td>${t.key}</td>
                        <td>${t.value.substr(0,4096)}</td>
                      </tr>`))}
                      <tr placeholder></tr>
                  </table>
                </devtools-data-grid>
              </devtools-widget>
              <devtools-widget
                  slot="sidebar"
                  .widgetConfig=${Fi(Di,{minimumSize:new Oi(0,50)})}
                  jslog=${p.pane("preview").track({resize:!0})}>
               ${e.preview?.element}
              </devtools-widget>
            </devtools-split-view>`,s,{host:e})}),super(e,t,r),this.#G=i,this.#b=s,this.performUpdate(),this.#H=new Pi(Ni(Ui.noPreviewSelected),Ni(Ui.selectAValueToPreview)),this.#_=null,this.showPreview(null,null)}performUpdate(){const e={items:this.#K,selectedKey:this.#q,editable:this.#G,preview:this.#H,onSelect:e=>{this.setCanDeleteSelected(Boolean(e.detail)),e.detail?this.#$({key:e.detail.dataset.key||"",value:e.detail.dataset.value||""}):this.#$(null)},onSort:e=>{this.#z=e.detail.ascending},onCreate:e=>{this.#X(e.detail.key,e.detail.value)},onEdit:e=>{this.#J(e.detail.node,e.detail.columnId,e.detail.valueBeforeEditing,e.detail.newText)},onDelete:e=>{this.#Q(e.detail.dataset.key||"")},onReferesh:()=>{this.refreshItems()}};this.#b(e,{},this.contentElement)}itemsCleared(){this.#K=[],this.performUpdate(),this.setCanDeleteSelected(!1)}itemRemoved(e){const t=this.#K.findIndex((t=>t.key===e));-1!==t&&(this.#K.splice(t,1),this.performUpdate(),this.setCanDeleteSelected(this.#K.length>1))}itemAdded(e,t){this.#K.some((t=>t.key===e))||(this.#K.push({key:e,value:t}),this.performUpdate())}itemUpdated(e,t){const i=this.#K.find((t=>t.key===e));i&&i.value!==t&&(i.value=t,this.performUpdate(),this.#q===e&&(this.#_!==t&&this.#$({key:e,value:t}),this.setCanDeleteSelected(!0)))}showItems(e){const t=this.#z?1:-1;this.#K=[...e].sort(((e,i)=>t*(e.key>i.key?1:-1)));const i=this.#K.find((e=>e.key===this.#q));i?this.#$(i):this.#q=null,this.performUpdate(),this.setCanDeleteSelected(Boolean(this.#q)),Bi.alert(Ni(Ui.numberEntries,{PH1:this.#K.length}))}deleteSelectedItem(){this.#q&&this.#Q(this.#q)}#X(e,t){this.setItem(e,t),this.#Y(e,t),this.#$({key:e,value:t})}isEditAllowed(e,t,i){return!0}#J(e,t,i,s){this.isEditAllowed(t,i,s)&&("key"===t?("string"==typeof i&&this.removeItem(i),this.setItem(s,e.dataset.value||""),this.#Y(s,e.dataset.value||""),e.dataset.key=s,this.#$({key:s,value:e.dataset.value||""})):(this.setItem(e.dataset.key||"",s),this.#$({key:e.dataset.key||"",value:s})))}#Y(e,t){for(let i=this.#K.length-1;i>=0;--i){const s=this.#K[i];s.key===e&&t!==s.value&&this.#K.splice(i,1)}}#Q(e){this.removeItem(e)}showPreview(e,t){this.#H&&this.#_===t||(this.#H&&this.#H.detach(),e||(e=new Pi(Ni(Ui.noPreviewSelected),Ni(Ui.selectAValueToPreview))),this.#_=t,this.#H=e,this.performUpdate())}async#$(e){if(e?.value){this.#q=e.key;const t=await this.createPreview(e.key,e.value);this.#q===e.key&&this.showPreview(t,e.value)}else this.#q=null,this.showPreview(null,null)}set editable(e){this.#G=e,this.performUpdate()}keys(){return this.#K.map((e=>e.key))}}var Hi=Object.freeze({__proto__:null,KeyValueStorageItemsView:ji});const _i={sharedStorage:"Shared storage",sharedStorageItemsCleared:"Shared Storage items cleared",sharedStorageFilteredItemsCleared:"Shared Storage filtered items cleared",sharedStorageItemDeleted:"The storage item was deleted.",sharedStorageItemEdited:"The storage item was edited.",sharedStorageItemEditCanceled:"The storage item edit was canceled."},Ki=i.i18n.registerUIStrings("panels/application/SharedStorageItemsView.ts",_i),qi=i.i18n.getLocalizedString.bind(void 0,Ki);class zi extends ji{#Z;sharedStorageItemsDispatcher;constructor(t,i){super(qi(_i.sharedStorage),"shared-storage-items-view",!0,i,new m.SharedStorageMetadataView.SharedStorageMetadataView(t,t.securityOrigin)),this.#Z=t,this.performUpdate(),this.#Z.addEventListener("SharedStorageChanged",this.#ee,this),this.sharedStorageItemsDispatcher=new e.ObjectWrapper.ObjectWrapper}static async createView(e,t){const i=new zi(e,t);return await i.updateEntriesOnly(),i}async updateEntriesOnly(){const e=await this.#Z.getEntries();e&&this.#te(e)}async#ee(){await this.refreshItems()}async refreshItems(){await this.metadataView.render(),await this.updateEntriesOnly(),this.sharedStorageItemsDispatcher.dispatchEventToListeners("ItemsRefreshed")}async deleteAllItems(){if(!this.hasFilter())return await this.#Z.clear(),await this.refreshItems(),this.sharedStorageItemsDispatcher.dispatchEventToListeners("ItemsCleared"),void c.ARIAUtils.alert(qi(_i.sharedStorageItemsCleared));await Promise.all(this.keys().map((e=>this.#Z.deleteEntry(e)))),await this.refreshItems(),this.sharedStorageItemsDispatcher.dispatchEventToListeners("FilteredItemsCleared"),c.ARIAUtils.alert(qi(_i.sharedStorageFilteredItemsCleared))}isEditAllowed(e,t,i){return"key"!==e||""!==i||(this.refreshItems().then((()=>{c.ARIAUtils.alert(qi(_i.sharedStorageItemEditCanceled))})),!1)}async setItem(e,t){await this.#Z.setEntry(e,t,!1),await this.refreshItems(),this.sharedStorageItemsDispatcher.dispatchEventToListeners("ItemEdited"),c.ARIAUtils.alert(qi(_i.sharedStorageItemEdited))}#te(e){const t=this.filter(e,(e=>`${e.key} ${e.value}`));this.showItems(t)}async removeItem(e){await this.#Z.deleteEntry(e),await this.refreshItems(),this.sharedStorageItemsDispatcher.dispatchEventToListeners("ItemDeleted",{key:e}),c.ARIAUtils.alert(qi(_i.sharedStorageItemDeleted))}async createPreview(e,t){const i=e&&{key:e,value:t||""};return l.JSONView.JSONView.createViewSync(i)}}var Gi=Object.freeze({__proto__:null,SharedStorageItemsView:zi});class $i extends F{view;constructor(e,t){super(e,t.securityOrigin,!1,"shared-storage-instance")}static async createElement(e,t){const i=new $i(e,t);return i.view=await zi.createView(t),i.view.element.setAttribute("jslog",`${p.pane("shared-storage-data")}`),i}get itemURL(){return"shared-storage://"}onselect(e){return super.onselect(e),this.resourcesPanel.showView(this.view),!1}}var Xi=Object.freeze({__proto__:null,SharedStorageTreeElement:$i});const Ji={storageBuckets:"Storage buckets",noStorageBuckets:"No storage buckets detected",storageBucketsDescription:"On this page you can view and delete storage buckets, and their associated `Storage APIs`."},Qi=i.i18n.registerUIStrings("panels/application/StorageBucketsTreeElement.ts",Ji),Yi=i.i18n.getLocalizedString.bind(void 0,Qi);class Zi extends O{bucketTreeElements=new Set;constructor(e){super(e,Yi(Ji.storageBuckets),Yi(Ji.noStorageBuckets),Yi(Ji.storageBucketsDescription),"storage-buckets");const t=n.Icon.create("database");this.setLeadingIcons([t]),this.setLink("https://github.com/WICG/storage-buckets/blob/gh-pages/explainer.md")}initialize(){o.TargetManager.TargetManager.instance().addModelListener(o.StorageBucketsModel.StorageBucketsModel,"BucketAdded",this.bucketAdded,this),o.TargetManager.TargetManager.instance().addModelListener(o.StorageBucketsModel.StorageBucketsModel,"BucketRemoved",this.bucketRemoved,this),o.TargetManager.TargetManager.instance().addModelListener(o.StorageBucketsModel.StorageBucketsModel,"BucketChanged",this.bucketChanged,this);for(const e of o.TargetManager.TargetManager.instance().models(o.StorageBucketsModel.StorageBucketsModel)){const t=e.getBuckets();for(const i of t)this.addBucketTreeElement(e,i)}}removeBucketsForModel(e){for(const t of this.bucketTreeElements)t.model===e&&this.removeBucketTreeElement(t)}bucketAdded({data:{model:e,bucketInfo:t}}){this.addBucketTreeElement(e,t)}bucketRemoved({data:{model:e,bucketInfo:t}}){const i=this.getBucketTreeElement(e,t);i&&this.removeBucketTreeElement(i)}bucketChanged({data:{model:e,bucketInfo:t}}){const i=this.getBucketTreeElement(e,t);i&&(i.bucketInfo=t)}addBucketTreeElement(e,t){if(void 0===t.bucket.name)return;const i=new es(this.resourcesPanel,e,t);this.bucketTreeElements.add(i),this.appendChild(i),i.initialize()}removeBucketTreeElement(e){this.removeChild(e),this.bucketTreeElements.delete(e),this.setExpandable(this.bucketTreeElements.size>0)}get itemURL(){return"storage-buckets-group://"}getBucketTreeElement(e,{bucket:{storageKey:t,name:i}}){for(const s of this.bucketTreeElements)if(s.model===e&&s.bucketInfo.bucket.storageKey===t&&s.bucketInfo.bucket.name===i)return s;return null}}class es extends O{storageBucketInfo;bucketModel;view;constructor(e,t,i){const{bucket:s}=i,{origin:r}=o.StorageKeyManager.parseStorageKey(i.bucket.storageKey);super(e,`${s.name} - ${r}`,"","","storage-bucket"),this.bucketModel=t,this.storageBucketInfo=i;const a=n.Icon.create("database");this.setLeadingIcons([a])}initialize(){const{bucket:e}=this.bucketInfo,t=new Cs(this.resourcesPanel,e);this.appendChild(t);const i=new Qt(this.resourcesPanel,e);this.appendChild(i),i.initialize()}get itemURL(){const{bucket:e}=this.bucketInfo;return`storage-buckets-group://${e.name}/${e.storageKey}`}get model(){return this.bucketModel}get bucketInfo(){return this.storageBucketInfo}set bucketInfo(e){this.storageBucketInfo=e,this.view&&this.view.getComponent().setStorageBucket(this.storageBucketInfo)}onselect(e){return super.onselect(e),this.view||(this.view=d.LegacyWrapper.legacyWrapper(c.Widget.Widget,new v.StorageMetadataView),this.view.getComponent().enableStorageBucketControls(this.model),this.view.getComponent().setStorageBucket(this.storageBucketInfo)),this.showView(this.view),!1}}var ts=Object.freeze({__proto__:null,StorageBucketsTreeElement:es,StorageBucketsTreeParentElement:Zi,i18nString:Yi}),is={cssText:`.report-row{display:flex;align-items:center;white-space:normal;&:has(.quota-override-error:empty){margin:0}}.clear-storage-button .report-row{display:flex}.link{margin-left:10px;display:none}.report-row:hover .link{display:inline}.quota-override-editor-with-button{align-items:baseline;display:flex}.quota-override-notification-editor{border:solid 1px var(--sys-color-neutral-outline);border-radius:4px;display:flex;flex:auto;margin-right:4px;max-width:200px;min-width:50px;min-height:19px;padding-left:4px;&:focus{border-color:var(--sys-color-state-focus-ring)}&:hover:not(:focus){background-color:var(--sys-color-state-hover-on-subtle)}}.quota-override-error:not(:empty){padding-top:10px;color:var(--sys-color-error)}.usage-breakdown-row{min-width:fit-content}.clear-storage-container{overflow:auto}.clear-storage-header{min-width:400px}.report-content-box{overflow:initial}.include-third-party-cookies{flex:1;min-width:0;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;margin-left:10px}\n/*# sourceURL=${import.meta.resolve("./storageView.css")} */\n`};const ss={storageQuotaUsed:"{PH1} used out of {PH2} storage quota",storageQuotaUsedWithBytes:"{PH1} bytes used out of {PH2} bytes storage quota",storageWithCustomMarker:"{PH1} (custom)",storageTitle:"Storage",usage:"Usage",mb:"MB",learnMore:"Learn more",clearSiteData:"Clear site data",SiteDataCleared:"Site data cleared",application:"Application",unregisterServiceWorker:"Unregister service workers",localAndSessionStorage:"Local and session storage",indexDB:"IndexedDB",webSql:"Web SQL",cookies:"Cookies",cacheStorage:"Cache storage",includingThirdPartyCookies:"including third-party cookies",sFailedToLoad:"{PH1} (failed to load)",internalError:"Internal error",pleaseEnterANumber:"Please enter a number",numberMustBeNonNegative:"Number must be non-negative",numberMustBeSmaller:"Number must be smaller than {PH1}",clearing:"Clearing...",storageQuotaIsLimitedIn:"Storage quota is limited in Incognito mode",fileSystem:"File System",other:"Other",storageUsage:"Storage usage",serviceWorkers:"Service workers",simulateCustomStorage:"Simulate custom storage quota"},rs=i.i18n.registerUIStrings("panels/application/StorageView.ts",ss),os=i.i18n.getLocalizedString.bind(void 0,rs);class as extends c.ThrottledWidget.ThrottledWidget{pieColors;reportView;target;securityOrigin;storageKey;settings;includeThirdPartyCookiesSetting;quotaRow;quotaUsage;pieChart;previousOverrideFieldValue;quotaOverrideCheckbox;quotaOverrideControlRow;quotaOverrideEditor;quotaOverrideErrorMessage;clearButton;constructor(){super(!0,1e3),this.registerRequiredCSS(is),this.contentElement.classList.add("clear-storage-container"),this.contentElement.setAttribute("jslog",`${p.pane("clear-storage")}`),this.pieColors=new Map([["cache_storage","rgb(229, 113, 113)"],["cookies","rgb(239, 196, 87)"],["indexeddb","rgb(155, 127, 230)"],["local_storage","rgb(116, 178, 102)"],["service_workers","rgb(255, 167, 36)"],["websql","rgb(203, 220, 56)"]]),this.reportView=new c.ReportView.ReportView(os(ss.storageTitle)),this.reportView.registerRequiredCSS(is),this.reportView.element.classList.add("clear-storage-header"),this.reportView.show(this.contentElement),this.target=null,this.securityOrigin=null,this.storageKey=null,this.settings=new Map;for(const t of ns)this.settings.set(t,e.Settings.Settings.instance().createSetting("clear-storage-"+s.StringUtilities.toKebabCase(t),!0));this.includeThirdPartyCookiesSetting=e.Settings.Settings.instance().createSetting("clear-storage-include-third-party-cookies",!1);const t=this.reportView.appendSection(os(ss.usage));t.element.setAttribute("jslog",`${p.section("usage")}`),this.quotaRow=t.appendSelectableRow(),this.quotaRow.classList.add("quota-usage-row");const i=t.appendRow(),r=c.XLink.XLink.create("https://developer.chrome.com/docs/devtools/progressive-web-apps#opaque-responses",os(ss.learnMore),void 0,void 0,"learn-more");i.appendChild(r),this.quotaUsage=null,this.pieChart=new B.PieChart.PieChart,this.populatePieChart(0,[]);const a=t.appendRow();a.classList.add("usage-breakdown-row"),a.appendChild(this.pieChart),this.previousOverrideFieldValue="";const n=t.appendRow();n.classList.add("quota-override-row"),this.quotaOverrideCheckbox=c.UIUtils.CheckboxLabel.create(os(ss.simulateCustomStorage),!1),this.quotaOverrideCheckbox.setAttribute("jslog",`${p.toggle("simulate-custom-quota").track({change:!0})}`),n.appendChild(this.quotaOverrideCheckbox),this.quotaOverrideCheckbox.checkboxElement.addEventListener("click",this.onClickCheckbox.bind(this),!1),this.quotaOverrideControlRow=t.appendRow(),this.quotaOverrideEditor=this.quotaOverrideControlRow.createChild("input","quota-override-notification-editor"),this.quotaOverrideEditor.setAttribute("jslog",`${p.textField("quota-override").track({change:!0})}`),this.quotaOverrideControlRow.appendChild(c.UIUtils.createLabel(os(ss.mb))),this.quotaOverrideControlRow.classList.add("hidden"),this.quotaOverrideEditor.addEventListener("keyup",(e=>{"Enter"===e.key&&(this.applyQuotaOverrideFromInputField(),e.consume(!0))})),this.quotaOverrideEditor.addEventListener("focusout",(e=>{this.applyQuotaOverrideFromInputField(),e.consume(!0)}));const d=t.appendRow();this.quotaOverrideErrorMessage=d.createChild("div","quota-override-error");const l=this.reportView.appendSection("","clear-storage-button").appendRow();this.clearButton=c.UIUtils.createTextButton(os(ss.clearSiteData),this.clear.bind(this),{jslogContext:"storage.clear-site-data"}),this.clearButton.id="storage-view-clear-button",l.appendChild(this.clearButton);const h=c.SettingsUI.createSettingCheckbox(os(ss.includingThirdPartyCookies),this.includeThirdPartyCookiesSetting);h.classList.add("include-third-party-cookies"),l.appendChild(h);const g=this.reportView.appendSection(os(ss.application));g.element.setAttribute("jslog",`${p.section("application")}`),this.appendItem(g,os(ss.unregisterServiceWorker),"service_workers"),g.markFieldListAsGroup();const u=this.reportView.appendSection(os(ss.storageTitle));u.element.setAttribute("jslog",`${p.section("storage")}`),this.appendItem(u,os(ss.localAndSessionStorage),"local_storage"),this.appendItem(u,os(ss.indexDB),"indexeddb"),this.appendItem(u,os(ss.webSql),"websql"),this.appendItem(u,os(ss.cookies),"cookies"),this.appendItem(u,os(ss.cacheStorage),"cache_storage"),u.markFieldListAsGroup(),o.TargetManager.TargetManager.instance().observeTargets(this)}appendItem(e,t,i){const s=e.appendRow(),r=this.settings.get(i);r&&s.appendChild(c.SettingsUI.createSettingCheckbox(t,r))}targetAdded(e){if(e!==o.TargetManager.TargetManager.instance().primaryPageTarget())return;this.target=e;const t=e.model(o.SecurityOriginManager.SecurityOriginManager);this.updateOrigin(t.mainSecurityOrigin(),t.unreachableMainSecurityOrigin()),t.addEventListener(o.SecurityOriginManager.Events.MainSecurityOriginChanged,this.originChanged,this);const i=e.model(o.StorageKeyManager.StorageKeyManager);this.updateStorageKey(i.mainStorageKey()),i.addEventListener("MainStorageKeyChanged",this.storageKeyChanged,this)}targetRemoved(e){if(this.target!==e)return;e.model(o.SecurityOriginManager.SecurityOriginManager).removeEventListener(o.SecurityOriginManager.Events.MainSecurityOriginChanged,this.originChanged,this);e.model(o.StorageKeyManager.StorageKeyManager).removeEventListener("MainStorageKeyChanged",this.storageKeyChanged,this)}originChanged(e){const{mainSecurityOrigin:t,unreachableMainSecurityOrigin:i}=e.data;this.updateOrigin(t,i)}storageKeyChanged(e){const{mainStorageKey:t}=e.data;this.updateStorageKey(t)}updateOrigin(e,t){const i=this.securityOrigin;t?(this.securityOrigin=t,this.reportView.setSubtitle(os(ss.sFailedToLoad,{PH1:t}))):(this.securityOrigin=e,this.reportView.setSubtitle(e)),i!==this.securityOrigin&&(this.quotaOverrideControlRow.classList.add("hidden"),this.quotaOverrideCheckbox.checkboxElement.checked=!1,this.quotaOverrideErrorMessage.textContent=""),this.doUpdate()}updateStorageKey(e){const t=this.storageKey;this.storageKey=e,this.reportView.setSubtitle(e),t!==this.storageKey&&(this.quotaOverrideControlRow.classList.add("hidden"),this.quotaOverrideCheckbox.checkboxElement.checked=!1,this.quotaOverrideErrorMessage.textContent=""),this.doUpdate()}async applyQuotaOverrideFromInputField(){if(!this.target||!this.securityOrigin)return void(this.quotaOverrideErrorMessage.textContent=os(ss.internalError));this.quotaOverrideErrorMessage.textContent="";const e=this.quotaOverrideEditor.value;if(""===e)return await this.clearQuotaForOrigin(this.target,this.securityOrigin),void(this.previousOverrideFieldValue="");const t=parseFloat(e);if(!Number.isFinite(t))return void(this.quotaOverrideErrorMessage.textContent=os(ss.pleaseEnterANumber));if(t<0)return void(this.quotaOverrideErrorMessage.textContent=os(ss.numberMustBeNonNegative));const i=9e12;if(t>=i)return void(this.quotaOverrideErrorMessage.textContent=os(ss.numberMustBeSmaller,{PH1:i.toLocaleString()}));const s=1e6,r=Math.round(t*s),o=""+r/s;this.quotaOverrideEditor.value=o,this.previousOverrideFieldValue=o,await this.target.storageAgent().invoke_overrideQuotaForOrigin({origin:this.securityOrigin,quotaSize:r})}async clearQuotaForOrigin(e,t){await e.storageAgent().invoke_overrideQuotaForOrigin({origin:t})}async onClickCheckbox(){this.quotaOverrideControlRow.classList.contains("hidden")?(this.quotaOverrideControlRow.classList.remove("hidden"),this.quotaOverrideCheckbox.checkboxElement.checked=!0,this.quotaOverrideEditor.value=this.previousOverrideFieldValue,this.quotaOverrideEditor.focus()):this.target&&this.securityOrigin&&(this.quotaOverrideControlRow.classList.add("hidden"),this.quotaOverrideCheckbox.checkboxElement.checked=!1,await this.clearQuotaForOrigin(this.target,this.securityOrigin),this.quotaOverrideErrorMessage.textContent="")}clear(){if(!this.securityOrigin)return;const e=[];for(const t of this.settings.keys()){const i=this.settings.get(t);i?.get()&&e.push(t)}if(this.target){const t=this.includeThirdPartyCookiesSetting.get();as.clear(this.target,this.storageKey,this.securityOrigin,e,t)}this.clearButton.disabled=!0;const t=this.clearButton.textContent;this.clearButton.textContent=os(ss.clearing),window.setTimeout((()=>{this.clearButton.disabled=!1,this.clearButton.textContent=t,this.clearButton.focus()}),500),c.ARIAUtils.alert(os(ss.SiteDataCleared))}static clear(e,t,i,s,r){if(console.assert(Boolean(t)),!t)return;e.storageAgent().invoke_clearDataForStorageKey({storageKey:t,storageTypes:s.join(",")});const a=new Set(s),n=a.has("all");if(a.has("local_storage")||n){const i=e.model(ce);i&&i.clearForStorageKey(t)}if(a.has("indexeddb")||n)for(const e of o.TargetManager.TargetManager.instance().targets()){const i=e.model(we);i&&i.clearForStorageKey(t)}if(i&&(a.has("cookies")||n)){e.storageAgent().invoke_clearDataForOrigin({origin:i,storageTypes:"cookies"});const t=e.model(o.CookieModel.CookieModel);t&&t.clear(void 0,r?void 0:i)}if(a.has("cache_storage")||n){const e=o.TargetManager.TargetManager.instance().primaryPageTarget(),i=e?.model(o.ServiceWorkerCacheModel.ServiceWorkerCacheModel);i&&i.clearForStorageKey(t)}}async doUpdate(){if(!this.securityOrigin||!this.target)return this.quotaRow.textContent="",void this.populatePieChart(0,[]);const e=this.securityOrigin,t=await this.target.storageAgent().invoke_getUsageAndQuota({origin:e});if(this.quotaRow.textContent="",t.getError())return void this.populatePieChart(0,[]);const s=t.overrideActive,r=i.ByteUtilities.bytesToString(t.quota),o=i.ByteUtilities.bytesToString(t.usage),a=os(ss.storageWithCustomMarker,{PH1:r}),d=s?c.Fragment.Fragment.build`<b>${a}</b>`.element():r,l=i.i18n.getFormatLocalizedString(rs,ss.storageQuotaUsed,{PH1:o,PH2:d});if(this.quotaRow.appendChild(l),c.Tooltip.Tooltip.install(this.quotaRow,os(ss.storageQuotaUsedWithBytes,{PH1:t.usage.toLocaleString(),PH2:t.quota.toLocaleString()})),!t.overrideActive&&t.quota<125829120){const e=new n.Icon.Icon;e.data={iconName:"info",color:"var(--icon-info)",width:"14px",height:"14px"},c.Tooltip.Tooltip.install(this.quotaRow,os(ss.storageQuotaIsLimitedIn)),this.quotaRow.appendChild(e)}if(null===this.quotaUsage||this.quotaUsage!==t.usage){this.quotaUsage=t.usage;const e=[];for(const i of t.usageBreakdown.sort(((e,t)=>t.usage-e.usage))){const t=i.usage;if(!t)continue;const s=this.getStorageTypeName(i.storageType),r=this.pieColors.get(i.storageType)||"#ccc";e.push({value:t,color:r,title:s})}this.populatePieChart(t.usage,e)}this.update()}populatePieChart(e,t){this.pieChart.data={chartName:os(ss.storageUsage),size:110,formatter:i.ByteUtilities.bytesToString,showLegend:!0,total:e,slices:t}}getStorageTypeName(e){switch(e){case"file_systems":return os(ss.fileSystem);case"websql":return os(ss.webSql);case"indexeddb":return os(ss.indexDB);case"cache_storage":return os(ss.cacheStorage);case"service_workers":return os(ss.serviceWorkers);default:return os(ss.other)}}}const ns=["cache_storage","cookies","indexeddb","local_storage","service_workers","websql"];var ds=Object.freeze({__proto__:null,ActionDelegate:class{handleAction(e,t){switch(t){case"resources.clear":return this.handleClear(!1);case"resources.clear-incl-third-party-cookies":return this.handleClear(!0)}return!1}handleClear(e){const t=o.TargetManager.TargetManager.instance().primaryPageTarget();if(!t)return!1;const i=t.model(o.ResourceTreeModel.ResourceTreeModel);if(!i)return!1;const s=i.getMainSecurityOrigin();return i.getMainStorageKey().then((i=>{as.clear(t,i,s,ns,e)}),(e=>{})),!0}},AllStorageTypes:ns,StorageView:as});const ls={trustTokens:"Private state tokens"},cs=i.i18n.registerUIStrings("panels/application/TrustTokensTreeElement.ts",ls),hs=i.i18n.getLocalizedString.bind(void 0,cs);class gs extends F{view;constructor(e){super(e,hs(ls.trustTokens),!1,"private-state-tokens");const t=n.Icon.create("database");this.setLeadingIcons([t])}get itemURL(){return"trustTokens://"}onselect(e){return super.onselect(e),this.view||(this.view=d.LegacyWrapper.legacyWrapper(c.Widget.Widget,new m.TrustTokensView.TrustTokensView,"trust-tokens")),this.showView(this.view),t.userMetrics.panelShown("trust-tokens"),!1}}var us=Object.freeze({__proto__:null,TrustTokensTreeElement:gs,i18nString:hs});const ps={application:"Application",storage:"Storage",noLocalStorage:"No local storage detected",localStorage:"Local storage",localStorageDescription:"On this page you can view, add, edit, and delete local storage key-value pairs.",sessionStorage:"Session storage",noSessionStorage:"No session storage detected",sessionStorageDescription:"On this page you can view, add, edit, and delete session storage key-value pairs.",extensionStorage:"Extension storage",noExtensionStorage:"No extension storage detected",extensionStorageDescription:"On this page you can view, add, edit, and delete extension storage key-value pairs.",extensionSessionStorage:"Session",extensionLocalStorage:"Local",extensionSyncStorage:"Sync",extensionManagedStorage:"Managed",cookies:"Cookies",noCookies:"No cookies set",cookiesDescription:"On this page you can view, add, edit, and delete cookies.",backgroundServices:"Background services",frames:"Frames",manifest:"Manifest",noManifestDetected:"No manifest detected",manifestDescription:"A manifest defines how your app appears on phone’s home screens and what the app looks like on launch.",appManifest:"Manifest",indexeddb:"IndexedDB",noIndexeddb:"No indexedDB detected",indexeddbDescription:"On this page you can view and delete indexedDB key-value pairs and databases.",refreshIndexeddb:"Refresh IndexedDB",versionSEmpty:"Version: {PH1} (empty)",versionS:"Version: {PH1}",clear:"Clear",keyPathS:"Key path: {PH1}",localFiles:"Local Files",cookiesUsedByFramesFromS:"Cookies used by frames from {PH1}",openedWindows:"Opened Windows",openedWindowsDescription:"On this page you can view windows opened via window.open().",webWorkers:"Web Workers",documentNotAvailable:"No document detected",theContentOfThisDocumentHasBeen:"The content of this document has been generated dynamically via 'document.write()'.",windowWithoutTitle:"Window without title",worker:"worker",workerDescription:"On this page you can view dedicated workers that are created by the parent frame.",onInvokeManifestAlert:"Manifest: Invoke to scroll to the top of manifest",beforeInvokeAlert:"{PH1}: Invoke to scroll to this section in manifest",onInvokeAlert:"Scrolled to {PH1}",applicationSidebarPanel:"Application panel sidebar",thirdPartyPhaseout:"Cookies from {PH1} may have been blocked due to third-party cookie phaseout.",resourceDescription:"On this page you can view the frame's resources."},ms=i.i18n.registerUIStrings("panels/application/ApplicationPanelSidebar.ts",ps),vs=i.i18n.getLocalizedString.bind(void 0,ms);function ws(e){if("main"===e)throw new Error("Unexpected main target id")}class Ss extends c.Widget.VBox{panel;sidebarTree;applicationTreeElement;serviceWorkersTreeElement;localStorageListTreeElement;sessionStorageListTreeElement;extensionStorageListTreeElement;indexedDBListTreeElement;interestGroupTreeElement;cookieListTreeElement;trustTokensTreeElement;cacheStorageListTreeElement;sharedStorageListTreeElement;storageBucketsTreeElement;backForwardCacheListTreeElement;backgroundFetchTreeElement;backgroundSyncTreeElement;bounceTrackingMitigationsTreeElement;notificationsTreeElement;paymentHandlerTreeElement;periodicBackgroundSyncTreeElement;pushMessagingTreeElement;reportingApiTreeElement;preloadingSummaryTreeElement;resourcesSection;domStorageTreeElements;extensionIdToStorageTreeParentElement;extensionStorageModels;extensionStorageTreeElements;sharedStorageTreeElements;domains;target;previousHoveredElement;sharedStorageTreeElementDispatcher;constructor(t){super(),this.panel=t,this.sidebarTree=new c.TreeOutline.TreeOutlineInShadow("NavigationTree"),this.sidebarTree.registerRequiredCSS(Ut),this.sidebarTree.element.classList.add("resources-sidebar"),this.sidebarTree.hideOverflow(),this.sidebarTree.element.classList.add("filter-all"),this.sidebarTree.addEventListener(c.TreeOutline.Events.ElementAttached,this.treeElementAdded,this),this.contentElement.appendChild(this.sidebarTree.element);const i=vs(ps.application);this.applicationTreeElement=this.addSidebarSection(i,"application");const s=this.applicationTreeElement.treeOutline?.contentElement;s&&(s.ariaLabel=vs(ps.applicationSidebarPanel));const r=new ks(t);this.applicationTreeElement.appendChild(r),r.generateChildren(),this.serviceWorkersTreeElement=new ys(t),this.applicationTreeElement.appendChild(this.serviceWorkersTreeElement);const a=new Is(t);this.applicationTreeElement.appendChild(a);const d=vs(ps.storage),l=this.addSidebarSection(d,"storage");this.localStorageListTreeElement=new O(t,vs(ps.localStorage),vs(ps.noLocalStorage),vs(ps.localStorageDescription),"local-storage"),this.localStorageListTreeElement.setLink("https://developer.chrome.com/docs/devtools/storage/localstorage/?utm_source=devtools");const h=n.Icon.create("table");this.localStorageListTreeElement.setLeadingIcons([h]),l.appendChild(this.localStorageListTreeElement),this.sessionStorageListTreeElement=new O(t,vs(ps.sessionStorage),vs(ps.noSessionStorage),vs(ps.sessionStorageDescription),"session-storage"),this.sessionStorageListTreeElement.setLink("https://developer.chrome.com/docs/devtools/storage/sessionstorage/?utm_source=devtools");const g=n.Icon.create("table");this.sessionStorageListTreeElement.setLeadingIcons([g]),l.appendChild(this.sessionStorageListTreeElement),this.extensionStorageListTreeElement=new O(t,vs(ps.extensionStorage),vs(ps.noExtensionStorage),vs(ps.extensionStorageDescription),"extension-storage"),this.extensionStorageListTreeElement.setLink("https://developer.chrome.com/docs/extensions/reference/api/storage/?utm_source=devtools");const u=n.Icon.create("table");this.extensionStorageListTreeElement.setLeadingIcons([u]),l.appendChild(this.extensionStorageListTreeElement),this.indexedDBListTreeElement=new Cs(t),this.indexedDBListTreeElement.setLink("https://developer.chrome.com/docs/devtools/storage/indexeddb/?utm_source=devtools"),l.appendChild(this.indexedDBListTreeElement),this.cookieListTreeElement=new O(t,vs(ps.cookies),vs(ps.noCookies),vs(ps.cookiesDescription),"cookies"),this.cookieListTreeElement.setLink("https://developer.chrome.com/docs/devtools/storage/cookies/?utm_source=devtools");const p=n.Icon.create("cookie");this.cookieListTreeElement.setLeadingIcons([p]),l.appendChild(this.cookieListTreeElement),this.trustTokensTreeElement=new gs(t),l.appendChild(this.trustTokensTreeElement),this.interestGroupTreeElement=new qe(t),l.appendChild(this.interestGroupTreeElement),this.sharedStorageListTreeElement=new fi(t),l.appendChild(this.sharedStorageListTreeElement),this.cacheStorageListTreeElement=new Qt(t),l.appendChild(this.cacheStorageListTreeElement),this.storageBucketsTreeElement=new Zi(t),l.appendChild(this.storageBucketsTreeElement);const m=vs(ps.backgroundServices),v=this.addSidebarSection(m,"background-services");this.backForwardCacheListTreeElement=new z(t),v.appendChild(this.backForwardCacheListTreeElement),this.backgroundFetchTreeElement=new bs(t,"backgroundFetch"),v.appendChild(this.backgroundFetchTreeElement),this.backgroundSyncTreeElement=new bs(t,"backgroundSync"),v.appendChild(this.backgroundSyncTreeElement),this.bounceTrackingMitigationsTreeElement=new ne(t),v.appendChild(this.bounceTrackingMitigationsTreeElement),this.notificationsTreeElement=new bs(t,"notifications"),v.appendChild(this.notificationsTreeElement),this.paymentHandlerTreeElement=new bs(t,"paymentHandler"),v.appendChild(this.paymentHandlerTreeElement),this.periodicBackgroundSyncTreeElement=new bs(t,"periodicBackgroundSync"),v.appendChild(this.periodicBackgroundSyncTreeElement),this.preloadingSummaryTreeElement=new yt(t),v.appendChild(this.preloadingSummaryTreeElement),this.preloadingSummaryTreeElement.constructChildren(t),this.pushMessagingTreeElement=new bs(t,"pushMessaging"),v.appendChild(this.pushMessagingTreeElement),this.reportingApiTreeElement=new Vt(t),v.appendChild(this.reportingApiTreeElement);const w=vs(ps.frames),S=this.addSidebarSection(w,"frames");this.resourcesSection=new Ps(t,S),this.domStorageTreeElements=new Map,this.extensionIdToStorageTreeParentElement=new Map,this.extensionStorageTreeElements=new Map,this.extensionStorageModels=[],this.sharedStorageTreeElements=new Map,this.domains={},this.sidebarTree.contentElement.addEventListener("mousemove",this.onmousemove.bind(this),!1),this.sidebarTree.contentElement.addEventListener("mouseleave",this.onmouseleave.bind(this),!1),o.TargetManager.TargetManager.instance().observeTargets(this,{scoped:!0}),o.TargetManager.TargetManager.instance().addModelListener(o.ResourceTreeModel.ResourceTreeModel,o.ResourceTreeModel.Events.FrameNavigated,this.frameNavigated,this,{scoped:!0});this.panel.lastSelectedItemPath().length||r.select(),o.TargetManager.TargetManager.instance().observeModels(ce,{modelAdded:e=>this.domStorageModelAdded(e),modelRemoved:e=>this.domStorageModelRemoved(e)},{scoped:!0}),o.TargetManager.TargetManager.instance().observeModels(pe,{modelAdded:e=>this.extensionStorageModelAdded(e),modelRemoved:e=>this.extensionStorageModelRemoved(e)},{scoped:!0}),o.TargetManager.TargetManager.instance().observeModels(we,{modelAdded:e=>this.indexedDBModelAdded(e),modelRemoved:e=>this.indexedDBModelRemoved(e)},{scoped:!0}),o.TargetManager.TargetManager.instance().observeModels(De,{modelAdded:e=>this.interestGroupModelAdded(e),modelRemoved:e=>this.interestGroupModelRemoved(e)},{scoped:!0}),o.TargetManager.TargetManager.instance().observeModels(Ti,{modelAdded:e=>this.sharedStorageModelAdded(e).catch((e=>{console.error(e)})),modelRemoved:e=>this.sharedStorageModelRemoved(e)},{scoped:!0}),o.TargetManager.TargetManager.instance().observeModels(o.StorageBucketsModel.StorageBucketsModel,{modelAdded:e=>this.storageBucketsModelAdded(e),modelRemoved:e=>this.storageBucketsModelRemoved(e)},{scoped:!0}),this.sharedStorageTreeElementDispatcher=new e.ObjectWrapper.ObjectWrapper,this.contentElement.style.contain="layout style"}addSidebarSection(e,t){const i=new c.TreeOutline.TreeElement(e,!0,t);return i.listItemElement.classList.add("storage-group-list-item"),i.setCollapsible(!1),i.selectable=!1,this.sidebarTree.appendChild(i),c.ARIAUtils.markAsHeading(i.listItemElement,3),c.ARIAUtils.setLabel(i.childrenListElement,e),i}targetAdded(e){if(e!==e.outermostTarget())return;this.target=e;const t=e.model(De);t&&t.addEventListener("InterestGroupAccess",this.interestGroupAccess,this);const i=e.model(o.ResourceTreeModel.ResourceTreeModel);i&&(i.cachedResourcesLoaded()&&this.initialize(),i.addEventListener(o.ResourceTreeModel.Events.CachedResourcesLoaded,this.initialize,this),i.addEventListener(o.ResourceTreeModel.Events.WillLoadCachedResources,this.resetWithFrames,this))}targetRemoved(e){if(e!==this.target)return;delete this.target;const t=e.model(o.ResourceTreeModel.ResourceTreeModel);t&&(t.removeEventListener(o.ResourceTreeModel.Events.CachedResourcesLoaded,this.initialize,this),t.removeEventListener(o.ResourceTreeModel.Events.WillLoadCachedResources,this.resetWithFrames,this));const i=e.model(De);i&&i.removeEventListener("InterestGroupAccess",this.interestGroupAccess,this),this.resetWithFrames()}focus(){this.sidebarTree.focus()}initialize(){for(const e of o.ResourceTreeModel.ResourceTreeModel.frames())this.addCookieDocument(e);const e=this.target?.model(De);e&&e.enable(),this.cacheStorageListTreeElement.initialize();const t=this.target?.model(G)||null;this.backgroundFetchTreeElement&&this.backgroundFetchTreeElement.initialize(t),this.backgroundSyncTreeElement&&this.backgroundSyncTreeElement.initialize(t),this.notificationsTreeElement.initialize(t),this.paymentHandlerTreeElement.initialize(t),this.periodicBackgroundSyncTreeElement.initialize(t),this.pushMessagingTreeElement.initialize(t),this.storageBucketsTreeElement?.initialize();const i=this.target?.model(o.PreloadingModel.PreloadingModel);i&&this.preloadingSummaryTreeElement?.initialize(i)}domStorageModelAdded(e){e.enable(),e.storages().forEach(this.addDOMStorage.bind(this)),e.addEventListener("DOMStorageAdded",this.domStorageAdded,this),e.addEventListener("DOMStorageRemoved",this.domStorageRemoved,this)}domStorageModelRemoved(e){e.storages().forEach(this.removeDOMStorage.bind(this)),e.removeEventListener("DOMStorageAdded",this.domStorageAdded,this),e.removeEventListener("DOMStorageRemoved",this.domStorageRemoved,this)}extensionStorageModelAdded(e){this.extensionStorageModels.push(e),e.enable(),e.storages().forEach(this.addExtensionStorage.bind(this)),e.addEventListener("ExtensionStorageAdded",this.extensionStorageAdded,this),e.addEventListener("ExtensionStorageRemoved",this.extensionStorageRemoved,this)}extensionStorageModelRemoved(e){console.assert(this.extensionStorageModels.includes(e)),this.extensionStorageModels.splice(this.extensionStorageModels.indexOf(e),1),e.storages().forEach(this.removeExtensionStorage.bind(this)),e.removeEventListener("ExtensionStorageAdded",this.extensionStorageAdded,this),e.removeEventListener("ExtensionStorageRemoved",this.extensionStorageRemoved,this)}indexedDBModelAdded(e){e.enable(),this.indexedDBListTreeElement.addIndexedDBForModel(e)}indexedDBModelRemoved(e){this.indexedDBListTreeElement.removeIndexedDBForModel(e)}interestGroupModelAdded(e){e.enable(),e.addEventListener("InterestGroupAccess",this.interestGroupAccess,this)}interestGroupModelRemoved(e){e.disable(),e.removeEventListener("InterestGroupAccess",this.interestGroupAccess,this)}async sharedStorageModelAdded(e){await e.enable();for(const t of e.storages())await this.addSharedStorage(t);e.addEventListener("SharedStorageAdded",this.sharedStorageAdded,this),e.addEventListener("SharedStorageRemoved",this.sharedStorageRemoved,this),e.addEventListener("SharedStorageAccess",this.sharedStorageAccess,this)}sharedStorageModelRemoved(e){e.disable();for(const t of e.storages())this.removeSharedStorage(t);e.removeEventListener("SharedStorageAdded",this.sharedStorageAdded,this),e.removeEventListener("SharedStorageRemoved",this.sharedStorageRemoved,this),e.removeEventListener("SharedStorageAccess",this.sharedStorageAccess,this)}storageBucketsModelAdded(e){e.enable()}storageBucketsModelRemoved(e){this.storageBucketsTreeElement?.removeBucketsForModel(e)}resetWithFrames(){this.resourcesSection.reset(),this.reset()}treeElementAdded(e){const t=this.panel.lastSelectedItemPath();if(!t.length)return;const i=e.data,s=[i];for(let e=i.parent;e&&"itemURL"in e&&e.itemURL;e=e.parent)s.push(e);let r=t.length-1,o=s.length-1;for(;r>=0&&o>=0&&t[r]===s[o].itemURL;)s[o].expanded||(r>0&&s[o].expand(),s[o].selected||s[o].select()),r--,o--}reset(){this.domains={},this.cookieListTreeElement.removeChildren(),this.interestGroupTreeElement.clearEvents()}frameNavigated(e){const t=e.data;t.isOutermostFrame()&&this.reset(),this.addCookieDocument(t)}interestGroupAccess(e){this.interestGroupTreeElement.addEvent(e.data)}addCookieDocument(t){const i=t.unreachableUrl()||t.url,s=e.ParsedURL.ParsedURL.fromString(i);if(!s||"http"!==s.scheme&&"https"!==s.scheme&&"file"!==s.scheme)return;const r=s.securityOrigin();if(!this.domains[r]){this.domains[r]=!0;const e=new As(this.panel,t,s);this.cookieListTreeElement.appendChild(e)}}domStorageAdded(e){const t=e.data;this.addDOMStorage(t)}addDOMStorage(e){console.assert(!this.domStorageTreeElements.get(e)),console.assert(Boolean(e.storageKey));const t=new Ms(this.panel,e);function i(e,t){const i=e.titleAsText().toLocaleLowerCase(),s=t.titleAsText().toLocaleUpperCase();return i.localeCompare(s)}this.domStorageTreeElements.set(e,t),e.isLocalStorage?this.localStorageListTreeElement.appendChild(t,i):this.sessionStorageListTreeElement.appendChild(t,i)}domStorageRemoved(e){const t=e.data;this.removeDOMStorage(t)}removeDOMStorage(e){const t=this.domStorageTreeElements.get(e);if(!t)return;const i=t.selected,s=t.parent;s&&(s.removeChild(t),i&&s.select()),this.domStorageTreeElements.delete(e)}extensionStorageAdded(e){const t=e.data;this.addExtensionStorage(t)}useTreeViewForExtensionStorage(e){return!e.matchesTarget(this.target)}getExtensionStorageAreaParent(e){if(!this.useTreeViewForExtensionStorage(e))return this.extensionStorageListTreeElement;const t=this.extensionIdToStorageTreeParentElement.get(e.extensionId);if(t)return t;const i=new Ls(this.panel,e.extensionId,e.name);return this.extensionIdToStorageTreeParentElement.set(e.extensionId,i),this.extensionStorageListTreeElement?.appendChild(i),i}addExtensionStorage(e){if(this.extensionStorageModels.find((t=>t!==e.model&&t.storageForIdAndArea(e.extensionId,e.storageArea))))return;console.assert(Boolean(this.extensionStorageListTreeElement)),console.assert(!this.extensionStorageTreeElements.get(e.key));const t=new Rs(this.panel,e);this.extensionStorageTreeElements.set(e.key,t),this.getExtensionStorageAreaParent(e)?.appendChild(t,(function(e,t){const i=e=>e.storageArea,s=["session","local","sync","managed"];return s.indexOf(i(e))-s.indexOf(i(t))}))}extensionStorageRemoved(e){const t=e.data;this.removeExtensionStorage(t)}removeExtensionStorage(e){if(this.extensionStorageModels.find((t=>t.storageForIdAndArea(e.extensionId,e.storageArea))))return;const t=this.extensionStorageTreeElements.get(e.key);if(!t)return;const i=t.selected,s=t.parent;s&&(s.removeChild(t),this.useTreeViewForExtensionStorage(e)&&0===s.childCount()?(this.extensionStorageListTreeElement?.removeChild(s),this.extensionIdToStorageTreeParentElement.delete(e.extensionId)):i&&s.select()),this.extensionStorageTreeElements.delete(e.key)}async sharedStorageAdded(e){await this.addSharedStorage(e.data)}async addSharedStorage(e){const t=await $i.createElement(this.panel,e);this.sharedStorageTreeElements.has(e.securityOrigin)||(this.sharedStorageTreeElements.set(e.securityOrigin,t),this.sharedStorageListTreeElement.appendChild(t),this.sharedStorageTreeElementDispatcher.dispatchEventToListeners("SharedStorageTreeElementAdded",{origin:e.securityOrigin}))}sharedStorageRemoved(e){this.removeSharedStorage(e.data)}removeSharedStorage(e){const t=this.sharedStorageTreeElements.get(e.securityOrigin);if(!t)return;const i=t.selected,s=t.parent;s&&(s.removeChild(t),s.setExpandable(s.childCount()>0),i&&s.select()),this.sharedStorageTreeElements.delete(e.securityOrigin)}sharedStorageAccess(e){this.sharedStorageListTreeElement.addEvent(e.data)}async showResource(e,t,i){await this.resourcesSection.revealResource(e,t,i)}showFrame(e){this.resourcesSection.revealAndSelectFrame(e)}innerShowView(e){this.panel.showView(e)}showPreloadingRuleSetView(e){this.preloadingSummaryTreeElement&&this.preloadingSummaryTreeElement.expandAndRevealRuleSet(e)}showPreloadingAttemptViewWithFilter(e){this.preloadingSummaryTreeElement&&this.preloadingSummaryTreeElement.expandAndRevealAttempts(e)}onmousemove(e){const t=e.target;if(!t)return;const i=c.UIUtils.enclosingNodeOrSelfWithNodeName(t,"li");if(!i)return;const s=c.TreeOutline.TreeElement.getTreeElementBylistItemNode(i);this.previousHoveredElement!==s&&(this.previousHoveredElement&&(this.previousHoveredElement.hovered=!1,delete this.previousHoveredElement),s instanceof Ds&&(this.previousHoveredElement=s,s.hovered=!0))}onmouseleave(e){this.previousHoveredElement&&(this.previousHoveredElement.hovered=!1,delete this.previousHoveredElement)}}class bs extends F{serviceName;view;model;selectedInternal;constructor(e,t){super(e,te.getUIString(t),!1,s.StringUtilities.toKebabCase(t)),this.serviceName=t,this.selectedInternal=!1,this.view=null,this.model=null;const i=n.Icon.create(this.getIconType());this.setLeadingIcons([i])}getIconType(){switch(this.serviceName){case"backgroundFetch":return"arrow-up-down";case"backgroundSync":return"sync";case"pushMessaging":return"cloud";case"notifications":return"bell";case"paymentHandler":return"credit-card";case"periodicBackgroundSync":return"watch";default:return console.error(`Service ${this.serviceName} does not have a dedicated icon`),"table"}}initialize(e){this.model=e,this.selectedInternal&&!this.view&&this.onselect(!1)}get itemURL(){return`background-service://${this.serviceName}`}get selectable(){return!!this.model&&super.selectable}onselect(e){return super.onselect(e),this.selectedInternal=!0,!!this.model&&(this.view||(this.view=new te(this.serviceName,this.model)),this.showView(this.view),c.Context.Context.instance().setFlavor(te,this.view),t.userMetrics.panelShown("background_service_"+this.serviceName),!1)}}class ys extends F{view;constructor(e){super(e,i.i18n.lockedString("Service workers"),!1,"service-workers");const t=n.Icon.create("gears");this.setLeadingIcons([t])}get itemURL(){return"service-workers://"}onselect(e){return super.onselect(e),this.view||(this.view=new ci),this.showView(this.view),t.userMetrics.panelShown("service-workers"),!1}}class ks extends F{view;constructor(t){super(t,vs(ps.manifest),!0,"manifest");const i=n.Icon.create("document");this.setLeadingIcons([i]),self.onInvokeElement(this.listItemElement,this.onInvoke.bind(this));const s=new c.EmptyWidget.EmptyWidget(vs(ps.noManifestDetected),vs(ps.manifestDescription)),r=new c.ReportView.ReportView(vs(ps.appManifest));this.view=new j(s,r,new e.Throttler.Throttler(1e3)),c.ARIAUtils.setLabel(this.listItemElement,vs(ps.onInvokeManifestAlert));const o=e=>{this.setExpandable(e)};this.view.addEventListener("ManifestDetected",(e=>o(e.data)))}get itemURL(){return"manifest://"}onselect(e){return super.onselect(e),this.showView(this.view),t.userMetrics.panelShown("app-manifest"),!1}generateChildren(){const e=this.view.getStaticSections();for(const t of e){const e=t.getTitleElement(),i=t.title(),s=t.getFieldElement(),r=new fs(this.resourcesPanel,e,i,s,t.jslogContext||"");this.appendChild(r)}}onInvoke(){this.view.getManifestElement().scrollIntoView(),c.ARIAUtils.alert(vs(ps.onInvokeAlert,{PH1:this.listItemElement.title}))}showManifestView(){this.showView(this.view)}}class fs extends F{#ie;#se;constructor(e,t,i,s,r){super(e,i,!1,r);const o=n.Icon.create("document");this.setLeadingIcons([o]),this.#ie=t,this.#se=s,self.onInvokeElement(this.listItemElement,this.onInvoke.bind(this)),this.listItemElement.addEventListener("keydown",this.onInvokeElementKeydown.bind(this)),c.ARIAUtils.setLabel(this.listItemElement,vs(ps.beforeInvokeAlert,{PH1:this.listItemElement.title}))}get itemURL(){return"manifest://"+this.title}onInvoke(){this.parent?.showManifestView(),this.#ie.scrollIntoView(),c.ARIAUtils.alert(vs(ps.onInvokeAlert,{PH1:this.listItemElement.title}))}onInvokeElementKeydown(e){if("Tab"!==e.key||e.shiftKey)return;const t=this.#se.querySelector(".mask-checkbox");let i=this.#se.querySelector('[tabindex="0"]');t?.shadowRoot?i=t.shadowRoot.querySelector("input")||null:i||(i=this.#se.querySelector("devtools-protocol-handlers-view")?.shadowRoot?.querySelector('[tabindex="0"]')||null),i&&(i?.focus(),e.consume(!0))}}class Is extends F{view;constructor(e){super(e,vs(ps.storage),!1,"storage");const t=n.Icon.create("database");this.setLeadingIcons([t])}get itemURL(){return"clear-storage://"}onselect(e){return super.onselect(e),this.view||(this.view=new as),this.showView(this.view),t.userMetrics.panelShown(t.UserMetrics.PanelCodes[t.UserMetrics.PanelCodes.storage]),!1}}class Cs extends O{idbDatabaseTreeElements;storageBucket;constructor(e,t){super(e,vs(ps.indexeddb),vs(ps.noIndexeddb),vs(ps.indexeddbDescription),"indexed-db");const i=n.Icon.create("database");this.setLeadingIcons([i]),this.idbDatabaseTreeElements=[],this.storageBucket=t,this.initialize()}initialize(){o.TargetManager.TargetManager.instance().addModelListener(we,Se.DatabaseAdded,this.indexedDBAdded,this,{scoped:!0}),o.TargetManager.TargetManager.instance().addModelListener(we,Se.DatabaseRemoved,this.indexedDBRemoved,this,{scoped:!0}),o.TargetManager.TargetManager.instance().addModelListener(we,Se.DatabaseLoaded,this.indexedDBLoaded,this,{scoped:!0}),o.TargetManager.TargetManager.instance().addModelListener(we,Se.IndexedDBContentUpdated,this.indexedDBContentUpdated,this,{scoped:!0}),this.idbDatabaseTreeElements=[];for(const e of o.TargetManager.TargetManager.instance().models(we,{scoped:!0})){const t=e.databases();for(let i=0;i<t.length;++i)this.addIndexedDB(e,t[i])}}addIndexedDBForModel(e){for(const t of e.databases())this.addIndexedDB(e,t)}removeIndexedDBForModel(e){const t=this.idbDatabaseTreeElements.filter((t=>t.model===e));for(const e of t)this.removeIDBDatabaseTreeElement(e)}onattach(){super.onattach(),this.listItemElement.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this),!0)}handleContextMenuEvent(e){const t=new c.ContextMenu.ContextMenu(e);t.defaultSection().appendItem(vs(ps.refreshIndexeddb),this.refreshIndexedDB.bind(this),{jslogContext:"refresh-indexeddb"}),t.show()}refreshIndexedDB(){for(const e of o.TargetManager.TargetManager.instance().models(we,{scoped:!0}))e.refreshDatabaseNames()}databaseInTree(e){return!this.storageBucket||e.inBucket(this.storageBucket)}indexedDBAdded({data:{databaseId:e,model:t}}){this.addIndexedDB(t,e)}addIndexedDB(e,t){if(!this.databaseInTree(t))return;const i=new Ts(this.resourcesPanel,e,t);this.idbDatabaseTreeElements.push(i),this.appendChild(i),e.refreshDatabase(t)}indexedDBRemoved({data:{databaseId:e,model:t}}){const i=this.idbDatabaseTreeElement(t,e);i&&this.removeIDBDatabaseTreeElement(i)}removeIDBDatabaseTreeElement(e){e.clear(),this.removeChild(e),s.ArrayUtilities.removeElement(this.idbDatabaseTreeElements,e),this.setExpandable(this.childCount()>0)}indexedDBLoaded({data:{database:e,model:t,entriesUpdated:i}}){const s=this.idbDatabaseTreeElement(t,e.databaseId);s&&(s.update(e,i),this.indexedDBLoadedForTest())}indexedDBLoadedForTest(){}indexedDBContentUpdated({data:{databaseId:e,objectStoreName:t,model:i}}){const s=this.idbDatabaseTreeElement(i,e);s&&s.indexedDBContentUpdated(t)}idbDatabaseTreeElement(e,t){return this.idbDatabaseTreeElements.find((i=>i.databaseId.equals(t)&&i.model===e))||null}}class Ts extends F{model;databaseId;idbObjectStoreTreeElements;database;view;constructor(e,t,i){super(e,i.name,!1,"indexed-db-database"),this.model=t,this.databaseId=i,this.idbObjectStoreTreeElements=new Map;const s=n.Icon.create("database");this.setLeadingIcons([s]),this.model.addEventListener(Se.DatabaseNamesRefreshed,this.refreshIndexedDB,this)}get itemURL(){return"indexedDB://"+this.databaseId.storageBucket.storageKey+"/"+(this.databaseId.storageBucket.name??"")+"/"+this.databaseId.name}onattach(){super.onattach(),this.listItemElement.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this),!0)}handleContextMenuEvent(e){const t=new c.ContextMenu.ContextMenu(e);t.defaultSection().appendItem(vs(ps.refreshIndexeddb),this.refreshIndexedDB.bind(this),{jslogContext:"refresh-indexeddb"}),t.show()}refreshIndexedDB(){this.model.refreshDatabase(this.databaseId)}indexedDBContentUpdated(e){const t=this.idbObjectStoreTreeElements.get(e);t&&t.markNeedsRefresh()}update(e,t){this.database=e;const i=new Set;for(const e of[...this.database.objectStores.keys()].sort()){const s=this.database.objectStores.get(e);if(!s)continue;i.add(s.name);let r=this.idbObjectStoreTreeElements.get(s.name);r||(r=new xs(this.resourcesPanel,this.model,this.databaseId,s),this.idbObjectStoreTreeElements.set(s.name,r),this.appendChild(r)),r.update(s,t)}for(const e of this.idbObjectStoreTreeElements.keys())i.has(e)||this.objectStoreRemoved(e);this.view&&this.view.getComponent().update(e),this.updateTooltip()}updateTooltip(){const e=this.database?this.database.version:"-";0===Object.keys(this.idbObjectStoreTreeElements).length?this.tooltip=vs(ps.versionSEmpty,{PH1:e}):this.tooltip=vs(ps.versionS,{PH1:e})}get selectable(){return!!this.database&&super.selectable}onselect(e){return super.onselect(e),!!this.database&&(this.view||(this.view=d.LegacyWrapper.legacyWrapper(c.Widget.VBox,new Le(this.model,this.database),"indexeddb-data")),this.showView(this.view),t.userMetrics.panelShown("indexed-db"),!1)}objectStoreRemoved(e){const t=this.idbObjectStoreTreeElements.get(e);t&&(t.clear(),this.removeChild(t)),this.idbObjectStoreTreeElements.delete(e),this.updateTooltip()}clear(){for(const e of this.idbObjectStoreTreeElements.keys())this.objectStoreRemoved(e)}}class xs extends F{model;databaseId;idbIndexTreeElements;objectStore;view;constructor(e,t,i,s){super(e,s.name,!1,"indexed-db-object-store"),this.model=t,this.databaseId=i,this.idbIndexTreeElements=new Map,this.objectStore=s,this.view=null;const r=n.Icon.create("table");this.setLeadingIcons([r])}get itemURL(){return"indexedDB://"+this.databaseId.storageBucket.storageKey+"/"+(this.databaseId.storageBucket.name??"")+"/"+this.databaseId.name+"/"+this.objectStore.name}onattach(){super.onattach(),this.listItemElement.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this),!0)}markNeedsRefresh(){this.view&&this.view.markNeedsRefresh();for(const e of this.idbIndexTreeElements.values())e.markNeedsRefresh()}handleContextMenuEvent(e){const t=new c.ContextMenu.ContextMenu(e);t.defaultSection().appendItem(vs(ps.clear),this.clearObjectStore.bind(this),{jslogContext:"clear"}),t.show()}refreshObjectStore(){this.view&&this.view.refreshData();for(const e of this.idbIndexTreeElements.values())e.refreshIndex()}async clearObjectStore(){await this.model.clearObjectStore(this.databaseId,this.objectStore.name),this.update(this.objectStore,!0)}update(e,t){this.objectStore=e;const i=new Set;for(const e of this.objectStore.indexes.values()){i.add(e.name);let s=this.idbIndexTreeElements.get(e.name);s||(s=new Es(this.resourcesPanel,this.model,this.databaseId,this.objectStore,e,this.refreshObjectStore.bind(this)),this.idbIndexTreeElements.set(e.name,s),this.appendChild(s)),s.update(this.objectStore,e,t)}for(const e of this.idbIndexTreeElements.keys())i.has(e)||this.indexRemoved(e);for(const[e,t]of this.idbIndexTreeElements.entries())i.has(e)||(this.removeChild(t),this.idbIndexTreeElements.delete(e));this.childCount()&&this.expand(),this.view&&t&&this.view.update(this.objectStore,null),this.updateTooltip()}updateTooltip(){const e=this.objectStore.keyPathString;let t=null!==e?vs(ps.keyPathS,{PH1:e}):"";this.objectStore.autoIncrement&&(t+="\n"+i.i18n.lockedString("autoIncrement")),this.tooltip=t}onselect(e){return super.onselect(e),this.view||(this.view=new Ae(this.model,this.databaseId,this.objectStore,null,this.refreshObjectStore.bind(this))),this.showView(this.view),t.userMetrics.panelShown("indexed-db"),!1}indexRemoved(e){const t=this.idbIndexTreeElements.get(e);t&&(t.clear(),this.removeChild(t)),this.idbIndexTreeElements.delete(e)}clear(){for(const e of this.idbIndexTreeElements.keys())this.indexRemoved(e);this.view&&this.view.clear()}}class Es extends F{model;databaseId;objectStore;index;refreshObjectStore;view;constructor(e,t,i,s,r,o){super(e,r.name,!1,"indexed-db"),this.model=t,this.databaseId=i,this.objectStore=s,this.index=r,this.refreshObjectStore=o}get itemURL(){return"indexedDB://"+this.databaseId.storageBucket.storageKey+"/"+(this.databaseId.storageBucket.name??"")+"/"+this.databaseId.name+"/"+this.objectStore.name+"/"+this.index.name}markNeedsRefresh(){this.view&&this.view.markNeedsRefresh()}refreshIndex(){this.view&&this.view.refreshData()}update(e,t,i){this.objectStore=e,this.index=t,this.view&&i&&this.view.update(this.objectStore,this.index),this.updateTooltip()}updateTooltip(){const e=[],t=this.index.keyPathString;e.push(vs(ps.keyPathS,{PH1:t})),this.index.unique&&e.push(i.i18n.lockedString("unique")),this.index.multiEntry&&e.push(i.i18n.lockedString("multiEntry")),this.tooltip=e.join("\n")}onselect(e){return super.onselect(e),this.view||(this.view=new Ae(this.model,this.databaseId,this.objectStore,this.index,this.refreshObjectStore)),this.showView(this.view),t.userMetrics.panelShown("indexed-db"),!1}clear(){this.view&&this.view.clear()}}class Ms extends F{domStorage;constructor(e,t){super(e,t.storageKey?o.StorageKeyManager.parseStorageKey(t.storageKey).origin:vs(ps.localFiles),!1,t.isLocalStorage?"local-storage-for-domain":"session-storage-for-domain"),this.domStorage=t;const i=n.Icon.create("table");this.setLeadingIcons([i])}get itemURL(){return"storage://"+this.domStorage.storageKey+"/"+(this.domStorage.isLocalStorage?"local":"session")}onselect(e){return super.onselect(e),t.userMetrics.panelShown("dom-storage"),this.resourcesPanel.showDOMStorage(this.domStorage),!1}onattach(){super.onattach(),this.listItemElement.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this),!0)}handleContextMenuEvent(e){const t=new c.ContextMenu.ContextMenu(e);t.defaultSection().appendItem(vs(ps.clear),(()=>this.domStorage.clear()),{jslogContext:"clear"}),t.show()}}class Rs extends F{extensionStorage;constructor(e,t){super(e,function(e){switch(e){case"session":return vs(ps.extensionSessionStorage);case"local":return vs(ps.extensionLocalStorage);case"sync":return vs(ps.extensionSyncStorage);case"managed":return vs(ps.extensionManagedStorage);default:throw new Error(`Unrecognized storage type: ${e}`)}}(t.storageArea),!1,"extension-storage-for-domain"),this.extensionStorage=t;const i=n.Icon.create("table");this.setLeadingIcons([i])}get storageArea(){return this.extensionStorage.storageArea}get itemURL(){return"extension-storage://"+this.extensionStorage.extensionId+"/"+this.extensionStorage.storageArea}onselect(e){return super.onselect(e),this.resourcesPanel.showExtensionStorage(this.extensionStorage),t.userMetrics.panelShown("extension-storage"),!1}onattach(){super.onattach(),this.listItemElement.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this),!0)}handleContextMenuEvent(e){const t=new c.ContextMenu.ContextMenu(e);t.defaultSection().appendItem(vs(ps.clear),(()=>this.extensionStorage.clear()),{jslogContext:"clear"}),t.show()}}class Ls extends F{extensionId;constructor(e,t,i){super(e,i||t,!0,"extension-storage-for-domain"),this.extensionId=t;const s=n.Icon.create("table");this.setLeadingIcons([s])}get itemURL(){return"extension-storage://"+this.extensionId}}class As extends F{target;cookieDomainInternal;constructor(e,t,i){super(e,i.securityOrigin()||vs(ps.localFiles),!1,"cookies-for-frame"),this.target=t.resourceTreeModel().target(),this.cookieDomainInternal=i.securityOrigin(),this.tooltip=vs(ps.cookiesUsedByFramesFromS,{PH1:this.cookieDomainInternal});const s=n.Icon.create("cookie");a.RelatedIssue.hasThirdPartyPhaseoutCookieIssueForDomain(i.domain())&&(s.name="warning-filled",s.classList.add("warn-icon"),this.tooltip=vs(ps.thirdPartyPhaseout,{PH1:this.cookieDomainInternal})),this.setLeadingIcons([s])}get itemURL(){return"cookies://"+this.cookieDomainInternal}cookieDomain(){return this.cookieDomainInternal}onattach(){super.onattach(),this.listItemElement.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this),!0)}handleContextMenuEvent(e){const t=new c.ContextMenu.ContextMenu(e);t.defaultSection().appendItem(vs(ps.clear),(()=>this.resourcesPanel.clearCookies(this.target,this.cookieDomainInternal)),{jslogContext:"clear"}),t.show()}onselect(e){return super.onselect(e),this.resourcesPanel.showCookies(this.target,this.cookieDomainInternal),t.userMetrics.panelShown(t.UserMetrics.PanelCodes[t.UserMetrics.PanelCodes.cookies]),!1}}class Bs extends c.Widget.VBox{emptyWidget;linkElement;constructor(){super(),this.element.classList.add("storage-view"),this.emptyWidget=new c.EmptyWidget.EmptyWidget("",""),this.linkElement=null,this.emptyWidget.show(this.element)}setText(e){this.emptyWidget.text=e}setHeadline(e){this.emptyWidget.header=e}setLink(e){e&&!this.linkElement&&(this.linkElement=this.emptyWidget.appendLink(e)),!e&&this.linkElement&&this.linkElement.classList.add("hidden"),e&&this.linkElement&&(this.linkElement.setAttribute("href",e),this.linkElement.setAttribute("title",e),this.linkElement.classList.remove("hidden"))}}class Ps{panel;treeElement;treeElementForFrameId;treeElementForTargetId;constructor(e,t){this.panel=e,this.treeElement=t,c.ARIAUtils.setLabel(this.treeElement.listItemNode,"Resources Section"),this.treeElementForFrameId=new Map,this.treeElementForTargetId=new Map;const i=o.FrameManager.FrameManager.instance();i.addEventListener("FrameAddedToTarget",(e=>this.frameAdded(e.data.frame)),this),i.addEventListener("FrameRemoved",(e=>this.frameDetached(e.data.frameId)),this),i.addEventListener("FrameNavigated",(e=>this.frameNavigated(e.data.frame)),this),i.addEventListener("ResourceAdded",(e=>this.resourceAdded(e.data.resource)),this),o.TargetManager.TargetManager.instance().addModelListener(o.ChildTargetManager.ChildTargetManager,"TargetCreated",this.windowOpened,this,{scoped:!0}),o.TargetManager.TargetManager.instance().addModelListener(o.ChildTargetManager.ChildTargetManager,"TargetInfoChanged",this.windowChanged,this,{scoped:!0}),o.TargetManager.TargetManager.instance().addModelListener(o.ChildTargetManager.ChildTargetManager,"TargetDestroyed",this.windowDestroyed,this,{scoped:!0}),o.TargetManager.TargetManager.instance().observeTargets(this,{scoped:!0})}initialize(){const e=o.FrameManager.FrameManager.instance();for(const t of e.getAllFrames()){this.treeElementForFrameId.get(t.id)||this.addFrameAndParents(t);const e=t.resourceTreeModel().target().model(o.ChildTargetManager.ChildTargetManager);if(e)for(const t of e.targetInfos())this.windowOpened({data:t})}}targetAdded(e){e.type()!==o.Target.Type.Worker&&e.type()!==o.Target.Type.ServiceWorker||this.workerAdded(e),e.type()===o.Target.Type.FRAME&&e===e.outermostTarget()&&this.initialize()}async workerAdded(e){const t=e.parentTarget();if(!t)return;const i=t.id(),s=this.treeElementForTargetId.get(i),r=e.id();ws(r);const{targetInfo:o}=await t.targetAgent().invoke_getTargetInfo({targetId:r});s&&o&&s.workerCreated(o)}targetRemoved(e){}addFrameAndParents(e){const t=e.parentFrame();t&&!this.treeElementForFrameId.get(t.id)&&this.addFrameAndParents(t),this.frameAdded(e)}expandFrame(e){if(!e)return!1;let t=this.treeElementForFrameId.get(e.id);return!(!t&&!this.expandFrame(e.parentFrame()))&&(t=this.treeElementForFrameId.get(e.id),!!t&&(t.expand(),!0))}async revealResource(e,t,i){if(!this.expandFrame(e.frame()))return;const s=Os.forResource(e);s&&await s.revealResource(t,i)}revealAndSelectFrame(e){const t=this.treeElementForFrameId.get(e.id);t?.reveal(),t?.select()}frameAdded(e){if(!o.TargetManager.TargetManager.instance().isInScope(e.resourceTreeModel()))return;const t=e.parentFrame(),i=t?this.treeElementForFrameId.get(t.id):this.treeElement;if(!i)return;const s=this.treeElementForFrameId.get(e.id);s&&(this.treeElementForFrameId.delete(e.id),s.parent&&s.parent.removeChild(s));const r=new Ds(this,e);this.treeElementForFrameId.set(e.id,r);const a=e.resourceTreeModel().target().id();this.treeElementForTargetId.get(a)||this.treeElementForTargetId.set(a,r),i.appendChild(r);for(const t of e.resources())this.resourceAdded(t)}frameDetached(e){const t=this.treeElementForFrameId.get(e);t&&(this.treeElementForFrameId.delete(e),t.parent&&t.parent.removeChild(t))}frameNavigated(e){if(!o.TargetManager.TargetManager.instance().isInScope(e.resourceTreeModel()))return;const t=this.treeElementForFrameId.get(e.id);t&&t.frameNavigated(e)}resourceAdded(e){const t=e.frame();if(!t)return;if(!o.TargetManager.TargetManager.instance().isInScope(t.resourceTreeModel()))return;const i=this.treeElementForFrameId.get(t.id);i&&i.appendResource(e)}windowOpened(e){const t=e.data;if(t.openerId&&"page"===t.type){const e=this.treeElementForFrameId.get(t.openerId);e&&(this.treeElementForTargetId.set(t.targetId,e),e.windowOpened(t))}}windowDestroyed(e){const t=e.data,i=this.treeElementForTargetId.get(t);i&&(i.windowDestroyed(t),this.treeElementForTargetId.delete(t))}windowChanged(e){const t=e.data;if(t.openerId&&"page"===t.type){const e=this.treeElementForFrameId.get(t.openerId);e&&e.windowChanged(t)}}reset(){this.treeElement.removeChildren(),this.treeElementForFrameId.clear(),this.treeElementForTargetId.clear()}}class Ds extends F{section;frame;frameId;categoryElements;treeElementForResource;treeElementForWindow;treeElementForWorker;view;constructor(e,t){super(e.panel,"",!1,"frame"),this.section=e,this.frame=t,this.frameId=t.id,this.categoryElements=new Map,this.treeElementForResource=new Map,this.treeElementForWindow=new Map,this.treeElementForWorker=new Map,this.frameNavigated(t),this.view=null}getIconTypeForFrame(e){return e.isOutermostFrame()?e.unreachableUrl()?"frame-crossed":"frame":e.unreachableUrl()?"iframe-crossed":"iframe"}async frameNavigated(e){const t=n.Icon.create(this.getIconTypeForFrame(e));if(e.unreachableUrl()&&t.classList.add("red-icon"),this.setLeadingIcons([t]),this.invalidateChildren(),this.frameId=e.id,this.title!==e.displayName()&&(this.title=e.displayName(),c.ARIAUtils.setLabel(this.listItemElement,this.title),this.parent)){const e=this.parent;e.removeChild(this),e.appendChild(this)}if(this.categoryElements.clear(),this.treeElementForResource.clear(),this.treeElementForWorker.clear(),this.selected?(this.view=d.LegacyWrapper.legacyWrapper(c.Widget.Widget,new m.FrameDetailsView.FrameDetailsReportView(this.frame)),this.showView(this.view)):this.view=null,e.isOutermostFrame()){const t=o.TargetManager.TargetManager.instance().targets();for(const i of t)if(i.type()===o.Target.Type.ServiceWorker&&o.TargetManager.TargetManager.instance().isInScope(i)){const t=i.id();ws(t);const s=e.resourceTreeModel().target().targetAgent(),r=(await s.invoke_getTargetInfo({targetId:t})).targetInfo;this.workerCreated(r)}}}get itemURL(){return this.frame.isOutermostFrame()?"frame://":"frame://"+encodeURI(this.frame.url)}onselect(e){return super.onselect(e),this.view||(this.view=d.LegacyWrapper.legacyWrapper(c.Widget.Widget,new m.FrameDetailsView.FrameDetailsReportView(this.frame))),t.userMetrics.panelShown("frame-details"),this.showView(this.view),this.listItemElement.classList.remove("hovered"),o.OverlayModel.OverlayModel.hideDOMNodeHighlight(),!1}set hovered(e){e?(this.listItemElement.classList.add("hovered"),this.frame.highlight()):(this.listItemElement.classList.remove("hovered"),o.OverlayModel.OverlayModel.hideDOMNodeHighlight())}appendResource(t){const i=t.statusCode();if(i>=301&&i<=303)return;const s=t.resourceType(),r=s.name();let o=s===e.ResourceType.resourceTypes.Document?this:this.categoryElements.get(r);o||(o=new O(this.section.panel,t.resourceType().category().title(),"",vs(ps.resourceDescription),r,"Frames"===r),this.categoryElements.set(s.name(),o),this.appendChild(o,Ds.presentationOrderCompare));const a=new Os(this.section.panel,t);o.appendChild(a,Ds.presentationOrderCompare),this.treeElementForResource.set(t.url,a)}windowOpened(e){const t="opened-windows";let i=this.categoryElements.get(t);if(i||(i=new O(this.section.panel,vs(ps.openedWindows),"",vs(ps.openedWindowsDescription),t),this.categoryElements.set(t,i),this.appendChild(i,Ds.presentationOrderCompare)),!this.treeElementForWindow.get(e.targetId)){const t=new Vs(this.section.panel,e);i.appendChild(t),this.treeElementForWindow.set(e.targetId,t)}}workerCreated(e){const t="service_worker"===e.type?"service-workers":"web-workers",s="service_worker"===e.type?i.i18n.lockedString("Service workers"):vs(ps.webWorkers);let r=this.categoryElements.get(t);if(r||(r=new O(this.section.panel,s,"",vs(ps.workerDescription),t),this.categoryElements.set(t,r),this.appendChild(r,Ds.presentationOrderCompare)),!this.treeElementForWorker.get(e.targetId)){const t=new Us(this.section.panel,e);r.appendChild(t),this.treeElementForWorker.set(e.targetId,t)}}windowChanged(e){const t=this.treeElementForWindow.get(e.targetId);t&&(t.title!==e.title&&(t.title=e.title),t.update(e))}windowDestroyed(e){const t=this.treeElementForWindow.get(e);t&&t.windowClosed()}appendChild(e,t=Ds.presentationOrderCompare){super.appendChild(e,t)}static presentationOrderCompare(e,t){function i(e){return e instanceof O?2:e instanceof Ds?1:3}return i(e)-i(t)||e.titleAsText().localeCompare(t.titleAsText())}}const Fs=new WeakMap;class Os extends F{panel;resource;previewPromise;constructor(e,t){super(e,t.isGenerated?vs(ps.documentNotAvailable):t.displayName,!1,"frame-resource"),this.panel=e,this.resource=t,this.previewPromise=null,this.tooltip=t.url,Fs.set(this.resource,this);const i=n.Icon.create("document","navigator-file-tree-item");i.classList.add("navigator-"+t.resourceType().name()+"-tree-item"),this.setLeadingIcons([i])}static forResource(e){return Fs.get(e)}get itemURL(){return this.resource.url}preparePreview(){if(this.previewPromise)return this.previewPromise;const e=l.PreviewFactory.PreviewFactory.createPreview(this.resource,this.resource.mimeType);return this.previewPromise=e.then((e=>e||new c.EmptyWidget.EmptyWidget("",this.resource.url))),this.previewPromise}onselect(e){return super.onselect(e),this.resource.isGenerated?this.panel.showCategoryView("",vs(ps.documentNotAvailable),vs(ps.theContentOfThisDocumentHasBeen),null):this.panel.scheduleShowView(this.preparePreview()),t.userMetrics.panelShown("frame-resource"),!1}ondblclick(e){return t.InspectorFrontendHost.InspectorFrontendHostInstance.openInNewTab(this.resource.url),!1}onattach(){super.onattach(),this.listItemElement.draggable=!0,this.listItemElement.addEventListener("dragstart",this.ondragstart.bind(this),!1),this.listItemElement.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this),!0)}ondragstart(e){return!!e.dataTransfer&&(e.dataTransfer.setData("text/plain",this.resource.content||""),e.dataTransfer.effectAllowed="copy",!0)}handleContextMenuEvent(e){const t=new c.ContextMenu.ContextMenu(e);t.appendApplicableItems(this.resource),t.show()}async revealResource(e,t){this.revealAndSelect(!0);const i=await this.panel.scheduleShowView(this.preparePreview());i instanceof l.ResourceSourceFrame.ResourceSourceFrame&&"number"==typeof e&&i.revealPosition({lineNumber:e,columnNumber:t},!0)}}class Vs extends F{targetInfo;isWindowClosed;view;constructor(e,t){super(e,t.title||vs(ps.windowWithoutTitle),!1,"window"),this.targetInfo=t,this.isWindowClosed=!1,this.view=null,this.updateIcon(t.canAccessOpener)}updateIcon(e){const t=e?"popup":"frame",i=n.Icon.create(t);this.setLeadingIcons([i])}update(e){e.canAccessOpener!==this.targetInfo.canAccessOpener&&this.updateIcon(e.canAccessOpener),this.targetInfo=e,this.view&&(this.view.setTargetInfo(e),this.view.update())}windowClosed(){this.listItemElement.classList.add("window-closed"),this.isWindowClosed=!0,this.view&&(this.view.setIsWindowClosed(!0),this.view.update())}onselect(e){return super.onselect(e),this.view?this.view.update():this.view=new Ye(this.targetInfo,this.isWindowClosed),this.showView(this.view),t.userMetrics.panelShown("frame-window"),!1}get itemURL(){return this.targetInfo.url}}class Us extends F{targetInfo;view;constructor(e,t){super(e,t.title||t.url||vs(ps.worker),!1,"worker"),this.targetInfo=t,this.view=null;const i=n.Icon.create("gears","navigator-file-tree-item");this.setLeadingIcons([i])}onselect(e){return super.onselect(e),this.view?this.view.update():this.view=new Ze(this.targetInfo),this.showView(this.view),t.userMetrics.panelShown("frame-worker"),!1}get itemURL(){return this.targetInfo.url}}var Ws=Object.freeze({__proto__:null,AppManifestTreeElement:ks,ApplicationPanelSidebar:Ss,BackgroundServiceTreeElement:bs,ClearStorageTreeElement:Is,CookieTreeElement:As,DOMStorageTreeElement:Ms,ExtensionStorageTreeElement:Rs,ExtensionStorageTreeParentElement:Ls,FrameResourceTreeElement:Os,FrameTreeElement:Ds,IDBDatabaseTreeElement:Ts,IDBIndexTreeElement:Es,IDBObjectStoreTreeElement:xs,IndexedDBTreeElement:Cs,ManifestChildTreeElement:fs,ResourcesSection:Ps,ServiceWorkersTreeElement:ys,StorageCategoryView:Bs}),Ns={cssText:`.cookie-preview-widget{padding:2px 6px}.cookie-preview-widget-header{font-weight:bold;user-select:none;white-space:nowrap;margin-bottom:4px;flex:0 0 18px;display:flex;align-items:center}.cookie-preview-widget-header-label{line-height:18px;flex-shrink:0}.cookie-preview-widget-cookie-value{user-select:text;word-break:break-all;flex:1;overflow:auto}.cookie-preview-widget-toggle{margin-left:12px;font-weight:normal;flex-shrink:1}\n/*# sourceURL=${import.meta.resolve("./cookieItemsView.css")} */\n`};const js={showUrlDecoded:"Show URL-decoded",cookies:"Cookies",noCookieSelected:"No cookie selected",selectACookieToPreviewItsValue:"Select a cookie to preview its value",onlyShowCookiesWithAnIssue:"Only show cookies with an issue",onlyShowCookiesWhichHaveAn:"Only show cookies that have an associated issue",clearFilteredCookies:"Clear filtered cookies",clearAllCookies:"Clear all cookies",numberOfCookiesShownInTableS:"Number of cookies shown in table: {PH1}"},Hs=i.i18n.registerUIStrings("panels/application/CookieItemsView.ts",js),_s=i.i18n.getLocalizedString.bind(void 0,Hs);class Ks extends c.Widget.VBox{cookie;showDecodedSetting;toggle;value;constructor(){super(),this.setMinimumSize(230,45),this.cookie=null,this.showDecodedSetting=e.Settings.Settings.instance().createSetting("cookie-view-show-decoded",!1);const t=document.createElement("div");t.classList.add("cookie-preview-widget-header");const i=document.createElement("span");i.classList.add("cookie-preview-widget-header-label"),i.textContent="Cookie Value",t.appendChild(i),this.contentElement.appendChild(t);const s=c.UIUtils.CheckboxLabel.create(_s(js.showUrlDecoded),this.showDecodedSetting.get(),void 0,"show-url-decoded");s.title=_s(js.showUrlDecoded),s.classList.add("cookie-preview-widget-toggle"),s.checkboxElement.addEventListener("click",(()=>this.showDecoded(!this.showDecodedSetting.get()))),t.appendChild(s),this.toggle=s;const r=document.createElement("div");r.classList.add("cookie-preview-widget-cookie-value"),r.textContent="",r.addEventListener("dblclick",this.handleDblClickOnCookieValue.bind(this)),this.value=r,this.contentElement.classList.add("cookie-preview-widget"),this.contentElement.setAttribute("jslog",`${p.section("cookie-preview")}`),this.contentElement.appendChild(r)}showDecoded(e){this.cookie&&(this.showDecodedSetting.set(e),this.toggle.checkboxElement.checked=e,this.updatePreview())}updatePreview(){this.cookie?this.value.textContent=this.showDecodedSetting.get()?decodeURIComponent(this.cookie.value()):this.cookie.value():this.value.textContent=""}setCookie(e){this.cookie=e,this.updatePreview()}handleDblClickOnCookieValue(e){e.preventDefault();const t=document.createRange();t.selectNode(this.value);const i=window.getSelection();i&&(i.removeAllRanges(),i.addRange(t))}}class qs extends Li{model;cookieDomain;totalSize;cookiesTable;splitWidget;previewPanel;previewWidget;emptyWidget;onlyIssuesFilterUI;allCookies;shownCookies;selectedCookie;constructor(e,t){super(_s(js.cookies),"cookiesPanel"),this.registerRequiredCSS(Ns),this.element.classList.add("storage-view"),this.element.setAttribute("jslog",`${p.pane("cookies-data")}`),this.model=e,this.cookieDomain=t,this.totalSize=0,this.cookiesTable=new P.CookiesTable.CookiesTable(!1,this.saveCookie.bind(this),this.refreshItems.bind(this),this.handleCookieSelected.bind(this),this.deleteCookie.bind(this)),this.cookiesTable.setMinimumSize(0,50),this.splitWidget=new c.SplitWidget.SplitWidget(!1,!0,"cookie-items-split-view-state"),this.splitWidget.show(this.element),this.previewPanel=new c.Widget.VBox,this.previewPanel.element.setAttribute("jslog",`${p.pane("preview").track({resize:!0})}`);const i=this.previewPanel.element.createChild("div","preview-panel-resizer");this.splitWidget.setMainWidget(this.cookiesTable),this.splitWidget.setSidebarWidget(this.previewPanel),this.splitWidget.installResizer(i),this.previewWidget=new Ks,this.emptyWidget=new c.EmptyWidget.EmptyWidget(_s(js.noCookieSelected),_s(js.selectACookieToPreviewItsValue)),this.emptyWidget.show(this.previewPanel.contentElement),this.onlyIssuesFilterUI=new c.Toolbar.ToolbarCheckbox(_s(js.onlyShowCookiesWithAnIssue),_s(js.onlyShowCookiesWhichHaveAn),(()=>{this.updateWithCookies(this.allCookies)}),"only-show-cookies-with-issues"),this.appendToolbarItem(this.onlyIssuesFilterUI),this.allCookies=[],this.shownCookies=[],this.selectedCookie=null,this.setCookiesDomain(e,t)}setCookiesDomain(e,t){this.model.removeEventListener("CookieListUpdated",this.onCookieListUpdate,this),this.model=e,this.cookieDomain=t,this.refreshItems(),this.model.addEventListener("CookieListUpdated",this.onCookieListUpdate,this)}showPreview(e){e!==this.selectedCookie&&(this.selectedCookie=e,e?(this.emptyWidget.detach(),this.previewWidget.setCookie(e),this.previewWidget.show(this.previewPanel.contentElement)):(this.previewWidget.detach(),this.emptyWidget.show(this.previewPanel.contentElement)))}handleCookieSelected(){const e=this.cookiesTable.selectedCookie();this.setCanDeleteSelected(Boolean(e)),this.showPreview(e)}async saveCookie(e,t){return t&&e.key()!==t.key()&&await this.model.deleteCookie(t),await this.model.saveCookie(e)}deleteCookie(e,t){this.model.deleteCookie(e).then(t)}updateWithCookies(t){this.allCookies=t,this.totalSize=t.reduce(((e,t)=>e+t.size()),0);const i=e.ParsedURL.ParsedURL.fromString(this.cookieDomain),s=i?i.host:"";this.cookiesTable.setCookieDomain(s),this.shownCookies=this.filter(t,(e=>`${e.name()} ${e.value()} ${e.domain()}`)),this.hasFilter()?(this.setDeleteAllTitle(_s(js.clearFilteredCookies)),this.setDeleteAllGlyph("filter-clear")):(this.setDeleteAllTitle(_s(js.clearAllCookies)),this.setDeleteAllGlyph("clear-list")),this.cookiesTable.setCookies(this.shownCookies,this.model.getCookieToBlockedReasonsMap()),c.ARIAUtils.alert(_s(js.numberOfCookiesShownInTableS,{PH1:this.shownCookies.length})),this.setCanFilter(!0),this.setCanDeleteAll(this.shownCookies.length>0),this.setCanDeleteSelected(Boolean(this.cookiesTable.selectedCookie())),this.cookiesTable.selectedCookie()||this.showPreview(null)}filter(e,t){return super.filter(e,t).filter((e=>!this.onlyIssuesFilterUI.checked()||e instanceof o.Cookie.Cookie&&a.RelatedIssue.hasIssues(e)))}deleteAllItems(){this.showPreview(null),this.model.deleteCookies(this.shownCookies)}deleteSelectedItem(){const e=this.cookiesTable.selectedCookie();e&&(this.showPreview(null),this.model.deleteCookie(e))}onCookieListUpdate(){this.model.getCookiesForDomain(this.cookieDomain).then(this.updateWithCookies.bind(this))}refreshItems(){this.model.getCookiesForDomain(this.cookieDomain,!0).then(this.updateWithCookies.bind(this))}}var zs=Object.freeze({__proto__:null,CookieItemsView:qs});const Gs={domStorageItems:"DOM Storage Items",domStorageItemsCleared:"DOM Storage Items cleared",domStorageItemDeleted:"The storage item was deleted."},$s=i.i18n.registerUIStrings("panels/application/DOMStorageItemsView.ts",Gs),Xs=i.i18n.getLocalizedString.bind(void 0,$s);class Js extends ji{domStorage;eventListeners;constructor(e){super(Xs(Gs.domStorageItems),"dom-storage",!0),this.domStorage=e,e.storageKey&&this.setStorageKey(e.storageKey),this.element.classList.add("storage-view","table"),this.showPreview(null,null),this.eventListeners=[],this.setStorage(e)}createPreview(t,i){const s=`${this.domStorage.isLocalStorage?"localstorage":"sessionstorage"}://${t}`,r=x.StaticContentProvider.StaticContentProvider.fromString(s,e.ResourceType.resourceTypes.XHR,i);return l.PreviewFactory.PreviewFactory.createPreview(r,"text/plain")}setStorage(t){e.EventTarget.removeEventListeners(this.eventListeners),this.domStorage=t;const i=t.isLocalStorage?"local-storage-data":"session-storage-data";this.element.setAttribute("jslog",`${p.pane().context(i)}`),t.storageKey&&this.setStorageKey(t.storageKey),this.eventListeners=[this.domStorage.addEventListener("DOMStorageItemsCleared",this.domStorageItemsCleared,this),this.domStorage.addEventListener("DOMStorageItemRemoved",this.domStorageItemRemoved,this),this.domStorage.addEventListener("DOMStorageItemAdded",this.domStorageItemAdded,this),this.domStorage.addEventListener("DOMStorageItemUpdated",this.domStorageItemUpdated,this)],this.refreshItems()}domStorageItemsCleared(){this.isShowing()&&this.itemsCleared()}itemsCleared(){super.itemsCleared(),c.ARIAUtils.alert(Xs(Gs.domStorageItemsCleared))}domStorageItemRemoved(e){this.isShowing()&&this.itemRemoved(e.data.key)}itemRemoved(e){super.itemRemoved(e),c.ARIAUtils.alert(Xs(Gs.domStorageItemDeleted))}domStorageItemAdded(e){this.isShowing()&&this.itemAdded(e.data.key,e.data.value)}domStorageItemUpdated(e){this.isShowing()&&this.itemUpdated(e.data.key,e.data.value)}refreshItems(){this.#re()}async#re(){const e=await this.domStorage.getItems();if(!e)return;const t=this.filter(e.map((e=>({key:e[0],value:e[1]}))),(e=>`${e.key} ${e.value}`));this.showItems(t)}deleteAllItems(){this.domStorage.clear(),this.domStorageItemsCleared()}removeItem(e){this.domStorage?.removeItem(e)}setItem(e,t){this.domStorage?.setItem(e,t)}}var Qs=Object.freeze({__proto__:null,DOMStorageItemsView:Js});const Ys={extensionStorageItems:"Extension Storage Items",extensionStorageItemsCleared:"Extension Storage Items cleared"},Zs=i.i18n.registerUIStrings("panels/application/ExtensionStorageItemsView.ts",Ys),er=i.i18n.getLocalizedString.bind(void 0,Zs);class tr extends ji{#oe;extensionStorageItemsDispatcher;constructor(t,i){super(er(Ys.extensionStorageItems),"extension-storage",!0,i),this.element.setAttribute("jslog",`${p.pane().context("extension-storage-data")}`),this.element.classList.add("storage-view","table"),this.extensionStorageItemsDispatcher=new e.ObjectWrapper.ObjectWrapper,this.setStorage(t)}get#ae(){return"managed"!==this.#oe.storageArea}parseValue(e){try{return D.parse(e)}catch{return e}}removeItem(e){this.#oe.removeItem(e).then((()=>{this.refreshItems()}))}setItem(e,t){this.#oe.setItem(e,this.parseValue(t)).then((()=>{this.refreshItems(),this.extensionStorageItemsDispatcher.dispatchEventToListeners("ItemEdited")}))}createPreview(t,i){const s="extension-storage://"+this.#oe.extensionId+"/"+this.#oe.storageArea+"/preview/"+t,r=x.StaticContentProvider.StaticContentProvider.fromString(s,e.ResourceType.resourceTypes.XHR,i);return l.PreviewFactory.PreviewFactory.createPreview(r,"text/plain")}setStorage(e){this.#oe=e,this.editable=this.#ae,this.refreshItems()}#ne(){this.isShowing()&&(this.itemsCleared(),c.ARIAUtils.alert(er(Ys.extensionStorageItemsCleared)))}deleteSelectedItem(){this.#ae&&this.deleteSelectedItem()}refreshItems(){this.#re()}async#re(){const e=await this.#oe.getItems();if(!e)return;const t=this.filter(Object.entries(e).map((([e,t])=>({key:e,value:"string"==typeof t?t:JSON.stringify(t)}))),(e=>`${e.key} ${e.value}`));this.showItems(t),this.extensionStorageItemsDispatcher.dispatchEventToListeners("ItemsRefreshed")}deleteAllItems(){this.#ae&&this.#oe.clear().then((()=>{this.#ne()}),(()=>{throw new Error("Unable to clear storage.")}))}}var ir=Object.freeze({__proto__:null,ExtensionStorageItemsView:tr}),sr={cssText:`.resources-toolbar{border-top:1px solid var(--sys-color-divider);background-color:var(--sys-color-cdt-base-container)}.top-resources-toolbar{border-bottom:1px solid var(--sys-color-divider);background-color:var(--sys-color-cdt-base-container)}.resources.panel .status{float:right;height:16px;margin-top:1px;margin-left:4px;line-height:1em}.storage-view{display:flex;overflow:hidden}.storage-view .data-grid:not(.inline){border:none;flex:auto}.storage-view .storage-table-error{color:var(--sys-color-error);font-size:24px;font-weight:bold;padding:10px;display:flex;align-items:center;justify-content:center}.storage-view.query{padding:2px 0;overflow:hidden auto}.storage-view .filter-bar{border-top:none;border-bottom:1px solid var(--sys-color-divider)}.database-query-group-messages{overflow-y:auto}.database-query-prompt-container{position:relative;padding:1px 22px 1px 24px;min-height:16px}.database-query-prompt{white-space:pre-wrap}.prompt-icon{position:absolute;display:block;left:7px;top:9px;margin-top:-7px;user-select:none}.database-user-query .prompt-icon{margin-top:-10px}.database-query-prompt-container .prompt-icon{top:6px}.database-user-query{position:relative;border-bottom:1px solid var(--sys-color-divider);padding:1px 22px 1px 24px;min-height:16px;flex-shrink:0}.database-user-query:focus-visible{background-color:var(--sys-color-state-focus-highlight)}.database-query-text{color:var(--sys-color-primary-bright);user-select:text}.database-query-result{position:relative;padding:1px 22px;min-height:16px;margin-left:-22px;padding-right:0}.database-query-result.error{color:var(--sys-color-token-property-special);user-select:text}.database-query-result.error .prompt-icon{margin-top:-9px}.resources-sidebar{padding:0;overflow-x:auto;background-color:var(--sys-color-cdt-base-container)}\n/*# sourceURL=${import.meta.resolve("./resourcesPanel.css")} */\n`};let rr;class or extends c.Panel.PanelWithSidebar{resourcesLastSelectedItemSetting;visibleView;pendingViewPromise;categoryView;storageViews;storageViewToolbar;domStorageView;extensionStorageView;cookieView;sidebar;constructor(){super("resources"),this.registerRequiredCSS(sr),this.resourcesLastSelectedItemSetting=e.Settings.Settings.instance().createSetting("resources-last-selected-element-path",[]),this.visibleView=null,this.pendingViewPromise=null,this.categoryView=null;const t=new c.Widget.VBox;t.setMinimumSize(100,0),this.storageViews=t.element.createChild("div","vbox flex-auto"),this.storageViewToolbar=t.element.createChild("devtools-toolbar","resources-toolbar"),this.splitWidget().setMainWidget(t),this.domStorageView=null,this.extensionStorageView=null,this.cookieView=null,this.sidebar=new Ss(this),this.sidebar.show(this.panelSidebarElement())}static instance(e={forceNew:null}){const{forceNew:t}=e;return rr&&!t||(rr=new or),rr}static shouldCloseOnReset(e){return[l.ResourceSourceFrame.ResourceSourceFrame,l.ImageView.ImageView,l.FontView.FontView,Li].some((t=>e instanceof t))}static async showAndGetSidebar(){return await c.ViewManager.ViewManager.instance().showView("resources"),or.instance().sidebar}focus(){this.sidebar.focus()}lastSelectedItemPath(){return this.resourcesLastSelectedItemSetting.get()}setLastSelectedItemPath(e){this.resourcesLastSelectedItemSetting.set(e)}resetView(){this.visibleView&&or.shouldCloseOnReset(this.visibleView)&&this.showView(null)}showView(e){this.pendingViewPromise=null,this.visibleView!==e&&(this.visibleView&&this.visibleView.detach(),e&&e.show(this.storageViews),this.visibleView=e,this.storageViewToolbar.removeToolbarItems(),this.storageViewToolbar.classList.toggle("hidden",!0),e instanceof c.View.SimpleView&&e.toolbarItems().then((e=>{e.map((e=>this.storageViewToolbar.appendToolbarItem(e))),this.storageViewToolbar.classList.toggle("hidden",!e.length)})))}async scheduleShowView(e){this.pendingViewPromise=e;const t=await e;return this.pendingViewPromise!==e?null:(this.showView(t),t)}showCategoryView(e,t,i,r){this.categoryView||(this.categoryView=new Bs),this.categoryView.element.setAttribute("jslog",`${p.pane().context(s.StringUtilities.toKebabCase(e))}`),this.categoryView.setHeadline(t),this.categoryView.setText(i),this.categoryView.setLink(r),this.showView(this.categoryView)}showDOMStorage(e){e&&(this.domStorageView?this.domStorageView.setStorage(e):this.domStorageView=new Js(e),this.showView(this.domStorageView))}showExtensionStorage(e){e&&(this.extensionStorageView?this.extensionStorageView.setStorage(e):this.extensionStorageView=new tr(e),this.showView(this.extensionStorageView))}showCookies(e,t){const i=e.model(o.CookieModel.CookieModel);i&&(this.cookieView?this.cookieView.setCookiesDomain(i,t):this.cookieView=new qs(i,t),this.showView(this.cookieView))}clearCookies(e,t){const i=e.model(o.CookieModel.CookieModel);i&&i.clear(t).then((()=>{this.cookieView&&this.cookieView.refreshItems()}))}}var ar=Object.freeze({__proto__:null,AttemptViewWithFilterRevealer:class{async reveal(e){(await or.showAndGetSidebar()).showPreloadingAttemptViewWithFilter(e)}},FrameDetailsRevealer:class{async reveal(e){(await or.showAndGetSidebar()).showFrame(e)}},ResourceRevealer:class{async reveal(e){const t=await or.showAndGetSidebar();await t.showResource(e)}},ResourcesPanel:or,RuleSetViewRevealer:class{async reveal(e){(await or.showAndGetSidebar()).showPreloadingRuleSetView(e)}}});export{H as AppManifestView,Ws as ApplicationPanelSidebar,X as BackgroundServiceModel,se as BackgroundServiceView,de as BounceTrackingMitigationsTreeElement,zs as CookieItemsView,Qs as DOMStorageItemsView,ge as DOMStorageModel,ir as ExtensionStorageItemsView,me as ExtensionStorageModel,Ce as IndexedDBModel,Pe as IndexedDBViews,Fe as InterestGroupStorageModel,je as InterestGroupStorageView,ze as InterestGroupTreeElement,Hi as KeyValueStorageItemsView,et as OpenedWindowDetailsView,It as PreloadingTreeElement,mt as PreloadingView,Mt as ReportingApiReportsView,Pt as ReportingApiView,ar as ResourcesPanel,Gt as ServiceWorkerCacheViews,oi as ServiceWorkerUpdateCycleView,gi as ServiceWorkersView,Si as SharedStorageEventsView,Gi as SharedStorageItemsView,Ii as SharedStorageListTreeElement,xi as SharedStorageModel,Xi as SharedStorageTreeElement,ts as StorageBucketsTreeElement,Ai as StorageItemsView,ds as StorageView,us as TrustTokensTreeElement};
