{"version": 3, "file": "Updates.js", "names": ["_sdkRuntimeVersions", "data", "require", "_fs", "_interopRequireDefault", "_getenv", "_path", "_resolveFrom", "_semver", "AndroidVersion", "_interopRequireWildcard", "IOSVersion", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "FINGERPRINT_RUNTIME_VERSION_SENTINEL", "exports", "getExpoUpdatesPackageVersion", "projectRoot", "expoUpdatesPackageJsonPath", "resolveFrom", "silent", "fs", "existsSync", "packageJson", "JSON", "parse", "readFileSync", "version", "getUpdateUrl", "config", "updates", "url", "getAppVersion", "getNativeVersion", "platform", "getVersion", "buildNumber", "getBuildNumber", "versionCode", "getVersionCode", "Error", "getRuntimeVersionNullableAsync", "getRuntimeVersionAsync", "boolish", "console", "log", "runtimeVersion", "policy", "resolveRuntimeVersionPolicyAsync", "sdkVersion", "getRuntimeVersionForSDKVersion", "getSDKVersion", "getUpdatesEnabled", "enabled", "undefined", "getUpdatesUseEmbeddedUpdate", "useEmbeddedUpdate", "getUpdatesTimeout", "fallbackToCacheTimeout", "getUpdatesCheckOnLaunch", "expoUpdatesPackageVersion", "checkAutomatically", "semver", "gte", "getUpdatesCodeSigningCertificate", "codeSigningCertificatePath", "codeSigningCertificate", "finalPath", "path", "join", "getUpdatesCodeSigningMetadata", "codeSigningMetadata", "getUpdatesCodeSigningMetadataStringified", "metadata", "stringify", "getUpdatesRequestHeaders", "requestHeaders", "getUpdatesRequestHeadersStringified", "getDisableAntiBrickingMeasures", "disableAntiBrickingMeasures"], "sources": ["../../src/utils/Updates.ts"], "sourcesContent": ["import { Android, ExpoConfig, IOS } from '@expo/config-types';\nimport { getRuntimeVersionForSDKVersion } from '@expo/sdk-runtime-versions';\nimport fs from 'fs';\nimport { boolish } from 'getenv';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\nimport semver from 'semver';\n\nimport * as AndroidVersion from '../android/Version';\nimport * as IOSVersion from '../ios/Version';\n\nexport type ExpoConfigUpdates = Pick<\n  ExpoConfig,\n  'sdkVersion' | 'runtimeVersion' | 'updates' | 'slug'\n>;\n\nexport const FINGERPRINT_RUNTIME_VERSION_SENTINEL = 'file:fingerprint';\n\nexport function getExpoUpdatesPackageVersion(projectRoot: string): string | null {\n  const expoUpdatesPackageJsonPath = resolveFrom.silent(projectRoot, 'expo-updates/package.json');\n  if (!expoUpdatesPackageJsonPath || !fs.existsSync(expoUpdatesPackageJsonPath)) {\n    return null;\n  }\n  const packageJson = JSON.parse(fs.readFileSync(expoUpdatesPackageJsonPath, 'utf8'));\n  return packageJson.version;\n}\n\nexport function getUpdateUrl(config: Pick<ExpoConfigUpdates, 'updates'>): string | null {\n  return config.updates?.url ?? null;\n}\n\nexport function getAppVersion(config: Pick<ExpoConfig, 'version'>): string {\n  return config.version ?? '1.0.0';\n}\n\nexport function getNativeVersion(\n  config: Pick<ExpoConfig, 'version'> & {\n    android?: Pick<Android, 'versionCode'>;\n    ios?: Pick<IOS, 'buildNumber'>;\n  },\n  platform: 'android' | 'ios'\n): string {\n  const version = IOSVersion.getVersion(config);\n  switch (platform) {\n    case 'ios': {\n      const buildNumber = IOSVersion.getBuildNumber(config);\n      return `${version}(${buildNumber})`;\n    }\n    case 'android': {\n      const versionCode = AndroidVersion.getVersionCode(config);\n      return `${version}(${versionCode})`;\n    }\n    default: {\n      throw new Error(\n        `\"${platform}\" is not a supported platform. Choose either \"ios\" or \"android\".`\n      );\n    }\n  }\n}\n\nexport async function getRuntimeVersionNullableAsync(\n  ...[projectRoot, config, platform]: Parameters<typeof getRuntimeVersionAsync>\n): Promise<string | null> {\n  try {\n    return await getRuntimeVersionAsync(projectRoot, config, platform);\n  } catch (e) {\n    if (boolish('EXPO_DEBUG', false)) {\n      console.log(e);\n    }\n    return null;\n  }\n}\n\nexport async function getRuntimeVersionAsync(\n  projectRoot: string,\n  config: Pick<ExpoConfig, 'version' | 'runtimeVersion' | 'sdkVersion'> & {\n    android?: Pick<Android, 'versionCode' | 'runtimeVersion'>;\n    ios?: Pick<IOS, 'buildNumber' | 'runtimeVersion'>;\n  },\n  platform: 'android' | 'ios'\n): Promise<string | null> {\n  const runtimeVersion = config[platform]?.runtimeVersion ?? config.runtimeVersion;\n  if (!runtimeVersion) {\n    return null;\n  }\n\n  if (typeof runtimeVersion === 'string') {\n    if (runtimeVersion === FINGERPRINT_RUNTIME_VERSION_SENTINEL) {\n      throw new Error(\n        `${FINGERPRINT_RUNTIME_VERSION_SENTINEL} is a reserved value for runtime version. To use a fingerprint runtime version, use the \"fingerprint\" runtime version policy.`\n      );\n    }\n    return runtimeVersion;\n  } else if (!runtimeVersion.policy) {\n    throw new Error(\n      `\"${runtimeVersion}\" is not a valid runtime version. Only a string or a runtime version policy is supported.`\n    );\n  } else if (runtimeVersion.policy === 'fingerprint') {\n    return FINGERPRINT_RUNTIME_VERSION_SENTINEL;\n  } else {\n    return await resolveRuntimeVersionPolicyAsync(runtimeVersion.policy, config, platform);\n  }\n}\n\nexport async function resolveRuntimeVersionPolicyAsync(\n  policy: 'appVersion' | 'nativeVersion' | 'sdkVersion',\n  config: Pick<ExpoConfig, 'version' | 'sdkVersion'> & {\n    android?: Pick<Android, 'versionCode'>;\n    ios?: Pick<IOS, 'buildNumber'>;\n  },\n  platform: 'android' | 'ios'\n): Promise<string> {\n  if (policy === 'appVersion') {\n    return getAppVersion(config);\n  } else if (policy === 'nativeVersion') {\n    return getNativeVersion(config, platform);\n  } else if (policy === 'sdkVersion') {\n    if (!config.sdkVersion) {\n      throw new Error(\"An SDK version must be defined when using the 'sdkVersion' runtime policy.\");\n    }\n    return getRuntimeVersionForSDKVersion(config.sdkVersion);\n  } else {\n    // fingerprint is resolvable only at build time (not in config plugin).\n    throw new Error(`\"${policy}\" is not a valid runtime version policy type.`);\n  }\n}\n\nexport function getSDKVersion(config: Pick<ExpoConfigUpdates, 'sdkVersion'>): string | null {\n  return typeof config.sdkVersion === 'string' ? config.sdkVersion : null;\n}\n\nexport function getUpdatesEnabled(config: Pick<ExpoConfigUpdates, 'updates'>): boolean {\n  // allow override of enabled property\n  if (config.updates?.enabled !== undefined) {\n    return config.updates.enabled;\n  }\n\n  return getUpdateUrl(config) !== null;\n}\n\nexport function getUpdatesUseEmbeddedUpdate(config: Pick<ExpoConfigUpdates, 'updates'>): boolean {\n  if (config.updates?.useEmbeddedUpdate !== undefined) {\n    return config.updates.useEmbeddedUpdate;\n  }\n\n  return true;\n}\n\nexport function getUpdatesTimeout(config: Pick<ExpoConfigUpdates, 'updates'>): number {\n  return config.updates?.fallbackToCacheTimeout ?? 0;\n}\n\nexport function getUpdatesCheckOnLaunch(\n  config: Pick<ExpoConfigUpdates, 'updates'>,\n  expoUpdatesPackageVersion?: string | null\n): 'NEVER' | 'ERROR_RECOVERY_ONLY' | 'ALWAYS' | 'WIFI_ONLY' {\n  if (config.updates?.checkAutomatically === 'ON_ERROR_RECOVERY') {\n    // native 'ERROR_RECOVERY_ONLY' option was only introduced in 0.11.x\n    if (expoUpdatesPackageVersion && semver.gte(expoUpdatesPackageVersion, '0.11.0')) {\n      return 'ERROR_RECOVERY_ONLY';\n    }\n    return 'NEVER';\n  } else if (config.updates?.checkAutomatically === 'ON_LOAD') {\n    return 'ALWAYS';\n  } else if (config.updates?.checkAutomatically === 'WIFI_ONLY') {\n    return 'WIFI_ONLY';\n  } else if (config.updates?.checkAutomatically === 'NEVER') {\n    return 'NEVER';\n  }\n  return 'ALWAYS';\n}\n\nexport function getUpdatesCodeSigningCertificate(\n  projectRoot: string,\n  config: Pick<ExpoConfigUpdates, 'updates'>\n): string | undefined {\n  const codeSigningCertificatePath = config.updates?.codeSigningCertificate;\n  if (!codeSigningCertificatePath) {\n    return undefined;\n  }\n\n  const finalPath = path.join(projectRoot, codeSigningCertificatePath);\n  if (!fs.existsSync(finalPath)) {\n    throw new Error(`File not found at \\`updates.codeSigningCertificate\\` path: ${finalPath}`);\n  }\n\n  return fs.readFileSync(finalPath, 'utf8');\n}\n\nexport function getUpdatesCodeSigningMetadata(\n  config: Pick<ExpoConfigUpdates, 'updates'>\n): NonNullable<ExpoConfigUpdates['updates']>['codeSigningMetadata'] {\n  return config.updates?.codeSigningMetadata;\n}\n\nexport function getUpdatesCodeSigningMetadataStringified(\n  config: Pick<ExpoConfigUpdates, 'updates'>\n): string | undefined {\n  const metadata = getUpdatesCodeSigningMetadata(config);\n  if (!metadata) {\n    return undefined;\n  }\n\n  return JSON.stringify(metadata);\n}\n\nexport function getUpdatesRequestHeaders(\n  config: Pick<ExpoConfigUpdates, 'updates'>\n): NonNullable<ExpoConfigUpdates['updates']>['requestHeaders'] {\n  return config.updates?.requestHeaders;\n}\n\nexport function getUpdatesRequestHeadersStringified(\n  config: Pick<ExpoConfigUpdates, 'updates'>\n): string | undefined {\n  const metadata = getUpdatesRequestHeaders(config);\n  if (!metadata) {\n    return undefined;\n  }\n\n  return JSON.stringify(metadata);\n}\n\nexport function getDisableAntiBrickingMeasures(\n  config: Pick<ExpoConfigUpdates, 'updates'>\n): boolean | undefined {\n  return config.updates?.disableAntiBrickingMeasures;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AACA,SAAAA,oBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,mBAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,IAAA;EAAA,MAAAF,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAC,GAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,QAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,OAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,MAAA;EAAA,MAAAL,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAI,KAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,aAAA;EAAA,MAAAN,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAK,YAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,QAAA;EAAA,MAAAP,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAM,OAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAQ,eAAA;EAAA,MAAAR,IAAA,GAAAS,uBAAA,CAAAR,OAAA;EAAAO,cAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAU,WAAA;EAAA,MAAAV,IAAA,GAAAS,uBAAA,CAAAR,OAAA;EAAAS,UAAA,YAAAA,CAAA;IAAA,OAAAV,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6C,SAAAW,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAH,wBAAAG,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAjB,uBAAAS,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAI,UAAA,GAAAJ,CAAA,KAAAK,OAAA,EAAAL,CAAA;AAOtC,MAAMmB,oCAAoC,GAAAC,OAAA,CAAAD,oCAAA,GAAG,kBAAkB;AAE/D,SAASE,4BAA4BA,CAACC,WAAmB,EAAiB;EAC/E,MAAMC,0BAA0B,GAAGC,sBAAW,CAACC,MAAM,CAACH,WAAW,EAAE,2BAA2B,CAAC;EAC/F,IAAI,CAACC,0BAA0B,IAAI,CAACG,aAAE,CAACC,UAAU,CAACJ,0BAA0B,CAAC,EAAE;IAC7E,OAAO,IAAI;EACb;EACA,MAAMK,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACJ,aAAE,CAACK,YAAY,CAACR,0BAA0B,EAAE,MAAM,CAAC,CAAC;EACnF,OAAOK,WAAW,CAACI,OAAO;AAC5B;AAEO,SAASC,YAAYA,CAACC,MAA0C,EAAiB;EACtF,OAAOA,MAAM,CAACC,OAAO,EAAEC,GAAG,IAAI,IAAI;AACpC;AAEO,SAASC,aAAaA,CAACH,MAAmC,EAAU;EACzE,OAAOA,MAAM,CAACF,OAAO,IAAI,OAAO;AAClC;AAEO,SAASM,gBAAgBA,CAC9BJ,MAGC,EACDK,QAA2B,EACnB;EACR,MAAMP,OAAO,GAAGlC,UAAU,CAAD,CAAC,CAAC0C,UAAU,CAACN,MAAM,CAAC;EAC7C,QAAQK,QAAQ;IACd,KAAK,KAAK;MAAE;QACV,MAAME,WAAW,GAAG3C,UAAU,CAAD,CAAC,CAAC4C,cAAc,CAACR,MAAM,CAAC;QACrD,OAAO,GAAGF,OAAO,IAAIS,WAAW,GAAG;MACrC;IACA,KAAK,SAAS;MAAE;QACd,MAAME,WAAW,GAAG/C,cAAc,CAAD,CAAC,CAACgD,cAAc,CAACV,MAAM,CAAC;QACzD,OAAO,GAAGF,OAAO,IAAIW,WAAW,GAAG;MACrC;IACA;MAAS;QACP,MAAM,IAAIE,KAAK,CACb,IAAIN,QAAQ,kEACd,CAAC;MACH;EACF;AACF;AAEO,eAAeO,8BAA8BA,CAClD,GAAG,CAACxB,WAAW,EAAEY,MAAM,EAAEK,QAAQ,CAA4C,EACrD;EACxB,IAAI;IACF,OAAO,MAAMQ,sBAAsB,CAACzB,WAAW,EAAEY,MAAM,EAAEK,QAAQ,CAAC;EACpE,CAAC,CAAC,OAAOvC,CAAC,EAAE;IACV,IAAI,IAAAgD,iBAAO,EAAC,YAAY,EAAE,KAAK,CAAC,EAAE;MAChCC,OAAO,CAACC,GAAG,CAAClD,CAAC,CAAC;IAChB;IACA,OAAO,IAAI;EACb;AACF;AAEO,eAAe+C,sBAAsBA,CAC1CzB,WAAmB,EACnBY,MAGC,EACDK,QAA2B,EACH;EACxB,MAAMY,cAAc,GAAGjB,MAAM,CAACK,QAAQ,CAAC,EAAEY,cAAc,IAAIjB,MAAM,CAACiB,cAAc;EAChF,IAAI,CAACA,cAAc,EAAE;IACnB,OAAO,IAAI;EACb;EAEA,IAAI,OAAOA,cAAc,KAAK,QAAQ,EAAE;IACtC,IAAIA,cAAc,KAAKhC,oCAAoC,EAAE;MAC3D,MAAM,IAAI0B,KAAK,CACb,GAAG1B,oCAAoC,+HACzC,CAAC;IACH;IACA,OAAOgC,cAAc;EACvB,CAAC,MAAM,IAAI,CAACA,cAAc,CAACC,MAAM,EAAE;IACjC,MAAM,IAAIP,KAAK,CACb,IAAIM,cAAc,2FACpB,CAAC;EACH,CAAC,MAAM,IAAIA,cAAc,CAACC,MAAM,KAAK,aAAa,EAAE;IAClD,OAAOjC,oCAAoC;EAC7C,CAAC,MAAM;IACL,OAAO,MAAMkC,gCAAgC,CAACF,cAAc,CAACC,MAAM,EAAElB,MAAM,EAAEK,QAAQ,CAAC;EACxF;AACF;AAEO,eAAec,gCAAgCA,CACpDD,MAAqD,EACrDlB,MAGC,EACDK,QAA2B,EACV;EACjB,IAAIa,MAAM,KAAK,YAAY,EAAE;IAC3B,OAAOf,aAAa,CAACH,MAAM,CAAC;EAC9B,CAAC,MAAM,IAAIkB,MAAM,KAAK,eAAe,EAAE;IACrC,OAAOd,gBAAgB,CAACJ,MAAM,EAAEK,QAAQ,CAAC;EAC3C,CAAC,MAAM,IAAIa,MAAM,KAAK,YAAY,EAAE;IAClC,IAAI,CAAClB,MAAM,CAACoB,UAAU,EAAE;MACtB,MAAM,IAAIT,KAAK,CAAC,4EAA4E,CAAC;IAC/F;IACA,OAAO,IAAAU,oDAA8B,EAACrB,MAAM,CAACoB,UAAU,CAAC;EAC1D,CAAC,MAAM;IACL;IACA,MAAM,IAAIT,KAAK,CAAC,IAAIO,MAAM,+CAA+C,CAAC;EAC5E;AACF;AAEO,SAASI,aAAaA,CAACtB,MAA6C,EAAiB;EAC1F,OAAO,OAAOA,MAAM,CAACoB,UAAU,KAAK,QAAQ,GAAGpB,MAAM,CAACoB,UAAU,GAAG,IAAI;AACzE;AAEO,SAASG,iBAAiBA,CAACvB,MAA0C,EAAW;EACrF;EACA,IAAIA,MAAM,CAACC,OAAO,EAAEuB,OAAO,KAAKC,SAAS,EAAE;IACzC,OAAOzB,MAAM,CAACC,OAAO,CAACuB,OAAO;EAC/B;EAEA,OAAOzB,YAAY,CAACC,MAAM,CAAC,KAAK,IAAI;AACtC;AAEO,SAAS0B,2BAA2BA,CAAC1B,MAA0C,EAAW;EAC/F,IAAIA,MAAM,CAACC,OAAO,EAAE0B,iBAAiB,KAAKF,SAAS,EAAE;IACnD,OAAOzB,MAAM,CAACC,OAAO,CAAC0B,iBAAiB;EACzC;EAEA,OAAO,IAAI;AACb;AAEO,SAASC,iBAAiBA,CAAC5B,MAA0C,EAAU;EACpF,OAAOA,MAAM,CAACC,OAAO,EAAE4B,sBAAsB,IAAI,CAAC;AACpD;AAEO,SAASC,uBAAuBA,CACrC9B,MAA0C,EAC1C+B,yBAAyC,EACiB;EAC1D,IAAI/B,MAAM,CAACC,OAAO,EAAE+B,kBAAkB,KAAK,mBAAmB,EAAE;IAC9D;IACA,IAAID,yBAAyB,IAAIE,iBAAM,CAACC,GAAG,CAACH,yBAAyB,EAAE,QAAQ,CAAC,EAAE;MAChF,OAAO,qBAAqB;IAC9B;IACA,OAAO,OAAO;EAChB,CAAC,MAAM,IAAI/B,MAAM,CAACC,OAAO,EAAE+B,kBAAkB,KAAK,SAAS,EAAE;IAC3D,OAAO,QAAQ;EACjB,CAAC,MAAM,IAAIhC,MAAM,CAACC,OAAO,EAAE+B,kBAAkB,KAAK,WAAW,EAAE;IAC7D,OAAO,WAAW;EACpB,CAAC,MAAM,IAAIhC,MAAM,CAACC,OAAO,EAAE+B,kBAAkB,KAAK,OAAO,EAAE;IACzD,OAAO,OAAO;EAChB;EACA,OAAO,QAAQ;AACjB;AAEO,SAASG,gCAAgCA,CAC9C/C,WAAmB,EACnBY,MAA0C,EACtB;EACpB,MAAMoC,0BAA0B,GAAGpC,MAAM,CAACC,OAAO,EAAEoC,sBAAsB;EACzE,IAAI,CAACD,0BAA0B,EAAE;IAC/B,OAAOX,SAAS;EAClB;EAEA,MAAMa,SAAS,GAAGC,eAAI,CAACC,IAAI,CAACpD,WAAW,EAAEgD,0BAA0B,CAAC;EACpE,IAAI,CAAC5C,aAAE,CAACC,UAAU,CAAC6C,SAAS,CAAC,EAAE;IAC7B,MAAM,IAAI3B,KAAK,CAAC,8DAA8D2B,SAAS,EAAE,CAAC;EAC5F;EAEA,OAAO9C,aAAE,CAACK,YAAY,CAACyC,SAAS,EAAE,MAAM,CAAC;AAC3C;AAEO,SAASG,6BAA6BA,CAC3CzC,MAA0C,EACwB;EAClE,OAAOA,MAAM,CAACC,OAAO,EAAEyC,mBAAmB;AAC5C;AAEO,SAASC,wCAAwCA,CACtD3C,MAA0C,EACtB;EACpB,MAAM4C,QAAQ,GAAGH,6BAA6B,CAACzC,MAAM,CAAC;EACtD,IAAI,CAAC4C,QAAQ,EAAE;IACb,OAAOnB,SAAS;EAClB;EAEA,OAAO9B,IAAI,CAACkD,SAAS,CAACD,QAAQ,CAAC;AACjC;AAEO,SAASE,wBAAwBA,CACtC9C,MAA0C,EACmB;EAC7D,OAAOA,MAAM,CAACC,OAAO,EAAE8C,cAAc;AACvC;AAEO,SAASC,mCAAmCA,CACjDhD,MAA0C,EACtB;EACpB,MAAM4C,QAAQ,GAAGE,wBAAwB,CAAC9C,MAAM,CAAC;EACjD,IAAI,CAAC4C,QAAQ,EAAE;IACb,OAAOnB,SAAS;EAClB;EAEA,OAAO9B,IAAI,CAACkD,SAAS,CAACD,QAAQ,CAAC;AACjC;AAEO,SAASK,8BAA8BA,CAC5CjD,MAA0C,EACrB;EACrB,OAAOA,MAAM,CAACC,OAAO,EAAEiD,2BAA2B;AACpD", "ignoreList": []}