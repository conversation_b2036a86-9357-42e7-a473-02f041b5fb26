import{render as t,html as e}from"../../lit/lit.js";import*as o from"../../visual_logging/visual_logging.js";var r=`:host{display:inline-flex}:host(.hidden){display:none}slot{display:inline-flex;box-sizing:border-box;height:14px;line-height:13px;padding:0 6px;font-size:var(--override-adorner-font-size,8.5px);color:var(--override-adorner-text-color,var(--sys-color-primary));background-color:var(--override-adorner-background-color,var(--sys-color-cdt-base-container));border:1px solid var(--override-adorner-border-color,var(--sys-color-tonal-outline));border-radius:10px;position:relative;&:hover::after,\n  &:active::before{content:"";height:100%;width:100%;border-radius:inherit;position:absolute;top:0;left:0}&:hover::after{background-color:var(--sys-color-state-hover-on-subtle)}&:active::before{background-color:var(--sys-color-state-ripple-neutral-on-subtle)}}:host(:focus-visible) slot{outline:2px solid var(--sys-color-state-focus-ring);outline-offset:2px;z-index:999}:host([aria-pressed="true"]) slot{color:var(--override-adorner-active-text-color,var(--sys-color-on-primary));background-color:var(--override-adorner-active-background-color,var(--sys-color-primary));border:1px solid var(--override-adorner-active-background-color,var(--sys-color-primary));&:hover::after{background-color:var(--sys-color-state-hover-on-prominent)}&:active::before{background-color:var(--sys-color-state-ripple-primary)}}::slotted(*){height:10px}\n/*# sourceURL=${import.meta.resolve("./adorner.css")} */\n`;class s extends HTMLElement{name="";#t=this.attachShadow({mode:"open"});#e=!1;#o;#r;#s;#i;set data(t){this.name=t.name,this.#i=t.jslogContext,t.content&&(this.#s?.remove(),this.append(t.content),this.#s=t.content),this.#a()}cloneNode(t){const e=super.cloneNode(t);return e.data={name:this.name,content:this.#s,jslogContext:this.#i},e}connectedCallback(){this.getAttribute("aria-label")||this.setAttribute("aria-label",this.name),this.#i&&!this.getAttribute("jslog")&&this.setAttribute("jslog",`${o.adorner(this.#i)}`)}isActive(){return"true"===this.getAttribute("aria-pressed")}toggle(t){if(!this.#e)return;const e=void 0===t?!this.isActive():t;this.setAttribute("aria-pressed",Boolean(e).toString()),this.setAttribute("aria-label",(e?this.#r:this.#o)||this.name)}show(){this.classList.remove("hidden")}hide(){this.classList.add("hidden")}addInteraction(t,e){const{isToggle:r=!1,shouldPropagateOnKeydown:s=!1,ariaLabelDefault:i,ariaLabelActive:a}=e;this.#e=r,this.#o=i,this.#r=a,this.setAttribute("aria-label",i),this.#i&&this.setAttribute("jslog",`${o.adorner(this.#i).track({click:!0})}`),r&&(this.addEventListener("click",(()=>{this.toggle()})),this.toggle(!1)),this.addEventListener("click",t),this.classList.add("clickable"),this.setAttribute("role","button"),this.tabIndex=0,this.addEventListener("keydown",(t=>{"Enter"!==t.code&&"Space"!==t.code||(this.click(),s||t.stopPropagation())}))}#a(){t(e`<style>${r}</style><slot></slot>`,this.#t,{host:this})}}customElements.define("devtools-adorner",s);var i=Object.freeze({__proto__:null,Adorner:s});export{i as Adorner};
