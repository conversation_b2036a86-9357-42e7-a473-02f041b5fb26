import*as e from"../../ui/legacy/legacy.js";import*as t from"../../core/common/common.js";import*as i from"../../core/host/host.js";import*as o from"../../core/i18n/i18n.js";import*as s from"../../core/root/root.js";import*as r from"../../models/workspace_diff/workspace_diff.js";import*as n from"../utils/utils.js";import{PanelUtils as a}from"../utils/utils.js";import*as d from"../../third_party/diff/diff.js";import*as c from"../../ui/components/diff_view/diff_view.js";import*as l from"../../ui/visual_logging/visual_logging.js";import*as f from"../../core/platform/platform.js";import*as h from"../../models/workspace/workspace.js";import*as u from"../../ui/components/icon_button/icon_button.js";import*as p from"../snippets/snippets.js";import*as m from"../../models/persistence/persistence.js";import"../../ui/components/buttons/buttons.js";import*as g from"../../ui/lit/lit.js";var v={cssText:`.tree-outline li{min-height:20px}devtools-icon{color:var(--icon-file-default)}.navigator-sm-script-tree-item devtools-icon,\n.navigator-script-tree-item devtools-icon,\n.navigator-snippet-tree-item devtools-icon{color:var(--icon-file-script)}.navigator-sm-stylesheet-tree-item devtools-icon,\n.navigator-stylesheet-tree-item devtools-icon{color:var(--icon-file-styles)}.navigator-image-tree-item devtools-icon{color:var(--icon-file-image)}.navigator-font-tree-item devtools-icon{color:var(--icon-file-font)}.tree-outline li:hover:not(.selected) .selection{display:block;& devtools-icon{color:var(--icon-default-hover)}}@media (forced-colors: active){li,\n  devtools-icon{forced-color-adjust:none;color:ButtonText!important}}\n/*# sourceURL=${import.meta.resolve("./changesSidebar.css")} */\n`};const C={sFromSourceMap:"{PH1} (from source map)"},S=o.i18n.registerUIStrings("panels/changes/ChangesSidebar.ts",C),b=o.i18n.getLocalizedString.bind(void 0,S);class w extends(t.ObjectWrapper.eventMixin(e.Widget.Widget)){treeoutline;treeElements;workspaceDiff;constructor(t){super(),this.treeoutline=new e.TreeOutline.TreeOutlineInShadow("NavigationTree"),this.treeoutline.registerRequiredCSS(v),this.treeoutline.setFocusable(!1),this.treeoutline.hideOverflow(),this.treeoutline.setComparator(((e,t)=>f.StringUtilities.compare(e.titleAsText(),t.titleAsText()))),this.treeoutline.addEventListener(e.TreeOutline.Events.ElementSelected,this.selectionChanged,this),e.ARIAUtils.markAsTablist(this.treeoutline.contentElement),this.element.appendChild(this.treeoutline.element),this.element.setAttribute("jslog",`${l.pane("sidebar").track({resize:!0})}`),this.treeElements=new Map,this.workspaceDiff=t,this.workspaceDiff.modifiedUISourceCodes().forEach(this.addUISourceCode.bind(this)),this.workspaceDiff.addEventListener("ModifiedStatusChanged",this.uiSourceCodeMofiedStatusChanged,this)}selectedUISourceCode(){return this.treeoutline.selectedTreeElement?this.treeoutline.selectedTreeElement.uiSourceCode:null}selectionChanged(){this.dispatchEventToListeners("SelectedUISourceCodeChanged")}uiSourceCodeMofiedStatusChanged(e){e.data.isModified?this.addUISourceCode(e.data.uiSourceCode):this.removeUISourceCode(e.data.uiSourceCode)}removeUISourceCode(e){const t=this.treeElements.get(e);if(this.treeElements.delete(e),this.treeoutline.selectedTreeElement===t){const e=t.previousSibling||t.nextSibling;e?e.select(!0):(t.deselect(),this.selectionChanged())}t&&(this.treeoutline.removeChild(t),t.dispose()),0===this.treeoutline.rootElement().childCount()&&this.treeoutline.setFocusable(!1)}addUISourceCode(e){const t=new y(e);this.treeElements.set(e,t),this.treeoutline.setFocusable(!0),this.treeoutline.appendChild(t),this.treeoutline.selectedTreeElement||t.select(!0)}}class y extends e.TreeOutline.TreeElement{uiSourceCode;eventListeners;constructor(t){super(),this.uiSourceCode=t,this.listItemElement.classList.add("navigator-"+t.contentType().name()+"-tree-item"),e.ARIAUtils.markAsTab(this.listItemElement);let i="document";p.ScriptSnippetFileSystem.isSnippetsUISourceCode(this.uiSourceCode)&&(i="snippet");const o=u.Icon.create(i);this.setLeadingIcons([o]),this.eventListeners=[t.addEventListener(h.UISourceCode.Events.TitleChanged,this.updateTitle,this),t.addEventListener(h.UISourceCode.Events.WorkingCopyChanged,this.updateTitle,this),t.addEventListener(h.UISourceCode.Events.WorkingCopyCommitted,this.updateTitle,this)],this.updateTitle()}updateTitle(){let e=this.uiSourceCode.displayName();this.uiSourceCode.isDirty()&&(e="*"+e),this.title=e;let t=this.uiSourceCode.url();this.uiSourceCode.contentType().isFromSourceMap()&&(t=b(C.sFromSourceMap,{PH1:this.uiSourceCode.displayName()})),this.tooltip=t}dispose(){t.EventTarget.removeEventListeners(this.eventListeners)}}var D=Object.freeze({__proto__:null,ChangesSidebar:w,UISourceCodeTreeElement:y}),k={cssText:`[slot="main"]{flex-direction:column;display:flex}[slot="sidebar"]{overflow:auto}.diff-container{flex:1;overflow:auto;& .widget:first-child{height:100%}.combined-diff-view{padding:var(--sys-size-6)}}:focus.selected{background-color:var(--sys-color-tonal-container);color:var(--sys-color-on-tonal-container)}.changes-toolbar{background-color:var(--sys-color-cdt-base-container);border-top:1px solid var(--sys-color-divider)}\n/*# sourceURL=${import.meta.resolve("./changesView.css")} */\n`},I={cssText:`.combined-diff-view{display:flex;flex-direction:column;gap:var(--sys-size-5);height:100%;background-color:var(--sys-color-surface3);overflow:auto;details{flex-shrink:0;summary{background-color:var(--sys-color-surface1);border-radius:var(--sys-shape-corner-medium-small);height:var(--sys-size-12);padding:var(--sys-size-3);font:var(--sys-typescale-body5-bold);display:flex;justify-content:space-between;gap:var(--sys-size-2);.summary-left{display:flex;align-items:center;flex-grow:0;.file-name-link{padding-left:var(--sys-size-5);width:100%;text-overflow:ellipsis;overflow:hidden;text-wrap-mode:nowrap;border:none;background:none;font:inherit;&:hover{color:var(--sys-color-primary);text-decoration:underline;cursor:pointer}}devtools-icon{transform:rotate(270deg)}devtools-file-source-icon{flex-shrink:0}}.summary-right{flex-shrink:0;.copied{padding-right:var(--sys-size-4);font:var(--sys-typescale-body5-regular)}}&::marker{content:''}}.diff-view-container{overflow-x:auto;background-color:var(--sys-color-cdt-base-container);border-bottom-left-radius:var(--sys-shape-corner-medium-small);border-bottom-right-radius:var(--sys-shape-corner-medium-small)}&[open]{summary{border-radius:0;border-top-left-radius:var(--sys-shape-corner-medium-small);border-top-right-radius:var(--sys-shape-corner-medium-small);devtools-icon{transform:rotate(0deg)}}}}}\n/*# sourceURL=${import.meta.resolve("./combinedDiffView.css")} */\n`};const{html:T}=g,U={copied:"Copied to clipboard"},E=o.i18n.registerUIStrings("panels/changes/CombinedDiffView.ts",U),x=o.i18n.getLocalizedString.bind(void 0,E);class L extends e.Widget.Widget{#e;#t=[];#i={};#o;constructor(e,t=(e,t,i)=>{g.render(T`
      <div class="combined-diff-view">
        ${e.singleDiffViewInputs.map((e=>function(e){const{fileName:t,fileUrl:i,mimeType:o,icon:s,diff:r,copied:n,onCopy:a,onFileNameClick:d}=e;return T`
    <details open>
      <summary>
        <div class="summary-left">
          <devtools-icon class="drop-down-icon" .name=${"arrow-drop-down"}></devtools-icon>
          ${s}
          <button class="file-name-link" @click=${()=>d(i)}>${t}</button>
        </div>
        <div class="summary-right">
          ${n?T`<span class="copied">${x(U.copied)}</span>`:T`
            <devtools-button
              title=${"Copy"}
              .size=${"SMALL"}
              .iconName=${"copy"}
              .jslogContext=${"combined-diff-view.copy"}
              .variant=${"icon"}
              @click=${()=>a(i)}></devtools-button>
          `}
        </div>
      </summary>
      <div class='diff-view-container'>
        <devtools-diff-view
          .data=${{diff:r,mimeType:o}}>
        </devtools-diff-view>
      </div>
    </details>
  `}(e)))}
      </div>
    `,i,{host:i})}){super(!1,!1,e),this.registerRequiredCSS(I),this.#o=t}wasShown(){super.wasShown(),this.#e?.addEventListener("ModifiedStatusChanged",this.#s,this),this.#r()}willHide(){this.#e?.removeEventListener("ModifiedStatusChanged",this.#s,this)}set workspaceDiff(e){this.#e=e,this.#r()}async#n(e){const t=this.#t.find((t=>t.url()===e));if(!t)return;const o=t.workingCopyContentData();o.isTextContent&&(i.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(o.text),this.#i[e]=!0,this.requestUpdate(),setTimeout((()=>{delete this.#i[e],this.requestUpdate()}),1e3))}#a(e){const i=this.#t.find((t=>t.url()===e));t.Revealer.reveal(i)}async#r(){if(!this.#e)return;const e=this.#t,t=this.#e.modifiedUISourceCodes();e.filter((e=>!t.includes(e))).forEach((e=>this.#e?.unsubscribeFromDiffChange(e,this.requestUpdate,this)));t.filter((t=>!e.includes(t))).forEach((e=>this.#e?.subscribeToDiffChange(e,this.requestUpdate,this))),this.#t=t,this.isShowing()&&this.requestUpdate()}async#s(){this.#e&&await this.#r()}async performUpdate(){const e=(await Promise.all(this.#t.map((async e=>{const t=await(this.#e?.requestDiff(e));if(t&&0!==t.diff.length)return{diff:t.diff,uiSourceCode:e}})))).filter((e=>!!e)).map((({uiSourceCode:e,diff:t})=>{let i=e.fullDisplayName();const o=m.Persistence.PersistenceImpl.instance().fileSystem(e);return o&&(i=[o.project().displayName(),...m.FileSystemWorkspaceBinding.FileSystemWorkspaceBinding.relativePath(o)].join("/")),{diff:t,fileName:`${e.isDirty()?"*":""}${i}`,fileUrl:e.url(),mimeType:e.mimeType(),icon:n.PanelUtils.getIconForSourceFile(e,{width:18,height:18}),copied:this.#i[e.url()],onCopy:this.#n.bind(this),onFileNameClick:this.#a.bind(this)}})).sort(((e,t)=>f.StringUtilities.compare(e.fileName,t.fileName)));this.#o({singleDiffViewInputs:e},void 0,this.contentElement)}}var F=Object.freeze({__proto__:null,CombinedDiffView:L});const M="https://developer.chrome.com/docs/devtools/changes",j={noChanges:"No changes yet",changesViewDescription:"On this page you can track code changes made within DevTools.",noTextualDiff:"No textual diff available",binaryDataDescription:"The changes tab doesn't show binary data changes",sInsertions:"{n, plural, =1 {# insertion (+)} other {# insertions (+)}}",sDeletions:"{n, plural, =1 {# deletion (-)} other {# deletions (-)}}",copy:"Copy"},W=o.i18n.registerUIStrings("panels/changes/ChangesView.ts",j),A=o.i18n.getLocalizedString.bind(void 0,W),V=o.i18n.getLazilyComputedLocalizedString.bind(void 0,W);class $ extends e.Widget.VBox{emptyWidget;workspaceDiff;changesSidebar;selectedUISourceCode;#d;#c;diffContainer;toolbar;diffStats;diffView;combinedDiffView;constructor(){super(!0),this.registerRequiredCSS(k),this.element.setAttribute("jslog",`${l.panel("changes").track({resize:!0})}`);const t=new e.SplitWidget.SplitWidget(!0,!1),i=new e.Widget.VBox;t.setMainWidget(i),t.show(this.contentElement),this.emptyWidget=new e.EmptyWidget.EmptyWidget("",""),this.emptyWidget.show(i.element),this.workspaceDiff=r.WorkspaceDiff.workspaceDiff(),this.changesSidebar=new w(this.workspaceDiff),this.changesSidebar.addEventListener("SelectedUISourceCodeChanged",this.selectedUISourceCodeChanged,this),t.setSidebarWidget(this.changesSidebar),this.selectedUISourceCode=null,this.diffContainer=i.element.createChild("div","diff-container"),e.ARIAUtils.markAsTabpanel(this.diffContainer),z()?(this.combinedDiffView=new L,this.combinedDiffView.workspaceDiff=this.workspaceDiff,this.combinedDiffView.show(this.diffContainer)):(this.diffView=this.diffContainer.appendChild(new c.DiffView.DiffView),this.diffContainer.addEventListener("click",(e=>this.click(e)))),this.toolbar=i.element.createChild("devtools-toolbar","changes-toolbar"),this.toolbar.setAttribute("jslog",`${l.toolbar()}`),this.toolbar.appendToolbarItem(e.Toolbar.Toolbar.createActionButton("changes.revert")),z()||(this.diffStats=new e.Toolbar.ToolbarText(""),this.toolbar.appendToolbarItem(this.diffStats),this.toolbar.appendToolbarItem(new e.Toolbar.ToolbarSeparator),this.toolbar.appendToolbarItem(e.Toolbar.Toolbar.createActionButton("changes.copy",{label:V(j.copy)}))),this.hideDiff(A(j.noChanges),A(j.changesViewDescription),M),this.selectedUISourceCodeChanged()}selectedUISourceCodeChanged(){this.revealUISourceCode(this.changesSidebar.selectedUISourceCode()),e.ActionRegistry.ActionRegistry.instance().getAction("changes.copy").setEnabled(this.selectedUISourceCode?.contentType()===t.ResourceType.resourceTypes.Stylesheet)}revert(){const e=this.selectedUISourceCode;e&&this.workspaceDiff.revertToOriginal(e)}async copy(){const e=this.selectedUISourceCode;if(!e)return;const t=await this.workspaceDiff.requestDiff(e);if(!t||t?.diff.length<2)return;const o=await a.formatCSSChangesFromDiff(t.diff);i.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(o)}click(e){if(this.selectedUISourceCode)for(const i of e.composedPath()){if(!(i instanceof HTMLElement))continue;const o=i.ownerDocument.getSelection();if(o?.toString())break;if(i.classList.contains("diff-line-content")&&i.hasAttribute("data-line-number")){let o=Number(i.dataset.lineNumber)-1;this.#d&&(o=this.#d.formattedToOriginal(o,0)[0]),t.Revealer.reveal(this.selectedUISourceCode.uiLocation(o,0),!1),e.consume(!0);break}if(i.classList.contains("diff-listing"))break}}revealUISourceCode(e){this.selectedUISourceCode!==e&&(this.selectedUISourceCode&&this.workspaceDiff.unsubscribeFromDiffChange(this.selectedUISourceCode,this.refreshDiff,this),e&&this.isShowing()&&this.workspaceDiff.subscribeToDiffChange(e,this.refreshDiff,this),this.selectedUISourceCode=e,this.refreshDiff())}wasShown(){e.Context.Context.instance().setFlavor($,this),super.wasShown(),this.refreshDiff()}willHide(){super.willHide(),e.Context.Context.instance().setFlavor($,null)}async refreshDiff(){if(!this.isShowing())return;if(!this.selectedUISourceCode)return void this.renderDiffRows();const e=this.selectedUISourceCode;if(!e.contentType().isTextType())return void this.hideDiff(A(j.noTextualDiff),A(j.binaryDataDescription));const t=await this.workspaceDiff.requestDiff(e);this.selectedUISourceCode===e&&(this.#d=t?.formattedCurrentMapping,this.renderDiffRows(t?.diff))}hideDiff(e,t,i){this.diffStats?.setText(""),this.toolbar.setEnabled(!1),this.diffContainer.style.display="none",this.emptyWidget.header=e,this.emptyWidget.text=t,i&&!this.#c?this.#c=this.emptyWidget.appendLink(i):i&&this.#c?(this.#c.setAttribute("href",i),this.#c.setAttribute("title",i)):!i&&this.#c&&(this.#c.remove(),this.#c=void 0),this.emptyWidget.showWidget()}renderDiffRows(e){if(!e||1===e.length&&e[0][0]===d.Diff.Operation.Equal)this.hideDiff(A(j.noChanges),A(j.changesViewDescription),M);else{this.diffStats?.setText(function(e){const t=e.reduce(((e,t)=>e+(t[0]===d.Diff.Operation.Insert?t[1].length:0)),0),i=e.reduce(((e,t)=>e+(t[0]===d.Diff.Operation.Delete?t[1].length:0)),0),o=A(j.sDeletions,{n:i});return`${A(j.sInsertions,{n:t})}, ${o}`}(e)),this.toolbar.setEnabled(!0),this.emptyWidget.hideWidget();const t=this.selectedUISourceCode.mimeType();this.diffContainer.style.display="block",this.diffView&&(this.diffView.data={diff:e,mimeType:t})}}}function z(){return Boolean(s.Runtime.hostConfig.devToolsFreestyler?.patching)}var _=Object.freeze({__proto__:null,ActionDelegate:class{handleAction(e,t){const i=e.flavor($);if(null===i)return!1;switch(t){case"changes.revert":return i.revert(),!0;case"changes.copy":return i.copy(),!0}return!1}},ChangesView:$});export{D as ChangesSidebar,_ as ChangesView,F as CombinedDiffView};
