{"version": 3, "file": "obj.js", "names": ["get", "obj", "key", "branches", "split", "current", "branch", "shift", "undefined"], "sources": ["../../src/utils/obj.ts"], "sourcesContent": ["/** `lodash.get` */\nexport function get(obj: any, key: string): any {\n  const branches = key.split('.');\n  let current: any = obj;\n  let branch: string | undefined;\n  while ((branch = branches.shift())) {\n    if (!(branch in current)) {\n      return undefined;\n    }\n    current = current[branch];\n  }\n  return current;\n}\n"], "mappings": ";;;;;;AAAA;AACO,SAASA,GAAGA,CAACC,GAAQ,EAAEC,GAAW,EAAO;EAC9C,MAAMC,QAAQ,GAAGD,GAAG,CAACE,KAAK,CAAC,GAAG,CAAC;EAC/B,IAAIC,OAAY,GAAGJ,GAAG;EACtB,IAAIK,MAA0B;EAC9B,OAAQA,MAAM,GAAGH,QAAQ,CAACI,KAAK,CAAC,CAAC,EAAG;IAClC,IAAI,EAAED,MAAM,IAAID,OAAO,CAAC,EAAE;MACxB,OAAOG,SAAS;IAClB;IACAH,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC;EAC3B;EACA,OAAOD,OAAO;AAChB", "ignoreList": []}