/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict-local
 * @format
 */

import type { EventReporter } from "../types/EventReporter";
import type { CreateCustomMessageHandlerFn } from "./CustomMessageHandler";
import type { Page } from "./types";

import WS from "ws";

// should be aligned with
// https://github.com/facebook/react-native-devtools-frontend/blob/3d17e0fd462dc698db34586697cce2371b25e0d3/front_end/ui/legacy/components/utils/TargetDetachedDialog.ts#L50-L64
declare export const WS_CLOSE_REASON: {
  PAGE_NOT_FOUND: "[PAGE_NOT_FOUND] Debugger page not found",
  CONNECTION_LOST: "[CONNECTION_LOST] Connection lost to corresponding device",
  RECREATING_DEVICE: "[RECREATING_DEVICE] Recreating device connection",
  NEW_DEBUGGER_OPENED: "[NEW_DEBUGGER_OPENED] New debugger opened for the same app instance",
};

export type DeviceOptions = $ReadOnly<{
  id: string,
  name: string,
  app: string,
  socket: WS,
  projectRoot: string,
  eventReporter: ?EventReporter,
  createMessageMiddleware: ?CreateCustomMessageHandlerFn,
  deviceRelativeBaseUrl: URL,
  serverRelativeBaseUrl: URL,
  isProfilingBuild: boolean,
}>;

/**
 * Device class represents single device connection to Inspector Proxy. Each device
 * can have multiple inspectable pages.
 */
declare export default class Device {
  constructor(deviceOptions: DeviceOptions): void;
  /**
   * Used to recreate the device connection if there is a device ID collision.
   * 1. Checks if the same device is attempting to reconnect for the same app.
   * 2. If not, close both the device and debugger socket.
   * 3. If the debugger connection can be reused, close the device socket only.
   *
   * This hack attempts to allow users to reload the app, either as result of a
   * crash, or manually reloading, without having to restart the debugger.
   */
  dangerouslyRecreateDevice(deviceOptions: DeviceOptions): void;
  getName(): string;
  getApp(): string;
  getPagesList(): $ReadOnlyArray<Page>;
  // Handles new debugger connection to this device:
  // 1. Sends connect event to device
  // 2. Forwards all messages from the debugger to device as wrappedEvent
  // 3. Sends disconnect event to device when debugger connection socket closes.
  handleDebuggerConnection(
    socket: WS,
    pageId: string,
    $$PARAM_2$$: $ReadOnly<{
      debuggerRelativeBaseUrl: URL,
      userAgent: string | null,
    }>
  ): void;
  dangerouslyGetSocket(): WS;
}
