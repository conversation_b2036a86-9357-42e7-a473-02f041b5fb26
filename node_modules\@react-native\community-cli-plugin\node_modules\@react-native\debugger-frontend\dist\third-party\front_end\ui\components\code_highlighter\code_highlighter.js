import*as t from"../../../third_party/codemirror.next/codemirror.next.js";var e={cssText:`.token-variable{color:var(--sys-color-token-variable)}.token-property{color:var(--sys-color-token-property)}.token-type{color:var(--sys-color-token-type)}.token-variable-special{color:var(--sys-color-token-variable-special)}.token-definition{color:var(--sys-color-token-definition)}.token-builtin{color:var(--sys-color-token-builtin)}.token-number{color:var(--sys-color-token-number)}.token-string{color:var(--sys-color-token-string)}.token-string-special{color:var(--sys-color-token-string-special)}.token-atom{color:var(--sys-color-token-atom)}.token-keyword{color:var(--sys-color-token-keyword)}.token-comment{color:var(--sys-color-token-comment)}.token-meta{color:var(--sys-color-token-meta)}.token-invalid{color:var(--sys-color-error)}.token-tag{color:var(--sys-color-token-tag)}.token-attribute{color:var(--sys-color-token-attribute)}.token-attribute-value{color:var(--sys-color-token-attribute-value)}.token-inserted{color:var(--sys-color-token-inserted)}.token-deleted{color:var(--sys-color-token-deleted)}.token-heading{color:var(--sys-color-token-variable-special);font-weight:bold}.token-link{color:var(--sys-color-token-variable-special);text-decoration:underline}.token-strikethrough{text-decoration:line-through}.token-strong{font-weight:bold}.token-emphasis{font-style:italic}\n/*# sourceURL=${import.meta.resolve("./codeHighlighter.css")} */\n`};const a=t.tags,s=t.HighlightStyle.define([{tag:a.variableName,class:"token-variable"},{tag:a.definition(a.variableName),class:"token-definition"},{tag:a.propertyName,class:"token-property"},{tag:[a.typeName,a.className,a.namespace,a.macroName],class:"token-type"},{tag:[a.special(a.name),a.constant(a.className)],class:"token-variable-special"},{tag:a.standard(a.variableName),class:"token-builtin"},{tag:[a.number,a.literal,a.unit],class:"token-number"},{tag:a.string,class:"token-string"},{tag:[a.special(a.string),a.regexp,a.escape],class:"token-string-special"},{tag:[a.atom,a.labelName,a.bool],class:"token-atom"},{tag:a.keyword,class:"token-keyword"},{tag:[a.comment,a.quote],class:"token-comment"},{tag:a.meta,class:"token-meta"},{tag:a.invalid,class:"token-invalid"},{tag:a.tagName,class:"token-tag"},{tag:a.attributeName,class:"token-attribute"},{tag:a.attributeValue,class:"token-attribute-value"},{tag:a.inserted,class:"token-inserted"},{tag:a.deleted,class:"token-deleted"},{tag:a.heading,class:"token-heading"},{tag:a.link,class:"token-link"},{tag:a.strikethrough,class:"token-strikethrough"},{tag:a.strong,class:"token-strong"},{tag:a.emphasis,class:"token-emphasis"}]);async function r(e,a){const s=await o(a);let r;return r=s?s.language.parser.parse(e):new t.Tree(t.NodeType.none,[],[],e.length),new n(e,r)}async function o(e){switch(e){case"application/javascript":case"application/ecmascript":case"application/x-ecmascript":case"application/x-javascript":case"text/ecmascript":case"text/javascript1.0":case"text/javascript1.1":case"text/javascript1.2":case"text/javascript1.3":case"text/javascript1.4":case"text/javascript1.5":case"text/jscript":case"text/livescript ":case"text/x-ecmascript":case"text/x-javascript":case"text/javascript":case"text/jsx":return t.javascript.javascript({jsx:!0});case"text/typescript":return t.javascript.javascript({typescript:!0});case"text/typescript-jsx":return t.javascript.javascript({typescript:!0,jsx:!0});case"text/css":return t.css.css();case"text/html":return t.html.html({autoCloseTags:!1,selfClosingTags:!0});case"application/xml":case"application/xhtml+xml":case"image/svg+xml":return(await t.xml()).xml();case"application/wasm":return(await t.wast()).wast();case"text/x-c++src":return(await t.cpp()).cpp();case"text/x-go":return new t.LanguageSupport(await t.go());case"text/x-java":return(await t.java()).java();case"text/x-kotlin":return new t.LanguageSupport(await t.kotlin());case"application/json":case"application/manifest+json":{const e=t.javascript.javascriptLanguage.configure({top:"SingleExpression"});return new t.LanguageSupport(e)}case"application/x-httpd-php":return(await t.php()).php();case"text/x-python":return(await t.python()).python();case"text/markdown":return(await t.markdown()).markdown();case"text/x-sh":return new t.LanguageSupport(await t.shell());case"text/x-coffeescript":return new t.LanguageSupport(await t.coffeescript());case"text/x-clojure":return new t.LanguageSupport(await t.clojure());case"application/vnd.dart":return new t.LanguageSupport(await t.dart());case"text/x-gss":return new t.LanguageSupport(await t.gss());case"text/x-less":return(await t.less()).less();case"text/x-sass":return(await t.sass()).sass({indented:!0});case"text/x-scala":return new t.LanguageSupport(await t.scala());case"text/x-scss":return(await t.sass()).sass({indented:!1});case"text/x.angular":return(await t.angular()).angular();case"text/x.svelte":return(await t.svelte()).svelte();case"text/x.vue":return(await t.vue()).vue();default:return null}}class n{code;tree;constructor(t,e){this.code=t,this.tree=e}highlight(t){this.highlightRange(0,this.code.length,t)}highlightRange(e,a,r){let o=e;const n=(t,e)=>{t>o&&(r(this.code.slice(o,t),e),o=t)};t.highlightTree(this.tree,s,((t,e,a)=>{n(t,""),n(e,a)}),e,a),n(a,"")}}var c=Object.freeze({__proto__:null,CodeHighlighter:n,create:r,highlightNode:async function(t,e){const a=t.textContent||"",s=await r(a,e);t.removeChildren(),s.highlight(((e,a)=>{let s=document.createTextNode(e);if(a){const t=document.createElement("span");t.className=a,t.appendChild(s),s=t}t.appendChild(s)}))},highlightStyle:s,languageFromMIME:o});export{c as CodeHighlighter,e as codeHighlighterStyles};
