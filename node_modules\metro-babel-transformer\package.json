{"name": "metro-babel-transformer", "version": "0.83.1", "description": "🚇 Base Babel transformer for Metro.", "main": "src/index.js", "exports": {".": "./src/index.js", "./package.json": "./package.json", "./private/*": "./src/*.js"}, "repository": {"type": "git", "url": "**************:facebook/metro.git"}, "scripts": {"prepare-release": "test -d build && rm -rf src.real && mv src src.real && mv build src", "cleanup-release": "test ! -e build && mv src build && mv src.real src"}, "keywords": ["transformer", "metro"], "license": "MIT", "dependencies": {"@babel/core": "^7.25.2", "flow-enums-runtime": "^0.0.6", "hermes-parser": "0.29.1", "nullthrows": "^1.1.1"}, "engines": {"node": ">=20.19.4"}}