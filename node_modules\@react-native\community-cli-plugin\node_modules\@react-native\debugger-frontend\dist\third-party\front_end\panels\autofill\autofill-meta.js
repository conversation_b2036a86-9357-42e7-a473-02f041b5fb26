import*as i from"../../core/i18n/i18n.js";import*as e from"../../ui/components/legacy_wrapper/legacy_wrapper.js";import*as t from"../../ui/legacy/legacy.js";const o={autofill:"Autofill",showAutofill:"Show Autofill"},l=i.i18n.registerUIStrings("panels/autofill/autofill-meta.ts",o),a=i.i18n.getLazilyComputedLocalizedString.bind(void 0,l);let r;t.ViewManager.registerViewExtension({location:"drawer-view",id:"autofill-view",title:a(o.autofill),commandPrompt:a(o.showAutofill),order:100,persistence:"closeable",async loadView(){const i=await async function(){return r||(r=await import("./autofill.js")),r}();return e.LegacyWrapper.legacyWrapper(t.Widget.Widget,new i.AutofillView.AutofillView)}});
