import*as e from"../../core/common/common.js";import*as s from"../../core/host/host.js";import*as t from"../../core/platform/platform.js";import*as l from"../../core/sdk/sdk.js";import*as i from"../../ui/legacy/legacy.js";let d;class a extends e.ObjectWrapper.ObjectWrapper{#e;#s="";#t=[];#l=[];#i=null;constructor(){super(),l.TargetManager.TargetManager.instance().addModelListener(l.AutofillModel.AutofillModel,"AddressFormFilled",this.#d,this,{scoped:!0}),this.#e=e.Settings.Settings.instance().createSetting("auto-open-autofill-view-on-event",!0)}static instance(e={forceNew:null}){const{forceNew:s}=e;return d&&!s||(d=new a),d}onShowAutofillTestAddressesSettingsChanged(){for(const e of l.TargetManager.TargetManager.instance().models(l.AutofillModel.AutofillModel))e.setTestAddresses()}async#d({data:e}){this.#e.get()?(await i.ViewManager.ViewManager.instance().showView("autofill-view"),s.userMetrics.actionTaken(s.UserMetrics.Action.AutofillReceivedAndTabAutoOpened)):s.userMetrics.actionTaken(s.UserMetrics.Action.AutofillReceived),this.#i=e.autofillModel,this.#a(e.event),this.#s&&this.dispatchEventToListeners("AddressFormFilled",{address:this.#s,filledFields:this.#t,matches:this.#l,autofillModel:this.#i})}getLastFilledAddressForm(){return this.#s&&this.#i?{address:this.#s,filledFields:this.#t,matches:this.#l,autofillModel:this.#i}:null}#a({addressUi:e,filledFields:s}){this.#s=e.addressFields.map((e=>(e=>e.fields.filter((e=>e.value.length)).map((e=>e.value)).join(" "))(e))).filter((e=>e.length)).join("\n"),this.#t=s,this.#l=[];for(let e=0;e<this.#t.length;e++){if(""===this.#t[e].value)continue;const s=t.StringUtilities.escapeForRegExp(this.#t[e].value.replaceAll(/\s/g," ")).replaceAll(/([.,]+)\s/g,"$1? "),l=this.#s.replaceAll(/\s/g," ").matchAll(new RegExp(s,"g"));for(const s of l)void 0!==s.index&&this.#l.push({startIndex:s.index,endIndex:s.index+s[0].length,filledFieldIndex:e})}}}var o=Object.freeze({__proto__:null,AutofillManager:a});export{o as AutofillManager};
