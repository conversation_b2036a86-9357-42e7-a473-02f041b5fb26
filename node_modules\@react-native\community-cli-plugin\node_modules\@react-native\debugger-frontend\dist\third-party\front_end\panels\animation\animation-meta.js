import*as i from"../../core/common/common.js";import*as n from"../../core/i18n/i18n.js";import*as e from"../../core/sdk/sdk.js";import*as a from"../../ui/legacy/legacy.js";let o;const t={animations:"Animations",showAnimations:"Show Animations"},s=n.i18n.registerUIStrings("panels/animation/animation-meta.ts",t),m=n.i18n.getLazilyComputedLocalizedString.bind(void 0,s);async function r(){return o||(o=await import("./animation.js")),o}a.ViewManager.registerViewExtension({location:"drawer-view",id:"animations",title:m(t.animations),commandPrompt:m(t.showAnimations),persistence:"closeable",order:0,loadView:async()=>(await r()).AnimationTimeline.AnimationTimeline.instance()}),i.Revealer.registerRevealer({contextTypes:()=>[e.AnimationModel.AnimationGroup],destination:i.Revealer.RevealerDestination.SOURCES_PANEL,loadRevealer:async()=>new((await r()).AnimationTimeline.AnimationGroupRevealer)});
