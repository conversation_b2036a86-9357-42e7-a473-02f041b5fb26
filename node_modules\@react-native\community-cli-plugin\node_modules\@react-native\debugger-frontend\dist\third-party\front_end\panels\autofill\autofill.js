import"../../ui/components/adorners/adorners.js";import"../../ui/legacy/components/data_grid/data_grid.js";import*as e from"../../core/common/common.js";import*as o from"../../core/i18n/i18n.js";import*as t from"../../core/sdk/sdk.js";import*as r from"../../models/autofill_manager/autofill_manager.js";import*as a from"../../ui/components/helpers/helpers.js";import*as n from"../../ui/components/input/input.js";import*as i from"../../ui/components/legacy_wrapper/legacy_wrapper.js";import*as s from"../../ui/lit/lit.js";import*as l from"../../ui/visual_logging/visual_logging.js";var d={cssText:`*{box-sizing:border-box;min-width:0;min-height:0}:root{height:100%;overflow:hidden;interpolate-size:allow-keywords}body{height:100%;width:100%;position:relative;overflow:hidden;margin:0;cursor:default;font-family:var(--default-font-family);font-size:12px;tab-size:4;user-select:none;color:var(--sys-color-on-surface);background:var(--sys-color-cdt-base-container)}:focus{outline-width:0}.monospace{font-family:var(--monospace-font-family);font-size:var(\n    --monospace-font-size\n  )!important}.source-code{font-family:var(--source-code-font-family);font-size:var(\n    --source-code-font-size\n  )!important;white-space:pre-wrap;&:not(input)::selection{color:var(--sys-color-on-surface)}}.source-code.breakpoint{white-space:nowrap}.source-code .devtools-link.text-button{max-width:100%;overflow:hidden;text-overflow:ellipsis}img{-webkit-user-drag:none}iframe,\na img{border:none}.fill{position:absolute;inset:0}iframe.fill{width:100%;height:100%}.widget{position:relative;flex:auto;contain:style}.hbox{display:flex;flex-direction:row!important;position:relative}.vbox{display:flex;flex-direction:column!important;position:relative}.view-container > devtools-toolbar{border-bottom:1px solid var(--sys-color-divider)}.flex-auto{flex:auto}.flex-none{flex:none}.flex-centered{display:flex;align-items:center;justify-content:center}.overflow-auto{overflow:auto;background-color:var(--sys-color-cdt-base-container)}iframe.widget{position:absolute;width:100%;height:100%;inset:0}.hidden{display:none!important}.highlighted-search-result{border-radius:1px;background-color:var(--sys-color-yellow-container);outline:1px solid var(--sys-color-yellow-container)}.link{cursor:pointer;text-decoration:underline;color:var(--sys-color-primary);outline-offset:2px}button,\ninput,\nselect{font-family:inherit;font-size:inherit}select option,\nselect optgroup,\ninput{background-color:var(--sys-color-cdt-base-container)}input{color:inherit;&[type="checkbox"]{position:relative;outline:none;display:flex;align-items:center;justify-content:center;&:hover::after,\n    &:active::before{content:"";height:24px;width:24px;border-radius:var(--sys-shape-corner-full);position:absolute}&:not(.-theme-preserve){accent-color:var(--sys-color-primary-bright);color:var(--sys-color-on-primary)}&:not(:disabled):hover::after{background-color:var(--sys-color-state-hover-on-subtle)}&:not(:disabled):active::before{background-color:var(--sys-color-state-ripple-neutral-on-subtle)}&:not(:disabled):focus-visible::before{content:"";height:15px;width:15px;border-radius:5px;position:absolute;border:2px solid var(--sys-color-state-focus-ring)}&.small:hover::after,\n    &.small:active::before{height:12px;width:12px;border-radius:2px}}}input::placeholder{--override-input-placeholder-color:rgb(0 0 0/54%);color:var(--override-input-placeholder-color)}.theme-with-dark-background input::placeholder,\n:host-context(.theme-with-dark-background) input::placeholder{--override-input-placeholder-color:rgb(230 230 230/54%)}.harmony-input:not([type]),\n.harmony-input[type="number"],\n.harmony-input[type="text"]{padding:3px 6px;height:24px;border:1px solid var(--sys-color-neutral-outline);border-radius:4px;&.error-input,\n  &:invalid{border-color:var(--sys-color-error)}&:not(.error-input, :invalid):focus{border-color:var(--sys-color-state-focus-ring)}&:not(.error-input, :invalid):hover:not(:focus){background:var(--sys-color-state-hover-on-subtle)}}input[type="radio"]{height:17px;width:17px;min-width:17px;border-radius:8px;vertical-align:sub;margin:0 5px 5px 0;accent-color:var(--sys-color-primary-bright);color:var(--sys-color-on-primary);&:focus{box-shadow:var(--legacy-focus-ring-active-shadow)}}@media (forced-colors: active){input[type="radio"]{--gradient-start:ButtonFace;--gradient-end:ButtonFace;&:checked{--gradient-start:Highlight;--gradient-end:Highlight}}}input[type="range"]{appearance:none;margin:0;padding:0;height:10px;width:88px;outline:none;background:none}input[type="range"]::-webkit-slider-thumb,\n.-theme-preserve{appearance:none;margin:0;padding:0;border:0;width:12px;height:12px;margin-top:-5px;border-radius:50%;background-color:var(--sys-color-primary)}input[type="range"]::-webkit-slider-runnable-track{appearance:none;margin:0;padding:0;width:100%;height:2px;background-color:var(--sys-color-surface-variant)}input[type="range"]:focus::-webkit-slider-thumb{box-shadow:0 0 0 2px var(--sys-color-inverse-primary)}input[type="range"]:disabled::-webkit-slider-thumb{background-color:var(--sys-color-state-disabled)}@media (forced-colors: active){input[type="range"]{forced-color-adjust:none}}.highlighted-search-result.current-search-result{--override-current-search-result-background-color:rgb(255 127 0/80%);border-radius:1px;padding:1px;margin:-1px;background-color:var(--override-current-search-result-background-color)}.dimmed{opacity:60%}.editing{box-shadow:var(--drop-shadow);background-color:var(--sys-color-cdt-base-container);text-overflow:clip!important;padding-left:2px;margin-left:-2px;padding-right:2px;margin-right:-2px;margin-bottom:-1px;padding-bottom:1px;opacity:100%!important}.editing,\n.editing *{color:var(\n    --sys-color-on-surface\n  )!important;text-decoration:none!important}select{appearance:none;user-select:none;height:var(--sys-size-11);border:var(--sys-size-1) solid var(--sys-color-neutral-outline);border-radius:var(--sys-shape-corner-extra-small);color:var(--sys-color-on-surface);font:inherit;margin:0;outline:none;padding:0 var(--sys-size-9) 0 var(--sys-size-5);background-image:var(--combobox-dropdown-arrow);background-color:transparent;background-position:right center;background-repeat:no-repeat;&:disabled{opacity:100%;border-color:transparent;color:var(--sys-color-state-disabled);background-color:var(--sys-color-state-disabled-container);pointer-events:none}&:enabled{&:hover{background-color:var(--sys-color-state-hover-on-subtle)}&:active{background-color:var(--sys-color-state-ripple-neutral-on-subtle)}&:hover:active{background:var(--combobox-dropdown-arrow),linear-gradient(var(--sys-color-state-hover-on-subtle),var(--sys-color-state-hover-on-subtle)),linear-gradient(var(--sys-color-state-ripple-neutral-on-subtle),var(--sys-color-state-ripple-neutral-on-subtle));background-position:right center;background-repeat:no-repeat}&:focus{outline:var(--sys-size-2) solid var(--sys-color-state-focus-ring);outline-offset:-1px}}}@media (forced-colors: active) and (prefers-color-scheme: light){:root,\n  .theme-with-dark-background,\n  :host-context(.theme-with-dark-background){--combobox-dropdown-arrow:var(--image-file-arrow-drop-down-light)}}@media (forced-colors: active) and (prefers-color-scheme: dark){:root,\n  .theme-with-dark-background,\n  :host-context(.theme-with-dark-background){--combobox-dropdown-arrow:var(--image-file-arrow-drop-down-dark)}}.chrome-select-label{margin:0 var(--sys-size-10);flex:none;p p{margin-top:0;color:var(--sys-color-token-subtle)}.reload-warning{margin-left:var(--sys-size-5)}}.settings-select{margin:0}select optgroup,\nselect option{background-color:var(--sys-color-cdt-base-container);color:var(--sys-color-on-surface)}.gray-info-message{text-align:center;font-style:italic;padding:6px;color:var(--sys-color-token-subtle);white-space:nowrap}.empty-state{margin:var(--sys-size-5);display:flex;flex-grow:1;justify-content:center;align-items:center;flex-direction:column;text-align:center;min-height:fit-content;min-width:fit-content;> *{max-width:var(--sys-size-29)}.empty-state-header{font:var(--sys-typescale-headline5);margin-bottom:var(--sys-size-3)}.empty-state-description{font:var(--sys-typescale-body4-regular);color:var(--sys-color-on-surface-subtle);> x-link{white-space:nowrap;margin-left:var(--sys-size-3)}}> devtools-button{margin-top:var(--sys-size-7)}}dt-icon-label{flex:none}.full-widget-dimmed-banner a{color:inherit}.full-widget-dimmed-banner{color:var(--sys-color-token-subtle);background-color:var(--sys-color-cdt-base-container);display:flex;justify-content:center;align-items:center;text-align:center;padding:20px;position:absolute;inset:0;font-size:13px;overflow:auto;z-index:500}.dot::before{content:var(--image-file-empty);width:6px;height:6px;border-radius:50%;outline:1px solid var(--icon-gap-default);left:9px;position:absolute;top:9px;z-index:1}.green::before{background-color:var(--sys-color-green-bright)}.purple::before{background-color:var(--sys-color-purple-bright)}.expandable-inline-button{background-color:var(--sys-color-cdt-base-container);color:var(--sys-color-on-surface);cursor:pointer;border-radius:3px}.undisplayable-text,\n.expandable-inline-button{border:none;padding:1px 3px;margin:0 2px;font-size:11px;font-family:sans-serif;white-space:nowrap;display:inline-block}.undisplayable-text::after,\n.expandable-inline-button::after{content:attr(data-text)}.undisplayable-text{color:var(--sys-color-state-disabled);font-style:italic}.expandable-inline-button:hover,\n.expandable-inline-button:focus-visible{background-color:var(--sys-color-state-hover-on-subtle)}.expandable-inline-button:focus-visible{background-color:var(--sys-color-state-focus-highlight)}::selection{background-color:var(--sys-color-state-text-highlight);color:var(--sys-color-state-on-text-highlight)}button.link{border:none;background:none;padding:3px}button.link:focus-visible{outline:2px solid var(--sys-color-state-focus-ring);outline-offset:2px;border-radius:var(--sys-shape-corner-full)}.data-grid-data-grid-node button.link:focus-visible{border-radius:var(--sys-shape-corner-extra-small);padding:0;margin-top:3px}@media (forced-colors: active){.dimmed,\n  select:disabled{opacity:100%}.harmony-input:not([type]),\n  .harmony-input[type="number"],\n  .harmony-input[type="text"]{border:1px solid ButtonText}.harmony-input:not([type]):focus,\n  .harmony-input[type="number"]:focus,\n  .harmony-input[type="text"]:focus{border:1px solid Highlight}}input.custom-search-input::-webkit-search-cancel-button{appearance:none;width:16px;height:15px;margin-right:0;opacity:70%;mask-image:var(--image-file-cross-circle-filled);mask-position:center;mask-repeat:no-repeat;mask-size:99%;background-color:var(--icon-default)}input.custom-search-input::-webkit-search-cancel-button:hover{opacity:99%}.spinner::before{display:block;width:var(--dimension,24px);height:var(--dimension,24px);border:var(--override-spinner-size,3px) solid var(--override-spinner-color,var(--sys-color-token-subtle));border-radius:12px;clip:rect(0,var(--clip-size,15px),var(--clip-size,15px),0);content:"";position:absolute;animation:spinner-animation 1s linear infinite;box-sizing:border-box}@keyframes spinner-animation{from{transform:rotate(0)}to{transform:rotate(360deg)}}.adorner-container{display:inline-flex;vertical-align:middle}.adorner-container.hidden{display:none}.adorner-container devtools-adorner{margin-left:3px}:host-context(.theme-with-dark-background) devtools-adorner{--override-adorner-border-color:var(--sys-color-tonal-outline);--override-adorner-active-background-color:var(\n    --sys-color-state-riple-neutral-on-subtle\n  )}.panel{display:flex;overflow:hidden;position:absolute;inset:0;z-index:0;background-color:var(--sys-color-cdt-base-container)}.panel-sidebar{overflow-x:hidden;background-color:var(--sys-color-cdt-base-container)}iframe.extension{flex:auto;width:100%;height:100%}iframe.panel.extension{display:block;height:100%}@media (forced-colors: active){:root{--legacy-accent-color:Highlight;--legacy-focus-ring-inactive-shadow-color:ButtonText}}devtools-toolbar{& > *{position:relative;display:flex;background-color:transparent;flex:none;align-items:center;justify-content:center;height:var(--toolbar-height);border:none;white-space:pre;overflow:hidden;max-width:100%;color:var(--icon-default);cursor:default;& .devtools-link{color:var(--icon-default)}}.status-buttons{padding:0 var(--sys-size-2);gap:var(--sys-size-2)}& > :not(select){padding:0}& > devtools-issue-counter{margin-top:-4px;padding:0 1px}devtools-adorner.fix-perf-icon{--override-adorner-text-color:transparent;--override-adorner-border-color:transparent;--override-adorner-background-color:transparent}devtools-issue-counter.main-toolbar{margin-left:1px;margin-right:1px}.toolbar-dropdown-arrow{pointer-events:none;flex:none;top:2px}.toolbar-button.dark-text .toolbar-dropdown-arrow{color:var(--sys-color-on-surface)}.toolbar-button{white-space:nowrap;overflow:hidden;min-width:28px;background:transparent;border-radius:0;&[aria-haspopup="true"][aria-expanded="true"]{pointer-events:none}}.toolbar-item-search{min-width:5.2em;max-width:300px;flex:1 1 auto;justify-content:start;overflow:revert}.toolbar-text{margin:0 5px;flex:none;color:var(--ui-text)}.toolbar-text:empty{margin:0}.toolbar-has-dropdown{justify-content:space-between;height:var(--sys-size-9);padding:0 var(--sys-size-2) 0 var(--sys-size-4);margin:0 var(--sys-size-2);gap:var(--sys-size-2);border-radius:var(--sys-shape-corner-extra-small);&:hover::after,\n    &:active::before{content:"";height:100%;width:100%;border-radius:inherit;position:absolute;top:0;left:0}&:hover::after{background-color:var(--sys-color-state-hover-on-subtle)}&:active::before{background-color:var(--sys-color-state-ripple-neutral-on-subtle)}&:focus-visible{outline:var(--sys-size-2) solid var(--sys-color-state-focus-ring)}&[disabled]{pointer-events:none;background-color:var(--sys-color-state-disabled-container);color:var(--sys-color-state-disabled)}}.toolbar-has-dropdown-shrinkable{flex-shrink:1}.toolbar-has-dropdown .toolbar-text{margin:0;text-overflow:ellipsis;flex:auto;overflow:hidden;text-align:right}.toolbar-button:not(.toolbar-has-dropdown):focus-visible::before{position:absolute;inset:2px;background-color:var(--sys-color-state-focus-highlight);border-radius:2px;content:"";z-index:-1}.toolbar-glyph{flex:none}.toolbar-button:disabled{opacity:50%}.toolbar-button.copied-to-clipboard::after{content:attr(data-content);position:fixed;margin-top:calc(2 * var(--toolbar-height));padding:3px 5px;color:var(--sys-color-token-subtle);background:var(--sys-color-cdt-base-container);animation:2s fade-out;font-weight:normal;border:1px solid var(--sys-color-divider);border-radius:3px}.toolbar-button.toolbar-state-on .toolbar-glyph{color:var(--icon-toggled)}.toolbar-state-on.toolbar-toggle-with-dot .toolbar-text::after{content:"";position:absolute;bottom:2px;background-color:var(--sys-color-primary-bright);width:4.5px;height:4.5px;border:2px solid var(--override-toolbar-background-color,--sys-color-cdt-base-container);border-radius:50%;right:0}.toolbar-button.toolbar-state-on.toolbar-toggle-with-red-color .toolbar-glyph,\n  .toolbar-button.toolbar-state-off.toolbar-default-with-red-color\n    .toolbar-glyph{color:var(\n      --icon-error\n    )!important}.toolbar-button:not(\n      .toolbar-has-glyph,\n      .toolbar-has-dropdown,\n      .largeicon-menu,\n      .toolbar-button-secondary\n    ){font-weight:bold}.toolbar-button.dark-text .toolbar-text{color:var(\n      --sys-color-on-surface\n    )!important}.toolbar-button.toolbar-state-on .toolbar-text{color:var(--sys-color-primary)}.toolbar-button.toolbar-state-on:enabled:active .toolbar-text{color:var(--sys-color-primary-bright)}.toolbar-button:enabled:hover:not(:active) .toolbar-glyph{color:var(--sys-color-on-surface)}.toolbar-button:enabled:hover:not(:active) .toolbar-text{color:var(--sys-color-on-surface)}.toolbar-button.toolbar-state-on:enabled:hover:not(:active) .toolbar-glyph{color:var(--sys-color-primary)}.toolbar-button.toolbar-state-on:enabled:hover:not(:active) .toolbar-text{color:var(--sys-color-primary)}& > dt-checkbox{padding:0 5px 0 0}& > select{height:var(--sys-size-9);min-width:var(--sys-size-14)}.toolbar-input{box-shadow:inset 0 0 0 2px transparent;box-sizing:border-box;width:120px;height:var(--sys-size-9);padding:0 var(--sys-size-2) 0 var(--sys-size-5);margin:1px 3px;border-radius:100px;min-width:35px;position:relative;&.focused{box-shadow:inset 0 0 0 2px var(--sys-color-state-focus-ring)}&:not(:has(devtools-button:hover), .disabled):hover{background-color:var(--sys-color-state-hover-on-subtle)}&::before{content:"";box-sizing:inherit;height:100%;width:100%;position:absolute;left:0;background:var(--sys-color-cdt-base);z-index:-1}& > devtools-icon{color:var(--sys-color-on-surface-subtle);width:var(--sys-size-8);height:var(--sys-size-8);margin-right:var(--sys-size-3)}&.disabled > devtools-icon{color:var(--sys-color-state-disabled)}}.toolbar-filter .toolbar-input-clear-button{margin-right:var(--sys-size-4)}.toolbar-input-empty .toolbar-input-clear-button{display:none}.toolbar-prompt-proxy{flex:1}.toolbar-input-prompt{flex:1;overflow:hidden;white-space:nowrap;cursor:text;color:var(--sys-color-on-surface)}.toolbar-divider{background-color:var(--sys-color-divider);width:1px;margin:5px 4px;height:16px}.toolbar-spacer{flex:auto}.toolbar-button.emulate-active{background-color:var(--sys-color-surface-variant)}&:not([floating]) > :last-child:not(:first-child, select){flex-shrink:1;justify-content:left}&:not([floating]) > .toolbar-button:last-child:not(:first-child, select){justify-content:left;margin-right:2px}& > .highlight::before{content:"";position:absolute;inset:2px;border-radius:2px;background:var(--sys-color-neutral-container);z-index:-1}& > .highlight:focus-visible{background:var(--sys-color-tonal-container);& > .title{color:var(--sys-color-on-tonal-container)}}devtools-icon.leading-issue-icon{margin:0 7px}@media (forced-colors: active){.toolbar-button:disabled{opacity:100%;color:Graytext}devtools-toolbar > *,\n    .toolbar-text{color:ButtonText}.toolbar-button:disabled .toolbar-text{color:Graytext}devtools-toolbar > select:disabled{opacity:100%;color:Graytext}.toolbar-button.toolbar-state-on .toolbar-glyph{forced-color-adjust:none;color:Highlight}.toolbar-button.toolbar-state-on .toolbar-text{forced-color-adjust:none;color:Highlight}.toolbar-button:enabled:hover:not(:active) .toolbar-text,\n    .toolbar-button:enabled:focus:not(:active) .toolbar-text{color:HighlightText}.toolbar-button:disabled devtools-icon{color:GrayText}.toolbar-button:disabled .toolbar-glyph{color:GrayText}.toolbar-button:enabled.hover:not(:active) .toolbar-glyph{forced-color-adjust:none;color:Highlight}.toolbar-button:enabled:hover .toolbar-glyph,\n    .toolbar-button:enabled:focus .toolbar-glyph,\n    .toolbar-button:enabled:hover:not(:active) .toolbar-glyph,\n    .toolbar-button:enabled:hover devtools-icon,\n    .toolbar-button:enabled:focus devtools-icon{color:HighlightText}.toolbar-input{forced-color-adjust:none;background:canvas;box-shadow:var(--legacy-focus-ring-inactive-shadow)}.toolbar-input.focused,\n    .toolbar-input:not(.toolbar-input-empty){forced-color-adjust:none;background:canvas;box-shadow:var(--legacy-focus-ring-active-shadow)}.toolbar-input:hover{box-shadow:var(--legacy-focus-ring-active-shadow)}devtools-toolbar .devtools-link{color:linktext}.toolbar-has-dropdown{forced-color-adjust:none;background:ButtonFace;color:ButtonText}}}@keyframes fade-out{from{opacity:100%}to{opacity:0%}}.webkit-css-property{color:var(--webkit-css-property-color,var(--sys-color-token-property-special))}.webkit-html-comment{color:var(--sys-color-token-comment)}.webkit-html-tag{color:var(--sys-color-token-tag)}.webkit-html-tag-name,\n.webkit-html-close-tag-name{color:var(--sys-color-token-tag)}.webkit-html-pseudo-element{color:var(--sys-color-token-pseudo-element)}.webkit-html-js-node,\n.webkit-html-css-node{color:var(--text-primary);white-space:pre-wrap}.webkit-html-text-node{color:var(--text-primary);unicode-bidi:-webkit-isolate}.webkit-html-entity-value{background-color:rgb(0 0 0/15%);unicode-bidi:-webkit-isolate}.webkit-html-doctype{color:var(--text-secondary)}.webkit-html-attribute-name{color:var(--sys-color-token-attribute);unicode-bidi:-webkit-isolate}.webkit-html-attribute-value{color:var(--sys-color-token-attribute-value);unicode-bidi:-webkit-isolate;word-break:break-all}.devtools-link{color:var(--text-link);text-decoration:underline;outline-offset:2px;.elements-disclosure &{color:var(--text-link)}devtools-icon{vertical-align:baseline;color:var(--sys-color-primary)}:focus .selected & devtools-icon{color:var(--sys-color-tonal-container)}&:focus-visible{outline-width:unset}&.invalid-link{color:var(--text-disabled);text-decoration:none}&:not(.devtools-link-prevent-click, .invalid-link){cursor:pointer}@media (forced-colors: active){&:not(.devtools-link-prevent-click){forced-color-adjust:none;color:linktext}&:focus-visible{background:Highlight;color:HighlightText}}}\n/*# sourceURL=${import.meta.resolve("./inspectorCommon.css")} */\n`},c={cssText:`main{height:100%}.header{display:flex;border-bottom:1px solid var(--sys-color-divider);width:100%}.placeholder-container{height:calc(100% - 29px);display:flex;justify-content:center;align-items:center}.address{padding:10px;margin-right:auto}.filled-fields-grid{border-top:1px solid var(--sys-color-divider);box-sizing:border-box}.content-container{display:flex;flex-flow:column;height:100%}.grid-wrapper{flex-grow:1}devtools-data-grid{border:none;height:100%}.checkbox-label{display:flex;align-items:center}.right-to-left{border-bottom:1px solid var(--sys-color-divider);display:flex;flex-flow:row-reverse wrap;justify-content:flex-end}.label-container{padding:5px;display:flex;align-items:flex-start}.top-left-corner{border-bottom:1px solid var(--sys-color-divider);display:flex;padding:5px;gap:10px}.matches-filled-field{background-color:var(--sys-color-tonal-container)}.highlighted{background-color:var(--sys-color-state-focus-select)}.link{color:var(--sys-color-primary);text-decoration-line:underline}.feedback{margin:auto 5px auto auto}\n/*# sourceURL=${import.meta.resolve("./autofillView.css")} */\n`};const h=new CSSStyleSheet;h.replaceSync(c.cssText);const p=new CSSStyleSheet;p.replaceSync(d.cssText);const{html:u,render:b,Directives:{styleMap:g}}=s,{FillingStrategy:v}=Protocol.Autofill,f={noAutofill:"No autofill detected",toStartDebugging:"To start debugging autofill, use Chrome's autofill menu to fill an address form.",value:"Value",predictedAutofillValue:"Predicted autofill value",formField:"Form field",autocompleteAttribute:"Autocomplete attribute",attr:"attr",inferredByHeuristics:"Inferred by heuristics",heur:"heur",autoShow:"Automatically open this panel",showTestAddressesInAutofillMenu:"Show test addresses in autofill menu",autoShowTooltip:"Open the autofill panel automatically when an autofill activity is detected.",addressPreview:"Address preview",formInspector:"Form inspector",learnMore:"Learn more",sendFeedback:"Send feedback"},m="https://crbug.com/329106326",y=o.i18n.registerUIStrings("panels/autofill/AutofillView.ts",f),x=o.i18n.getLocalizedString.bind(void 0,y);class w extends i.LegacyWrapper.WrappableComponent{#e=this.attachShadow({mode:"open"});#o=this.#t.bind(this);#r;#a;#n="";#i=[];#s=[];#l=[];constructor(){super(),this.#r=e.Settings.Settings.instance().createSetting("auto-open-autofill-view-on-event",!0),this.#a=e.Settings.Settings.instance().createSetting("show-test-addresses-in-autofill-menu-on-event",!1)}connectedCallback(){this.#e.adoptedStyleSheets=[n.checkboxStyles,h,p];const e=r.AutofillManager.AutofillManager.instance(),o=e.getLastFilledAddressForm();o&&({address:this.#n,filledFields:this.#i,matches:this.#s}=o),e.addEventListener("AddressFormFilled",this.#d,this),t.TargetManager.TargetManager.instance().addModelListener(t.ResourceTreeModel.ResourceTreeModel,t.ResourceTreeModel.Events.PrimaryPageChanged,this.#c,this),a.ScheduledRender.scheduleRender(this,this.#o)}#c(){this.#n="",this.#i=[],this.#s=[],this.#l=[],a.ScheduledRender.scheduleRender(this,this.#o)}#d({data:e}){({address:this.#n,filledFields:this.#i,matches:this.#s}=e),this.#l=[],a.ScheduledRender.scheduleRender(this,this.#o)}async#t(){if(!a.ScheduledRender.isScheduledRender(this))throw new Error("AutofillView render was not scheduled");this.#n||this.#i.length?b(u`
      <main>
        <div class="content-container" jslog=${l.pane("autofill")}>
          <div class="right-to-left" role="region" aria-label=${x(f.addressPreview)}>
            <div class="header">
              <div class="label-container">
                <label class="checkbox-label" title=${x(f.showTestAddressesInAutofillMenu)}>
                  <input
                    type="checkbox"
                    ?checked=${this.#a.get()}
                    @change=${this.#h.bind(this)}
                    jslog=${l.toggle(this.#a.name).track({change:!0})}
                  >
                  <span>${x(f.showTestAddressesInAutofillMenu)}</span>
                </label>
              </div>
              <div class="label-container">
                <label class="checkbox-label" title=${x(f.autoShowTooltip)}>
                  <input
                    type="checkbox"
                    ?checked=${this.#r.get()}
                    @change=${this.#p.bind(this)}
                    jslog=${l.toggle(this.#r.name).track({change:!0})}
                  >
                  <span>${x(f.autoShow)}</span>
                </label>
              </div>
              <x-link href=${m} class="feedback link" jslog=${l.link("feedback").track({click:!0})}>${x(f.sendFeedback)}</x-link>
            </div>
            ${this.#u()}
          </div>
          ${this.#b()}
        </div>
      </main>
    `,this.#e,{host:this}):b(u`
        <main>
          <div class="top-left-corner">
            <label class="checkbox-label" title=${x(f.showTestAddressesInAutofillMenu)}>
              <input
                type="checkbox"
                ?checked=${this.#a.get()}
                @change=${this.#h.bind(this)}
                jslog=${l.toggle(this.#a.name).track({change:!0})}>
              <span>${x(f.showTestAddressesInAutofillMenu)}</span>
            </label>
            <label class="checkbox-label" title=${x(f.autoShowTooltip)}>
            <input
              type="checkbox"
              ?checked=${this.#r.get()}
              @change=${this.#p.bind(this)}
              jslog=${l.toggle(this.#r.name).track({change:!0})}>
            <span>${x(f.autoShow)}</span>
            </label>
            <x-link href=${m} class="feedback link" jslog=${l.link("feedback").track({click:!0})}>${x(f.sendFeedback)}</x-link>
          </div>
          <div class="placeholder-container" jslog=${l.pane("autofill-empty")}>
            <div class="empty-state">
              <span class="empty-state-header">${x(f.noAutofill)}</span>
              <div class="empty-state-description">
                <span>${x(f.toStartDebugging)}</span>
                <x-link href=${"https://goo.gle/devtools-autofill-panel"} class="link" jslog=${l.link("learn-more").track({click:!0})}>${x(f.learnMore)}</x-link>
              </div>
            </div>
          </div>
        </main>
      `,this.#e,{host:this})}#p(e){const{checked:o}=e.target;this.#r.set(o)}#h(e){const{checked:o}=e.target;this.#a.set(o),r.AutofillManager.AutofillManager.instance().onShowAutofillTestAddressesSettingsChanged()}#u(){if(!this.#n)return s.nothing;const e=(e,o)=>{const t=this.#n.substring(e,o).split("\n"),r=t.map(((e,o)=>o===t.length-1?e:u`${e}<br>`)),a=this.#s.some((o=>o.startIndex<=e&&o.endIndex>e));if(!a)return u`<span>${r}</span>`;const n=s.Directives.classMap({"matches-filled-field":a,highlighted:this.#l.some((o=>o.startIndex<=e&&o.endIndex>e))});return u`
        <span
          class=${n}
          @mouseenter=${()=>this.#g(e)}
          @mouseleave=${this.#v}
          jslog=${l.item("matched-address-item").track({hover:!0})}
        >${r}</span>`},o=[],t=new Set([0,this.#n.length]);for(const e of this.#s)t.add(e.startIndex),t.add(e.endIndex);const r=Array.from(t).sort(((e,o)=>e-o));for(let t=0;t<r.length-1;t++)o.push(e(r[t],r[t+1]));return u`
      <div class="address">
        ${o}
      </div>
    `}#g(e){this.#l=this.#s.filter((o=>o.startIndex<=e&&o.endIndex>e)),a.ScheduledRender.scheduleRender(this,this.#o)}#v(){this.#l=[],a.ScheduledRender.scheduleRender(this,this.#o)}#b(){if(!this.#i.length)return s.nothing;const e=new Set(this.#l.map((e=>e.filledFieldIndex)));return u`
      <div class="grid-wrapper" role="region" aria-label=${x(f.formInspector)}>
        <devtools-data-grid
          striped
          class="filled-fields-grid"
        >
          <table>
            <tr>
              <th id="name" weight="50" sortable>${x(f.formField)}</th>
              <th id="autofill-type" weight="50" sortable>${x(f.predictedAutofillValue)}</th>
              <th id="value" weight="50" sortable>${x(f.value)}</th>
            </tr>
            ${this.#i.map(((o,t)=>u`
                <tr style=${g({"font-family":"var(--monospace-font-family)","font-size":"var(--monospace-font-size)","background-color":e.has(t)?"var(--sys-color-state-hover-on-subtle)":null})}
                  @mouseenter=${()=>this.#f(t)}
                  @mouseleave=${this.#m.bind(this)}
                >
                  <td>${o.name||`#${o.id}`} (${o.htmlType})</td>
                  <td>
                      ${o.autofillType}
                      ${"autocompleteAttribute"===o.fillingStrategy?u`<devtools-adorner title=${x(f.autocompleteAttribute)} .data=${{name:o.fillingStrategy}}>
                              <span>${x(f.attr)}</span>
                            </devtools-adorner>`:"autofillInferred"===o.fillingStrategy?u`<devtools-adorner title=${x(f.inferredByHeuristics)} .data=${{name:o.fillingStrategy}}>
                              <span>${x(f.heur)}</span>
                            </devtools-adorner>`:s.nothing}
                  </td>
                  <td>"${o.value}"</td>
                </tr>`))}
          </table>
        </devtools-data-grid>
      </div>
    `}#f(e){this.#l=this.#s.filter((o=>o.filledFieldIndex===e)),a.ScheduledRender.scheduleRender(this,this.#o);const o=this.#i[e].fieldId,r=t.FrameManager.FrameManager.instance().getFrame(this.#i[e].frameId)?.resourceTreeModel().target();if(r){const e=new t.DOMModel.DeferredDOMNode(r,o),a=r.model(t.DOMModel.DOMModel);e&&a&&a.overlayModel().highlightInOverlay({deferredNode:e},"all")}}#m(){this.#l=[],a.ScheduledRender.scheduleRender(this,this.#o),t.OverlayModel.OverlayModel.hideDOMNodeHighlight()}}customElements.define("devtools-autofill-view",w);var k=Object.freeze({__proto__:null,AutofillView:w,i18nString:x});export{k as AutofillView};
