/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict
 * @format
 */

'use strict';

import type {ComponentShape, SchemaType} from '../../CodegenSchema';

const {
  IncludeTemplate,
  convertDefaultTypeToString,
  getImports,
  getSourceProp,
  isWrappedPropType,
} = require('./CppHelpers');

// File path -> contents
type FilesOutput = Map<string, string>;

const FileTemplate = ({
  imports,
  componentClasses,
  headerPrefix,
}: {
  imports: string,
  componentClasses: string,
  headerPrefix: string,
}) => `
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * ${'@'}generated by codegen project: GeneratePropsCpp.js
 */

${IncludeTemplate({headerPrefix, file: 'Props.h'})}
${imports}

namespace facebook::react {

${componentClasses}

} // namespace facebook::react
`;

const ComponentTemplate = ({
  className,
  extendClasses,
  props,
  diffProps,
}: {
  className: string,
  extendClasses: string,
  props: string,
  diffProps: string,
}) =>
  `
${className}::${className}(
    const PropsParserContext &context,
    const ${className} &sourceProps,
    const RawProps &rawProps):${extendClasses}

    ${props} {}
    ${diffProps}

`.trim();

function generatePropsDiffString(
  className: string,
  componentName: string,
  component: ComponentShape,
) {
  const diffProps = component.props
    .map(prop => {
      const typeAnnotation = prop.typeAnnotation;
      switch (typeAnnotation.type) {
        case 'StringTypeAnnotation':
        case 'Int32TypeAnnotation':
        case 'BooleanTypeAnnotation':
        case 'MixedTypeAnnotation':
          return `
  if (${prop.name} != oldProps->${prop.name}) {
    result["${prop.name}"] = ${prop.name};
  }`;
        case 'DoubleTypeAnnotation':
        case 'FloatTypeAnnotation':
          return `
  if ((${prop.name} != oldProps->${prop.name}) && !(std::isnan(${prop.name}) && std::isnan(oldProps->${prop.name}))) {
    result["${prop.name}"] = ${prop.name};
  }`;
        case 'ArrayTypeAnnotation':
        case 'ObjectTypeAnnotation':
        case 'StringEnumTypeAnnotation':
        case 'Int32EnumTypeAnnotation':
          return `
  if (${prop.name} != oldProps->${prop.name}) {
    result["${prop.name}"] = toDynamic(${prop.name});
  }`;
        case 'ReservedPropTypeAnnotation':
          switch (typeAnnotation.name) {
            case 'ColorPrimitive':
              return `
  if (${prop.name} != oldProps->${prop.name}) {
    result["${prop.name}"] = *${prop.name};
  }`;
            case 'ImageSourcePrimitive':
            case 'PointPrimitive':
            case 'EdgeInsetsPrimitive':
            case 'DimensionPrimitive':
              return `
  if (${prop.name} != oldProps->${prop.name}) {
    result["${prop.name}"] = toDynamic(${prop.name});
  }`;
            case 'ImageRequestPrimitive':
              // Shouldn't be used in props
              throw new Error(
                'ImageRequestPrimitive should not be used in Props',
              );
            default:
              (typeAnnotation.name: empty);
              throw new Error('Received unknown ReservedPropTypeAnnotation');
          }
        default:
          return '';
      }
    })
    .join('\n' + '    ');

  return `
#ifdef RN_SERIALIZABLE_STATE
ComponentName ${className}::getDiffPropsImplementationTarget() const {
  return "${componentName}";
}

folly::dynamic ${className}::getDiffProps(
    const Props* prevProps) const {
  static const auto defaultProps = ${className}();
  const ${className}* oldProps = prevProps == nullptr
      ? &defaultProps
      : static_cast<const ${className}*>(prevProps);
  if (this == oldProps) {
    return folly::dynamic::object();
  }
  folly::dynamic result = HostPlatformViewProps::getDiffProps(prevProps);
  ${diffProps}
  return result;
}
#endif`;
}
function generatePropsString(componentName: string, component: ComponentShape) {
  return component.props
    .map(prop => {
      const sourceProp = getSourceProp(componentName, prop);
      const defaultValue = convertDefaultTypeToString(componentName, prop);
      const isWrappedProp = isWrappedPropType(prop);
      let convertRawProp = `convertRawProp(context, rawProps, "${prop.name}", ${sourceProp}, {${defaultValue}})`;
      if (isWrappedProp) {
        convertRawProp += '.value';
      }
      return `${prop.name}(${convertRawProp})`;
    })
    .join(',\n' + '    ');
}

function getClassExtendString(component: ComponentShape): string {
  const extendString =
    ' ' +
    component.extendsProps
      .map(extendProps => {
        switch (extendProps.type) {
          case 'ReactNativeBuiltInType':
            switch (extendProps.knownTypeName) {
              case 'ReactNativeCoreViewProps':
                return 'ViewProps(context, sourceProps, rawProps)';
              default:
                (extendProps.knownTypeName: empty);
                throw new Error('Invalid knownTypeName');
            }
          default:
            (extendProps.type: empty);
            throw new Error('Invalid extended type');
        }
      })
      .join(', ') +
    `${component.props.length > 0 ? ',' : ''}`;

  return extendString;
}

module.exports = {
  generate(
    libraryName: string,
    schema: SchemaType,
    packageName?: string,
    assumeNonnull: boolean = false,
    headerPrefix?: string,
  ): FilesOutput {
    const fileName = 'Props.cpp';
    const allImports: Set<string> = new Set([
      '#include <react/renderer/core/propsConversions.h>',
      '#include <react/renderer/core/PropsParserContext.h>',
    ]);

    const componentProps = Object.keys(schema.modules)
      .map(moduleName => {
        const module = schema.modules[moduleName];
        if (module.type !== 'Component') {
          return;
        }

        const {components} = module;
        // No components in this module
        if (components == null) {
          return null;
        }

        return Object.keys(components)
          .map(componentName => {
            const component = components[componentName];
            const newName = `${componentName}Props`;

            const propsString = generatePropsString(componentName, component);
            const extendString = getClassExtendString(component);
            const diffPropsString = generatePropsDiffString(
              newName,
              componentName,
              component,
            );

            const imports = getImports(component.props);
            // $FlowFixMe[method-unbinding] added when improving typing for this parameters
            imports.forEach(allImports.add, allImports);

            const replacedTemplate = ComponentTemplate({
              className: newName,
              extendClasses: extendString,
              props: propsString,
              diffProps: diffPropsString,
            });

            return replacedTemplate;
          })
          .join('\n');
      })
      .filter(Boolean)
      .join('\n');

    const replacedTemplate = FileTemplate({
      componentClasses: componentProps,
      imports: Array.from(allImports).sort().join('\n').trim(),
      headerPrefix: headerPrefix ?? '',
    });

    return new Map([[fileName, replacedTemplate]]);
  },
};
