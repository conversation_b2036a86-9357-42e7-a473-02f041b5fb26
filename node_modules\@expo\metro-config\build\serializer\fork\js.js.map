{"version": 3, "file": "js.js", "sourceRoot": "", "sources": ["../../../src/serializer/fork/js.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;GASG;;;;;AAqBH,gCAaC;AAED,0CAoGC;AAED,kCAwBC;AAED,gCAEC;AAED,gCAEC;AAxKD,oDAA4B;AAC5B,gEAAsC;AAEtC,qEAAgE;AAEhE,gDAAwB;AAcxB,SAAgB,UAAU,CACxB,MAAc,EACd,OAAgB;IAEhB,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;IAEnC,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;QACxC,OAAO,EAAE,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;IAC9C,CAAC;IAED,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC3D,MAAM,GAAG,GAAG,IAAA,+CAAqB,EAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC;IAC/D,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;AACxB,CAAC;AAED,SAAgB,eAAe,CAC7B,MAAc,EACd,OAUC;IAED,MAAM,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAErD,MAAM,KAAK,GAAyC,EAAE,CAAC;IACvD,IAAI,QAAQ,GAAG,KAAK,CAAC;IAErB,MAAM,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;QACrF,IAAI,UAAU,GAAG,UAAU,CAAC,YAAY,CAAC;QAEzC,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;YACvB,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACpC,yDAAyD;gBACzD,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;YACpC,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CACb,WAAW,MAAM,CAAC,IAAI,iDAAiD,IAAI,CAAC,SAAS,CACnF,UAAU,EACV,IAAI,EACJ,CAAC,CACF,EAAE,CACJ,CAAC;YACJ,CAAC;QACH,CAAC;QACD,MAAM,EAAE,GAAG,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAC9C;QACE,+FAA+F;QAC/F,gEAAgE;QAChE,+BAA+B;QAE/B,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,EACtC,CAAC;YACD,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBAC9B,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;oBACtB,QAAQ,GAAG,IAAI,CAAC;oBAChB,6DAA6D;oBAE7D,oEAAoE;oBACpE,8CAA8C;oBAE9C,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,GAAG,CAAC,sBAAU,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;oBAC5E,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE,CAAC;wBAChD,8DAA8D;wBAC9D,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;wBACzC,YAAY,CAAC,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;wBACtC,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;oBACjC,CAAC;yBAAM,CAAC;wBACN,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;wBACxC,YAAY,CAAC,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;oBACzC,CAAC;oBAED,MAAM,UAAU,GAAG,cAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC;oBAC9E,KAAK,CAAC,EAAE,CAAC;wBACP,GAAG;4BACH,cAAI,CAAC,IAAI,CACP,cAAI,CAAC,OAAO,CAAC,UAAU,CAAC;4BACxB,2BAA2B;4BAC3B,cAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,cAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CACpD;4BACD,UAAU;4BACV,YAAY,CAAC,QAAQ,EAAE,CAAC;gBAC5B,CAAC;YACH,CAAC;iBAAM,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,wBAAwB,IAAI,IAAI,EAAE,CAAC;gBAC3E,QAAQ,GAAG,IAAI,CAAC;gBAChB,uGAAuG;gBACvG,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,wBAAwB,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC,CAAC,CAAC;IAEH,MAAM,MAAM,GAAG;QACb,QAAQ;QACR,QAAQ;YACN,CAAC,CAAC;gBACE,GAAG,kBAAkB;gBACrB,KAAK;aACN;YACH,CAAC,CAAC,kBAAkB;KACvB,CAAC;IAEF,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;QAChB,gEAAgE;QAChE,0DAA0D;QAC1D,MAAM,CAAC,IAAI,CAAC,cAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/D,CAAC;IAED,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;AAC3B,CAAC;AAED,SAAgB,WAAW,CAAC,MAI3B;IACC,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;IAE7E,IAAA,gBAAM,EACJ,SAAS,CAAC,MAAM,KAAK,CAAC,EACtB,gDAAgD,MAAM,CAAC,IAAI,IAAI,gBAAgB,QAC7E,SAAS,CAAC,MACZ,cAAc,CACf,CAAC;IAEF,MAAM,QAAQ,GAAa,SAAS,CAAC,CAAC,CAAmB,CAAC;IAE1D,IAAA,gBAAM,EACJ,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,EACxC,0CAA0C,MAAM,CAAC,IAAI,IAAI,gBAAgB,QACvE,QAAQ,CAAC,IACX,2BAA2B,QAAQ,CAAC,IAAI,CAAC,SAAS,GAAG,CACtD,CAAC;IAEF,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAgB,UAAU,CAAC,MAAc;IACvC,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AACrD,CAAC;AAED,SAAgB,UAAU,CAAC,MAAmB;IAC5C,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AACvC,CAAC"}